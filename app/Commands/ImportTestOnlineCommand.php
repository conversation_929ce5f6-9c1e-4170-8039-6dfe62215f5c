<?php
namespace App\Commands;

use App\Exports\TaskChoiceImport;
use App\Http\Models\ThiThu\Answer;
use App\Http\Models\ThiThu\Question;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Http;
use Maatwebsite\Excel\Facades\Excel;

class ImportTestOnlineCommand extends Command
{
    protected $name        = 'import:test_online';
    protected $description = 'Import test online from excel file';

    public function handle()
    {
        $data   = Excel::toArray(new TaskChoiceImport(), storage_path('app/imports/test_online_'. auth()->id() .'.xlsx'));
        $sheets = isset($data[0]) ? $data[0] : 0;

        $sheets = array_slice($sheets, 3);

        foreach ($sheets as $key => $sheet) {
            $this->info('Importing task no.: ' . $sheet[0]);

            $this->info("Importing for lesson id: " . $sheet[1]);

            $this->info('Importing question ' . $sheet[2]);

            $lessonId = $sheet[1];
            $question     = $this->highlightBrackets($this->getFurigana($sheet[2], false));

            if (empty($question)) {
                $this->error('Word is empty');
                continue;
            }

            $explain     = $this->highlightBrackets($this->getFurigana($sheet[3], false));

            $is_content   = $sheet[5];
            $point  = $sheet[6];
            $skill = $sheet[7];
            $answers = array_map(fn($item) => mb_trim(str_replace("\n", '', $item)), explode(';', $sheet[4]));

            $answers = array_map(function ($item, $key) {
                return [
                    'answer' => $this->highlightBrackets($this->getFurigana($item, false))
                ];
            }, $answers, array_keys($answers));

            $maxPosition = Question::where('lesson_id', $lessonId)->max('position');
            $entity = new Question();

            $entity->lesson_id = $lessonId;
            $entity->content     = $question;
            $entity->explain   = str_replace("\n", '<br/>', $explain);
            $entity->point = $point;
            $entity->is_content = $is_content;
            $entity->skill = $skill;
            $entity->position = $maxPosition + 1;
            $entity->save();

            foreach ($answers as $index => $answer) {
                $isAnswerCorrect = str_ends_with($answer['answer'], '*');
                $newAnswer = new Answer();
                $newAnswer->question_id = $entity->id;
                $newAnswer->content = $isAnswerCorrect ? rtrim($answer['answer'], '*') : $answer['answer'];
                $newAnswer->is_true = $isAnswerCorrect ? 1 : 0;
                $newAnswer->position = $index + 1;
                $newAnswer->created_at = now();
                $newAnswer->updated_at = now();
                $newAnswer->save();
                
                $this->info("Imported answer id: " . $entity->id);
            }
            $this->info("Imported question id: " . $entity->id);
        }
    }

    /**
     * Retrieves the furigana (phonetic guide) for the given text.
     *
     * This method attempts to fetch the furigana from an external service
     * specified by the MECAB_URL environment variable. If the `$force` parameter
     * is set to `false`, it will instead use a local method to convert the text
     * to furigana.
     *
     * @param string $text The input text for which furigana is to be retrieved.
     * @param bool $force Whether to force the use of the external service (default: true).
     * @return string The furigana representation of the text, or the original text if unavailable.
     */
    private function getFurigana($text, $force = true)
    {
        $url = env('MECAB_URL', 'http://mecab:8080');

        if (! $force) {
            return $this->convertToFurigana($text);
        }

        $response = Http::post($url, [
            'text' => $text,
        ]);

        $data = $response->json();

        if (isset($data['furigana_html'])) {
            return $data['furigana_html'];
        }

        return $text;
    }

    /**
     * Converts the given text to its Furigana representation.
     *
     * This method processes the input text and replaces any substrings
     * enclosed in parentheses with their corresponding Furigana representation
     * by using the `getFurigana` method.
     *
     * @param string $text The input text to be converted.
     * @return string The text with Furigana applied.
     */
    private function convertToFurigana($text)
    {
        return preg_replace_callback('/\((.*?)\)/u', function ($matches) {
            return $this->getFurigana($matches[1]);
        }, $text);
    }

    /**
     * Highlights text enclosed in square brackets and curly brackets.
     *
     * This method uses regular expressions to find all substrings enclosed
     * in square brackets ([ and ]) and curly brackets ({ and }) within the
     * given text and replaces them with the same text wrapped in <strong> tags
     * for square brackets and <span style="text-decoration:overline"> tags
     * for curly brackets.
     *
     * @param string $text The input text to be highlighted.
     * @return string The text with highlighted square brackets and curly brackets.
     */
    private function highlightBrackets($text)
    {
        $square_brackets = preg_replace('/\[(.*?)\]/', '<span style="color:#EF6D13">$1</span>', $text);
        $curly_brackets  = preg_replace('/\{(.*?)\}/', '<span style="text-decoration:overline">$1</span>', $square_brackets);

        // highlight text
        $underline = preg_replace('/\=(.*?)\=/', '<span style="text-decoration:underline; font-weight: bold; color: red">$1</span>', $curly_brackets);

        // câu hỏi dạng ___ ___ __*__ ___
        $star1 = str_replace('* _ _ _', '<span style="text-decoration: underline;text-decoration-thickness: 1px;">&nbsp;&nbsp; ★ &nbsp;&nbsp;</span> <span style="text-decoration: underline;text-decoration-thickness: 1px;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;</span> <span style="text-decoration: underline;text-decoration-thickness: 1px;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;</span> <span style="text-decoration: underline;text-decoration-thickness: 1px;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;</span>', $underline);
        $star2 = str_replace('_ * _ _', '<span style="text-decoration: underline;text-decoration-thickness: 1px;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;</span> <span style="text-decoration: underline;text-decoration-thickness: 1px;">&nbsp;&nbsp; ★ &nbsp;&nbsp;</span> <span style="text-decoration: underline;text-decoration-thickness: 1px;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;</span> <span style="text-decoration: underline;text-decoration-thickness: 1px;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;</span>', $star1);
        $star3 = str_replace('_ _ * _', '<span style="text-decoration: underline;text-decoration-thickness: 1px;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;</span> <span style="text-decoration: underline;text-decoration-thickness: 1px;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;</span> <span style="text-decoration: underline;text-decoration-thickness: 1px;">&nbsp;&nbsp; ★ &nbsp;&nbsp;</span> <span style="text-decoration: underline;text-decoration-thickness: 1px;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;</span>', $star2);
        $star4 = str_replace('_ _ _ *', '<span style="text-decoration: underline;text-decoration-thickness: 1px;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;</span> <span style="text-decoration: underline;text-decoration-thickness: 1px;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;</span> <span style="text-decoration: underline;text-decoration-thickness: 1px;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;</span><span style="text-decoration: underline;text-decoration-thickness: 1px;">&nbsp;&nbsp; ★ &nbsp;&nbsp;</span>', $star3);

        // đóng khung nội dung
        $frame = preg_replace('/\|(.*?)\|/', '<table border="1" cellspacing="1" cellpadding="1" style="width: 500px;"><tbody><tr><td>$1</td></tr></tbody></table>', $star4);
        return $frame;
    }
}
