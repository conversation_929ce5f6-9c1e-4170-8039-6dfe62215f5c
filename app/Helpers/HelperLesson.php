<?php

namespace App\Helpers;

use App\Http\Models\CourseOwner;
use App\Http\Models\LessonToTask;
use App\Http\Models\UserAd;
use App\Http\ModelsFrontend\Course;
use App\Http\ModelsFrontend\LessonCategory;
use App\Http\ModelsFrontend\LessonGroup;
use App\Http\ModelsFrontend\LessonProgress;
use App\Jobs\SysncLessonProgress;
use App\Models\Backend\Lesson;
use Illuminate\Support\Collection;
use HTMLPurifier;
use HTMLPurifier_Config;

class HelperLesson
{
    public static function calculationProcess($user_id, $lesson_id = null): void
    {
        if (!$user_id) {
            return;
        }
        $user = UserAd::query()->find($user_id);
        if (!$user) {
            return;
        }
        if (!$lesson_id) {
            $user = self::calculationAllProcessOfUser($user_id);
            foreach ($user->courses as $course) {
                CourseOwner::query()->where('course_id', $course->id)->where('owner_id', $user_id)->update(['progress' => round($course->process, 0, PHP_ROUND_HALF_ODD)]);
            }
            return;
        }
        $lesson_current = Lesson::query()
            ->where('show', '<>', 0)
            ->select('id', 'course_id')
            ->find($lesson_id);
        $courseCurrent = Course::find($lesson_current->course_id);

        $lesson_group = LessonGroup::query()->where('course_id', $lesson_current->course_id)
            ->where('is_specialezed', 0)
            ->where('show', 1)
            ->select('id')
            ->get()
            ->toArray();
        $lessons = Lesson::query()->whereIn('group_id', $lesson_group)
            ->where('show', '<>', 0)
            ->with(['progress' => function ($q) use ($user_id) {
                $q->select('id', 'user_id', 'lesson_id', 'video_progress', 'example_progress')->where('user_id', $user_id);
            }])
            ->select('id', 'group_id', 'course_id')
            ->get();

        $total_progress = $lessons->sum(function ($q) {
            return $q->progress->count();
        });

        $total_percent_progress = $lessons->sum(function ($q) {
            if (!$q->progress->isEmpty()) {
                return ($q->progress[0]->video_progress + $q->progress[0]->example_progress) / 2;
            }
            return 0;
        });

        $progress = round(($total_progress / $lessons->count()) * ($total_percent_progress / ($total_progress * 100) * 100), 0, PHP_ROUND_HALF_ODD);

        if (in_array($courseCurrent->SEOurl, ['so-cap-n4', 'so-cap-n5', 'luyen-de-n4', 'luyen-de-n5'])) {
            self::calculationBasicProgress($lesson_current->course_id);
        } else {
            CourseOwner::query()->where('course_id', $lesson_current->course_id)->where('owner_id', $user_id)->update(['progress' => $progress]);
        }
    }

    public static function calculationAllProcessOfUser($user_id)
    {
        $user = UserAd::query()
            ->select('id', 'name', 'email')
            ->with(['courses' => function ($q) use ($user_id) {
                $q->select('course.id', 'course.name')->with(['availableGroups' => function ($q) use ($user_id) {
                    $q->select('id', 'name', 'parent_id', 'course_id')->with(['lesson_default' => function ($q) use ($user_id) {
                        $q->select('id', 'group_id', 'course_id')->with(['progress' => function ($q) use ($user_id) {
                            $q->select('id', 'example_progress', 'lesson_id', 'user_id', 'video_progress')->where('user_id', $user_id);
                        }]);
                    }]);
                }]);
            }])->find($user_id);

        foreach ($user->courses as $key => $course) {
            $user->courses[$key]->total_lesson = 0;
            $user->courses[$key]->total_progress = 0;
            $user->courses[$key]->total_percent_progress = 0;
            $course->availableGroups->map(function ($q) use ($key, $user) {
                $q->lesson_default->map(function ($q) use ($key, $user) {
                    $user->courses[$key]->total_lesson++;
                    $user->courses[$key]->total_progress += $q->progress->count();
                    if ($q->progress->count() > 0) {
                        $user->courses[$key]->total_percent_progress += ($q->progress[0]->video_progress + $q->progress[0]->example_progress);
                    }
                });
            });
            $user->courses[$key]->average_percent_progress = $user->courses[$key]->total_progress != 0 ? round($user->courses[$key]->total_percent_progress / ($user->courses[$key]->total_progress * 2), 1, PHP_ROUND_HALF_ODD) : 0;
            $user->courses[$key]->process = round($user->courses[$key]->average_percent_progress  *  $user->courses[$key]->total_progress / $user->courses[$key]->total_lesson, 1, PHP_ROUND_HALF_ODD);
        }

        return $user;
    }

    public static function calculationBasicProgress($courseId) {
        $course = Course::find($courseId);
        $categories = LessonCategory::query()->where('course_id', $courseId)
            ->orderBy('stage', 'ASC')
            ->orderBy('sort', 'ASC')
            ->get();

        $stages = $categories->groupBy('stage_name');
        $groups = LessonGroup::query()
            ->where('lesson_group.course_id', $courseId)
            ->with(['lessons' => function ($query) {
                $query
                    ->with('component')
                    ->where('show', ACTIVE)
                    ->orderBy('sort_order', 'asc');
            }])
            ->select('lesson_group.*', 'lesson_categories.stage')
            ->join('lesson_categories', 'lesson_group.lesson_category_id', '=', 'lesson_categories.id')
            ->where('lesson_group.show', ACTIVE)
            ->where('lesson_group.is_specialezed', INACTIVE)
            ->orderBy('lesson_categories.stage', 'asc')
            ->orderBy('lesson_categories.sort', 'asc')
            ->orderBy('lesson_group.sort', 'asc')
            ->get();
        $lessonProgress = new Collection();
        if (auth()->check() && auth()->id()) {
            $lessonProgress = LessonProgress::query()
                ->select('id', 'lesson_id', 'user_id', 'video_progress', 'example_progress', 'updated_at')
                ->with([
                    'lesson' => function ($q) {
                        $q->select('id', 'course_id', 'group_id', 'show', 'type', 'require')
                            ->with(['lesson_group' => function ($q) {
                                $q->select('id', 'lesson_category_id', 'show');
                            }, 'component' => function ($q) {
                                $q->select('id', 'type', 'lesson_id', 'sort', 'show')
                                    ->where('show', 1)
                                    ->wherein('type', array_merge(LessonToTask::COMPONENT_TYPES_EXERCISE, [LessonToTask::TYPE_VIDEO]));
                            }]);
                    }
                ])
                ->where(function ($q) {
                    $q->whereNotNull('video_progress')->orWhereNotNull('example_progress');
                })
                ->where('user_id', auth()->id())
                ->whereHas('lesson', function ($q) use ($course) {
                    $q->where('course_id', $course->id)->where('show', ACTIVE);
                })
                ->whereHas('lesson.lesson_group', function ($q) {
                    $q->where('show', ACTIVE);
                })
                ->whereHas('lesson.lesson_group.category', function ($q) {
                    $q->where('show', ACTIVE);
                })
                ->get();
        }

        $groups->map(function ($group) use ($lessonProgress) {
            $lessonIds = $group->lessons->where('require', 1)->pluck('id');
            $nonRequireLessonIds = $group->lessons->where('require', 0)->pluck('id');
            if ((count($lessonIds) == 0 && count($nonRequireLessonIds) == 0) || count($lessonProgress) == 0) {
                $group->progress = 0;
                return $group;
            }
            $progress = $lessonProgress->whereIn('lesson_id', $lessonIds);
            $nonRequireProgress = $lessonProgress->whereIn('lesson_id', $nonRequireLessonIds);
            $group->totalLessonRequired = 0;
            $group->totalLessonNonRequired = 0;
            $group->lessonRequiredComplete = 0;
            $group->lessonNonRequiredComplete = 0;
            foreach ($progress as $itemProgress) {
                if (!$itemProgress->lesson) continue;
                $group->totalLessonRequired++;
                $componentExercise = $itemProgress->lesson->component->whereIn('type', LessonToTask::COMPONENT_TYPES_EXERCISE);
                if ((($itemProgress->lesson->component->isEmpty() || $componentExercise->isEmpty()) && $itemProgress->video_progress >= 85) || ($componentExercise && $itemProgress->example_progress >= 85)) {
                    $group->lessonRequiredComplete++;
                }
            }
            foreach ($nonRequireProgress as $nProgress) {
                if (!$nProgress->lesson) continue;
                $group->totalLessonNonRequired++;
                $componentExercise = $nProgress->lesson->component->whereIn('type', LessonToTask::COMPONENT_TYPES_EXERCISE);
                if ((($nProgress->lesson->component->isEmpty() || $componentExercise->isEmpty()) && $nProgress->video_progress >= 85) || ($componentExercise && $nProgress->example_progress >= 85)) {
                    $group->lessonNonRequiredComplete++;
                }
            }

            $group->is_pass = $lessonIds->count() > 0 ?
                ($lessonIds->count() == $group->lessonRequiredComplete ? 1 : 0) :
                ($nonRequireLessonIds->count() == $group->lessonNonRequiredComplete && $group->lessonNonRequiredComplete > 0 ? 1 : 0);
            return $group;
        });

        $completedStages = 0;
        $completedRequireStages = 0;
        $requireStages = 0;
        foreach ($stages as $stageName => $stage) {
            $stageCategories = $categories->whereIn('stage', $stage->pluck('stage')->toArray());
            $completedRequireCategories = 0;
            $completedNonRequireCategories = 0;
            $requiredCategories = 0;
            foreach ($stageCategories as $category) {
                $categoryGroups = $groups->where('lesson_category_id', $category->id);
                $completedRequireGroups = 0;
                $completedNonRequireGroups = 0;
                $requiredGroups = 0;

                foreach ($categoryGroups as $group) {
                    $groupIsRequired = $group->lessons->where('require', 1)->count();
                    if ($groupIsRequired) {
                        $requiredGroups++;
                    }

                    if ($groupIsRequired && $group->is_pass) {
                        $completedRequireGroups++;
                    }

                    if (!$groupIsRequired && $group->is_pass) {
                        $completedNonRequireGroups++;
                    }
                }
                if ($requiredGroups > 0) {
                    $requiredCategories++;
                    if ($completedRequireGroups == $requiredGroups) {
                        $completedRequireCategories += 1;
                    }
                } else {
                    if ($completedNonRequireGroups == $categoryGroups->count()) {
                        $completedNonRequireCategories += 1;
                    }
                }
            }
            if ($requiredCategories > 0) {
                $requireStages++;
                $stagePercent = round($completedRequireCategories * 100 / $requiredCategories);
                if ($stagePercent == 100) {
                    $completedRequireStages += $stagePercent;
                }
            } else {
                $stagePercent = round($completedNonRequireCategories * 100 / $stageCategories->count());
            }
        }
        return round($completedRequireStages / $requireStages);
    }

    public static function cleanHtml($dirtyHtml)
    {
        $config = HTMLPurifier_Config::createDefault();

        // --- Configuration Examples ---
        // 1. Allow only basic formatting
        $config->set('HTML.Allowed', 'p,b,strong,i,em,u,ul,ol,li,a[href|title],br,img[src|alt|title]');

        // 2. Allow specific CSS properties (use with extreme caution)
        // $config->set('CSS.AllowedProperties', 'font,font-size,font-weight,font-style,text-decoration,color,background-color,text-align');

        // 3. Disallow all iframes (good for security)
        // $config->set('HTML.BlockIframes', true);
        $config->set('HTML.SafeEmbed', false); // Disables <embed>
        $config->set('HTML.SafeObject', false); // Disables <object>

        // 4. Automatically add rel="nofollow" to external links
        $config->set('HTML.Nofollow', true);

        // 5. Open external links in a new tab
        // $config->set('URI.MakeAbsolute', true); // Convert relative URLs to absolute
        // $config->set('Attr.AllowedFrameTargets', ['_blank']); // Allow target="_blank"
        // You might need a custom attribute definition for target="_blank" if it's stripped.
        // Or, better yet, use JavaScript on the frontend to open external links in new tabs.

        // 6. Path to HTMLPurifier's cache (make sure it's writable by your web server)
        $cachePath = storage_path('app/purifier');
        if (!is_dir($cachePath)) {
            mkdir($cachePath, 0755, true);
        }
        $config->set('Cache.SerializerPath', $cachePath);
        $config->set('Cache.SerializerPermissions', 0755);


        $purifier = new HTMLPurifier($config);
        return $purifier->purify($dirtyHtml);
    }

}
