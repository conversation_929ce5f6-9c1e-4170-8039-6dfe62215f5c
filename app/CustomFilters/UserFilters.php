<?php
namespace App\CustomFilters;

use App\Http\Models\BookingKaiwa;
use App\Http\Models\BookingKaiwaHistory;
use App\Http\Models\Course;
use App\Http\Models\CourseOwner;
use App\Http\Models\LessonComponents;
use App\Http\ModelsFrontend\LessonProgress;
use App\QueryFilter;
use App\User;
use Carbon\Carbon;
use DB, DateTime;

class UserFilters extends QueryFilter
{
    private $timeType = 1;
    private $courseId = 0;
    private $boughtTime = null;
    public function id($id)
    {
        return $this->builder->where('id', $id);
    }
    public function is_tester($condition)
    {
        return $this->builder->where('is_tester', $condition);
    }

    public function course($course)
    {
        $this->courseId = $course;
        return $this->builder->whereHas('courses', function ($query) use ($course) {
            $query->where('course_id', $course);
        });
    }
    public function boughtTimeFrom($time) {
        return $this->builder->whereHas('course_owner', function ($query) use ($time) {
            $query->where('course_owner.course_id', $this->courseId)->where('created_at','>=', $time . ' 00:00:00');
        });
    }
    public function boughtTimeTo($time) {
        return $this->builder->whereHas('course_owner', function ($query) use ($time) {
            $query->where('course_owner.course_id', $this->courseId)->where('created_at','<=', $time . ' 23:59:59');
        });
    }
    public function completed($status)
    {
//        $ownerIds = CourseOwner::query()->where('course_id', $this->courseId)->toBase()->pluck('owner_id');
//        $course = Course::query()->with(['lessons' => function ($query) {
//            $query->where('show', 1);
//        }])->find($this->courseId);
//        $lessonIds = $course->lessons->pluck('id');
//        $lessonCount = $course->lessons->count();
//        $completedUsers = array();
//        $e = LessonProgress::query()
//            ->select('id')
//            ->whereIn('lesson_id', $lessonIds)
//            ->where('user_id', $ownerIds)
//            ->where('video_progress', '>',  80)
//            ->count();
//        dd($e);
        return $this->builder->whereHas('course_owner', function ($query) use ($status) {
            $query->where('course_owner.course_id', $this->courseId)->where('course_owner.progress', '>=', $status);
        });
    }
    public function skype() {
        //lay ra tat ca nhung user_id co trong course_owner co course_id = 21, kaiwa_total_booking <> null -> pluck
        $kaiwaUsersQuery = CourseOwner::query()->whereCourseOwner(21)->whereNotNull('kaiwa_total_booking');
        $kaiwaUsers = $kaiwaUsersQuery->get(['id','owner_id','kaiwa_total_booking']);
        $kaiwaUserIds = $kaiwaUsersQuery->pluck('owner_id')->all();
        $countBookingPerUser = BookingKaiwaHistory::query()->select('user_id',DB::raw('count(id) as total_booking'))->whereIn('user_id', $kaiwaUserIds)->groupBy('user_id')->get();
        $idsToFind = [];
        foreach ($countBookingPerUser as $key => $value) {

            $userHasBooking = $kaiwaUsers->first(function($item) use ($value) {
                return $item['owner_id'] == $value['user_id'];
            });
            if ($userHasBooking && ($userHasBooking['kaiwa_total_booking'] <= $value['total_booking'])) {
                array_push($idsToFind, $userHasBooking['owner_id']);
            }
        }

        $courseId = $this->courseId;
        return $this->builder->whereHas('courses', function ($query) use ($courseId){
            $query->where('course_id', $courseId)->whereNotNull('kaiwa_total_booking');
        })->whereNotIn('id', $idsToFind);
    }

    public function watch_expired_day($watch_expired_day)
    {
        $courseId = $this->courseId;
        $time = Carbon::parse($watch_expired_day);
        return $this->builder->whereHas('courses', function ($query) use ($time, $courseId) {
            $query->where('course_id', $courseId)->whereMonth('watch_expired_day', $time->month)->whereYear('watch_expired_day', $time->year);
        });
    }

    public function time_from($time)
    {
        return $this->builder->where('created_at','>=', $time . ' 00:00:00');
    }

    public function time_to($time)
    {
        return $this->builder->where('created_at','<=', $time . ' 23:59:59');
    }

    public function orderBy($orderBy) {
        $this->orderBy = $orderBy;
    }

    public function sort($sort) {
        return $this->builder->orderBy($this->orderBy, $sort);
    }
}
?>
