<?php

namespace App\Http\Controllers\Backend\Community;

use App\Base\Controller\BaseController;
use App\Http\Models\Community\CommunityGroup;
use App\Http\Models\Community\CommunityGroupUser;
use App\Http\Models\Invoice;
use App\Http\Models\School\SchoolUser;
use App\Http\ModelsFrontend\ConversationMessage;
use App\Http\ModelsFrontend\ConversationUser;
use App\Http\ModelsFrontend\Conversation;
use App\Services\Community\CommunityUserService;
use App\Services\Community\CommunityGroupService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Auth;

class CommunityUserController extends BaseController
{
    protected $communityUserService;
    protected $communityGroupService;

    public function __construct(
        CommunityUserService  $communityUserService,
        CommunityGroupService $communityGroupService
    )
    {
        $this->communityUserService = $communityUserService;
        $this->communityGroupService = $communityGroupService;
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        $data = $request->only(['group_id']);
        return view('backend.community.users.index')->with('group_id', $request->group_id);
    }

    public function getList(Request $request)
    {
        $page = $request->page ? $request->page : 1;
        $perPage = $request->perPage ? $request->perPage : 10;
        $dataFilter = $request->all();

        $group = CommunityGroup::query()
            ->select('id', 'name', 'slug', 'size', 'links', 'logs')
            ->with(['users' => function ($q) use ($request) {
                if (!empty($request['textSearch'])) {
                    $q->where('name', 'LIKE', '%' . $request['textSearch'] . '%');
                    $q->orWhere('email', 'LIKE', '%' . $request['textSearch'] . '%');
                    $q->orWhere('phone', 'LIKE', '%' . $request['textSearch'] . '%');
                }
                $q->with(['group_user' => function ($q) use ($request) {
                    if (!empty($request['note'])) {
                        $q->where('note', $request['note'], null);
                    }
                    $q->where('group_id', $request['group_id'])->select('id', 'group_id', 'user_id', 'invoice_id', 'note', 'send_doc', 'entry_point')
                        ->with(['invoice' => function ($q) {
                            $q->select('id', 'price', 'user_id', 'payment_status', 'invoice_status', 'discount_money', 'paid_money', 'paid_for_id', 'info_contact', 'note')
                                ->with(['children' => function ($q) {
                                    $q->orderBy('created_at', 'desc')->select('id', 'price', 'user_id', 'payment_status', 'invoice_status', 'discount_money', 'paid_money', 'paid_for_id', 'created_at');
                                }]);
                        }]);
                },
                    'conversation' => function ($q) {
                        $q->select('id', 'creator_id', 'friend_id');
                    }]);
            }])
            ->find($request['group_id']);

        $users = $group->users->filter(function ($q) {
            return !$q->group_user->isEmpty();
        });

        $users = $users->map(function ($item) use ($request) {
            $item->user_id = $item->id;
            $item->id = !$item->group_user->isEmpty() ? $item->group_user[0]->id : null;
            $item->entry_point = !$item->group_user->isEmpty() ? $item->group_user[0]->entry_point : 0;
            $item->is_debt = 0;
            $item->info_contact = !$item->group_user->isEmpty() && $item->group_user[0]->invoice != null ? json_encode($item->group_user[0]->invoice->info_contact) : null;
            $item->send_doc = !$item->group_user->isEmpty() ? $item->group_user[0]->send_doc : 0;
            $item->conversation_id = $item->conversation != null ? $item->conversation->id : null;
            $item->invoice_note = !$item->group_user->isEmpty() && $item->group_user[0]->invoice != null ? $item->group_user[0]->invoice->note : null;
            $item->note = !$item->group_user->isEmpty() ? $item->group_user[0]->note : null;
            if (!$item->group_user->isEmpty() && $item->group_user[0]->invoice != null) {
                if ($item->group_user[0]->invoice->children->isEmpty()) {
                    if (($item->group_user[0]->invoice->price - $item->group_user[0]->invoice->paid_money - $item->group_user[0]->invoice->discount_money != 0 || $item->group_user[0]->invoice->payment_status != 'paid') && $item->group_user[0]->invoice->invoice_status != 'canceled') {
                        $item->is_debt = 1;
                    }
                } else {
                    $invoice_last = $item->group_user[0]->invoice->children->first();
                    if (($invoice_last->price - $invoice_last->paid_money - $invoice_last->discount_money != 0 || $invoice_last->payment_status != 'paid') && $invoice_last->invoice_status != 'canceled') {
                        $item->is_debt = 1;
                    }
                }
            }
            return $item;
        });

        if (isset($request['is_debt']) && $request['is_debt'] != null && $request['is_debt'] != 'null') {
            $users = $users->filter(function ($q) use ($request) {
                return $q->is_debt == $request['is_debt'];
            });
        }
        data_forget($group, 'users');

        $data_return = [
            'from' => $page * $perPage - 10 + 1,
            'to' => $page * $perPage <= $users->count() ? $page * $perPage : $users->count(),
            'current_page' => (int)$page,
            'last_page' => $users->count() % $perPage == 0 ? $users->count() / $perPage : floor($users->count() / $perPage) + 1,
            'per_page' => (int)$perPage,
            'total' => $users->count(),
            'group' => $group
        ];

        $data_return['items'] = $users->forPage($page, $perPage)->values();

        $items = $this->communityUserService->searchAndPaginate($page, $perPage, $dataFilter);
        return $this->statusOk($data_return);
    }

    public function notInGroup(Request $request)
    {
        $page = $request->page ? $request->page : 1;
        $perPage = $request->perPage ? $request->perPage : 10;
        $dataFilter = $request->all();
        $items = $this->communityUserService->searchAndPaginateUserList($page, $perPage, $dataFilter);
        return $this->statusOk($items);
    }

    public function addGroupUser(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'user_id' => 'required',
            'group_id' => 'required'
        ]);

        if ($validator->fails()) {
            return $this->statusNG($validator->errors());
        }
        $user_id = $request->user_id;
        $group_id = $request->group_id;
        $result = $this->communityUserService->addGroupUser($user_id, $group_id);
        $this->communityGroupService->addGroupChatMember($user_id, $group_id);
        return $this->statusOk($result);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        $comGU = CommunityGroupUser::find($id);
        $group = CommunityGroup::find($comGU->group_id);
        if ($group) {
            $this->communityGroupService->removeGroupChatMember($comGU->user_id, $comGU->group_id);
        }

        $items = $this->communityUserService->delete($id);
        return $this->statusOk($items);
    }

    public function saveNote(Request $request)
    {

        $commGU = CommunityGroupUser::query()->where('id', $request->id)->first();

        if ($commGU) {
            $commGU->note = $request->note;
            $commGU->save();
            return $commGU;
        }

        return $this->statusNG(['message' => 'Có lỗi sảy ra liên hệ admin']);
    }

    public function saveSupport(Request $request)
    {
        $commGU = CommunityGroupUser::query()->where('id', $request->id)->first();

        if ($commGU) {
            $commGU->support_num = $request->update_value;
            $commGU->save();
            return $commGU;
        }

        return $this->statusNG(['message' => 'Có lỗi sảy ra liên hệ admin']);
    }

    public function saveSendDoc(Request $request)
    {
        $commGU = CommunityGroupUser::query()->where('id', $request->id)->first();

        if ($commGU) {
            $commGU->send_doc = $request->update_value;
            $commGU->save();
            return $commGU;
        }

        return $this->statusNG(['message' => 'Có lỗi sảy ra liên hệ admin']);
    }

    public function transfer(Request $request)
    {
        // update group id in community_group_user, leave a source from_group_id for tracking
        $groupUser = CommunityGroupUser::find($request->id);
        $oldGroupId = $groupUser->group_id;
        $newGroupId = $request->groupId;
        $groupUser->from_group_id = $groupUser->group_id;
        $groupUser->group_id = $request->groupId;
        $groupUser->save();

        // log to old group
        $oldGroup = CommunityGroup::find($oldGroupId);
        $newLog = $this->communityUserService->generateLog($groupUser->user->email, 'user_transfer', 'community_group_user.group_id', $oldGroupId, $newGroupId);
        $tmp = $oldGroup->logs ?? [];
        $tmp[] = $newLog;
        $oldGroup->logs = $tmp;
        $oldGroup->save();

        // log to new group
        $newGroup = CommunityGroup::find($newGroupId);
        $tmp = $newGroup->logs ?? [];
        $tmp[] = $newLog;
        $newGroup->logs = $tmp;
        $newGroup->save();

        return $this->statusOK();
    }

    public function update(Request $request)
    {
        CommunityGroupUser::where('group_id', $request->input('groupId'))
            ->where('user_id', $request->input('userId'))
            ->update([
                'note' => $request->input('note')
            ]);
        return response()->json();
    }

    public function toggleTax(Request $request)
    {
        $user = SchoolUser::find($request->id);
        $user->tax_id = $request->value ? 1 : null;
        $user->save();
        return $this->statusOK();
    }
}
