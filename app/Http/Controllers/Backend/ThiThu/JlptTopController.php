<?php

namespace App\Http\Controllers\Backend\ThiThu;

use App\Exports\JLPTResultExport;
use App\Http\Models\Admin;
use App\Http\Models\Invoice;
use App\Http\Models\Conversation;
use App\Http\Models\ConversationMessage;
use App\CustomFilters\JLPTResultFilters;
use App\Http\Models\ThiThu\Result;
use App\Http\Models\ThiThu\Career;
use App\Http\Models\Users;
use App\User;
use Carbon\Carbon;
use Gumlet\ImageResize;
use Input;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Illuminate\Support\Collection;
use Illuminate\Http\Response;
use Auth, DateTime;

use function foo\func;

class JlptTopController extends Controller
{
    public function __construct(){
        $this->middleware('auth:admin');
    }

    public function getTop(){

        $n1 = Result::whereRaw("(user_id, total_score) IN (select user_id, MAX(total_score) from results where `course` = 'N1' GROUP BY user_id order by total_score desc)")
              ->where('hidden', 0)->orderBy('total_score', 'desc')->get();
        $n2 = Result::whereRaw("(user_id, total_score) IN (select user_id, MAX(total_score) from results where `course` = 'N2' GROUP BY user_id order by total_score desc)")
              ->where('hidden', 0)->orderBy('total_score', 'desc')->get();
        $n3 = Result::whereRaw("(user_id, total_score) IN (select user_id, MAX(total_score) from results where `course` = 'N3' GROUP BY user_id order by total_score desc)")
              ->where('hidden', 0)->orderBy('total_score', 'desc')->get();
        $n4 = Result::whereRaw("(user_id, total_score) IN (select user_id, MAX(total_score) from results where `course` = 'N4' GROUP BY user_id order by total_score desc)")
              ->where('hidden', 0)->orderBy('total_score', 'desc')->get();
        $n5 = Result::whereRaw("(user_id, total_score) IN (select user_id, MAX(total_score) from results where `course` = 'N5' GROUP BY user_id order by total_score desc)")
              ->where('hidden', 0)->orderBy('total_score', 'desc')->get();
        
        // $n1 = Result::where('course', 'N1')->where('hidden', 0)->orderBy('total_score', 'DESC')->limit(60)->get();
        // $n2 = Result::where('course', 'N2')->where('hidden', 0)->orderBy('total_score', 'DESC')->limit(60)->get();
        // $n3 = Result::where('course', 'N3')->where('hidden', 0)->orderBy('total_score', 'DESC')->limit(60)->get();
        // $n4 = Result::where('course', 'N4')->where('hidden', 0)->orderBy('total_score', 'DESC')->limit(60)->get();
        // $n5 = Result::where('course', 'N5')->where('hidden', 0)->orderBy('total_score', 'DESC')->limit(60)->get();

        $arr = [$n1, $n2, $n3, $n4, $n5];

        for($i = 0; $i< 5; $i++){

            foreach($arr[$i] as $item){
                $tmp = Users::find($item->user_id);

                if($tmp && $tmp->address != null){
                    // $item->address = $tmp->address;
                    // $item->save();
                    $item->conversation = Conversation::where('creator_id', $tmp->id)->first();
                }
                $item->user = $tmp;
            }
        }

        return view('backend.thi_thu.top')
            ->with("arr", $arr);
    }

    public function changeAddress(Request $request){

        $item = Result::find($_POST['id']);

        //ẩn tất cả các bài còn lại
        // Result::where('user_id', $item->user_id)->where('id', '<>', $item->id)->update(['hidden' => 1]);

        if($item){
            $item->address = $_POST['address'];
            $item->save();
            return "success";
        }

        return "false";
    }

    /**
     * @param JLPTResultFilters $filters
     * @param JLPTResultExport $export
     * @param Request $request
     * @return mixed
     */
    public function exportExcel(JLPTResultFilters $filters, JLPTResultExport $export, Request $request) {
        $page = $request->page ? $request->page : 1;
        $per_page = $request->per_page ? $request->per_page : 20;
        // Handle the export
        $query = Result::filter($filters);

//        $total_result = $query->count();
        $results = $query->limit($per_page)->offset(($page - 1)*$per_page)->get();
        $export->setResults($results);
        return $export->handleExport();
    }
    /**
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function setPrinted(Request $request)
    {
        $result = Result::find($request->id);
        $result->is_printed = $request->is_printed;
        $result->save();

        return response()->json([
            'code' => 200,
            'data' => $result
        ]);
    }
    /**
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function setWrong(Request $request)
    {
        $result = Result::find($request->id);
        $result->is_wrong = $request->is_wrong;
        $result->save();

        return response()->json([
            'code' => 200,
            'data' => $result
        ]);
    }
    public function checkFailedPassed ($course, $score_1, $score_2, $score_3)
    {
        if ($score_1 < 19 || $score_2 < 19 || $score_3 < 19) {
            return false;
        } else {
            $total_score = $score_1 + $score_2 + $score_3;
            switch (true) {
                case ($course == 'N5' && $total_score >= 80): return true; break;
                case ($course == 'N4' && $total_score >= 90): return true; break;
                case ($course == 'N3' && $total_score >= 95): return true; break;
                case ($course == 'N2' && $total_score >= 90): return true; break;
                case ($course == 'N1' && $total_score >= 100): return true; break;
                default: return false; break;
            }
        }
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function sync(Request $request)
    {
        Result::select(['id', 'course', 'score_1', 'score_2', 'score_3', 'is_passed', 'platform'])->chunkById(100, function ($results) {
            foreach ($results as $result) {
                $result->is_passed = $this->checkFailedPassed($result->course, $result->score_1, $result->score_2, $result->score_3);
                $result->save();
            }
        }, 'id');
        return response()->json([
            'code' => 200
        ]);
    }
    /**
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function syncPlatform(Request $request)
    {
        Result::select(['id', 'user_id', 'platform'])->chunkById(100, function ($results) {
            foreach ($results as $result) {
                $user = Users::select('id', 'platform')->find($result->user_id);
                if ($user && $result->platform == null)
                    $result->platform = $user->platform;
                $result->save();
            }
        }, 'id');
        return response()->json([
            'code' => 200
        ]);
    }

    public function getIdsForPrint(JLPTResultFilters $filters, Request $request)
    {
        // Handle the export
        $query = Result::filter($filters)->orderBy('id', 'DESC')->get();

        $data = $query->groupBy('user_id');
        $newArray = new Collection();
        foreach ($data as $item) {
            $newArray->push($item->sortBy('course')->first());
        }
        $results = $newArray->pluck('id');
        return $results;
    }

    public function getUsersToSendMessage(JLPTResultFilters $filters, Request $request)
    {
        // Handle the export
        $query = Result::select('id', 'user_id', 'is_passed', 'course')->filter($filters)->where('promoted', 0)->orderBy('id', 'DESC');
        $results = $query->get();

        return response()->json([
            'code' => 200,
            'data' => $results
        ]);
    }

    public function sendPromote(Request $request)
    {
        // nếu k pass và có kq pass rồi thì k gửi
        // nếu k pass và có kq k pass nhắn r thì k gửi
        // nếu pass và có kq pass nhắn r thì k gửi
//        $passResult = Result::where('user_id', $request->user_id)->where('is_passed', 1)->first();
//        $promotedFailedResult = Result::where('user_id', $request->user_id)->where('is_passed', 0)->where('promoted', 1)->first();
//        $promotedPassResult = Result::where('user_id', $request->user_id)->where('is_passed', 1)->where('promoted', 1)->first();
        $promoted = Result::where('user_id', $request->user_id)->where('created_at', '>=', $request->filter['time_from'])->where('created_at', '<=', $request->filter['time_to'])->where('promoted', 1)->first();

        if (!is_null($promoted)) {
            return response()->json([
                'code' => 404,
                'data' => [
                    'message' => 'error1',
                ]
            ]);
        }
//        else {
//            if (!is_null($passResult)) {
//                return response()->json([
//                    'code' => 404,
//                    'data' => [
//                        'message' => 'error2',
//                    ]
//                ]);
//            }
//            if (!is_null($promotedFailedResult)) {
//                return response()->json([
//                    'code' => 404,
//                    'data' => [
//                        'message' => 'error3',
//                    ]
//                ]);
//            }
//        }
        $conversation = Conversation::where('creator_id', $request->user_id)->first();
        if (is_null($conversation)) {
            $newConversation = new Conversation();

            $newConversation->creator_id = $request->user_id;
            $newConversation->save();

            $conversation = $newConversation;
        }
        Result::where('id', $request->result_id)->update(['promoted' => 1]);
        return response()->json([
            'code' => 200,
            'data' => [
                'conversation' => $conversation,
            ]
        ]);
    }
    public function setFlag(Request $request)
    {
        $result = Result::find($request->id);
        if (isset($result)) {
            $result->admin_id = Auth::guard('admin')->user()->id;
            if ($request->flag == 'vn') {
                $result->jp = null;
                $result->vn = Carbon::now();
                $result->save();
            } else {
                $result->vn = null;
                $result->jp = Carbon::now();
                $result->save();
            }
        }
        return response()->json([
            'code' => 200,
            'data' => $result
        ]);
    }
    public function checkFlag(Request $request) {
        $result = Result::find($request->id);
        if (!is_null($result->admin_id)) {
            return response()->json([
                'code' => 400
            ]);
        } else {
            return response()->json([
                'code' => 200
            ]);
        }
    }
}
