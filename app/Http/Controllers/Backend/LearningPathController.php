<?php

namespace App\Http\Controllers\Backend;

use App\Http\Models\Course;
use App\Http\Models\LearningCheckpoint;
use App\Http\Models\LearningPath;
use App\Http\Models\LessonCategory;
use App\Http\Models\LessonHistory;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Input;
use DateTime;

class LearningPathController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth:admin');
    }

    public function index() {
        $courses = Course::query()->get(['id','name']);
        return view('backend.adventure.index')
            ->with('courses', $courses);
    }
    public function getPathList(Request $request)
    {
        $periods = LearningPath::query()->where('course_id', $request->courseId)->where('period_id', $request->periodId)->orderBy('sort_no', 'ASC')->get();
        return response()->json([
           'code' => 200,
           'data' => $periods
        ]);
    }

    public function addPath(Request $request)
    {
        $newPath = new LearningPath();
        $newPath->title = $request->title;
        $newPath->course_id = $request->course_id;
        $newPath->period_id = (int) $request->period;
        $newPath->status = (int) $request->status;
        $maxSort = LearningPath::query()->where('course_id', $request->course_id)->where('period_id', $request->period_id)->max('sort_no');
        $newPath->sort_no = $maxSort ? $maxSort + 1 : 1;
        $newPath->illustrator = [];

        if ($request->hasFile('iconImg')) {
            $img = Input::file('iconImg');

            $supported_image = array('.gif', '.jpg', '.jpeg', '.png');

            // Lấy ra thông tin đuôi file ảnh
            $info = getimagesize($img);
            $extension = strtolower(image_type_to_extension($info[2]));

            if (in_array($extension, $supported_image)) {
                // Nếu ảnh hợp lệ

                $date = new DateTime();

                // Tạo tên ảnh theo người dùng và ngày tháng
                $imgName = $date->getTimestamp() . "_" .
                    substr(md5($date->getTimestamp()), 0, 6) .
                    $extension;

                // Tạo folder neu chua ton tai
                if (!file_exists("upload/adventure/")) mkdir("upload/adventure/", 0777, true);

                // Copy dữ liệu vào thư mục
                $target = "upload/adventure/" . $imgName;

                if (!file_exists($target))
                    copy($img, $target);

                // Copy dữ liệu vào thư mục
                $targetCDN = "cdn/adventure/default/" . $imgName;

                if (!file_exists($targetCDN))
                    copy($img, $targetCDN);

                // Cập nhật lại dữ liệu icon mới
                $newPath->img = $imgName;
            } else {
                return response()->json([
                    'code' => 400,
                    'msg' => 'Ảnh không hợp lệ'
                ]);
            }
        }

        $newPath->save();
        return response()->json([
            'code' => 200,
            'data' => $newPath
        ]);
    }

    public function updatePath(Request $request)
    {
        $path = LearningPath::find($request->id);
        if (!is_null($path)) {
            $path->title = $request->title;
            $path->status = (int) $request->status;

            if ($request->hasFile('iconImg')) {
                $img = Input::file('iconImg');

                $supported_image = array('.gif', '.jpg', '.jpeg', '.png');

                // Lấy ra thông tin đuôi file ảnh
                $info = getimagesize($img);
                $extension = strtolower(image_type_to_extension($info[2]));

                if (in_array($extension, $supported_image)) {
                    // Nếu ảnh hợp lệ

                    $date = new DateTime();

                    // Tạo tên ảnh theo người dùng và ngày tháng
                    $imgName = $date->getTimestamp() . "_" .
                        substr(md5($date->getTimestamp()), 0, 6) .
                        $extension;

                    // Tạo folder neu chua ton tai
                    if (!file_exists("upload/lesson_category/")) mkdir("upload/lesson_category/", 0777, true);

                    // Copy dữ liệu vào thư mục
                    $target = "upload/adventure/" . $imgName;

                    if (!file_exists($target))
                        copy($img, $target);

                    // Copy dữ liệu vào thư mục
                    $targetCDN = "cdn/adventure/default/" . $imgName;

                    if (!file_exists($targetCDN))
                        copy($img, $targetCDN);

                    // Cập nhật lại dữ liệu icon mới
                    $path->img = $imgName;
                } else {
                    return response()->json([
                        'code' => 400,
                        'msg' => 'Ảnh không hợp lệ'
                    ]);
                }
            }
            $path->save();
            return response()->json([
                'code' => 200,
                'data' => $path
            ]);
        } else {
            return response()->json([
                'code' => 409,
                'msg' => 'Chặng không tồn tại'
            ]);
        }
    }
    public function destroy (Request $request)
    {
        $path = LearningPath::query()->find($request->id);
        $lessonIds = LearningCheckpoint::query()->where('path_id', $path->id)->pluck('lesson_id');
        LessonHistory::query()->whereIn('lesson_id', $lessonIds)->delete();
        LearningCheckpoint::query()->where('path_id', $path->id)->delete();
        $path->delete();
        return response()->json([
            'code' => 200
        ]);
    }

    public function sortPath (Request $request)
    {
        $ids = $request->ids;
        for ($i = 0; $i < count($ids); $i++) {
            $path = LearningPath::find($ids[$i]);
            if ($path) {
                $path->sort_no = $i + 1;
                $path->save();
            }
        }
        $paths = LearningPath::query()->where('course_id', $request->course_id)->where('period_id', $request->periodId)->orderBy('sort_no', 'ASC')->get();
        return response()->json([
            'code' => 200,
            'data' => $paths
        ]);
    }
    public function updateIllustrator(Request $request)
    {
        $path = LearningPath::query()->find($request->id);
        $path->illustrator = $request->illustrator;
        $path->save();
        return response()->json([
            'code' => 200,
            'data' => $path
        ]);
    }
}
