<?php

namespace App\Http\Controllers\Backend;

use App\Http\Controllers\Controller;
use App\Http\Models\Users;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use App\Http\Models\Combo;
use App\Http\Models\Invoice;
use App\Http\Models\Voucher;
use App\Http\Models\Payment_method;
use App\Http\Models\LessonOwner;
use App\Http\Models\LessonCourse;
use DB, Auth, Excel, DateTime, Exception;

class InvoiceTaxController extends Controller{

	public function __construct(){
        $this->middleware('auth:admin');
    }
    public function getInvoice(){
    	return view('backend.invoicetax.invoice');
    }

    // lay tat ca cac don hang cho xu ly
    public function getInvoicePending(){
        $invoice =  Invoice::where('invoice_status','new')->where('id', '>', 39195)->where('hide', false)->orderBy('id', 'DESC')->paginate(30);
        return view ( 'backend.invoicetax.invoice_status', compact( 'invoice' ))->render();
    }

    // lay tat ca cac don hang hoan thanh
    public function getInvoiceCompleted(){

    	$invoice =  Invoice::where('invoice_status','completed')->where('id', '>', 39195)->where('hide', false)->orderBy('id', 'DESC')->paginate(30);
                    
        foreach ($invoice as $inv) {
            if($inv->payment_method_id == '4'){
                $inv->voucher_key = "";
                $voucher = Voucher::where('invoice_id', $inv->id)->first();
                if($voucher) $inv->voucher_key = $voucher->key;
            }
        }

	    return view ( 'backend.invoicetax.invoice_status', compact( 'invoice' ))->render();
    }
   
    // lay tat ca cac don hang huy
    public function getInvoiceCancelled(){
    	$invoice =  Invoice::where('invoice_status','canceled')->where('id', '>', 39195)->where('hide', false)->orderBy('id', 'DESC')->paginate(30);
    	return view ( 'backend.invoicetax.invoice_status', compact( 'invoice' ))->render();
    }

    
    //active đơn hàng
    public function activeInvoice(Request $request){
        return $this->commonActive($request->id);
    }

    //truyen vao id cua bang lessonOrder
    public function commonActive($id){

        $invoice = Invoice::find($id);

        //Check đơn hàng là course
        if($invoice->product_type == 'course'){

            //lay thong tin trong bang khoa hoc
            $course = LessonCourse::find($invoice->product_id);

            //check sở hữu xem có chưa
            $oldItem = LessonOwner::where('owner_id', $invoice->user_id)->where('course_id', $invoice->product_id)->first();

            //nếu chưa tồn tại sở hữu -> tạo record mới
            if(!$oldItem){

                $active = new LessonOwner();
                $active->title    = $course->name;
                $active->owner_id = $invoice->user_id;
                $active->course_id = $invoice->product_id;
                $active->watch_expired_day = date('Y-m-d H:i:s', strtotime("+".strval($course->watch_expired)." days"));

                //nếu là admin kích hoạt
                if(Auth::guard('admin')->user())
                    $active->admin_active = Auth::guard('admin')->user()->username;
                else
                    $active->admin_active = "user";

                //gia hạn 10 buổi kaiwa
                if($active->course_id == 21) $active->kaiwa_total_booking = 10;

                $active->save();
            
            //nếu đã tồn tại sở hữu -> gia hạn thêm
            }else{

                //truong hop khoa đó còn hạn
                if($oldItem->watch_expired_day >= date("Y-m-d H:i:s")){

                    $oldItem->watch_expired_day = date('Y-m-d H:i:s', strtotime($oldItem->watch_expired_day."+".strval($course->watch_expired)." days"));

                    //gia hạn 10 buổi kaiwa
                    if($oldItem->course_id == 21) $oldItem->kaiwa_total_booking += 10;
                
                //truong hop khoa đó hết hạn
                }else{

                    $oldItem->watch_expired_day = date('Y-m-d H:i:s', strtotime("+".strval($course->watch_expired)." days"));
                    
                    //gia hạn 10 buổi kaiwa
                    if($oldItem->course_id == 21) $oldItem->kaiwa_total_booking = 10;

                }

                $oldItem->save();
                
            }

        }else if($invoice->product_type == 'combo'){

            $combo = Combo::find($invoice->product_id);
            $listCourses = json_decode($combo->services)->courses;
            $watch_expired = json_decode($combo->services)->course_watch_expired_value;
            // dd($listCourses);

            //xu ly voi tung khoa hoc trong combo dua vao id
            foreach ($listCourses as  $cid) {

                //lay thong tin trong bang khoa hoc
                $course = LessonCourse::find($cid);

                //check sở hữu xem có chưa
                $oldItem = LessonOwner::where('owner_id', $invoice->user_id)->where('course_id', $cid)->first();

                //nếu chưa tồn tại sở hữu -> tạo record mới
                if(!$oldItem){

                    $active = new LessonOwner();
                    $active->title    = $course->name;
                    $active->owner_id = $invoice->user_id;
                    $active->course_id = $cid;
                    $active->watch_expired_day = date('Y-m-d H:i:s', strtotime("+".strval($watch_expired)." days"));

                    //nếu là admin kích hoạt
                    if(Auth::guard('admin')->user())
                        $active->admin_active = Auth::guard('admin')->user()->username;
                    else
                        $active->admin_active = "user";

                    $active->save();
                
                //nếu đã tồn tại sở hữu -> gia hạn thêm
                }else{

                    //truong hop khoa đó còn hạn
                    if($oldItem->watch_expired_day >= date("Y-m-d H:i:s")){

                        $oldItem->watch_expired_day = date('Y-m-d H:i:s', strtotime($oldItem->watch_expired_day."+".strval($watch_expired)." days"));
                    
                    //truong hop khoa đó hết hạn
                    }else{

                        $oldItem->watch_expired_day = date('Y-m-d H:i:s', strtotime("+".strval($watch_expired)." days"));
                    }
                    $oldItem->save();
                    
                }

            }
        }

        //cập nhật lại trạng thái đơn hàng
        $invoice->payment_status = "paid";
        $invoice->invoice_status = "completed";
        $invoice->active_time = date('Y-m-d H:i:s');

        //nếu là admin kích hoạt
        if(Auth::guard('admin')->user()) {
            $invoice->admin_active_name = Auth::guard('admin')->user()->username;
            $invoice->admin_active = Auth::guard('admin')->user()->id;
        }
        else {
            $invoice->admin_active = "user";
        }
        $invoice->save();
        return 'success';

    }
    
    //huy don hang
    public function deActiveInvoice(Request $request){
        $invoice = Invoice::find($request->id);
        $invoice->payment_status = "unpaid";
        $invoice->invoice_status = "canceled";
        $invoice->save();
        return 'success';
    }






    // tim kiem don hang theo key
    public function findByKey($key){

        $invoice = Invoice::where('info_contact', 'LIKE', "%$key%")->where('id', '>', 39195)->where('hide', false)->paginate(10);
        foreach ($invoice as $inv) {
            if($inv->payment_method_id == '4'){
                $inv->voucher_key = "";
                $voucher = Voucher::where('invoice_id', $inv->id)->first();
                if($voucher) $inv->voucher_key = $voucher->key;
            }
        }
        
        return view ( 'backend.invoicetax.invoice_status', compact( 'invoice' ))->render();
    }

    
    //tim kiem don hang theo trang thai, kieu don hang va ngay bat dau va ngay ket thuc
    public function findByStartToEnd($date_from, $date_to, $status, $sort){

        if($status == 'pending') $status = 'new';

        //nếu là sắp xếp theo thứ tự
        if($sort == 'created')
            $invoice = Invoice::where('created_at', '>=', $this->normalizeDate($date_from))
                                ->where('created_at', '<=', $this->normalizeDate($date_to))
                                ->where('invoice_status', $status)->where('id', '>', 39195)->where('hide', false)
                                ->orderBy('id', 'DESC')->paginate(30);
        else if($sort == 'active_down')
            $invoice = Invoice::where('created_at', '>=', $this->normalizeDate($date_from))
                                ->where('created_at', '<=', $this->normalizeDate($date_to))
                                ->where('invoice_status', $status)->where('id', '>', 39195)->where('hide', false)
                                ->orderBy('price', 'DESC')->orderBy('id', 'DESC')->paginate(30);
        else if($sort == 'active_up')
            $invoice = Invoice::where('created_at', '>=', $this->normalizeDate($date_from))
                                ->where('created_at', '<=', $this->normalizeDate($date_to))
                                ->where('invoice_status', $status)->where('id', '>', 39195)->where('hide', false)
                                ->orderBy('price', 'ASC')->orderBy('id', 'DESC')->paginate(30);
        
        foreach ($invoice as $inv) {
            if($inv->payment_method_id == '4'){
                $inv->voucher_key = "";
                $voucher = Voucher::where('invoice_id', $inv->id)->first();
                if($voucher) $inv->voucher_key = $voucher->key;
            }
        }

        return  view ( 'backend.invoicetax.invoice_status', compact( 'invoice' ))->render();
    }

    // tim kiem don hang
    public function findInvoice(Request $request){
        if($request->ajax()){

            if($request->email != NULL ){
                return $this->findByKey($request->email);

            }else if($request->date_from != NUll && $request->date_to != NULL){
                return $this->findByStartToEnd($request->date_from, $request->date_to, $request->status, $request->method);
            }
        }
    }


    // tim kiem don hang
    public function hideInvoice(Request $request){

        $inv = Invoice::find($request->id);
        $inv->hide = true;
        $inv->save();
        return "success";

    }





    //xuat don hang ra file excel
    public function exportInvoice(Request $request){

        $date_from = $request->date_from;
        $date_to = $request->date_to;
        $status = $request->status;
        $sort = $request->orderby;

        Excel::create('donhang', function($excel) use ($date_from, $date_to, $status, $sort) {

            $excel->sheet('donhang', function($sheet) use ($date_from, $date_to, $status, $sort) {

                //query database;
                $invoice = $this->queryDB($date_from, $date_to, $status, $sort);

                foreach($invoice as $item) {

                    //nếu là chuyển phát thẻ hoặc inapp
                    if($item->payment_method_id == 4 || $item->payment_method_id == 6 )
                        $item->admin_active_name = "User kích";

                    $data[] = array(
                        $item->id,
                        // ($item->product_type == 'combo') ? 'Mua combo' : 'Mua khóa học',
                        $item->product_name,
                        json_decode($item->info_contact)->name,
                        json_decode($item->info_contact)->phone,
                        json_decode($item->info_contact)->email,
                        json_decode($item->info_contact)->address,
                        number_format($item->price).'đ',
                        ($item->invoice_status == 'completed') ? 'Hoàn thành' : (($item->invoice_status == 'new') ? 'Chờ xử lý' : 'Đã hủy'),
                        $item->getPaymentMethodName(),
                        $item->admin_active_name,
                        $item->active_time,
                        $item->created_at,
                    );
                }

                $headings = array('Mã số', 'Tên sản phẩm', 'Tên', 'Điện thoại', 'Email', 'Địa chỉ', 'Số tiền', 'Đơn hàng', 'Hình thức', 'Người kích', 'Thời gian kích', 'Ngày tạo');
                array_unshift($data, $headings);
                $sheet->fromArray($data, null, 'A1', false, false);
            });
        })->store('xls', public_path(). '/upload/excel/');
        return response()->download(public_path(). '/upload/excel/donhang.xls');
    }
    
    //hàm truy vấn phục vụ xuất excel
    public function queryDB($date_from, $date_to, $status, $sort){

        if($status == 'pending') $status = 'new';

        //nếu là sắp xếp theo thứ tự
        if($sort == 'created')
            $invoices = Invoice::where('created_at', '>=', $this->normalizeDate($date_from))
                                ->where('created_at', '<=', $this->normalizeDate($date_to))
                                ->where('invoice_status', $status)->where('id', '>', 39195)->where('hide', false)
                                ->orderBy('id', 'DESC')->paginate(30);
        else if($sort == 'active_down')
            $invoices = Invoice::where('created_at', '>=', $this->normalizeDate($date_from))
                                ->where('created_at', '<=', $this->normalizeDate($date_to))
                                ->where('invoice_status', $status)->where('id', '>', 39195)->where('hide', false)
                                ->orderBy('price', 'DESC')->orderBy('id', 'DESC')->paginate(30);
        else if($sort == 'active_up')
            $invoices = Invoice::where('created_at', '>=', $this->normalizeDate($date_from))
                                ->where('created_at', '<=', $this->normalizeDate($date_to))
                                ->where('invoice_status', $status)->where('id', '>', 39195)->where('hide', false)
                                ->orderBy('price', 'ASC')->orderBy('id', 'DESC')->paginate(30);
        
        foreach ($invoices as $inv) {
            if($inv->payment_method_id == '4'){
                $inv->voucher_key = "";
                $voucher = Voucher::where('invoice_id', $inv->id)->first();
                if($voucher) $inv->voucher_key = $voucher->key;
            }
        }

        return $invoices;

    }


    public function normalizeDate($date){

        return DateTime::createFromFormat('d/m/Y H:i', $date);
    }



}