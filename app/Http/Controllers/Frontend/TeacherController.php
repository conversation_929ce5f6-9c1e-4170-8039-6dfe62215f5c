<?php

namespace App\Http\Controllers\Frontend;

use App\Http\Controllers\Controller;
use App\Http\ModelsFrontend\Teacher;
use App\Http\ModelsFrontend\Lesson;

use DB,<PERSON>ie,App;

class TeacherController extends Controller{

    //trang giáo viên
    public function getTeacherPage(){

        $teacherData = (object) [
            'feature_teacher' => [],
            'normal_teacher' => []
        ];
    	$teachers = Teacher::query()->where('show', 1)->orderBy('sort_order', 'asc')->get();
        return view('frontend.teacher.teachers')
        	->with("teachers", $teachers);
    }

    //xem chi tiết giáo viên
    public function getTeacherDetail($url){
    	$author = Teacher::where('SEOurl', '=', $url)->first();
      $previewLessons = [];
      if (!is_null($author)) {
        $previewLessons = Lesson::where('author_id', '=', $author->id)->where('avatar_name', '<>', null)->take(3)->get();
      }

      return view('frontend.teacher.newDetail')
        	->with("author", $author)
        	->with("previewLessons", $previewLessons);
    }

}
