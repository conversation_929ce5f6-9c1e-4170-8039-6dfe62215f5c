<?php

namespace App\Http\Controllers\Frontend;

use App\Http\Controllers\Controller;
use App\Http\Models\LessonToTask;
use App\Http\ModelsFrontend\Course;
use App\Models\Backend\Lesson;
use App\Models\FrontEnd\UserFlashCardPackage;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use App\Http\Models\CourseOwner;
use Carbon\Carbon;

class VocabularyController extends Controller
{
    public function index(Request $request): \Illuminate\Contracts\View\Factory|\Illuminate\Foundation\Application|\Illuminate\Contracts\View\View|\Illuminate\Contracts\Foundation\Application
    {
        $jlpt = DB::table('course as c')
            ->leftJoin('lesson as l', 'l.course_id', '=', 'c.id')
            ->leftJoin('lesson_group as lg', 'l.group_id', '=', 'lg.id')
            ->leftJoin('lesson_categories as cat', 'lg.lesson_category_id', '=', 'cat.id')
            ->leftJoin('lesson_components as lc', function ($join) {
                $join->on('lc.lesson_id', '=', 'l.id')
                    ->where('l.show', ACTIVE)
                    ->where('c.show', ACTIVE)
                    ->where('lg.show', ACTIVE)
                    ->where('cat.status', ACTIVE)
                    ->where('lc.show', ACTIVE)
                    ->where('lc.type', '=', LessonToTask::TYPE_FLASHCARD);
            })
            ->groupBy('c.id')
            ->whereIn('c.id', [39, 40])
            ->select('c.id', 'c.name', 'c.SEOurl', DB::raw('COALESCE(COUNT(lc.id), 0) as word_count'))
            ->get();

        $courseOwner = CourseOwner::query()
            ->where('owner_id', auth()->id())
            ->whereIn('course_id', [39, 40])
            ->where('watch_expired_day', '>', Carbon::now())
            ->get();
        
        $jlpt->map(function ($jlpt) use ($courseOwner) {
            $jlpt->hasCourseOwner = $courseOwner->contains('course_id', $jlpt->id);
            return $jlpt;
        });

        $jlpt = $jlpt->keyBy('id');

        foreach (Course::TYPES_ID_JLPT as $key => $value) {
            if (!$jlpt->has($key)) {
                $jlpt->push((object) [
                    'id' => $key,
                    'name' => $value,
                    'SEOurl' => 'khoa-' . strtolower($value),
                    'word_count' => 0
                ]);
            } else {
                $jlpt[$key]->name = $value;
            }
        }

        $jlpt = $jlpt->sortByDesc('name')->values();

        $favorite = $this->overviewFavorite();
        $hideFooter = true;
        $hideSupportMenu = true;

        return view('frontend.vocabulary.index', compact('jlpt', 'favorite', 'hideFooter', 'hideSupportMenu'));
    }

    /*
     * Lấy ra danh sách các khoá học của user
     * @param Request $request
     * @param int $type ('normal', 'like')
     * @param int $course_id
     * @param string $query (find, all)
     * @return \Illuminate\Http\JsonResponse
     */
    public function getCourse(Request $request): \Illuminate\Http\JsonResponse
    {
        $courses = UserFlashCardPackage::query()
            ->where('user_id', auth()->id());
        if ($request['type']) {
            $courses->where('type', $request['type']);
        }
        if ($request['course_id']) {
            if (is_array($request['course_id'])) {
                $courses->whereIn('course_id', $request['course_id']);
            } else {
                $courses->where('course_id', $request['course_id']);
            }
        }

        if ($request['query'] == 'find') {
            $courses = $courses->first();
        } else {
            $courses = $courses->get();
        }

        return response()->json([
            'code' => 200,
            'data' => $courses
        ]);
    }

    public function getFlashcardByCourse(Request $request): \Illuminate\Http\JsonResponse
    {
        if (!$request['course_id']) {
            return response()->json([
                'code' => 400,
                'message' => 'Missing course id'
            ]);
        }

        $flashcards = DB::table('lesson_components as lc')
            ->select('lc.*', 'l.course_id', 'c.name as course_name')
            ->join('lesson as l', 'lc.lesson_id', '=', 'l.id')
            ->join('course as c', 'l.course_id', '=', 'c.id')
            ->join('lesson_group as lg', 'l.group_id', '=', 'lg.id')
            ->join('lesson_categories as cat', 'lg.lesson_category_id', '=', 'cat.id')
            ->where('lc.type', LessonToTask::TYPE_FLASHCARD)
            ->where('l.show', ACTIVE)
            ->where('c.show', ACTIVE)
            ->where('lg.show', ACTIVE)
            ->where('cat.status', ACTIVE)
            ->where('lc.show', ACTIVE)
            ->where('l.course_id', $request['course_id'])
            ->get();

        $likeFlashcard = UserFlashCardPackage::query()
            ->where('user_id', auth()->id())
            ->where('type', 'like')
            ->first();

        if (!$likeFlashcard) {
            $likeFlashcardData = collect();
        } else {
            $likeFlashcardData = collect(json_decode($likeFlashcard->data));
        }
        
        // dd($likeFlashcardData, Auth::id());
        
        // lặp qua flashcards và check xem có trong likeFlashcard không
        $flashcards->map(function ($flashcard) use ($likeFlashcardData) {
            $flashcard->isLike = $likeFlashcardData->contains('i', $flashcard->id);
            return $flashcard;
        });

        return response()->json([
            'code' => 200,
            'data' => $flashcards
        ]);
    }

    public function getFlashcardById(Request $request): \Illuminate\Http\JsonResponse
    {
        if (!$request['ids']) {
            return response()->json([
                'code' => 400,
                'message' => 'Missing flashcard ids'
            ]);
        }

        $flashcards = LessonToTask::query()
            ->whereIn('id', $request['ids'])
            ->select('id', 'value', 'lesson_id', 'show', 'type')
            ->get();

        $flashcards = $flashcards->map(function ($flashcard) {
            $flashcard->value = json_decode($flashcard->value);
            return $flashcard;
        });

        return response()->json([
            'code' => 200,
            'data' => $flashcards
        ]);
    }

    public function deleteFlashcard(Request $request): \Illuminate\Http\JsonResponse
    {
        if (!$request['course_id'] || !$request['user_id']) {
            return response()->json([
                'code' => 400,
                'message' => 'Missing course id or user id'
            ]);
        }

        $flashcard = UserFlashCardPackage::query()
            ->where('user_id', $request['user_id'])
            ->where('course_id', $request['course_id'])
            ->first();

        if (!$flashcard) {
            return response()->json([
                'code' => 404,
                'message' => 'Flashcard not found'
            ]);
        }

        $flashcard->update([
            'data' => json_encode([])
        ]);

        return response()->json([
            'code' => 200,
            'message' => 'Flashcard deleted successfully'
        ]);
    }

    public function searchWord(Request $request): \Illuminate\Http\JsonResponse
    {
        if (!$request['searchQuery']) {
            return response()->json([
                'code' => 400,
                'message' => 'Missing search query'
            ]);
        }

        $searchQuery = json_encode($request['searchQuery']);
        $searchQuery = str_replace('"', '', $searchQuery);
        $searchQuery = preg_replace('/\\\\u/', '\\\\\u', $searchQuery);

        $flashcards = DB::table('lesson_components as lc')
            ->select('lc.*', 'l.course_id', 'c.name as course_name')
            ->join('lesson as l', 'lc.lesson_id', '=', 'l.id')
            ->join('course as c', 'l.course_id', '=', 'c.id')
            ->join('lesson_group as lg', 'l.group_id', '=', 'lg.id')
            ->join('lesson_categories as cat', 'lg.lesson_category_id', '=', 'cat.id')
            ->where('lc.type', LessonToTask::TYPE_FLASHCARD)
            ->where('lc.value', 'LIKE', '%' . $searchQuery . '%')
            ->where('l.show', ACTIVE)
            ->where('c.show', ACTIVE)
            ->where('lg.show', ACTIVE)
            ->where('cat.status', ACTIVE)
            ->where('lc.show', ACTIVE)
            ->get();

        $flashcards = $flashcards->map(function ($flashcard) use ($searchQuery, $request) {
            $flashcard->value = json_decode($flashcard->value);
            if (str_contains($flashcard->value->word, $request['searchQuery']) || str_contains($flashcard->value->meaning, $request['searchQuery'])) {
                return $flashcard;
            }
            return null;
        })->filter(function ($flashcard) {
            return $flashcard;
        });

        return response()->json([
            'code' => 200,
            'data' => $flashcards
        ]);
    }

    public function getFavorite(Request $request): \Illuminate\Http\JsonResponse
    {
        $favorite = UserFlashCardPackage::query()
            ->where('user_id', auth()->id())
            ->where('type', 'like')
            ->first();

        if (!$favorite) {
            return response()->json([
                'code' => 404,
                'message' => 'Favorite not found'
            ]);
        }

        $favorite->data = json_decode($favorite->data);

        $ids = array_column($favorite->data, 'i');

        $results = DB::table('lesson_components as lc')
            ->select('lc.*', 'l.course_id', 'c.name as course_name')
            ->join('lesson as l', 'lc.lesson_id', '=', 'l.id')
            ->join('course as c', 'l.course_id', '=', 'c.id')
            ->join('lesson_group as lg', 'l.group_id', '=', 'lg.id')
            ->join('lesson_categories as cat', 'lg.lesson_category_id', '=', 'cat.id')
            ->where('lc.type', LessonToTask::TYPE_FLASHCARD)
            ->whereIn('lc.id', $ids)
            ->get();

        $favorite->flashcards = $results;

        return response()->json([
            'code' => 200,
            'data' => $favorite
        ]);
    }

    public function getOverviewFavorite(Request $request): \Illuminate\Http\JsonResponse
    {
        $favorite = $this->overviewFavorite();

        return response()->json([
            'code' => 200,
            'data' => $favorite
        ]);
    }

    public function overviewFavorite () {
        $favorite = UserFlashCardPackage::query()
            ->where('user_id', auth()->id())
            ->where('type', 'like')
            ->first();

        if ($favorite) {
            $data = json_decode($favorite->data, true); // decode thành array
            usort($data, function ($a, $b) {
                return $b['t'] - $a['t'];
            });

            $favorite->lastFlashcard = LessonToTask::query()
                ->where('id', $data[0]['i'])
                ->first();

            if ($favorite->lastFlashcard) {
                $favorite->lastFlashcard->value = json_decode($favorite->lastFlashcard->value);
            }

            // convert time 1750994406 -> 10:20 27-06-2025
            $data[0]['convert_time'] = date('H:i d-m-Y', $data[0]['t']);

            $favorite->data = $data;
        }

        return $favorite;
    }
    

    public function saveFlashcard(Request $request): \Illuminate\Http\JsonResponse
    {
        if (!$request['course_id'] || !$request['data'] || !is_array($request['data'])) {
            return response()->json([
                'code' => 400,
                'message' => 'Missing course id or data'
            ]);
        }

        $favorite = UserFlashCardPackage::query()
            ->where('user_id', auth()->id())
            ->where('type', 'normal')
            ->where('course_id', $request['course_id'])
            ->first();

        if (!$favorite) {
            $favorite = new UserFlashCardPackage();
            $favorite->user_id = auth()->id();
            $favorite->type = 'normal';
            $favorite->course_id = $request['course_id'];
        }

        $favorite->data = json_encode($request['data']);

        $favorite->save();

        return response()->json([
            'code' => 200,
            'data' => $favorite
        ]);
    }

    public function likeFlashcard(Request $request): \Illuminate\Http\JsonResponse
    {
        if (!$request['course_id'] || !$request['id']) {
            return response()->json([
                'code' => 400,
                'message' => 'Missing course id or id'
            ]);
        }

        $favorite = UserFlashCardPackage::query()
            ->where('user_id', auth()->id())
            ->where('type', 'like')
            ->first();

        if (!$favorite) {
            $favorite = new UserFlashCardPackage();
            $favorite->user_id = auth()->id();
            $favorite->type = 'like';
            $favorite->course_id = $request['course_id'];
            $favorite->data = json_encode([]);
        }

        $data = array_values(json_decode($favorite->data)); // [{"i": 103631, "t": 1748846914024}, {"i": 103632, "t": 1748830789742}, {"i": 103633, "t": 1748830790375}]
        $isLike = true;

        // check id in data. nếu có thì remove, không có thì add
        $checkExist = array_filter($data, function ($item) use ($request) {
            return $item->i == $request['id'];
        });

        if (count($checkExist) > 0) {
            $data = array_filter($data, function ($item) use ($request) {
                return $item->i != $request['id'];
            });
            $isLike = false;
        } else {
            $data[] = [
                'i' => $request['id'],
                't' => $request['t']
            ];
        }

        $favorite->data = json_encode($data);

        $favorite->save();

        return response()->json([
            'code' => 200,
            'data' => $favorite,
            'isLike' => $isLike
        ]);
    }
}
