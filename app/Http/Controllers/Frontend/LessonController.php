<?php

namespace App\Http\Controllers\Frontend;

use App\Http\Models\LessonToTask;
use App\Jobs\SysncLessonProgress;
use App\Models\Activity;
use DB;
use App;
use Auth;
use App\User;
use DateTime;
use Carbon\Carbon;
use Illuminate\Support\Arr;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Session;
use League\Flysystem\Exception;
use App\Http\Helpers\CourseHelper;
use Illuminate\Support\Collection;
use App\Http\ModelsFrontend\Course;
use App\Http\ModelsFrontend\Lesson;
use App\Http\ModelsFrontend\Server;
use Illuminate\Support\Facades\Log;
use App\Http\ModelsFrontend\Comment;
use App\Http\ModelsFrontend\Feedback;
use Illuminate\Support\Facades\Cache;
use App\Base\Controller\BaseController;
use App\Http\Models\LessonTrackingStat;
use App\Http\ModelsFrontend\CourseOwner;
use App\Http\ModelsFrontend\LessonGroup;
use App\Http\ModelsFrontend\LessonResult;
use App\Http\ModelsFrontend\RmCheckpoint;
use Illuminate\Support\Facades\Validator;
use App\Http\ModelsFrontend\LessonHistory;
use App\Http\ModelsFrontend\LessonCategory;
use App\Http\ModelsFrontend\LessonProgress;
use App\Http\ModelsFrontend\LessonComponents;
use App\Http\Requests\Lesson\LessonProgressRequest;

class LessonController extends BaseController
{

    private function cdbhCheck()
    {
        //Route::get('/cdbh/{url}/{id}/learn?session=????
        if (strpos($_SERVER['REQUEST_URI'], "/cdbh/") !== false) {
            if ($_GET['session'] == null) {
                abort(404);
                //nếu có phiên làm việc
            } else {
                // dd($_GET['session']);
                $session = $_GET['session'];
                $session = substr($session, 3);
                $session = base64_decode($session);
                $session = substr($session, 2);
                $session = base64_decode($session);
                $session = json_decode($session);

                //nếu token hợp lệ
                if ($session) {
                    auth()->loginUsingId($session->dmr_id);
                    auth()->user()->embed = 1;
                    // dd($session->dmr_id);
                } else {
                    dd("phiên truy cập không hợp lệ");
                }
                // abort(404);
            }
            return true;
        }
        return false;
    }

    //xem chi tiết bài học trong khóa học
    public function getLesson($course_url, $id)
    {

        $unlock = 0;

        // Check xem flashcards có từ đã học hay không
        $hasKnown = false;

        //Khóa học giao diện theo kiểu premium

        $isEju = false;
        $ejuLimit = 0;

        $isLD = false; //là bài học của luyện đề
        $ldResults = null; // kết quả bài kt của luyện đề

        $cdbh = $this->cdbhCheck();

        // Số ngày lại của khoá học
        $numberOfDay = 0;

        //lấy ra chi tiết bài học bao gồm video và các task không phải video
        $thisLesson = Cache::remember('lesson_detail_info_' . $id, 1440, function () use ($id) {
            return Lesson::query()->select(
                'id',
                'group_id',
                'course_id',
                'is_examination',
                'author_id',
                'name',
                'avatar_name',
                'description',
                'price_option',
                'feature',
                'show',
                'is_secret',
                'SEOurl',
                'count_view',
                'total_marks',
                'pass_marks',
                'max_duration',
                'default_speed',
                'flashcard_hint',
                'type',
                'sort_order',
                'require'
            )
                ->with('lesson_group.getCourseOfGroup')
                // ->with('lesson_group.category')
                // ->whereHas('lesson_group.category', function ($q) {
                //     $q->where('show', 1);
                // })
                ->with('getTasksNotVideo')->with('getVideo')->find($id);
        });

        if ($thisLesson == null) {
            abort(404);
        }

        //lấy course từ bài học, để tránh bug hack qua url
        $course = $thisLesson->load('getCourse:id,SEOurl,premium,name,description,author_id,intro,price,first_lesson,price_option,watch_expired,type,updated_at,stats_data,sort_order')->getCourse;

        if ($course == null) {
            abort(404);
        }
        //abort 404 nếu url không hợp lệ

        //nếu là 1 trong 3 khóa eju
        $isEju = in_array($course->id, array(8, 9, 10, 36, 37, 38));

        //nếu là 1 trong 4 khóa luyện đề
        $isLD = in_array($course->id, array(30, 31, 32, 33));

        $courseType = 'jlpt'; // 'jlpt': bài học khoá jlpt, 2: bài học khoá eju, 3: bài học khoá luyện đề

        if ($isEju) {
            $courseType = 'eju';
        }

        if ($isLD) {
            $courseType = 'ld';
        }

        //check quyền sở hữu khóa học
        $checkOwner = null;
        if (auth()->check()) {
            $checkOwner = DB::table('course_owner')->where('owner_id', auth()->user()->id)
                ->where('course_id', $course->id)
                ->orderBy('id')
                ->first(['id', 'owner_id', 'course_id', 'learning_time', 'start_learning_at', 'progress', 'watch_expired_day', 'period_id', 'created_at']);
        }

        $groupFlashcards = [];
        $group_and_flashcards = [];
        $lessons = [];
        $groups = [];
        $categories = [];

        //lấy groups không dùng cache
        if (auth()->guard('admin')->user() || (auth()->user() && auth()->user()->is_tester)) {
            if (!$checkOwner || !$checkOwner->period_id) {
                $groups = Cache::remember('course_groups_test_' . $courseType . '_' . $course->SEOurl, 1440, function () use ($course) {
                    return LessonGroup::where('course_id', $course->id)
                        ->select('id', 'name', 'is_step', 'lesson_category_id', 'course_id', 'eju_limited', 'type_ld')
                        ->with(['getAllLesson:id,group_id,show'])
                        ->where('show', 1)
                        ->where('is_specialezed', 0)
                        ->orderBy('sort')
                        ->get()
                        ->map(function ($lessonGroup) {
                            return Arr::only($lessonGroup->toArray(), ['id', 'name', 'is_step', 'lesson_category_id', 'course_id', 'eju_limited', 'type_ld', 'get_all_lesson']);
                        });
                });

                $categories = Cache::remember('course_category_test_' . $courseType . '_' . $course->SEOurl, 1440, function () use ($course) {
                    return DB::table('lesson_categories')->where('course_id', $course->id)->whereIn('status', [1, 2])->orderBy('stage', 'ASC')->orderBy('sort', 'ASC')
                        ->get(['id', 'title', 'type', 'icon', 'course_id', 'status', 'stage', 'stage_name', 'text_group_left']);
                });
            }
        } else {
            if (!$checkOwner || !$checkOwner->period_id) {
                //cache lại cho người dùng
                $groups = Cache::remember('course_groups_' . $courseType . '_' . $course->SEOurl, 1440, function () use ($course) {
                    return LessonGroup::where('course_id', $course->id)
                        ->where('show', 1)
                        ->where('is_specialezed', 0)
                        ->with('getAllLesson:id,group_id,show')
                        ->orderBy('sort')
                        ->get()
                        ->map(function ($lessonGroup) {
                            return Arr::only($lessonGroup->toArray(), ['id', 'name', 'is_step', 'lesson_category_id', 'course_id', 'eju_limited', 'type_ld', 'get_all_lesson']);
                        });
                    // ->where('lesson_category_id', '>', 0)->where('is_specialezed', 0)->orderBy('sort')->get();
                });

                $categories = Cache::remember('course_category_' . $courseType . '_' . $course->SEOurl, 1440, function () use ($course) {
                    return DB::table('lesson_categories')->where('course_id', $course->id)->where('status', 1)->orderBy('stage', 'ASC')->orderBy('sort', 'ASC')
                        ->get(['id', 'title', 'type', 'icon', 'course_id', 'stage', 'stage_name', 'sort', 'status', 'text_group_left']);
                });
            }
        }


        // check xem người dùng có phải admin không
        if (auth()->guard('admin')->user() || (auth()->user() && auth()->user()->is_tester)) {
            if (!$checkOwner || !$checkOwner->period_id) {
                $lessons = Cache::remember('course_lessons_test_' . $courseType . '_' . $course->SEOurl, 1440, function () use ($course) {
                    return DB::table('lesson')->select('id', 'group_id', 'name', 'SEOurl', 'price_option', 'feature', 'is_secret', 'type', 'total_marks', 'pass_marks', 'expect_time', 'sort_order')
                        ->where('course_id', $course->id)->orderBy('sort_order')->whereIn('show', [1, 2])->get();
                });
            }
        } else {
            if (!$checkOwner || !$checkOwner->period_id) {
                //cache lại cho người dùng
                $lessons = Cache::remember('course_lessons_' . $courseType . '_' . $course->SEOurl, 1440, function () use ($course) {
                    return DB::table('lesson')->select('id', 'group_id', 'name', 'SEOurl', 'price_option', 'feature', 'is_secret', 'type', 'total_marks', 'pass_marks', 'expect_time', 'sort_order')
                        ->where('course_id', $course->id)->orderBy('sort_order')->where('show', 1)->get();
                });
            }
        }

        //Đếm có bao nhiêu lượt xem
        //        $thisLesson->count_view = $thisLesson->count_view + 1;
        //        $thisLesson->save();
        DB::table('lesson')->where('id', $thisLesson->id)->update(['count_view' => $thisLesson->count_view + 1]);

        if ($thisLesson->price_option > 0) {
            //nếu người dùng chưa đăng nhập -> trả trang trống để bớt truy vấn
            if (!auth()->check()) {

                // dd($premium);
                $viewName = 'lesson_guess';

                return view('frontend.course.lesson_guess')
                    ->with("type", 'auth') //kiểu thông báo cho user
                    ->with("isld", $isLD)
                    ->with("premium", $course->premium)
                    ->with("course", $course)
                    ->with("categories", $categories)
                    ->with("groups", $groups)
                    ->with("lessons", $lessons)
                    ->with("thisLesson", $thisLesson);

                //người dùng đã đăng nhập
            } else {
                //kiểm tra người dùng đã mua nhưng hết hạn
                if ($checkOwner) {
                    $now = new DateTime();
                    $today = $now->getTimestamp();
                    if ($today < strtotime($checkOwner->watch_expired_day)) {
                        $unlock = 1;
                    }
                }
                //enable bổ trợ cho khóa n3/n2/n1 còn hạn
                if ($unlock == 0) {

                    if ($course->id == 27 && auth()->user()->checkOwner(3) == 1) {
                        $unlock = 1;
                    }
                    //n3
                    if ($course->id == 28 && auth()->user()->checkOwner(16) == 1) {
                        $unlock = 1;
                    }
                    //n2
                    if ($course->id == 29 && auth()->user()->checkOwner(17) == 1) {
                        $unlock = 1;
                    }
                    //n1
                }

                //nếu là cao đẳng bắc hà
                if ($unlock == 0 && $cdbh == true) {
                    return "<h2 style='text-align:center; width: 100%; margin: 200px 0; font-family: 'arial';'>Bạn chưa có quyền tham gia khóa học này<br/> Vui lòng liên hệ nhà trường</h2>";
                }
                //qua bước check rồi mà vẫn chưa unlock
                if ($unlock == 0) {
                    $viewName = 'lesson_guess';

                    return view('frontend.course.' . $viewName)
                        ->with("type", 'expried') //kiểu thông báo cho user
                        ->with("isld", $isLD)
                        ->with("premium", $course->premium)
                        ->with("course", $course)
                        ->with("categories", $categories)
                        ->with("groups", $groups)
                        ->with("lessons", $lessons)
                        ->with("thisLesson", $thisLesson);
                }
            }
        } else {
            if (auth()->check()) {
                $checkOwner = CourseOwner::where('owner_id', auth()->user()->id)->where('course_id', $course->id)->first(['id', 'owner_id', 'course_id', 'learning_time', 'start_learning_at', 'progress', 'watch_expired_day', 'period_id', 'created_at']);

                if ($checkOwner) {
                    $now = new DateTime();
                    $today = $now->getTimestamp();
                    if ($today < strtotime($checkOwner->watch_expired_day)) {
                        $unlock = 1;
                    }
                }
            }
        }
        ######################Người dùng có quyền học######################

        // //check có phải bài flashcard hay không để ignore comment component
        // $isFlashcardLesson = false;
        // $tmpCheckF = DB::Table('lesson_components')->where('lesson_id', $thisLesson->id)->where('type', 9)->first();
        // if($tmpCheckF) $isFlashcardLesson = true;
        // // dd($isFlashcardLesson);
        // $lessonInfo = DB::Table('lesson')->select('id','SEOurl')->where('id', $id)->first();
        $lessonInfo = (object)array("id" => $id, "SEOurl" => $thisLesson->SEOurl);

        //danh sách các server hiện có
        $servers = Cache::remember('list_servers', 1440, function () {
            return Server::select('id', 'name', 'url')->where('status', 1)->toBase()->get();
        });

        // Lấy ra tiến độ hiện tại của user đăng nhập
        $currentLessonProgress = null;
        $currentLessonPoints = [];
        $lessonProgress = null;

        //lấy ra kết quả bài kiểm tra đã làm
        $lessonResults = [];

        $period = [];
        if (auth()->check()) {

            //tạo trước khi lấy progress về để phục vụ lộ trình mới
            //nếu chưa có trong lộ trình -> tạo mới
            LessonProgress::query()->where('user_id', auth()->user()->id)->where('lesson_id', $thisLesson->id)->firstOrCreate(
                ['user_id' => auth()->user()->id, 'lesson_id' => $thisLesson->id],
                ['example_progress' => 0, 'video_progress' => 0]
            );

            //check quyền sở hữu khóa học
            //giới hạn bài của những người mua trước năm 2021 của eju

            if ($checkOwner) {
                if ($isEju == true && $checkOwner->created_at < '2021-01-01 13:29:32') {
                    $ejuLimit = 0;
                }

                $dateToday = new DateTime(); // dd( $dateToday->format('Y-m-d'));

                //nếu chưa có bản ghi cho ngày hôm nay (của bài này)-> tạo mới ->sqlite
                $checkSQLite = null;
                try {
                    $checkSQLite = LessonHistory::select('user_id', 'lesson_id', 'created_at')
                        ->where('user_id', Auth::user()->id)
                        ->where('lesson_id', $thisLesson->id)
                        ->where('created_at', '>=', now()->startOfDay())
                        ->where('created_at', '<=', now()->endOfDay())
                        ->orderBy('created_at')
                        ->first();
                } catch (Exception $e) {
                    Log::critical($e);
                }
                if (!$checkSQLite) {
                    try {
                        //                    $newrc= new LessonHistory();
                        //                    $newrc->user_id = auth()->user()->id;
                        //                    $newrc->lesson_id = $thisLesson->id;
                        //                    $newrc->created_at = now()->format('Y-m-d H:i:s');
                        //                    $newrc->save();
                        //                    dd(now()->format('Y-m-d H:i:s'));
                        DB::connection('mongodb')->table('lesson_history')->insert([
                            'user_id' => auth()->user()->id,
                            'lesson_id' => $thisLesson->id,
                            'created_at' => now()->format('Y-m-d H:i:s'),
                        ]);
                    } catch (Exception $e) {
                        Log::critical($e);
                    }
                }
                $today = $dateToday->getTimestamp();
                if ($today < strtotime($checkOwner->watch_expired_day)) {
                    $unlock = 1;
                }
            }

            //lấy tiến độ
            //            $lessonProgress = DB::table('lesson_progress')
            //                    ->select('lesson_group.lesson_category_id', 'lesson.course_id', 'lesson.group_id', 'lesson_progress.lesson_id', 'video_progress', 'example_progress')
            //                    ->leftJoin('lesson', 'lesson.id', '=', 'lesson_progress.lesson_id')
            //                    ->leftJoin('lesson_group', 'lesson.group_id', '=', 'lesson_group.id')
            //                    ->where('lesson_progress.user_id', auth()->user()->id)->where('lesson.course_id', $thisLesson->course_id)
            //                    ->where('lesson_group.show', 1)->where('lesson.show', 1)->get();
            $lessonProgress = LessonProgress::query()
                ->select('id', 'lesson_id', 'user_id', 'video_progress', 'example_progress', DB::raw('ROUND((video_progress + example_progress) / 2) as total_progress'))
                ->with([
                    'lesson:id,course_id,group_id,show',
                    'lesson.lesson_group:id,lesson_category_id,show',
                ])
                ->where('user_id', auth()->id())
                ->whereHas('lesson', function ($q) use ($thisLesson) {
                    $q->where('course_id', $thisLesson->course_id)->where('show', 1);
                })
                ->whereHas('lesson.lesson_group', function ($q) {
                    $q->where('show', 1);
                })
                ->get();
            $currentLessonProgress = $lessonProgress->first(function ($value) use ($thisLesson) {
                return $value->lesson_id == $thisLesson->id && $value->user_id == auth()->user()->id;
            });
            $currentLessonPoints = App\Models\ExperienceAudit::query()->select('id', 'user_id', 'lesson_id')->where('user_id', auth()->user()->id)->where('lesson_id', $thisLesson->id)->exists();

            // lấy Kết quả bài kiểm tra
            $lessonResults = DB::table('lesson_result')->where('user_id', auth()->user()->id)->where('lesson_id', $thisLesson->id)->orderBy('id', 'desc')->get(['id', 'lesson_id', 'user_id', 'grade', 'total_grade', 'data', 'passed', 'duration', 'score_data', 'is_ld', 'created_at']);

            $checkFlashcard = Cache::remember('check_flashcard_' . $thisLesson->id, 1440, function () use ($thisLesson) {
                return DB::table('lesson_components')->where('lesson_id', $thisLesson->id)->where('type', 9)->first(['id', 'lesson_id', 'type']);
            });

            if ($checkFlashcard) {
                // Kiểm tra flashcard stack có ít nhất 1 card được học
                $lessonCards = DB::table('lesson_components')->where('lesson_id', $thisLesson->id)->where('type', 9)->orderBy('sort', 'asc')->pluck('id');
                $hasKnown = DB::table('user_learn_flashcard')->select('user_id', 'flashcard_id')->where('user_id', auth()->user()->id)->whereIn('flashcard_id', $lessonCards)->exists();
            }
            //lấy thông tin lộ trình mới
            $vipLevel = $course->name;
            $userGroupLevels = DB::select("
              select `community_groups`.`id`, `community_groups`.`vip_level`,
                     `community_group_users`.`user_id` as `pivot_user_id`,
                     `community_group_users`.`group_id` as `pivot_group_id`
              from `community_groups`
              left join `community_group_users` on `community_groups`.`id` = `community_group_users`.`group_id`
              where `community_group_users`.`user_id` = ?
            ", [auth()->user()->id]);
            $userGroupLevels = array_map(function ($item) {
                return $item->vip_level;
            }, $userGroupLevels);
            $vipCheck = in_array($vipLevel, $userGroupLevels);

            //lấy thông tin lộ trình mới
            if ($vipCheck) {
                $period = DB::table('learning_period')->where('course_id', $course->id)->where('status', 1)->orderBy('is_vip', 'desc')->orderBy('name')->get(['id', 'course_id', 'name', 'description', 'status', 'duration', 'is_vip']); //dd($period);
            } else {
                $period = DB::table('learning_period')->where('course_id', $course->id)->where('status', 1)->where('is_vip', 0)->orderBy('name')->get(['id', 'course_id', 'name', 'description', 'status', 'duration', 'is_vip']); //dd($period);
            }
            // nếu là khóa luyện đề
            // nếu là 1 trong 3 khóa luyện đề
            if ($isLD == true) {
                // lấy Kết quả bài kiểm tra của luyện đề
                // $ldResults = LessonResult::where('user_id', auth()->user()->id)->where('is_ld', true)->orderBy('id', 'desc')->get();
                $ldResults = LessonResult::select('id', 'lesson_id', 'user_id', 'grade', 'score_data')
                    ->where(function ($q) use ($course) {
                        $q->where('course_id', $course->id)->orWhere('is_ld', 1);
                    })
                    ->where('user_id', auth()->user()->id)
                    ->orderBy('id', 'DESC')
                    ->toBase()
                    ->get();
            }
        }
        $taskToAnswer = $thisLesson->getTasksNotVideo->mapWithKeys(function ($task) {
            return [$task->id => $task->answers];
        });
        // Bài kiểm tra tự luận
        $writeQuestions = $thisLesson->getTasksNotVideo
            ->filter(function ($task) {
                return $task->type == 6;
            })
            ->mapWithKeys(function ($task) {
                return [$task->id => json_decode($task->value)];
            });

        // danh sách clips mp4 ngoài (với bài có nhiều mp4)
        $otherVideo = Cache::remember('lesson_detail_otherVideo_' . $courseType . '_' . $thisLesson->id, 1440, function () use ($thisLesson, $isLD) {
            return LessonComponents::where('lesson_id', $thisLesson->id)->whereIn('type', $isLD ? [2, 4] : [2])->where('server', null)->orderBy('sort', 'asc')->get(['id', 'lesson_id', 'video_name', 'video_title', 'type', 'server', 'sort', 'value', 'video_full']);
        });

        $nbOfYoutubeVideo = Cache::remember('lesson_detail_nbOfYoutubeVideo_' . $courseType . '_' . $thisLesson->id, 1440, function () use ($thisLesson) {
            return LessonComponents::select('id', 'lesson_id', 'video_name', 'video_title', 'type', 'server', 'sort')->where('lesson_id', $thisLesson->id)->where('type', 2)->where('server', 'youtube')->count('id');
        });

        // videoId đang chạy trong danh sách các video
        $video = null;
        // Check if the GET request contains a video ID
        if (isset($_GET["id"]) && $_GET["id"] != null && $_GET["id"] != "") {
            // Get the video ID from the GET request
            $currentVideoId = $_GET["id"];
            // Get the video by ID
            $video = LessonComponents::find($currentVideoId);
        }
        // If the video is not found, check if there are other videos in the lesson
        if ($video == null && $otherVideo->count('id') > 0) {
            // Get the first video in the lesson
            $video = $otherVideo[0];
        }
        // If there are no other videos in the lesson, use the lesson's default video
        if ($video == null) {
            $video = $thisLesson->getVideo;
        }

        //blade mặc định của bài học
        $viewName = 'lesson';

        //nếu là 1 trong 4 khóa luyện đề
        if (in_array($course->id, array(30, 31, 32, 33))) {
            $isLD = true;
            $viewName = "lesson-ld";
        }

        $paths = [];
        $checkpoints = [];
        $dailyLogs = [];

        //đoạn lộ trình học mới
        if ($unlock == 1) {

            //nếu người dùng đã chọn lộ trình
            if ($checkOwner && $checkOwner->period_id) {
                $pathsQuery = DB::table('learning_paths')->where('period_id', $checkOwner->period_id)
                    ->where('status', 1);
                $paths = $pathsQuery->clone()->orderBy('sort_no')
                    ->get(['id', 'course_id', 'period_id', 'title', 'sort_no', 'status', 'img', 'illustrator']);
                $pathIds = $pathsQuery->clone()->pluck('id');
                //dd($pathIds);

                $checkpoints = RmCheckpoint::whereIn('path_id', $pathIds)
                    ->join('lesson', 'lesson.id', '=', 'learning_checkpoints.lesson_id')
                    ->where('lesson.show', 1)
                    ->where('learning_checkpoints.status', 1)
                    ->orderBy('key')
                    ->orderBy('sort')
                    ->toBase()
                    ->get();
                // lưu ngay lịch sử học vào sqlite nếu chưa có
                // nếu người dùng đang trong 1 lộ trình học
                // if($checkOwner->period_id > 0){
                $dailyLogs = [];
                try {
                    $dailyLogs = LessonHistory::where('user_id', auth()->user()->id)->get(['id', 'user_id', 'lesson_id', 'is_complete', 'created_at']);
                } catch (Exception $e) {
                    Log::critical($e);
                }
                $groupFlashcards = DB::table('learning_period_group')->where('course_id', $course->id)->get(['id', 'course_id', 'title', 'sort', 'status']);
                $gfcIds = array_map(function ($item) {
                    return $item->id;
                }, $groupFlashcards->toArray());
                $group_and_flashcards = DB::table('learning_period_flashcard')
                    ->leftJoin('lesson', 'learning_period_flashcard.lesson_id', '=', 'lesson.id')
                    ->select(
                        'learning_period_flashcard.id',
                        'learning_period_flashcard.group_id',
                        'lesson_id',
                        'learning_period_flashcard.sort',
                        'learning_period_flashcard.status',
                        'lesson.name as lesson_name',
                        'lesson.SEOurl as SEOurl'
                    )
                    ->whereIn('learning_period_flashcard.group_id', $gfcIds)
                    ->get();
            }
        }

        //https://stackoverflow.com/questions/39483348/how-
        // header('X-Frame-Options: SAMEORIGIN');
        header("Access-Control-Allow-Origin: *");

        //Lấy ra số ngày còn lại của khoá
        if ($checkOwner && $unlock == 1) {
            $dayOfDb = DB::table('course_owner')
                ->select(DB::raw('DATEDIFF(course_owner.watch_expired_day, CURDATE()) AS days'))
                ->where('id', $checkOwner->id)
                ->get();

            $numberOfDay = $dayOfDb[0]->days;
        }

        if (in_array($thisLesson->type, ['exam', 'last_exam'])) {
            $viewName = 'lesson_exam';
        }

        return view('frontend.course.' . $viewName)
            ->with("thisLesson", $thisLesson)
            ->with("lessonInfo", $lessonInfo)
            ->with("course", $course)
            ->with("premium", $course->premium)
            ->with("isEju", $isEju)
            ->with("isLD", $isLD)
            ->with("ejuLimit", $ejuLimit)
            ->with("categories", $categories)
            ->with("groups", $groups)
            ->with("lessons", $lessons)
            ->with("lessonProgress", $lessonProgress)
            ->with("currentLessonProgress", $currentLessonProgress)
            ->with("currentLessonPoints", $currentLessonPoints)
            ->with("dailyLogs", $dailyLogs)
            ->with("video", $video)
            ->with("tasks", $thisLesson->getTasksNotVideo)
            ->with("random_id", substr(md5(mt_rand()), 0, 7))
            ->with("servers", $servers)
            ->with("unlock", $unlock)
            ->with("taskToAnswer", $taskToAnswer)
            ->with("writeQuestions", $writeQuestions)
            ->with("lessonResults", $lessonResults)
            ->with("otherVideo", $otherVideo)
            ->with("nbOfYoutubeVideo", $nbOfYoutubeVideo)
            ->with("openSpecial", $course->new)
            ->with("hasKnown", $hasKnown)
            ->with("checkOwner", $checkOwner)
            ->with("period", $period)
            ->with("paths", $paths)
            ->with("groupFlashcards", $groupFlashcards)
            ->with("group_and_flashcards", $group_and_flashcards)
            ->with("checkpoints", $checkpoints)
            ->with("cdbh", $cdbh)
            ->with("ldResults", $ldResults)
            ->with("numberOfDay", $numberOfDay);
    }

    public function getListLesson($idLesson = null)
    {
        $contentLesson = Lesson::query()
            ->with(["component" => function ($q) {
                $q->whereIn('type', [3, 13])->orderBy('sort')->with('answers');
            }])
            ->find($idLesson);
        foreach ($contentLesson->component as $key => $component) {
            if ($component->type === App\Http\Models\LessonToTask::TYPE_FILL_IN_THE_BLANK) {
                $contentLesson->component[$key]->value = json_decode($component->value, true);
            }
        }
        return view('frontend.course.list-course')->with('contentLesson', $contentLesson);
    }

    public function saveResultExerciseUser(Request $request)
    {
        try {
            $data = $request->all();
            $data['user_id'] = \Illuminate\Support\Facades\Auth::id();
//            $data['data'] = json_encode($data['data']);
            if (isset($request['audio']) && is_array($request['audio'])) {
                $resultUser = json_decode($data['data']);
                foreach ($resultUser as $questionId => $value) {
                    if (isset($data['audio'][$questionId])) {
                        $value->slow = saveFile($data['audio'][$questionId]['slow'], 'cdn/kaiwa/');
                        $value->default = saveFile($data['audio'][$questionId]['default'], 'cdn/kaiwa/');
                    }
                }
            } else {
                $resultUser = $data['data'];
            }
            $data['data'] = json_encode($resultUser);
            data_forget($data, 'audio');

            $resultExists = LessonResult::query()->where('user_id', $data['user_id'])->where('lesson_id', $data['lesson_id'])->first();


            $result = new LessonResult();
            $result->fill($data)->save();

            $resultExists?->delete();

            $lessonProgress = LessonProgress::query()->where('user_id', $data['user_id'])->where('lesson_id', $data['lesson_id'])->first();

            $isLessonPass = false;
            if ($lessonProgress && $lessonProgress->example_progress >= 85) $isLessonPass = true;


            if ($lessonProgress === null) {
                $lessonProgress = new LessonProgress();
                $dataLessonProgress = [
                    'user_id' => $data['user_id'],
                    'lesson_id' => $data['lesson_id'],
                    'example_progress' => $data['ratio_correct_answer'] * 100
                ];
                $lessonProgress->fill($dataLessonProgress)->save();
            } else if ($lessonProgress->example_progress < ($data['ratio_correct_answer'] * 100)) {
                $lessonProgress->example_progress = ($data['ratio_correct_answer'] * 100);
                $lessonProgress->save();
            }
            $countLessonComplete = CourseHelper::countLessonComplete($request['course_id'], $data['lesson_id']);
            $lessonComplete = $countLessonComplete['lessonComplete'];
            $countLessonGroup = $countLessonComplete['countLessonGroup'];

            if ($lessonComplete && !$isLessonPass) {
                $lesson = Lesson::query()
                    ->with([
                        'components' => function ($q) {
                            $q->where('show', ACTIVE)
                                ->orderBy('sort', 'asc');
                        },
                        'video.video_info',
                        'lesson_group.category',
                    ])->findOrfail($data['lesson_id']);
                $countCourseComplete = Session::get('set_popup_achievement_course_' . $request['course_id'], 0);
                $countStageComplete = Session::get('set_popup_achievement_stage_' . $lesson->course_id .'_'. $lesson?->lesson_group?->category?->stage, 0);
                $courseProgress = CourseOwner::where('owner_id', $data['user_id'])->where('course_id', $request['course_id'])->first();
                $coursePercent = $courseProgress->progress ?? 0;
                $courseCompleted = CourseHelper::checkIfCourseCompleted($request['course_id']);
                if ($courseCompleted >= 85) {
                    $courseProgress->progress = $courseCompleted;
                    $courseProgress->save();
                    if ($coursePercent < 85) {
                        Session::put('set_popup_achievement_course_' . $request['course_id'], $countCourseComplete + 1);
                        $res = [
                            'error_code' => 0,
                            'msg' => 'Lưu dữ liệu thành công!',
                            'show_popup_done_stage' => 1,
                            'show_btn_next_lesson' => 0
                        ];
                        return response()->json($res);
                    }
                }


                $isFirstStageComplete = CourseHelper::checkIsFirstStageComplete($data['lesson_id']);
                if ($coursePercent < 85 && $countStageComplete == 0 && $isFirstStageComplete['status']) {
                    Session::put('set_popup_achievement_stage_' . $lesson->course_id .'_'. $lesson?->lesson_group?->category?->stage, $countStageComplete + 1);
                    $res = [
                        'error_code' => 0,
                        'msg' => 'Lưu dữ liệu thành công!',
                        'text_content_achievement' => !$isFirstStageComplete['status'] ? null : 'Hoàn thành ' . $isFirstStageComplete['stageName'],
                        'show_popup_achievement' => $isFirstStageComplete['status'] ? 1 : 0,
                        'name_popup_achievement' => !$isFirstStageComplete['status'] ? null : 'complete_stage_' . rand(1, 4),
                    ];
                    return response()->json($res);
                }
            }

            $res = [
                'error_code' => 0,
                'msg' => 'Lưu dữ liệu thành công!',
                'text_content_achievement' => !$lessonComplete ? null : ($lessonComplete === 3 ? 'hoàn thành chuỗi 3 bài học' : null),
                'show_popup_achievement' => $lessonComplete != 0 && $lessonComplete === 3 ? 1 : 0,
                'name_popup_achievement' => !$lessonComplete ? null : ($lessonComplete === 3 ? 'three_consecutive' : null),
            ];

            Session::put('show_btn_next_lesson', 0);

            if ($data['ratio_correct_answer'] * 100 < 85 || $isLessonPass) {
                $res['text_content_achievement'] = null;
                $res['show_popup_achievement'] = 0;
                $res['name_popup_achievement'] = null;
            }

            return response()->json($res);
        } catch (\Exception $e) {
            return response()->json([
                'error_code' => 1,
                'msg' => $e->getMessage()
            ]);
        }
    }

    public function setAchievement(Request $request)
    {
        $request->validate([
            'course_id' => 'required',
            'lesson_id' => 'required'
        ]);

        $lesson = Lesson::query()
            ->with([
                'components' => function ($q) {
                    $q->where('show', ACTIVE)
                        ->orderBy('sort', 'asc');
                },
                'video.video_info',
                'lesson_group.category',
            ])->findOrfail($request->lesson_id);

        $countLessonComplete = CourseHelper::countLessonComplete($request['course_id'], $request['lesson_id']);
        $lessonComplete = $countLessonComplete['lessonComplete'];
        $countLessonGroup = $countLessonComplete['countLessonGroup'];

        $is_pass_lesson = false;
        $count = Session::get('set_popup_achievement_lesson_' . $request['lesson_id'], 0);
        $countStageComplete = Session::get('set_popup_achievement_stage_' . $lesson->course_id .'_'. $lesson?->lesson_group?->category?->stage, 0);
        $countCourseComplete = Session::get('set_popup_achievement_course_' . $lesson->course_id, 0);
        $courseCompleted = CourseHelper::checkIfCourseCompleted($lesson->course_id);
        $courseProgress = CourseOwner::where('owner_id', auth()->id())->where('course_id', $request['course_id'])->first();
        $coursePercent = $courseProgress->progress ?? 0;
        if ($courseCompleted >= 85) {
            $courseProgress->progress = $courseCompleted;
            $courseProgress->save();
            if ($coursePercent < 85) {
                Session::put('show_popup_done_stage', 1);
                Session::put('show_btn_next_lesson', 0);
                Session::put('set_popup_achievement_course_' . $lesson->course_id, $countCourseComplete + 1);
                return response()->json([
                    'error_code' => 0,
                    'data' => [
                        'countLessonComplete' => $countLessonComplete,
                        'is_pass_lesson' => $is_pass_lesson
                    ],
                ]);
            }
        }


        if ($coursePercent < 85 && $countStageComplete == 0) {
            $isFirstStageComplete = CourseHelper::checkIsFirstStageComplete($request->lesson_id);
            if ($isFirstStageComplete['status']) {
                Session::put('text_content_achievement', !$isFirstStageComplete['status'] ? null : 'Hoàn thành ' . $isFirstStageComplete['stageName']);
                Session::put('show_popup_achievement', $isFirstStageComplete['status'] ? 1 : 0);
                Session::put('name_popup_achievement', !$isFirstStageComplete['status'] ? null : 'complete_stage_' . rand(1, 4));
                Session::put('show_btn_next_lesson', 1);
                Session::put('set_popup_achievement_stage_' . $lesson->course_id .'_'. $lesson?->lesson_group?->category?->stage, $countStageComplete + 1);
                $is_pass_lesson = true;
                return response()->json([
                    'error_code' => 0,
                    'data' => [
                        'countLessonComplete' => $countLessonComplete,
                        'is_pass_lesson' => $is_pass_lesson
                    ],
                ]);
            }
        }

        if ($request->has('progress_before') && $request['progress_before'] < 85 && $countLessonComplete['isLessonPass'] && $count == 0 || $lessonComplete % 3 === 0) {
            Session::put('set_popup_achievement_lesson_' . $request['lesson_id'], $count + 1);
            Session::put('text_content_achievement', !$lessonComplete ? null : ($lessonComplete === 3 ? 'hoàn thành chuỗi 3 bài học' : null));
            Session::put('show_popup_achievement', $lessonComplete != 0 && ($lessonComplete === 3 ? 1 : 0));
            Session::put('name_popup_achievement', !$lessonComplete ? null : ($lessonComplete === 3 ? 'three_consecutive' : null));
            Session::put('show_btn_next_lesson', 0);
            $is_pass_lesson = true;
        }

        return response()->json([
            'error_code' => 0,
            'data' => [
                'countLessonComplete' => $countLessonComplete,
                'is_pass_lesson' => $is_pass_lesson
            ],
        ]);
    }

    public function getHistoryResultLesson(Request $request, $idLesson = null)
    {
        if ($idLesson == null) {
            return abort(404, 'Not Found!');
        }

        $result = LessonResult::query()->where('user_id', \Illuminate\Support\Facades\Auth::id())->where('lesson_id', $idLesson)->first();

        if ($result == null) {
            return abort(404, 'Not Found!');
        }
        $result->data = (array)json_decode($result->data);

        $contentLesson = Lesson::query()
            ->with(["component" => function ($q) {
                $q->orderBy('sort')->with('answers');
            }])
            ->find($idLesson);

        foreach ($contentLesson->component as $key => $component) {
            if ($component->type === App\Http\Models\LessonToTask::TYPE_FILL_IN_THE_BLANK) {
                $value = json_decode($component->value, true);
                $indexResult = 0;
                foreach ($value['question'] as $keyChild => $componentChild) {
                    if ($componentChild['type'] == 'question') {
                        $value['question'][$keyChild]['user_result'] = $result->data[$component->id][$indexResult];
                        $contentLesson->component[$key]->value = $value;
                    }
                }
            } else if ($component->type === App\Http\Models\LessonToTask::MULTIPLE_CHOICE) {
                foreach ($component->answers as $keyAnswer => $answer) {
                    $component->answers[$keyAnswer]['user_choice'] = $result->data[$component->id] === $answer->id ? 1 : 0;
                }
            }
        }


        return view('frontend.course.result-lesson-test', compact('contentLesson', 'result'));
    }

    private function convert($size)
    {
        $unit = array('b', 'kb', 'mb', 'gb', 'tb', 'pb');
        return @round($size / pow(1024, ($i = floor(log($size, 1024)))), 2) . ' ' . $unit[$i];
    }

    //bài kiểm tra đầu vào
    public function getCheckpoint($id)
    {

        //lấy ra chi tiết bài học bao gồm video và các task không phải video
        $thisLesson = Cache::remember('lesson_detail_info_' . $id, 1440, function () use ($id) {
            return Lesson::with('getTasksNotVideo')->with('getVideo')->find($id);
        });
        $tasks = $thisLesson->getTasksNotVideo;

        if ($thisLesson->type != 'checkpoint') {
            abort(404);
        }

        return view('frontend.course.checkpoint')
            ->with("thisLesson", $thisLesson)
            ->with("tasks", $tasks);
    }

    public function saveCheckpointResult(Request $request)
    {

        $data = json_decode($request->user_info, true);

        //xoá bỏ khooảng trắng trong email
        $data['email'] = trim($data['email'], ' ');

        $validator = Validator::make($data, [
            'name' => 'required',
            'phone' => 'required|numeric',
            'email' => 'required|email',
            'age' => 'required|numeric',
            'career' => 'required',
            'place' => 'required',
            'purpose' => 'required',
        ], [
            'name.required' => 'Trường Tên là bắt buộc',
            'phone.required' => 'Trường Số điện thoại là bắt buộc',
            'phone.numeric' => 'Trường Số điện thoại phải là số',
            'email.required' => 'Trường Email là bắt buộc',
            'age.required' => 'Trường Tuổi là bắt buộc',
            'career.required' => 'Trường Nghề nghiệp là bắt buộc',
            'place.required' => 'Trường Nơi ở là bắt buộc',
            'purpose.required' => 'Trường Mục đích là bắt buộc',
            'email.email' => 'Trường Email không đúng định dạng (<EMAIL>)',
        ]);

        if ($validator->fails()) {
            return response($validator->errors(), 422);
        }

        DB::table('checkpoint_log')->insert([
            'lesson_id' => $request->lessonId,
            'grade' => $request->grade,
            'total_grade' => $request->total_grade,
            'data' => $request->answers,
            'user_info' => $request->user_info,
            'created_at' => Carbon::now(),
        ]);

        return response()->json(['error_code' => 0, 'msg' => 'success'], 200);
    }

    //lưu kết quả bài kiểm tra
    public function sendTestResult(Request $request)
    {
        if (!auth()->check()) {
            return $this->statusNG([], '', 200);
        }

        $courseId = Lesson::find($request->lesson_id)->pluck('course_id');
        $lessonResult = new LessonResult();
        $lessonResult->lesson_id = $request->lesson_id;
        $lessonResult->course_id = $courseId;
        $lessonResult->user_id = auth()->user()->id;
        $lessonResult->grade = $request->grade;
        $lessonResult->data = $request->data;
        $lessonResult->total_grade = $request->total_grade;
        $pass = $request->passed;

        // convert vì khi gửi ajax, biến boolean sẽ trở thành string "true' 'false"
        if ($pass === "true") {
            $pass = true;
        } else if ($pass === "false") {
            $pass = false;
        }
        $lessonResult->passed = $pass;
        $lessonResult->save();
        return $lessonResult;
    }

    //lưu kết quả bài kiểm tra luện đè
    public function submitExamResult(Request $req)
    {

        // return $request;

        $courseId = Lesson::find($req->lessonId)->pluck('course_id');
        $result = new LessonResult();

        $result->lesson_id = $req->lessonId;
        $result->course_id = $courseId;
        $result->user_id = auth()->user()->id;
        $result->duration = $req->duration;
        $result->is_ld = 1;
        $result->data = json_encode($req->answers);

        $result->grade = $req->myScore;
        $result->total_grade = $req->totalGrade;
        $result->passed = $req->passed;

        if ($req->scoreData != null) {
            $result->score_data = json_encode($req->scoreData);
        }

        $result->save();

        $result->username = auth()->user()->name;

        return $result;
    }

    //lấy ra lịch sử làm bài
    public function loadResults(Request $req)
    {
        $results = LessonResult::where('user_id', $req->userId)->where('lesson_id', $req->lessonId)->orderBy('id', 'DESC')->get();
        return $results;
    }

    //lấy khóa học chuyên ngành học viên chọn
    public function getSpecialized($groupId, $courseActiveId)
    {
        $CourseOwner = CourseOwner::find($courseActiveId);
        $CourseOwner->specialized_id = $groupId;
        $CourseOwner->save();
        return 'success';
    }

    //Tắt hiện thị bài học dở trước đó
    public function closePreviousLesson()
    {
        $user = User::find(auth()->user()->id);
        $user->show_url_lesson = 0;
        $user->save();
        return 'success';
    }

    /**
     * @param Request $request
     * @return int
     */
    public function saveTimer(Request $request)
    {
        if (auth()->check()) {
            $lesson = Lesson::with('lesson_group.category')->find($request->lessonId);
            $stat = LessonTrackingStat::query()
                ->where('user_id', auth()->user()->id)
                ->where('lesson_id', (int)$request->lessonId)
                ->first();
            if (is_null($stat)) {
                DB::connection('mongodb')->table('lesson_counter')->insert([
                    'user_id' => (int)auth()->user()->id,
                    'lesson_id' => (int)$request->lessonId,
                    'category_id' => (int)$lesson->lesson_group->lesson_category_id ?? null,
                    'joined_time' => (int)$request->joinedTime,
                    'spent_time' => (int)$request->time,
                    'created_at' => now()->format('Y-m-d H:i:s'),
                ]);
            } else {
                DB::connection('mongodb')->table('lesson_counter')->where('user_id', auth()->user()->id)
                    ->where('lesson_id', $request->lessonId)->update([
                        'spent_time' => (int)$stat->spent_time + (int)$request->time,
                    ]);
            }
        }
        return 200;
    }

    /**
     * getGroupLesson
     *
     * @param mixed $courseSlug
     * @param mixed $groupId
     * @return void
     */
    public function getGroupLesson($courseSlug, $groupId)
    {
//        if (config('app.env') === 'prod' || config('app.env') === 'production') {
//            if (!auth()->check() || (auth()->check() && !auth()->user()->is_assistant)) {
//                abort(404);
//            }
//        }
//        try {

        $course = Cache::rememberForever('cache_course_' . $courseSlug, function () use ($courseSlug) {
            return Course::query()
                ->with('intro_lesson.video')
                ->where('show', ACTIVE)
                ->where('SEOurl', $courseSlug)
                ->whereHas('intro_lesson')
                ->firstOrFail();
        });

        $currentUserId = auth()->guard('web')->id();

        $lessonGroup = LessonGroup::query()->with('course', 'category')
            ->with([
                'lessons' => function ($query) use ($course, $currentUserId) {
                    $query
//                            ->where('require', 1)
                        ->where('show', ACTIVE)
                        ->where('course_id', $course->id)
                        ->with(['progress' => function ($q) use ($currentUserId) {
                            $q->where('user_id', $currentUserId);
                        }])
                        ->orderBy('sort_order', 'asc');
                },
                'lessons.components' => function ($query) {
                    $query
                        ->where('show', ACTIVE)
                        ->wherein('type', [LessonToTask::TYPE_VIDEO, LessonToTask::TYPE_FILL_IN_THE_BLANK, LessonToTask::MULTIPLE_CHOICE, LessonToTask::DOCS, LessonToTask::TYPE_SENTENCE_JUMBLE, LessonToTask::TYPE_WORD_PAIR_MATCHING, LessonToTask::TYPE_SPEAKING]);
                },
                'lessons.checkSubmit' => function ($query) use ($currentUserId) {
                    $query->where('user_id', $currentUserId);
                },
                'lessons.exam_result' => function ($query) use ($currentUserId) {
                    $query->where('user_id', $currentUserId);
                },
                'lessons.last_exam_result' => function ($query) use ($currentUserId) {
                    $query->where('user_id', $currentUserId);
                },
                'lessons.passed_exam_result' => function ($query) use ($currentUserId) {
                    $query->where('user_id', $currentUserId);
                },
            ])
            ->whereHas('course')
            ->whereHas('category')
            ->findOrFail($groupId);

        $lessonGroup->lessons->map(function ($lesson) {
            $componentExercise = $lesson->component->whereIn('type', LessonToTask::COMPONENT_TYPES_EXERCISE);
            $lesson->percent = !$componentExercise->isEmpty() ? ($lesson->progress->first()->example_progress ?? 0) : ($lesson->progress->first()->video_progress ?? 0);
            return $lesson;
        });

        $isUnlock = false;

        if ($currentUserId) {
            if ($course->price_option === 0 || $course->price_option === false) {
                $isUnlock = true;
            } else {
                $isUnlock = CourseOwner::query()->where('owner_id', $currentUserId)
                    ->where('course_id', $course->id)
                    ->where('watch_expired_day', '>', Carbon::now())
                    ->exists();
            }
        }

        $lessonProgress = $this->getLessonProgress($course);

        $categories = Cache::rememberForever('course_category_' . $course->id, function () use ($course) {
            return LessonCategory::query()
                ->where('course_id', $course->id)
                ->where('status', ACTIVE)
                ->orderBy('stage', 'ASC')
                ->orderBy('sort', 'ASC')
                ->get();
        });

        $totalGroup = LessonGroup::query()
            ->where('course_id', $course->id)
            ->where('is_specialezed', INACTIVE)
            ->where('show', ACTIVE)
            ->count();

        // Get next group
        $nextGroup = LessonGroup::query()
            ->where('course_id', $course->id)
            ->where('is_specialezed', INACTIVE)
            ->where('show', ACTIVE)
            ->where('sort', '>', $lessonGroup->sort)
            ->where('lesson_category_id', $lessonGroup->lesson_category_id)
            ->orderBy('sort', 'ASC')
            ->first();

        $nextLessonCategory = LessonCategory::query()
            ->where('course_id', $course->id)
            ->where('status', ACTIVE)
            ->where('stage', $lessonGroup->category->stage)
            ->where('sort', '>', $lessonGroup->category->sort)
            ->orderBy('sort', 'ASC')
            ->first();

            if (!$nextLessonCategory) {
                $nextLessonCategory = LessonCategory::where('course_id', $course->id)
                    ->where('status', ACTIVE)
                    ->where('stage', $lessonGroup->category->stage + 1)
                    ->orderBy('sort', 'ASC')
                    ->first();
            }

        if (!$nextGroup && $nextLessonCategory) {
            $nextGroup = LessonGroup::query()
                ->where('course_id', $course->id)
                ->where('is_specialezed', INACTIVE)
                ->where('show', ACTIVE)
                ->where('lesson_category_id', $nextLessonCategory->id)
                ->orderBy('sort', 'ASC')
                ->first();
        }

        // Get previous group

        $previousGroup = LessonGroup::query()
            ->where('course_id', $course->id)
            ->where('is_specialezed', INACTIVE)
            ->where('show', ACTIVE)
            ->where('sort', '<', $lessonGroup->sort)
            ->where('lesson_category_id', $lessonGroup->lesson_category_id)
            ->orderBy('sort', 'DESC')
            ->first();

        $previousLessonCategory = LessonCategory::query()
            ->where('course_id', $course->id)
            ->where('status', ACTIVE)
            ->where('stage', $lessonGroup->category->stage)
            ->where('sort', '<', $lessonGroup->category->sort)
            ->orderBy('sort', 'DESC')
            ->first();

        if (!$previousLessonCategory) {
            $previousLessonCategory = LessonCategory::query()
                ->where('course_id', $course->id)
                ->where('status', ACTIVE)
                ->where('stage', $lessonGroup->category->stage - 1)
                ->orderBy('sort', 'DESC')
                ->first();
        }

        if (!$previousGroup && $previousLessonCategory) {
            $previousGroup = LessonGroup::query()
                ->where('course_id', $course->id)
                ->where('is_specialezed', INACTIVE)
                ->where('show', ACTIVE)
                ->where('lesson_category_id', $previousLessonCategory->id)
                ->orderBy('sort', 'DESC')
                ->first();
        }

        $groups = LessonGroup::where('lesson_group.course_id', $course->id)
            ->with(['lessons' => function ($query) {
                $query->where('show', ACTIVE)
                    ->orderBy('sort_order', 'asc');
            }])
            ->select('lesson_group.*', 'lesson_categories.stage')
            ->join('lesson_categories', 'lesson_group.lesson_category_id', '=', 'lesson_categories.id')
            ->where('lesson_group.show', ACTIVE)
            ->where('lesson_group.is_specialezed', INACTIVE)
            ->orderBy('lesson_categories.stage', 'asc')
            ->orderBy('lesson_categories.sort', 'asc')
            ->orderBy('lesson_group.sort', 'asc')
            ->get();

        $lessonGroupIndex = $groups->search(function ($g) use ($lessonGroup) {
            return $g->id == $lessonGroup->id;
        });

        $learningLesson = Session::get('lesson_last_visit_' . $course->id) ? CourseHelper::getLearningLessonBySession($currentUserId, $course->id) : CourseHelper::getLearningLesson($course, $groups, $categories, $lessonProgress);
        if (!$learningLesson) $learningLesson = CourseHelper::getLearningLesson($course, $groups, $categories, $lessonProgress);

//            if (!\Illuminate\Support\Facades\Auth::check()) {
//                if ($lessonGroup->sort === 1) {
//                    $learningLesson = $lessonGroup->lessons->first();
//                } else {
//                    $lessonFirst = LessonGroup::query()
//                        ->where('sort', 1)
//                        ->where('course_id', $course->id)
//                        ->where('lesson_category_id', $lessonGroup->lesson_category_id)
//                        ->with(['lessons' => function ($q) {
//                            $q->where('sort_order', 1)->first();
//                        }])->first();
//                    $learningLesson = $lessonFirst->lessons->first();
//                    $learningLesson->percent = 0;
//                }
//            }

        return view('frontend.course.lesson-group')->with([
            'course' => $course,
            'lessonGroup' => $lessonGroup,
            'lessonGroupIndex' => $lessonGroupIndex,
            'lessonProgress' => $lessonProgress,
            'totalGroup' => $totalGroup,
            'nextGroup' => $nextGroup,
            'previousGroup' => $previousGroup,
            'learningLesson' => $learningLesson,
            'isUnlock' => $isUnlock,
            'hideBottomMenu' => true,
            'hideSupportMenu' => true,
            'hideFooter' => true,
        ]);
//        } catch (\Exception $e) {
//            return abort(404);
//        }
    }

    public function getLessonNew($courseSlug, $lessonId): \Illuminate\Contracts\View\Factory|\Illuminate\Foundation\Application|\Illuminate\Contracts\View\View|\Illuminate\Contracts\Foundation\Application
    {
//        if (config('app.env') === 'prod' || config('app.env') === 'production') {
//            if (!auth()->check() || (auth()->check() && !auth()->user()->is_assistant)) {
//                abort(404);
//            }
//        }
//        try {
        $visitCount = Session::get('lesson_visit_count', 0);
        Session::put('lesson_visit_count', $visitCount + 1);

        $lesson = Lesson::query()
            ->with([
                'components' => function ($q) {
                    $q->where('show', ACTIVE)
                        ->orderBy('sort', 'asc');
                },
                'video.video_info',
                'lesson_group.category',
            ])->findOrfail($lessonId);


        $lesson->documents = $lesson->components->whereIn('type', [LessonComponents::TYPE_CONTENT, LessonComponents::TYPE_PDF])->sortBy('sort');

        $course = Cache::rememberForever('cache_course_' . $lesson->course_id, function () use ($lesson) {
            $query = Course::query()
                ->with('intro_lesson.video')
                ->where('show', ACTIVE)
                ->where('id', $lesson->course_id);

            if (in_array($lesson->course_id, [39, 40, 44, 45, 46])) {
                $query = $query->whereHas('intro_lesson');
            }
            return $query->firstOrFail();
        });
        Session::put('lesson_last_visit_' . $course->id, $lessonId);

        $categories = Cache::rememberForever('course_category_' . $course->id, function () use ($course) {
            return LessonCategory::query()
                ->where('course_id', $course->id)
                ->where('status', ACTIVE)
                ->orderBy('stage', 'ASC')
                ->orderBy('sort', 'ASC')
                ->get();
        });

        $currentUserId = auth()->guard('web')->id();
        $isUnlock = false;
        $courseOwner = null;
        if (\Illuminate\Support\Facades\Auth::check()) {
            $courseOwnerPromotion = \App\Http\Models\CourseOwner::query()
                ->where('owner_id', \Illuminate\Support\Facades\Auth::id())
                ->where('watch_expired_day', '>', now())
                ->whereNotNull('code_active_at')
                ->whereIn('course_id', [Course::ID_COURSE_N4_NEW, Course::ID_COURSE_N5_NEW])
                ->select('id', 'course_id', 'owner_id', 'code_id', 'code_active_at', 'watch_expired_day')
                ->get();

            if ($courseOwnerPromotion->first()) {
                $timeCodeActive = Carbon::parse($courseOwnerPromotion->first()->code_active_at);
                $timeCodeActive_30m = $timeCodeActive->copy()->addMinutes(30);        // A + 30 phút
                $timeCodeActive_1h30m = $timeCodeActive->copy()->addHours(1)->addMinutes(30); // A + 1h30p
                $timeCodeActive_2h = $timeCodeActive->copy()->addHours(2);           // A + 2 giờ
                $timeCodeActive_24h = $timeCodeActive->copy()->addHours(24);          // A + 24 giờ

                $now = Carbon::now();

                if ($now->between($timeCodeActive, $timeCodeActive_30m)) {
                    Session::put('link_promotion_try_lesson', 'https://m.me/102856559258931?ref=IAP30'); // discount 30%
                } elseif ($now->between($timeCodeActive_30m, $timeCodeActive_1h30m)) {
                    Session::put('link_promotion_try_lesson', 'https://m.me/102856559258931?ref=IAP20'); // discount 20%
                } elseif ($now->between($timeCodeActive_2h, $timeCodeActive_24h)) {
                    Session::put('link_promotion_try_lesson', 'https://m.me/102856559258931?ref=IAP10'); // discount 10%
                } else {
                    Session::remove('link_promotion_try_lesson'); // del link discount
                }
            }

            $validCourses = $courseOwnerPromotion->filter(function ($q) {
                return $q->watch_expired_day != null && $q->code_id != null && \Carbon\Carbon::parse($q->code_active_at)->addDay()->greaterThan(Carbon::now()) && \Carbon\Carbon::parse($q->code_active_at)->addDay()->greaterThanOrEqualTo(Carbon::parse($q->watch_expired_day));
            });

            if ($validCourses->isEmpty()) {
                Session::remove('lesson_visit_count');
            }
        } else {
            Session::remove('lesson_visit_count');
        }
        $lessonProgress = new Collection();
        if ($currentUserId) {
            $courseOwner = CourseOwner::query()
                ->where('owner_id', $currentUserId)
                ->select('course_owner.*', 'codes.code')
                ->leftJoin('codes', 'codes.id', 'course_owner.code_id')
                ->where('course_id', $course->id)
                ->where('watch_expired_day', '>', Carbon::now())
                ->first();

            if ($course->price_option === 0 || $course->price_option === false) {
                $isUnlock = true;
                $checkCourseOwner = CourseOwner::query()
                    ->where('owner_id', $currentUserId)
                    ->where('course_id', $course->id)
                    ->first();
                if (!$checkCourseOwner) {
                    CourseOwner::create([
                        'owner_id' => $currentUserId,
                        'course_id' => $course->id,
                        'title' => $course->name,
                        'watch_expired_day' => '2035-12-12 00:00:00',
                    ]);
                }
            } else {
                $isUnlock = !!$courseOwner;
            }

            LessonProgress::firstOrCreate([
                'user_id' => $currentUserId,
                'lesson_id' => $lesson->id
            ]);
            if (in_array($lesson->type, ['docs', 'video'])) {
                if ($lesson->type == 'docs') {
                    LessonProgress::query()
                        ->updateOrCreate(
                            ['lesson_id' => $lesson->id, 'user_id' => auth()->id()],
                            ['video_progress' => 100, 'example_progress' => 100],
                        );
                }
//            Session::remove('set_popup_achievement_lesson_' . $lesson->id);
                // Create lesson progress if not exists when user access to document lesson

                $countLessonComplete = CourseHelper::countLessonComplete($course->id, $lesson->id, $course);
                $lessonComplete = $countLessonComplete['lessonComplete'];
                $countLessonGroup = $countLessonComplete['countLessonGroup'];
                $count = Session::get('set_popup_achievement_lesson_' . $lesson->id, 0);
                $countStageComplete = Session::get('set_popup_achievement_stage_' . $lesson->course_id .'_'. $lesson?->lesson_group?->category?->stage, 0);
                $countCourseComplete = Session::get('set_popup_achievement_course_' . $lesson->course_id, 0);
                $courseCompleted = CourseHelper::checkIfCourseCompleted($course->id);
                $courseProgress = CourseOwner::where('owner_id', auth()->id())->where('course_id', $lesson->course_id)->first();
                if ($courseProgress) {
                    if ($courseProgress->progress < 85 && $courseCompleted >= 85) {
                        Session::put('show_popup_done_stage', 1);
                        Session::put('show_btn_next_lesson', 0);
                        Session::put('set_popup_achievement_course_' . $lesson->course_id, $countCourseComplete + 1);
                    }
                    $courseProgress->progress = $courseCompleted;
                    $courseProgress->save();
                }
                if ($courseProgress && $courseProgress->progress < 85 && !$countStageComplete) {
                    $isFirstStageComplete = CourseHelper::checkIsFirstStageComplete($lesson->id);
                    if ($isFirstStageComplete['status']) {
                        Session::put('text_content_achievement', !$isFirstStageComplete['status'] ? null : 'Hoàn thành ' . $isFirstStageComplete['stageName']);
                        Session::put('show_popup_achievement', $isFirstStageComplete['status'] ? 1 : 0);
                        Session::put('name_popup_achievement', !$isFirstStageComplete['status'] ? null : 'complete_stage_' . rand(1, 4));
                        Session::put('show_btn_next_lesson', 1);
                        Session::put('set_popup_achievement_stage_' . $lesson->course_id .'_'. $lesson?->lesson_group?->category?->stage, $countStageComplete + 1);
                    }
                }

                if ($count == 0) {
                    Session::put('text_content_achievement', !$lessonComplete ? null : ($lessonComplete === 3 ? 'hoàn thành chuỗi 3 bài học' : null));
                    Session::put('show_popup_achievement', $lessonComplete != 0 && $lessonComplete === 3 ? 1 : 0);
                    Session::put('name_popup_achievement', !$lessonComplete ? null : ($lessonComplete === 3 ? 'three_consecutive' : null));
                    Session::put('show_btn_next_lesson', 1);
                }

                Session::put('set_popup_achievement_lesson_' . $lesson->id, $count + 1);
            }

            $lessonProgress = $this->getLessonProgress($course);
        }

        $groups = LessonGroup::query()
            ->where('lesson_group.course_id', $course->id)
            ->with([
                'lessons' => function ($query) use ($currentUserId) {
                    $query
                        ->with(['progress' => function ($q) use ($currentUserId) {
                            $q->where('user_id', $currentUserId);
                        }]);
                        if (auth()->check()) {
                            $query->with([
                                'exam_result' => function ($query) use ($currentUserId) {
                                    $query->where('user_id', $currentUserId);
                                },
                                'last_exam_result' => function ($query) use ($currentUserId) {
                                    $query->where('user_id', $currentUserId);
                                },
                                'passed_exam_result' => function ($query) use ($currentUserId) {
                                    $query->where('user_id', $currentUserId);
                                },
                            ]);
                        }
                        $query->where('show', ACTIVE)
                        ->orderBy('sort_order', 'asc');
                },
                'lessons.components' => function ($query) {
                    $query
                        ->where('show', ACTIVE)
                        ->orderBy('sort', 'asc');
                },
            ]);

        $groups = $groups
            ->select('lesson_group.*', 'lesson_categories.stage')
            ->join('lesson_categories', 'lesson_group.lesson_category_id', '=', 'lesson_categories.id')
            ->where('lesson_group.show', ACTIVE)
            ->where('lesson_categories.stage', $lesson->lesson_group->category->stage)
            ->where('lesson_group.is_specialezed', INACTIVE)
            ->orderBy('lesson_categories.sort', 'asc')
            ->orderBy('lesson_group.sort', 'asc')
            ->get();

        $groups->map(function ($group) use ($lessonProgress) {
            $lessonIds = $group->lessons->where('require', 0)->pluck('id');
            if ($lessonIds->isEmpty() || $lessonProgress->isEmpty()) {
                $group->progress = 0;
                return $group;
            }
            $sum = round($lessonProgress->whereIn('lesson_id', $lessonIds)->sum('total_progress') / count($lessonIds));
            $group->progress = $sum;
            return $group;
        });

        if ($groups->count()) {
            $course->percent = round($groups->sum('progress') / $groups->count());
        }

        $lessonListMenuData = new Collection();
        foreach ($categories->where('stage', $lesson->lesson_group->category->stage) as $category) {
            $stgName = $category->stage_name;
            $categoryName = $category->title;
            $groupTransforms = $groups->where('lesson_category_id', $category->id)->map(function ($group) use ($lessonProgress, $courseSlug, $stgName, $categoryName, $isUnlock) {

                $groupName = $group->name;
                $lessonTransforms = $group->lessons->map(function ($l) use ($lessonProgress, $courseSlug, $stgName, $categoryName, $groupName) {

                    $imageUrl = $l->avatar_name ? url('cdn/lesson/default/' . $l->avatar_name) : url('images/lessons/lesson-default.png');
//                    $isTestOrFlashcard = $l->type == 'test' || $l->type == 'flashcard';
//
//                    if ($isTestOrFlashcard) {
//                        $imageUrl = url('images/lessons/' . $l->type . '.png');
//                    }

                    if (!$l->components->whereIn('type', LessonToTask::COMPONENT_TYPES_EXERCISE)->isEmpty()) {
                        $progress = auth()->check() ? ($l->progress->where('user_id', auth()->id())->first() && $l->progress->where('user_id', auth()->id())->first()->example_progress !== null ? $l->progress->where('user_id', auth()->id())->first()->example_progress : 0) : 0;
                    } else {
                        $progress = auth()->check() ? ($l->progress->where('user_id', auth()->id())->first() && $l->progress->where('user_id', auth()->id())->first()->video_progress !== null ? $l->progress->where('user_id', auth()->id())->first()->video_progress : 0) : 0;
                    }

                    return [
                        'id' => $l->id,
                        'course_slug' => $courseSlug,
                        'stage_name' => $stgName,
                        'category_name' => $categoryName,
                        'group_name' => $groupName,
                        'image' => $imageUrl,
                        'name' => $l->name,
                        'name_html' => $l->name_html,
                        'slug' => $l->SEOurl,
                        'type' => $l->type,
                        'require' => $l->require,
                        'is_free' => $l->price_option == 0,
                        'expect_time' => $l->expect_time,
                        'component_types' => $l->components->pluck('type')->toArray(),
                        'progress' => $progress,
                        'checkSubmit' => $l->checkSubmit,
                        'exam_result' => $l->exam_result,
                        'last_exam_result' => $l->last_exam_result,
                        'passed_exam_result' => $l->passed_exam_result,
                        'lesson_type' => $l->type
                    ];
                });
                $expectTime = $group->lessons->sum('expect_time');
                $totalVideo = $group->lessons->count();

                $isTrial = !($group->lessons->where('price_option', 2)->count() > 0) && !$isUnlock;
                return [
                    'id' => $group->id,
                    'name' => $group->name,
                    'is_trial' => $isTrial,
                    'progress' => $group->progress,
                    'total_time' => convertMinutesToHours($expectTime),
                    'total_video' => $totalVideo,
                    'lessons' => $lessonTransforms
                ];
            });
            $lessonListMenuData[] = [
                'id' => $category->id,
                'title' => $category->title,
                'groups' => $groupTransforms,
            ];
        }

        $allLesson = $lessonListMenuData->flatMap(function ($category) {
            return $category['groups']->flatMap(function ($group) {
                return $group['lessons'];
            });
        });

        $stageName = $lesson->lesson_group->category->stage_name;

        $currentLesson = $lesson->only(['id', 'name', 'type', 'price_option', 'group_id', 'require']);
        $componentExercise = $lesson->component->filter(fn($q) => in_array($q->type, LessonToTask::COMPONENT_TYPES_EXERCISE));
        $progress = $lessonProgress->where('lesson_id', $lesson->id)->first();
        $currentLesson['category_id'] = $lesson->lesson_group->category->id;
        $currentLesson['percent'] = $progress ? (!$componentExercise->isEmpty() ? $progress->example_progress : $progress->video_progress) : 0;

        $currentCourse = $course->only(['id', 'name', 'SEOurl']);

        $nextCategory = $categories->where('stage', '>', $lesson->lesson_group->category->stage)->first();

        $nextGroup = null;
        if ($nextCategory) {
            $nextGroup = LessonGroup::where('lesson_category_id', $nextCategory->id)
                ->where('show', ACTIVE)
                ->where('is_step', INACTIVE)
                ->where('is_specialezed', INACTIVE)
                ->orderBy('sort', 'asc')
                ->first();
        }

        $nextLesson = null;
        if ($nextGroup) {
            $nextLesson = Lesson::query()
                ->where('course_id', $course->id)
                ->where('group_id', $nextGroup->id)
                ->where('show', ACTIVE)
                ->orderBy('sort_order', 'asc')
                ->first();
        }

        if (!empty($nextLesson)) {
            $nextLesson->stage_name = $nextLesson->lesson_group->category->stage_name;
            $nextLesson->course_slug = $courseSlug;
            $nextLesson->only(['id', 'name', 'SEOurl', 'stage_name']);
        }
        $nextLessonCurrentGroup = $this->findNextLesson($groups, $categories, $currentLesson, $lesson, $nextLesson);

        $contentLesson = Lesson::query()
            ->with(["component" => function ($q) {
                $q->where('show', ACTIVE)->whereIn('type', LessonToTask::COMPONENT_TYPES_EXERCISE)->orderBy('sort')->with('answers');
            }])
            ->find($lessonId);

        $nextToLesson = Lesson::where('group_id', $lesson->group_id)
            ->where('sort_order', '>', $lesson->sort_order)
            ->where('show', ACTIVE)
            ->orderBy('sort_order', 'asc')
            ->first();

        if (is_null($nextToLesson)) {
            $currentGroup = LessonGroup::find($lesson->group_id);
            $nextToGroup = LessonGroup::query()
                ->where('lesson_category_id', $currentGroup->lesson_category_id)
                ->where('sort', '>', $currentGroup->sort)
                ->where('show', ACTIVE)
                ->where('is_step', INACTIVE)
                ->where('is_specialezed', INACTIVE)
                ->orderBy('sort', 'asc')
                ->first();
            if (!is_null($nextToGroup)) {
                $nextToLesson = Lesson::where('group_id', $nextToGroup->id)
                    ->where('show', ACTIVE)
                    ->orderBy('sort_order', 'asc')
                    ->first();
            } else {
                $currentCategory = LessonCategory::find($currentGroup->lesson_category_id);
                $nextCategory = LessonCategory::query()
                    ->where('course_id', $course->id)
                    ->where('stage', $currentCategory->stage)
                    ->where('sort', '>', $currentCategory->sort)
                    ->where('status', ACTIVE)
                    ->orderBy('sort', 'asc')
                    ->first();
                if (!is_null($nextCategory)) {
                    $nextGroup = LessonGroup::query()
                        ->where('lesson_category_id', $nextCategory->id)
                        ->where('show', ACTIVE)
                        ->where('is_step', INACTIVE)
                        ->where('is_specialezed', INACTIVE)
                        ->orderBy('sort', 'asc')
                        ->first();
                    if (!is_null($nextGroup)) {
                        $nextToLesson = Lesson::where('group_id', $nextGroup->id)
                            ->orderBy('sort_order', 'asc')
                            ->first();
                    }
                } else {
                    $nextCategory = LessonCategory::query()
                        ->where('course_id', $course->id)
                        ->where('stage', $currentCategory->stage + 1)
                        ->where('status', ACTIVE)
                        ->orderBy('sort', 'asc')
                        ->first();
                    if (!is_null($nextCategory)) {
                        $nextGroup = LessonGroup::query()
                            ->where('lesson_category_id', $nextCategory->id)
                            ->where('show', ACTIVE)
                            ->where('is_step', INACTIVE)
                            ->where('is_specialezed', INACTIVE)
                            ->orderBy('sort', 'asc')
                            ->first();
                        if (!is_null($nextGroup)) {
                            $nextToLesson = Lesson::where('group_id', $nextGroup->id)
                                ->orderBy('sort_order', 'asc')
                                ->first();
                        }
                    }
                }
            }
        }
        if (!is_null($nextToLesson)) {
            $nextToLesson->course_slug = $courseSlug;
            $nextToLesson->stage_name = $nextToLesson->lesson_group->category->stage_name;
        }

        foreach ($contentLesson->component as $key => $component) {
            if (in_array($component->type, LessonToTask::COMPONENT_TYPES_EXERCISE) && $component->type !== LessonToTask::MULTIPLE_CHOICE) {
                $contentLesson->component[$key]->value = json_decode($component->value, true);

                if ($component->type === LessonToTask::TYPE_SENTENCE_JUMBLE && !empty($component->value['item_false'])) {
                    $tempValue = $component->value;
                    $tempValue['items'] = array_merge($component->value['question'], $component->value['item_false']);
                    shuffle($tempValue['items']);
                    $contentLesson->component[$key]->value = $tempValue;
                } else if ($component->type === LessonToTask::TYPE_SENTENCE_JUMBLE && empty($component->value['item_false'])) {
                    $tempValue = $component->value;
                    $tempValue['items'] = $component->value['question'];
                    shuffle($tempValue['items']);
                    $contentLesson->component[$key]->value = $tempValue;
                }
            }
        }

        DB::table('lesson')->where('id', $lesson->id)->update(['count_view' => $lesson->count_view + 1]);
        if ($nextLessonCurrentGroup) {
            $nextLessonCurrentGroup->course_slug = $courseSlug;
        }

        return view('frontend.course.lesson-basic-new')->with([
            'course' => $course,
            'lesson' => $lesson,
            'currentCourse' => $currentCourse,
            'currentLesson' => $currentLesson,
            'lessonProgress' => $lessonProgress,
            'menuData' => $lessonListMenuData,
            'allLesson' => $allLesson,
            'nextLesson' => $nextLesson,
            'isUnlock' => $isUnlock,
            'stageName' => $stageName,
            'hideBottomMenu' => true,
            'hideSupportMenu' => true,
            'hideFooter' => true,
            'contentLesson' => $contentLesson,
            'courseOwner' => $courseOwner,
            'nextLessonCurrentGroup' => $nextLessonCurrentGroup,
//                'courseOwnerPromotion' => $courseOwnerPromotion,
            'nextToLesson' => $nextToLesson,
        ]);
//        } catch (\Exception $e) {
//            return abort(404);
//        }
    }

    private function findNextLesson($groups, $categories, $currentLesson, $lesson, $nextLesson)
    {
        $currentGroup = $groups->find($currentLesson['group_id']);
        if (!$currentGroup) {
            return $nextLesson;
        }

        $nextLessonInGroup = $currentGroup->lessons()
            ->where('sort_order', '>', $lesson->sort_order)
            ->where('show', ACTIVE)
            ->orderBy('sort_order')
            ->first();

        if ($nextLessonInGroup) {
            return $nextLessonInGroup;
        }

        $nextGroup = $groups->where('lesson_category_id', $currentLesson['category_id'])
            ->where('sort', '>', $currentGroup->sort)
            ->sortBy('sort')
            ->first();

        if ($nextGroup) {
            $firstLessonInNextGroup = $nextGroup->lessons()
                ->orderBy('sort_order')
                ->first();
            return $firstLessonInNextGroup ?? $nextLesson;
        }

        $currentCategory = $categories->find($currentLesson['category_id']);
        if (!$currentCategory) {
            return $nextLesson;
        }

        $nextCategory = $categories->where('stage', $currentCategory->stage)
            ->where('sort', '>', $currentCategory->sort)
            ->sortBy('sort')
            ->first();

        if (!$nextCategory) {
            return $nextLesson;
        }

        $firstGroupInNextCategory = $groups->where('lesson_category_id', $nextCategory->id)
            ->sortBy('sort')
            ->first();

        if (!$firstGroupInNextCategory) {
            return $nextLesson;
        }

        $firstLessonInNextCategory = $firstGroupInNextCategory->lessons()
            ->orderBy('sort_order')
            ->first();

        return $firstLessonInNextCategory ?? $nextLesson;
    }

    private function getLessonProgress($course)
    {
        if (!auth()->check()) {
            return new Collection();
        }

        $currentUserId = auth()->id();
        return LessonProgress::select(
            'id',
            'lesson_id',
            'user_id',
            'video_progress',
            'example_progress',
            'updated_at',
            DB::raw('CASE WHEN example_progress > 0 THEN example_progress ELSE video_progress END as total_progress')
        )
            ->where('user_id', $currentUserId)
            ->whereHas('lesson', function ($q) use ($course) {
                $q->where('course_id', $course->id)->where('show', ACTIVE);
            })
            ->whereHas('lesson.lesson_group', function ($q) {
                $q->where('show', ACTIVE);
            })
            ->whereHas('lesson.lesson_group.category', function ($q) {
                $q->where('show', ACTIVE);
            })
            ->get();
    }

    /**
     * upsert lesson progress
     *
     * @return void
     */
    public function upsertLessonProgress(LessonProgressRequest $request)
    {
        $lesson = Lesson::select('id', 'type', 'course_id')->find($request->lesson_id);
        $data['user_id'] = auth()->id();
        $data['video_progress'] = $request->video_progress;
        $data['example_progress'] = $request->example_progress;
        $data['updated_at'] = Carbon::now();



        $existLessonProgress = LessonProgress::query()
            ->where('lesson_id', $request->lesson_id)
            ->where('user_id', auth()->id())
            ->first();

        if ($existLessonProgress) {
            $data['video_progress'] = !empty($request->video_progress) && intval($request->video_progress) > $existLessonProgress->video_progress ? $request->video_progress : $existLessonProgress->video_progress;
            $data['example_progress'] = !empty($request->example_progress) && intval($request->example_progress) > $existLessonProgress->example_progress ? $request->example_progress : $existLessonProgress->example_progress;
        }

        $lessonProgress = LessonProgress::query()
            ->updateOrCreate(
                ['lesson_id' => $request->lesson_id, 'user_id' => auth()->id()],
                $data
            );

        if ($lesson->type == 'video' && ((!$existLessonProgress || $existLessonProgress->video_progress < 85) && $request->video_progress >= 85)) {
            $isLessonPass = false;
            if ($existLessonProgress && $existLessonProgress->video_progress >= 85) $isLessonPass = true;

            $lesson->load([
                'components' => function ($q) {
                    $q->where('show', ACTIVE)
                        ->orderBy('sort', 'asc');
                },
                'video.video_info',
                'lesson_group.category',
            ]);

            $countLessonComplete = CourseHelper::countLessonComplete($lesson->course_id, $lesson->id);
            $lessonComplete = $countLessonComplete['lessonComplete'];
            $countLessonGroup = $countLessonComplete['countLessonGroup'];
            $courseProgress = CourseOwner::where('owner_id', auth()->id())->where('course_id', $lesson->course_id)->first();
            $coursePercent = $courseProgress->progress ?? 0;
            if ($lessonComplete && !$isLessonPass) {
                $countCourseComplete = Session::get('set_popup_achievement_course_' . $lesson->course_id, 0);
                $courseCompleted = CourseHelper::checkIfCourseCompleted($lesson->course_id);
                if ($courseCompleted >= 85) {
                    $courseProgress->progress = $courseCompleted;
                    $courseProgress->save();
                    if ($coursePercent < 85) {
                        $res = [
                            'error_code' => 0,
                            'msg' => 'Lưu dữ liệu thành công!',
                            'show_popup_done_stage' => 1,
                            'show_btn_next_lesson' => 0
                        ];
                        return response()->json($res);
                    }
                }

                $isFirstStageComplete = CourseHelper::checkIsFirstStageComplete($lesson->id);
                if ($coursePercent < 85 && $isFirstStageComplete['status']) {
                    $res = [
                        'error_code' => 0,
                        'msg' => 'Lưu dữ liệu thành công!',
                        'text_content_achievement' => !$isFirstStageComplete['status'] ? null : 'Hoàn thành ' . $isFirstStageComplete['stageName'],
                        'show_popup_achievement' => $isFirstStageComplete['status'] ? 1 : 0,
                        'name_popup_achievement' => !$isFirstStageComplete['status'] ? null : 'complete_stage_' . rand(1, 4),
                    ];
                    return response()->json($res);
                }
            }
        }
        return $lessonProgress;
    }

    public function getComments(Request $request)
    {
        try {
            $table_id = $request['lesson_id'];
            if ($request['name'] === 'flashcard') {
                $table_name = 'flashcard';
                $table_id = $request['id'];
            } else {
                $table_name = 'lesson';
            }

            $comments = Comment::query()->distinct();
            if ($request['name'] === 'flashcard') {
                $comments = $comments->with(['comment_like' => function ($q) {
                    $q->select('comment_id', 'user_id');
                }]);
            }

            $comments = $comments->with('getUserInfo')->where('table_id', $table_id)
                ->where('table_name', $table_name)
                ->where(function ($query) {
                    if (auth()->check()) {
                        $query->where('user_id', '<>', auth()->id());
                    }
                    $query->where('parent_id', INACTIVE);
                })
                ->orderByDesc('created_at')
                ->orderBy('id')
                ->paginate(10);

            $page = $request->get('page');

            $currentUserId = auth()->id();
            if ($currentUserId && (empty($page) || $page == 1)) {
                $ownerComment = Comment::query()->distinct()->with('getUserInfo')->where('table_id', $table_id)
                    ->where('table_name', $table_name)
                    ->where('user_id', auth()->id())
                    ->where('parent_id', INACTIVE)
                    ->orderByDesc('created_at')
                    ->orderBy('id')
                    ->get();
                $ownerComment = $ownerComment->merge($comments->items());
                $comments->setCollection($ownerComment);
            }

            $commendIds = $comments->pluck('id');

            $replies = Comment::query()
                ->distinct()
                ->with('getUserInfo')
                ->whereIn('parent_id', $commendIds)
                ->orderByDesc('created_at')
                ->orderBy('id');
            if ($request['name'] === 'flashcard') {
                $replies = $replies->with(['comment_like' => function ($q) {
                    $q->select('comment_id', 'user_id');
                }]);
            }
            $replies = $replies->get();

            $replies = $replies->map(function ($reply) use ($currentUserId, $request) {
                $userInfo = [
                    'name' => $reply->user_info['name'],
                    'avatar' => $reply->user_info['avatar'] ? url('cdn/avatar/default/' . $reply->user_info['avatar']) : url('images/icons/default-user.png'),
                ];

                if ($reply->user_id == 0) {
                    $userInfo['name'] = 'Dũng Mori';
                    $userInfo['avatar'] = url('images/icons/admin.png');
                }

                return [
                    'id' => $reply->id,
                    'parent_id' => $reply->parent_id,
                    'is_admin' => $reply->user_id == 0,
                    'is_owner' => $reply->user_id == $currentUserId,
                    'content' => $reply->content,
                    'image' => $reply->img ? url('cdn/comment/small/' . $reply->img) : $reply->img,
                    'user_info' => $userInfo,
                    'created_at' => $reply->time_created,
                    'count_like' => $reply->count_like,
                    'comment_like' => $request['name'] === 'flashcard' ? $reply->comment_like->pluck('user_id') : [],
                ];
            });
            // 21h30 -> 7h = 

            $paginate = [
                'total' => $comments->total(),
                'per_page' => $comments->perPage(),
                'current_page' => $comments->currentPage(),
                'last_page' => $comments->lastPage(),
            ];

            $comments = $comments->map(function ($comment) use ($replies, $currentUserId, $request) {
                $comment->replies = array_values($replies->where('parent_id', $comment->id)->toArray());
                $userInfo = [
                    'name' => $comment->user_info['name'],
                    'avatar' => $comment->user_info['avatar'] ? url('cdn/avatar/default/' . $comment->user_info['avatar']) : url('images/icons/default-user.png'),
                ];
                if ($comment->user_id == 0) {
                    $userInfo['name'] = 'Dũng Mori';
                    $userInfo['avatar'] = url('images/icons/admin.png');
                }
                return [
                    'id' => $comment->id,
                    'is_admin' => $comment->user_id == 0,
                    'is_owner' => $comment->user_id == $currentUserId,
                    'content' => $comment->content,
                    'image' => $comment->img ? url('cdn/comment/small/' . $comment->img) : $comment->img,
                    'user_info' => $userInfo,
                    'created_at' => $comment->time_created,
                    'replies' => $comment->replies,
                    'comment_like' => $request['name'] === 'flashcard' ? $comment->comment_like->pluck('user_id') : [],
                    'count_like' => $comment->count_like
                ];
            });


            return [
                'data' => $comments,
                'paginate' => $paginate,
            ];
        } catch (\Exception $e) {
            return response()->json(['error' => $e->getMessage()], 403);
        }
    }

    public function updateReadReply(Request $request)
    {
        try {
            $commentId = $request->get('comment_id');

            if (!auth()->check()) {
                return response()->json(['error' => 'Unauthorized'], 403);
            }

            $comment = Comment::where('user_id', auth()->id())
                ->where('id', $commentId)
                ->where('parent_id', INACTIVE)
                ->firstOrFail();

            DB::table('comment')
                ->where('parent_id', $comment->id)->update(['readed' => ACTIVE]);

            return response()->json('success', 200);
        } catch (\Exception $e) {
            return response()->json(['error' => $e->getMessage()], 403);
        }
    }


    /**
     * getTotalUnreadReplies
     *
     * @param Request $request
     * @return int
     */
    public function getTotalUnreadReplies(Request $request)
    {
        if (!auth()->check()) {
            return 0;
        }

        if ($request['type'] === 'flashcard') {
            return \Illuminate\Support\Facades\DB::table('comment')
                ->where('table_id', $request['flashcard_id'])
                ->where('table_name', 'flashcard')
                ->count('id');
        }

        $lessonId = $request->get('lesson_id');
        $commentIds = Comment::query()->where('user_id', auth()->id())
            ->where('table_id', $lessonId)
            ->where('table_name', 'lesson')
            ->where('parent_id', INACTIVE)
            ->pluck('id');

        if (empty($commentIds)) {
            return 0;
        }
        $totalUnreadReplied = Comment::query()->whereIn('parent_id', $commentIds)
            ->where('user_id', '<>', auth()->id())
            ->where('readed', INACTIVE)
            ->count();

        if ($totalUnreadReplied > 99) {
            return '99+';
        }
        return $totalUnreadReplied;
    }

    public function getExamLesson($lessonId)
    {
        return Lesson::with(['exam_parts' => function ($q) use ($lessonId) {
                $q->with(['mondais' => function ($q) use ($lessonId) {
                    $q->with(['tasks' => function ($q) use ($lessonId) {
                        $q->with('answers')->where('lesson_id', $lessonId)->where('show', ACTIVE);
                    }])->with(['questions' => function ($q) use ($lessonId) {
                        $q->with('answers')->where('lesson_id', $lessonId)->where('show', ACTIVE);
                    }])->where('lesson_id', $lessonId);
                }])->with(['questions' => function ($q) use ($lessonId) {
                    $q->with('answers')->where('lesson_id', $lessonId)->where('show', ACTIVE);
                }]);
            }])
            ->with(['exam_result' => function ($q) {
                $q->with('user')->where('user_id', auth()->id());
            }])
            ->with(['last_exam_result' => function ($q) {
                $q->with('user')->where('user_id', auth()->id());
            }])
            ->with(['passed_exam_result' => function ($q) {
                $q->with('user')->where('user_id', auth()->id());
            }])
            ->find($lessonId);
    }

    public function startExam($lessonId)
    {
        $lesson = Lesson::find($lessonId);
        if (is_null($lesson)) return;
        // tìm tất cả result có submit_at = null và sửa thành submit_at = now, logic này sẽ dùng sau
        App\Http\Models\ExamResult::where('lesson_id', $lessonId)
            ->where('user_id', auth()->id())
            ->whereNull('submit_at')
            ->update(['submit_at' => now()]);

        // giờ sẽ dùng tạm logic xóa mọi result có submit_at = null để tạo result mới đã
//        App\Http\Models\ExamResult::where('lesson_id', $lessonId)
//            ->where('user_id', auth()->id())
//            ->whereNull('submit_at')
//            ->delete();

        // tạo mới result
        $result = new App\Http\Models\ExamResult();
        $result->lesson_id = $lessonId;
        $result->user_id = auth()->id();
        $result->course = BASIC_LEVELS[$lesson->course_id];
        $result->score_1 = 0;
        $result->score_2 = 0;
        $result->score_3 = 0;
        $result->total_score = 0;
        $result->is_passed = 0;
        $result->time_start = [now()->timestamp, 0, 0];
        $result->join_time = [now()->timestamp, 0, 0];
        $result->passed_time = [0, 0, 0];
        $result->created_at = now();
        $result->updated_at = now();
        $result->save();
        return $this->statusOK($result->load('user'));
    }

    public function selectAnswer(Request $request, $lessonId)
    {
        $params = $request->all();
        $questionId = $params['question_id'];
        $answerId = $params['answer_id'];
        $stage = $params['stage'];
        $result = App\Http\Models\ExamResult::where('lesson_id', $lessonId)
            ->where('user_id', auth()->id())
            ->whereNull('submit_at')
            ->first();
        if (is_null($result)) return $this->statusNG([], 'Không tìm thấy bài kiểm tra');
        $question = LessonComponents::find($questionId);
        $lessonQuestions = LessonComponents::where('lesson_id', $lessonId)
            ->whereIn('type', [3, 13])
            ->pluck('id')
            ->toArray();
        if (!in_array($questionId, $lessonQuestions)) return $this->statusNG([], 'Không tìm thấy câu hỏi');
        if ($question->type == 3) {
            $answers = App\Http\ModelsFrontend\LessonAnswer::where('task_id', $questionId)
                ->pluck('id')
                ->toArray();
            if (!in_array($answerId, $answers)) return $this->statusNG([], 'Không tìm thấy câu trả lời');
        }
        if ($question->type == 13) {
            $answerId = array_map(function ($a) {
                return is_null($a) ? "" : $a;
            }, $answerId);
        }

        if ($stage == 1) {
            if (is_null($result->data_1)) {
                $result->data_1 = [
                    $questionId => $answerId
                ];
            } else {
                $tmp = $result->data_1;
                $tmp[$questionId] = $answerId;
                $result->data_1 = $tmp;
            }
        }
        if ($stage == 2) {
            if (is_null($result->data_2)) {
                $result->data_2 = [
                    $questionId => $answerId
                ];
            } else {
                $tmp = $result->data_2;
                $tmp[$questionId] = $answerId;
                $result->data_2 = $tmp;
            }
        }
        if ($stage == 3) {
            if (is_null($result->data_3)) {
                $result->data_3 = [
                    $questionId => $answerId
                ];
            } else {
                $tmp = $result->data_3;
                $tmp[$questionId] = $answerId;
                $result->data_3 = $tmp;
            }
        }
        $result->save();
        return $this->statusOK($result->load('user'));
    }

    public function submitExam(Request $request, $lessonId)
    {
        $params = $request->all();
        $stage = $params['stage'];
        $result = App\Http\Models\ExamResult::where('lesson_id', $lessonId)
            ->where('user_id', auth()->id())
            ->whereNull('submit_at')
            ->first();

        $lesson = Lesson::find($lessonId);
        if (is_null($result)) return $this->statusNG([], 'Không tìm thấy bài kiểm tra');
        if ($stage == 1) {
            $data = $result->data_1 ?? [];
            $data = array_unique($data);
            if (is_null($data)) {
                $result->score_1 = 0;
            } else {
                foreach ($data as $questionId => $answerId) {
                    $question = LessonComponents::find($questionId);
                    if ($question->type == 3) {
                        $answer = App\Http\ModelsFrontend\LessonAnswer::find($answerId);
                        if (!is_null($answer)) {
                            $result->score_1 += (int) $answer->grade;
                        }
                    }
                    if ($question->type == 13) {
                        $arr = array_map(function ($q) {
                            return $q->result;
                        }, array_filter(json_decode($question->value)->question, function ($q) {
                            return $q->type === 'question';
                        }));
                        if (implode(',', $arr) == implode(',', $answerId)) {
                            $result->score_1 += (int) $question->grade;
                        }
                    }
                }
            }
            $result->total_score = $result->score_1 + $result->score_2 + $result->score_3;
            if ($lesson->type == 'exam') {
                $result->submit_at = now();
                $result->end_waiting_time = null;
                $result->bonus_time = null;
                $user = auth()->user();
                $point = ceil($result->total_score * 100 / $lesson->total_marks);

                if ($point >= 85) {
                    $result->is_passed = 1;
                }

                $result->save();

                $passed = 0;
                $lessonProgress = LessonProgress::query()->where('user_id', auth()->user()->id)
                    ->where('lesson_id', $lesson->id)
                    ->first(['id', 'user_id', 'lesson_id', 'video_progress', 'example_progress']);

                $examProgress = max($lessonProgress->example_progress, $point);

                $params = [
                    'example_progress' => $examProgress,
                ];

                if ($examProgress >= 85) {
                    $params['finished_at'] = now();
                    $passed = 1;
                }

                DB::table('lesson_progress')->where('user_id', auth()->user()->id)->where('lesson_id', $lesson->id)
                    ->update($params);

                if ($examProgress >= 85) {
                    $user->completeLesson((int)$lesson->id, $passed);
                    $activity = Activity::find(2);
                    $user->recordLessonStreak($activity);
                }

                return $this->statusOK($result->load('user'));
            } else {
                if (in_array($result->course, ['N1', 'N2'])) {
                    $result->time_start = [$result->time_start[0], 0, now()->timestamp];
                    $result->join_time = [$result->join_time[0], 0, now()->timestamp];
                } else {
                    $result->time_start = [$result->time_start[0], now()->timestamp, 0];
                    $result->join_time = [$result->join_time[0], now()->timestamp, 0];
                }
            }
        }

        if ($stage == 2) {
            $data = $result->data_2 ?? [];
            $data = array_unique($data);

            if (is_null($data)) {
                $result->score_2 = 0;
            } else {
                foreach ($data as $questionId => $answerId) {
                    $question = LessonComponents::find($questionId);
                    if ($question->type == 3) {
                        $answer = App\Http\ModelsFrontend\LessonAnswer::find($answerId);
                        if (!is_null($answer)) {
                            $result->score_2 += (int) $answer->grade;
                        }
                    }
                    if ($question->type == 13) {
                        $arr = array_map(function ($q) {
                            return $q->result;
                        }, json_decode($question->value)->question);
                        if (implode(',', $arr) == $answerId) {
                            $result->score_2 += (int) $question->grade;
                        }
                    }
                }
            }
            if (!in_array($result->course, ['N1', 'N2'])) {
                $result->time_start = [$result->time_start[0], $result->time_start[1], now()->timestamp];
                $result->join_time = [$result->join_time[0], $result->join_time[1], now()->timestamp];
            }
        }

        $result->end_waiting_time = now()->addMinutes(10);

        if ($stage == 3) {
            $data = $result->data_3 ?? [];
            $data = array_unique($data);

            if (is_null($data)) {
                $result->score_3 = 0;
            } else {
                foreach ($data as $questionId => $answerId) {
                    $question = LessonComponents::find($questionId);
                    if ($question->type == 3) {
                        $answer = App\Http\ModelsFrontend\LessonAnswer::find($answerId);
                        if (!is_null($answer)) {
                            $result->score_3 += (int) $answer->grade;
                        }
                    }
                    if ($question->type == 13) {
                        $arr = array_map(function ($q) {
                            return $q->result;
                        }, json_decode($question->value)->question);
                        if (implode(',', $arr) == $answerId) {
                            $result->score_3 += (int) $question->grade;
                        }
                    }
                }
            }

            $result->submit_at = now();
            $result->end_waiting_time = null;
            $result->bonus_time = null;
        }

        $result->total_score = $result->score_1 + $result->score_2 + $result->score_3;

        $passed = 0;
        $lessonProgress = LessonProgress::query()->where('user_id', auth()->user()->id)
            ->where('lesson_id', $lesson->id)
            ->first(['id', 'user_id', 'lesson_id', 'video_progress', 'example_progress']);
        $examProgress = $result->score_1 + $result->score_2 >= 38 && $result->score_3 >= 19 && $result->total_score >= $lesson->pass_marks ? 100 : max($lessonProgress->example_progress, $result->total_score * 100 / $lesson->total_marks);
        $params = [
            'example_progress' => $examProgress,
        ];
        // save progress
        DB::table('lesson_progress')->where('user_id', auth()->user()->id)->where('lesson_id', $lesson->id)
            ->update($params);

        if ($lesson->type == 'last_exam' && $result->score_1 + $result->score_2 >= 38 && $result->score_3 >= 19 && $result->total_score >= $lesson->pass_marks) {
            $result->is_passed = 1;

            $params['finished_at'] = now();
            $passed = 1;
            $user = auth()->user();
            $user->completeLesson((int)$lesson->id, $passed);
            $activity = Activity::find(2);
            $user->recordLessonStreak($activity);
        }

        $tmpPassedTime = $result->passed_time;
        $tmpPassedTime[$stage - 1] = -1;
        $result->passed_time = $tmpPassedTime;
        $result->mp3_time = 0;
        $result->save();

        return $this->statusOK($result->load('user'));
    }

    public function nextStage(Request $request, $lessonId)
    {
        $params = $request->all();
        $stage = $params['stage'];
        $result = App\Http\Models\ExamResult::where('lesson_id', $lessonId)
            ->where('user_id', auth()->id())
            ->whereNull('submit_at')
            ->first();
        $lesson = Lesson::find($lessonId);
        if (is_null($result)) return $this->statusNG([], 'Không tìm thấy bài kiểm tra');
        if ($stage == 1) {
            $result->end_waiting_time = null;
            $result->bonus_time = null;
            if (in_array($result->course, ['N1', 'N2'])) {
                $result->time_start = [$result->time_start[0], 0, now()->timestamp];
                $result->join_time = [$result->join_time[0], 0, now()->timestamp];
            } else {
                $result->time_start = [$result->time_start[0], now()->timestamp, 0];
                $result->join_time = [$result->join_time[0], now()->timestamp, 0];
            }
        }
        if ($stage == 2) {
            $result->end_waiting_time = null;
            $result->bonus_time = null;
            $result->time_start = [$result->time_start[0], $result->time_start[1], now()->timestamp];
            $result->join_time = [$result->join_time[0], $result->join_time[1], now()->timestamp];
        }
        $result->save();
        return $this->statusOK($result->load('user'));
    }

    public function getLessonPercent($id)
    {
        $percent = 0;
        $l = Lesson::with('components')->find($id);
        $progress = LessonProgress::query()->where('user_id', auth()->id())->where('lesson_id', $id)->first();
        if (!$l->components->whereIn('type', [App\Http\Models\LessonToTask::MULTIPLE_CHOICE, App\Http\Models\LessonToTask::TYPE_FILL_IN_THE_BLANK, App\Http\Models\LessonToTask::TYPE_SENTENCE_JUMBLE, App\Http\Models\LessonToTask::TYPE_WORD_PAIR_MATCHING])->isEmpty()) {
            $percent = $progress && $progress->example_progress !== null ? $progress->example_progress : 0;
        } else {
            $percent = $progress && $progress->video_progress !== null ? $progress->video_progress : 0;
        }
        return $percent;
    }

    public function savePassedTime($resultId, $time, $stage)
    {
        $result = App\Http\Models\ExamResult::select('id', 'passed_time')->find($resultId);
        if (is_null($result->passed_time) || !is_array($result->passed_time)) {
            $result->passed_time = [$time,0,0];
        } else {
            $passedTime = $result->passed_time;
            if ($passedTime[$stage - 1] !== -1) {
                $passedTime[$stage - 1] = (int) $time;
            }
            $result->passed_time = $passedTime;
        }
        $result->save();
        return 200;
    }

    public function saveMp3Time($resultId, Request $request)
    {
        $time = $request->get('mp3Time');
        $result = App\Http\Models\ExamResult::select('id', 'passed_time')->find($resultId);
        if ($time*1000 > $result->mp3_time) {
            $result->mp3_time = $time;
        }
        $result->save();
        return 200;
    }
}
