<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Support\Facades\Auth;

class RedirectIfAuth{

    public function handle($request, Closure $next, $guard = null){
      switch ($guard) {
        case 'admin':
          if (Auth::guard($guard)->check()) {
            return redirect('/backend');
          }
          break;
        case 'teacher':
          if (Auth::guard($guard)->check()) {
            return redirect('/teacher/dashboard');
          }
          break;
        case 'vendor':
          if (Auth::guard($guard)->check()) {
            return redirect('/business/dashboard');
          }
          break;
        default:
          if (Auth::guard($guard)->check()) {
              return redirect('/');
          }
          break;
      }

      return $next($request);
    }
}
