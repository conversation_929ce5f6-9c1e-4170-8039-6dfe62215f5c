<?php

namespace App\Http\ModelsFrontend\Community;

use App\Http\ModelsFrontend\Notification;
use Illuminate\Database\Eloquent\Model;

class CommunityGroup extends Model{

  protected $table = 'community_groups';

  public function notifications() {
    return $this->hasMany(Notification::class, 'group_id');
  }

  public function newNotifications() {
    return $this->hasMany(Notification::class, 'group_id')->where('visible', 1);
  }
}
