<?php

namespace App\Http\ModelsFrontend;

use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;
use DB;

class CourseDocument extends Model{

    protected $table = 'course_document';

    protected $appends = ['course_name'];

    public function getCoursenameAttribute() {
        return $this->getCourse['name'];
    }

    public function getCourse() {
    	return $this->hasOne('App\Http\ModelsFrontend\Course', 'id', 'course_id');
    }

}
