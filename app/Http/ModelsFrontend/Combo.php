<?php

namespace App\Http\ModelsFrontend;

use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;
use DB;

class Combo extends Model{

    protected $table = 'combo';

    //lấy ra id tương ứng của khóa học đơn N1, N2...N5
    public function getCourseId(){

    	// //nếu là khóa lẻ lấy ra id
    	// $cs = array('N5', 'N4', 'N3', 'N2', 'N1', 'Kaiwa', 'EJU');
    	// if(in_array($this->name, $cs))
    	// 	return json_decode($this->services)->courses[0];

    	if(sizeof(json_decode($this->services)->courses) == 1){
    		return json_decode($this->services)->courses[0];
    	}

    	return -1;
    }

    //lấy ra seourl của khóa đơn
    public function getSEOurl(){
    	if(sizeof(json_decode($this->services)->courses) == 1){
    		$course = Course::find(json_decode($this->services)->courses[0]);
    		if($course) return $course->SEOurl;
    	}
    	return '#';
    }

    public function invoices()
    {
        return $this->hasMany(Invoice::class, 'product_id');
    }
}
