<?php

namespace App\Http\Models;

use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;
use DB;

class Page extends Model{

    protected $table = 'page';

    //lấy ra tên tác giả
    public function getAuthorName(){

    	return $this->hasOne('App\Http\Models\Admin', 'id', 'admin_id');
    }

    //convert title from vietnamese to vietnamese simple
    public function getFriendlyUrl(){

        $string = new StringConvert();

        //url = url than thien + id
        $url = $string->sanitizeTitle($this->title);
        return $url;
    }

    //lấy ra ngày giờ ở dạng thân thiện
    public function getFriendlyTime(){

    	//2015-12-01 07:43:55
        $thisDateTime = $this->created_at;
        $compareOnDay = substr($this->created_at, 0, 10);

        //nếu là trong ngày
        if(date('Y-m-d') == $compareOnDay){
            return "Hôm nay, lúc ". substr($thisDateTime, 11, 5);

            //nếu không phải trong ngày
        }else {

            $extraYear = substr($this->created_at, 0, 4);
            $thisYear = date("Y");

            //nếu là trong năm
            if ($extraYear == $thisYear) {
                return substr($thisDateTime, 8, 2)." tháng ". substr($thisDateTime, 5, 2).", lúc ". substr($thisDateTime, 11, 5);
            }else{
                return substr($thisDateTime, 8, 2)." tháng ". substr($thisDateTime, 5, 2).", ".$extraYear;
            }
        }


    }

}
