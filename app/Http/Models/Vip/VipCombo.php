<?php

namespace App\Http\Models\Vip;

use Illuminate\Database\Eloquent\Model;

class VipCombo extends Model
{
    protected $table = 'vip_combo';

    protected $fillable = [
        'name', 'seo_url', 'price', 'jp_price', 'type', 'image_name', 'status', 'services', 'short_desc', 'desc', 'vip_level'
    ];

    public function courses()
    {
        return $this->belongsToMany(VipCourse::class, 'vip_combo_course',
            'combo_id', 'course_id');
    }
}
