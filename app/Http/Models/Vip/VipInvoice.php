<?php

namespace App\Http\Models\Vip;

use App\Http\Models\Admin;
use App\Http\Models\Users;
use App\Http\Models\Voucher;
use App\QueryFilter;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use DateTimeInterface;

class VipInvoice extends Model
{
    use SoftDeletes;
    protected $table = 'vip_invoice';
    protected $casts = [
      'info_contact' => 'array',
    ];
    /**
     * Prepare a date for array / JSON serialization.
     *
     * @param  \DateTimeInterface  $date
     * @return string
     */
    protected function serializeDate(DateTimeInterface $date)
    {
      return $date->format('Y-m-d H:i:s');
    }
    //quan hệ bản product (1-1)
    public function combo() {
        return $this->hasOne(VipCombo::class, 'id', 'product_id');
    }

    //quan hệ bảng user (1-1)
    public function user() {
        return $this->hasOne(Users::class, 'id', 'user_id');
    }

  /**
   * @return \Illuminate\Database\Eloquent\Relations\HasOne
   */
    public function sale(): \Illuminate\Database\Eloquent\Relations\HasOne
    {
        return $this->hasOne(Admin::class, 'id', 'consulted_by');
    }

  /**
   * @param $query
   * @param QueryFilter $filters
   * @return \Illuminate\Database\Eloquent\Builder
   */
    public function scopeFilter($query, QueryFilter $filters): \Illuminate\Database\Eloquent\Builder
    {
      return $filters->apply($query);
    }
}
