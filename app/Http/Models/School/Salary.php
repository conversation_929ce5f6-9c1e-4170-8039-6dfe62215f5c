<?php

namespace App\Http\Models\School;

use App\Base\Model\BaseModel;
use App\Http\Models\Community\CommunityGroup;
use Illuminate\Database\Eloquent\SoftDeletes;

class Salary extends BaseModel
{
  use SoftDeletes;
  protected $connection = 'mysql3';
  protected $table = 'user_salary_tbl';
  protected $guarded = [];

  public function group() {
    return $this->belongsTo(CommunityGroup::class, 'group_id');
  }

  public function course()
  {
      return $this->belongsTo(Course::class, 'course_id');
  }
}
