<?php

namespace App\Http\Models;

use Illuminate\Database\Eloquent\Model;
use DB;

class LearningPeriod extends Model {
    protected $table = 'learning_period';
    protected $fillable = ['name', 'description', 'duration', 'course_id', 'status', 'is_vip'];

    public function paths()
    {
      return $this->hasMany(LearningPath::class, 'period_id')->orderBy('sort_no')->where('status', 1);
    }
}
