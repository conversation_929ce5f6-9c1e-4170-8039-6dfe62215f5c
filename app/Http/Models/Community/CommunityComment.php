<?php

namespace App\Http\Models\Community;

use App\Http\Models\Community\CommunityReport;

use App\Http\Traits\Filterable;
use App\Base\Model\BaseModel;
use Illuminate\Database\Eloquent\SoftDeletes;

class CommunityComment extends BaseModel
{
    use Filterable;
    use SoftDeletes;

    protected $table = 'community_comments';

    protected $fillable = [
        'name',
        'slug',
        'created_by',
        'is_public',
        'is_newfeed',
        'expired_at',
    ];


    public function reports() {
        return $this->hasMany(CommunityReport::class, 'comment_id');
    }
}
