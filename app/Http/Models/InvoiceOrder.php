<?php

namespace App\Http\Models;

use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;
use DB;

class InvoiceOrder extends Model{

    protected $table = 'invoice_order';

    protected $appends = ['uuid', 'friendly_time', 'order_type'];
    public function getUuidAttribute(){
        return $this->getUuid['uuid'];
    }
    public function getFriendlyTimeAttribute(){
        return $this->getFriendlyTime();
    }
    public function getOrderTypeAttribute(){
        return $this->getTypeOrder();
    }

    //lấy ra tên của dịch vụ tiếng việt
	public function getOrderName(){

		switch ($this->service_key) {

			case 'CourseOrder': return "Mua khóa học"; break;
			case 'ComboOrder': return "Mua combo"; break;
			case 'Commisson': return "Ủy quyền"; break;
			case 'DepositPayment': return "Thanh toán"; break;
			default: return "Mua khóa học"; break;
		}
	}

	//lấy ra uuid của invoice order theo bảng invoice
	public function getUuid(){

		return $this->hasOne('App\Http\Models\Invoice', 'id', 'invoice_id');
	}

	//lấy ra tên của thanh toán tiếng việt
	public function getPaymentStatus(){

		switch ($this->invoice_status) {

			case 'paid': return "Đã thanh toán"; break;
			default: return "Chưa thanh toán"; break;
		}
	}

	//lấy ra giá yên nhật của đơn hàng
	public function getJpyPrice(){

		if($this->service_key == 'ComboOrder'){

			return Combo::find($this->product_id)->jpy_price;
		}else{

			$name  = Course::find($this->product_id)->name;
		    $combo = Combo::where('name', '=', $name)->first();
			return $combo->jpy_price;
		}
	}

	//lấy ra tên của trạng thái đơn hàng tiếng việt
	public function getOrderStatus(){

		switch ($this->order_status) {

			case 'completed': return "<span class='label label-success'>Hoàn thành</span>"; break;
			case 'pending': return "<span class='label label-warning'>Chờ xử lý</span>"; break;
			default: return "<span class='label label-danger'>Đã hủy</span>"; break;
		}
	}

	//lấy ra tên kiểu đơn hàng
	public function getTypeOrder(){
		if($this->service_key == 'ComboOrder') return "Gói combo";
		return "Khóa học";
	}

	//lấy ra ngày giờ ở dạng thân thiện
    public function getFriendlyTime(){

    	return date("d/m/Y H:i", $this->created);

    }

    //lấy ra ngày giờ ở dạng thân thiện cấp 2
    public function getTimeDetail(){

    	return date("H:i", $this->created). " ngày " .date("d/m/Y", $this->created);

    }

}
