<?php

namespace App\Providers\SocialiteApple;

use Illuminate\Support\ServiceProvider;
use <PERSON><PERSON>\Socialite\Two\AbstractProvider;
use Illuminate\Support\Arr;
use <PERSON><PERSON>\Socialite\Two\User;

class AbstractCustomProvider extends AbstractProvider
{
    protected function getAuthUrl($state)
    {
        return $this->buildAuthUrlFromBase('https://appleid.apple.com/auth/authorize', $state).'&response_mode=form_post';
    }

    /**
     * {@inheritdoc}
     */
    protected function getTokenUrl()
    {
        return 'https://appleid.apple.com/auth/token';
    }

    /**
     * {@inheritdoc}
     */
    protected function getUserByToken($token)
    {
        // Refresh_token
        // User info already in the token response, you only get the email the first time
        // No user meta data in the grant_type refresh_token, https://developer.apple.com/documentation/signinwithapplerestapi/generate_and_validate_tokens
        return [];
    }

    public function user()
    {
        if ($this->hasInvalidState()) {
            throw new InvalidStateException;
        }

        $response = $this->getAccessTokenResponse($this->getCode());

        $token = Arr::get($response, 'access_token');
        $user = $this->mapUserToObject($this->getUserByAccessTokenResponse($response));

        return $user->setToken($token)
            ->setRefreshToken(Arr::get($response, 'refresh_token'))
            ->setExpiresIn(Arr::get($response, 'expires_in'));
    }

    /**
     * {@inheritdoc}
     */
    protected function mapUserToObject(array $user)
    {
        return (new User)->setRaw($user)->map([
            'id' => Arr::get($user, 'sub'),
            'nickname' => null,
            'name' => null,
            'email' => Arr::get($user, 'email'),
            'avatar' => null,
        ]);
    }
}
