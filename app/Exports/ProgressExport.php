<?php

namespace App\Exports;

use Illuminate\Contracts\View\View;
use Maatwebsite\Excel\Concerns\FromView;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;

class ProgressExport implements FromView, ShouldAutoSize
{
    protected $exportAll;
    protected $users;
    protected $lessonCount;
    protected $weeks;

    public function __construct(array $users, int $lessonCount, array $weeks, int $exportAll, int $courseId, array $days, string $type)
    {
      $this->exportAll = $exportAll;
      $this->users = $users;
      $this->lessonCount = $lessonCount;
      $this->weeks = $weeks;
      $this->days = $days;
      $this->courseId = $courseId;
      $this->type = $type;
    }

    public function view(): View
    {
      return view('backend.mkt.export', [
        'exportAll' => $this->exportAll,
        'users' => $this->users,
        'lessonCount' => $this->lessonCount,
        'weeks' => $this->weeks,
        'days' => $this->days,
        'courseId' => $this->courseId,
        'type' => $this->type,
      ]);
    }
}
