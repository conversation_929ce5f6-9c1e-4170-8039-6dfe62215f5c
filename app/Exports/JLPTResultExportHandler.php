<?php
namespace App\Exports;

use Maatwebsite\Excel\Files\ExportHandler;
use Illuminate\Database\Eloquent\Collection;

class JLPTResultExportHandler implements ExportHandler {

    protected $results;

    public function __construct(
        Collection $results
    )
    {
        $this->results = $results;
    }

    /**
     * export by template, passed invoices to blade
     * @param $export
     * @return mixed
     */
    public function handle($export)
    {
        $this->results = $export->results;
        // work on the export
        return $export->sheet('sheetName', function($sheet)
        {
            $sheet->loadView('backend.thi_thu.exam.template')->with('results', $this->results);
        })->store('xlsx', public_path(). '/upload/excel/')->download('xlsx');
    }
}