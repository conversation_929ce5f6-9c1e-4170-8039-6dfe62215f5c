<?php
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('lesson_exam_parts', function (Blueprint $table) {
            $table->id(); // Auto-increment primary key
            $table->integer('lesson_id')->unsigned()->nullable(false);
            $table->integer('part_id')->unsigned()->nullable(false);
            $table->foreign('lesson_id')->references('id')->on('lesson')->onDelete('cascade');
            $table->foreign('part_id')->references('id')->on('exam_parts')->onDelete('cascade');
            $table->timestamps(); // Created_at and updated_at columns

            // Add unique constraint to prevent duplicate entries
            $table->unique(['lesson_id', 'part_id']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('lesson_exam_parts');
    }
};
