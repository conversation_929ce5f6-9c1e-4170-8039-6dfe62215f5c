import _ from "lodash";

class StackedCards {
    constructor(options = {}) {
        this.options = {
            stackedView: "Top",
            rotate: true,
            visibleItems: 3,
            margin: 10,
            useOverlays: true,
            ...options
        };

        this.currentPosition = 0;
        this.isFirstTime = true;
        this.elTrans = 0;

        this.container = document.getElementById("stacked-cards-block");
        console.log(`this.container: `, this.container);
        this.cardsContainer = this.container.querySelector(".stackedcards-container");
        this.cards = Array.from(this.cardsContainer.children);
        this.rightOverlay = this.container.querySelector(".stackedcards-overlay.right");
        this.leftOverlay = this.container.querySelector(".stackedcards-overlay.left");

        console.log(`this.rightOverlay: `, this.rightOverlay);
        console.log(`this.leftOverlay: `, this.leftOverlay);

        this.maxElements = this.cards.length;
        this.options.visibleItems = Math.min(this.options.visibleItems, this.maxElements);
        this.init();
    }

    init() {
        this.setupCards();
        this.addEventListeners();
        this.updateUI();
        setTimeout(() => this.container.classList.remove("init"), 150);
    }

    setupCards() {
        const {stackedView, visibleItems, margin} = this.options;
        const marginTotal = margin * (visibleItems - 1);

        this.cardsContainer.style.marginBottom = `${marginTotal}px`;
        this.elTrans = stackedView === "Top" ? marginTotal : 0;

        this.cards.slice(visibleItems).forEach(card => {
            card.classList.add(`stackedcards-${stackedView.toLowerCase()}`, "stackedcards--animatable", `stackedcards-origin-${stackedView.toLowerCase()}`);
            card.style.cssText = `
        z-index: 0;
        opacity: 0;
        transform: scale(${1 - visibleItems * 0.04}) translateY(${this.elTrans}px);
      `;
        });

        this.updateActiveCard();
        this.setupOverlays();
    }

    setupOverlays() {
        if (!this.options.useOverlays) {
            this.leftOverlay.classList.add("stackedcards-overlay-hidden");
            this.rightOverlay.classList.add("stackedcards-overlay-hidden");
            return;
        }
        [this.leftOverlay, this.rightOverlay].forEach(overlay => {
            overlay.style.transform = `translateY(${this.elTrans}px)`;
        });
    }

    updateActiveCard() {
        if (this.cards[this.currentPosition]) {
            this.cards[this.currentPosition].classList.add("stackedcards-active");
        }
    }

    addEventListeners() {
        // First remove any existing click handlers
        this.cards.forEach(card => {
            card.removeEventListener('click', this.handleCardClick);
        });

        // Store reference to click handler for proper removal
        this.handleCardClick = (e) => {
            console.log(`e: `, e);
            let targetElement = e.target;
            let shouldFlip = true;

            while (targetElement && targetElement !== e.currentTarget) {
                if (
                    targetElement.classList.contains("noFlip") ||
                    targetElement.classList.contains("card_audio") ||
                    targetElement.classList.contains("underline") ||
                    targetElement.tagName === "BUTTON" ||
                    targetElement.tagName === "A" ||
                    targetElement.tagName === "SVG" ||
                    targetElement.tagName === "path" ||
                    targetElement.closest('.card-footer') !== null ||
                    targetElement.closest('[class*="cursor-pointer"]') !== null
                ) {
                    shouldFlip = false;
                    break;
                }
                targetElement = targetElement.parentElement;
            }

            if (shouldFlip) {
                let version = process.env.NODE_ENV;
                if (typeof course_id !== "undefined" && !e.currentTarget.querySelector(".card-inner").classList.contains("flip") && [39, 40].includes(course_id) && parseInt(authUser.isTester) === 0 && ["production", "prod"].includes(version)) {
                    if (typeof ga !== 'undefined') {
                        let event = parseInt(course_id) === 39 ? "n5_flc_behind" : "n4_flc_behind";
                        ga("send", "event", "nx_flc_behind", event, event);
                    }
                }
                e.currentTarget.querySelector(".card-inner").classList.toggle("flip");
            }
        };

        // Add new click handlers
        this.cards.forEach(card => {
            card.addEventListener("click", this.handleCardClick);
        });
    }

    swipeLeft() {
        if (this.currentPosition >= this.maxElements) {
            return;
        }
        this.transformCard(-1000, 0, 0);
        if (this.options.useOverlays) {
            this.transformOverlay(this.leftOverlay, -1000, 0, 1, () => this.resetOverlay(this.leftOverlay));
        }
        this.nextCard();
    }

    swipeRight() {
        if (this.currentPosition >= this.maxElements) {
            return;
        }
        this.transformCard(1000, 0, 0);
        if (this.options.useOverlays) {
            this.transformOverlay(this.rightOverlay, 1000, 0, 1, () => this.resetOverlay(this.rightOverlay));
        }
        this.nextCard();
    }

    undo() {
        if (this.currentPosition <= 0) {
            return;
        }
        this.currentPosition--;
        this.updateUI();
        this.updateActiveCard();
    }

    nextCard() {
        this.currentPosition++;
        this.updateUI();
        this.updateActiveCard();
    }

    transformCard(x, y, opacity) {
        const card = this.cards[this.currentPosition];
        if (!card) {
            return;
        }
        card.classList.remove("no-transition");
        card.style.zIndex = 6;
        this.transformElement(card, x, y, opacity);
    }

    transformOverlay(overlay, x, y, opacity, callback) {
        overlay.classList.remove("no-transition");
        overlay.style.zIndex = 8;
        this.transformElement(overlay, x, y, opacity);
        setTimeout(callback, 300);
    }

    resetOverlay(overlay) {
        overlay.classList.add("no-transition");
        requestAnimationFrame(() => {
            overlay.style.transform = `translateY(${this.elTrans}px)`;
            overlay.style.opacity = 0;
        });
        this.isFirstTime = false;
    }

    transformElement(element, x, y, opacity) {
        const rotate = this.options.rotate ? Math.min(Math.max(x / 10, -15), 15) : 0;
        requestAnimationFrame(() => {
            element.style.transform = `translateX(${x}px) translateY(${y + this.elTrans}px) rotate(${rotate}deg)`;
            element.style.opacity = opacity;
        });
    }

    updateUI() {
        requestAnimationFrame(() => {
            const {visibleItems, margin} = this.options;
            let zIndex = 5;
            let scale;

            this.cards.slice(this.currentPosition, this.currentPosition + visibleItems).forEach((card, i) => {
                scale = zIndex === 4 ? `scale(0.97)` : (zIndex === 3 ? "scale(0.95)" : "");
                card.style.cssText = `
                  transform: ${scale} translateY(${zIndex === 4 ? 23 : (zIndex === 3 ? 42 : (zIndex === 5 ? 0 : 44))}px);
                  z-index: ${zIndex--};
                  opacity: 1;
                `;
                card.classList.add("stackedcards--animatable");
            });
        });
    }

    destroy() {
        // Remove event listeners to prevent memory leaks
        this.cards.forEach(card => {
            const cardInner = card.querySelector(".card-inner");
            if (cardInner) {
                const newCard = card.cloneNode(true);
                card.parentNode.replaceChild(newCard, card);
            }
        });
    }

    swipeLeftNew() {
        let card = this.cards[this.currentPosition];
        //add attribute data-swiped-left="true" to card
        card.setAttribute("data-swiped-left", "true");

        if (this.currentPosition >= this.maxElements) {
            return;
        }
        this.transformCard(-1000, 0, 0);
        if (this.options.useOverlays) {
            this.transformOverlay(this.leftOverlay, -1000, 0, 1, () => this.resetOverlay(this.leftOverlay));
        }
        this.nextCard();
    }

    // xóa event listeners của cards
    destroyEventListenersCards() {
        console.log(`destroyEventListenersCards`);
        this.cards.forEach(card => {
            card.removeEventListener("click", this.handleCardClick);
        });
    }
}

function formatTime(timestamp) {
    // chuyển sang định dạng 2025-05-01 20:10:10
    return new Date(timestamp * 1000).toISOString().slice(0, 19).replace("T", " ");
}

function sortFlcVocabulary(history_learn_flc) {
    const conditions = {
        0: {ti: 0, li: 0}, // Đã học -> Tạm nhớ: 0 phút
        1: {ti: 2 * 60, li: 0}, // Đã học -> Tạm nhớ: 2 phút
        2: {ti: 5 * 60, li: 1}, // Tạm nhớ -> Ghi nhớ: 30 phút
        3: {ti: 10 * 60, li: 3}, // Ghi nhớ -> Thuộc lòng: 1 tiếng
        4: {ti: 15 * 60, li: 5}, // Thuộc lòng -> Siêu nhớ: 2 tiếng
        5: {ti: 20 * 60, li: 7} // Siêu nhớ -> Không hiển thị: 3 tiếng
    };
    history_learn_flc = _.sortBy(history_learn_flc, "t");
    console.log(`history_learn_flc: `, history_learn_flc);

    let t = [];
    history_learn_flc.forEach(item => {
        t.push({
            id: item.id,
            t: item.t,
            box: item.box,
            p: item.p,
        });
    });
    console.log(`t: `, t);

    let currentTime = Math.floor(Date.now() / 1000); // Timestamp hiện tại (giây)
    console.log(`currentTime: `, currentTime, formatTime(currentTime));

    // Danh sách thẻ đủ điều kiện hiển thị
    let eligibleCards = [];

    // danh sách card ko ưu tiên
    let notPriorityCards = [];

    // danh sách thẻ mới
    let newCards = [];

    // danh sách thẻ đã hoàn thành
    let completedCards = [];

    history_learn_flc.forEach((item, index) => {
        let box = item.p;
        item.take_break = 0;

        item = {...item, is_test: false};
        item.box = box;


        if (item.i && item.i !== "" && item.i !== null && item.i !== undefined && (item.id === null || item.id === undefined)) {
            item.id = item.i;
        }

        if (!item.t || box === 0) {
            newCards.push(item);
            return;
        }

        if (box >= 1 && box <= 4) {
            console.log(item);
            console.log(`conditions[box].ti >= currentTime - item.t: `, conditions[box].ti, currentTime, item.t, currentTime - item.t);
            console.log(`conditions[box].li <= index: `, conditions[box].li, index);
            if (conditions[box].ti <= currentTime - item.t && conditions[box].li >= index) {
                item.is_test = true;
                eligibleCards.push(item);
            } else {
                notPriorityCards.push(item);
            }
        } else { // box > 4 thì sẽ không học lại nữa và xóa khỏi các mảng
            eligibleCards = eligibleCards.filter(card => card.id !== item.id);
            notPriorityCards = notPriorityCards.filter(card => card.id !== item.id);
            newCards = newCards.filter(card => card.id !== item.id);
            completedCards.push(item);
        }
    });

    eligibleCards = eligibleCards.sort((a, b) => {
        if (a.is_test === b.is_test) {
            return b.box - a.box;
        } // Sắp xếp theo box nếu isTest giống nhau
        return a.is_test ? -1 : 1;
    });
    notPriorityCards = notPriorityCards.sort((a, b) => {
        return b.box - a.box;
    });
    newCards = _.sortBy(newCards, item => item.t === null ? -Infinity : item.t);

    let eligibleCardsTemp = [];
    eligibleCards.forEach(item => {
        eligibleCardsTemp.push({
            id: item.id,
            box: item.box,
            t: item.t,
            p: item.p,
        });
    });
    let newCardsTemp = [];
    newCards.forEach(item => {
        newCardsTemp.push({
            id: item.id,
            box: item.box,
            t: item.t,
            p: item.p,
        });
    });
    let notPriorityCardsTemp = [];
    notPriorityCards.forEach(item => {
        notPriorityCardsTemp.push({
            id: item.id,
            box: item.box,
            t: item.t,
            p: item.p,
        });
    });
    console.log(`eligibleCardsTemp: `, eligibleCardsTemp);
    console.log(`newCardsTemp: `, newCardsTemp);
    console.log(`notPriorityCardsTemp: `, notPriorityCardsTemp);

    console.log(`completedCards: `, completedCards);

    return {
        sortCards: [...eligibleCards, ...newCards, ...notPriorityCards],
        completedCards,
    };
}

function removeFontSizeStyle(htmlString) {
    // Thay thế font-size trong thuộc tính style
    return htmlString.replace(/style="([^"]*?)font-size\s*:\s*[^;]+;?\s*([^"]*?)"/g, (match, before, after) => {
        const newStyle = (before + after).trim();
        return newStyle ? `style="${newStyle}"` : "";
    });
}

export default StackedCards;
export {sortFlcVocabulary, removeFontSizeStyle};