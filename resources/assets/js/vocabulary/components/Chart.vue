<template>
  <div class="chart-container">
    <div class="chart-html">
      <!-- line max count learn word -->

      <!-- text when max count learn word -->
      <div v-if="options.showTextMaxLearnWord" class="chart-line-max-text text-center">
        <div class="text-center font-beanbag-medium text-[15px] text-[##073A3B] px-2 py-1 bg-[#CCF8D1] rounded-full">
          Rừng ngôn từ
        </div>
        <div class="text-center text-[14px] text-[#0C403F]">言葉の森</div>
      </div>

      <div class="chart-columns relative">
        <div class="chart-line-max flex items-center w-full justify-stretch pr-[30px] absolute left-[-11px]"
             :style="options.showTextMaxLearnWord ? 'top: 12px;' : 'top: 15px;'"
        >
          <div
              class="chart-line-max-value font-beanbag-medium text-[20px] text-white bg-[#176867] p-1 rounded-full w-[77px] text-center flex items-center justify-center">
            <span class="break-normal" style="font-size: clamp(12px, calc(5cqi - 2px), 17px)">{{ chartData.totalWords }} từ</span>
          </div>

          <!-- line đường kẻ nằm ngang style bằng tailwind -->
          <div class="chart-line-max-bar border-dashed border-[1px] border-[#176867] w-full"></div>
        </div>


        <div class="chart-column" :style="{ height: getBarHeight(chartData.learned + chartData.super_mastered) }">
          <div v-if="chartData.learned + chartData.super_mastered < chartData.totalWords"
               class="column-value font-beanbag-medium">
            {{ chartData.learned + chartData.super_mastered }} từ
          </div>
          <div class="column-bar learned-bar">
            <div class="column-bar-inner"></div>
          </div>
          <div class="column-label">Đã học</div>
        </div>
        <div class="chart-column" :style="{ height: getBarHeight(chartData.temporary + chartData.super_mastered) }">
          <div v-if="chartData.temporary + chartData.super_mastered < chartData.totalWords"
               class="column-value font-beanbag-medium">
            {{ chartData.temporary + chartData.super_mastered }} từ
          </div>
          <div class="column-bar temporary-bar">
            <div class="column-bar-inner"></div>
          </div>
          <div class="column-label">Tạm nhớ</div>
        </div>
        <div class="chart-column" :style="{ height: getBarHeight(chartData.memorized + chartData.super_mastered) }">
          <div v-if="chartData.memorized + chartData.super_mastered < chartData.totalWords"
               class="column-value font-beanbag-medium">
            {{ chartData.memorized + chartData.super_mastered }} từ
          </div>
          <div class="column-bar memorized-bar">
            <div class="column-bar-inner"></div>
          </div>
          <div class="column-label">Ghi nhớ</div>
        </div>
        <div class="chart-column" :style="{ height: getBarHeight(chartData.mastered + chartData.super_mastered) }">
          <div v-if="chartData.mastered + chartData.super_mastered < chartData.totalWords"
               class="column-value font-beanbag-medium">
            {{ chartData.mastered + chartData.super_mastered }} từ
          </div>
          <div class="column-bar mastered-bar">
            <div class="column-bar-inner"></div>
          </div>
          <div class="column-label">Thuộc lòng</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "ChartVocabulary",
  props: {
    chartData: {
      type: Object,
      required: true,
    },
    options: {
      type: Object,
      default: () => {
        return {
          showTextMaxLearnWord: false,
        }
      },
      required: false,
    }
  },
  watch: {
    chartData: {
      handler(newVal) {
        console.log('chartData changed:', newVal);
      },
      deep: true,
    },
  },
  methods: {
    getBarHeight(value) {
      if (!value) return '0%';

      // Tìm giá trị lớn nhất trong dữ liệu
      const maxValue = this.chartData.totalWords || 0;

      // Nếu không có giá trị nào lớn hơn 0, trả về chiều cao tối thiểu
      if (maxValue <= 0) return '1%';

      // Tính phần trăm so với giá trị lớn nhất (tối đa 90% chiều cao container)
      const percentage = (value / maxValue) * 90;

      // Đảm bảo các cột có giá trị nhỏ vẫn có chiều cao tối thiểu
      return `${Math.max(percentage, 1)}%`;
    },
  },
  created() {
    console.log(`chartData: `, this.chartData);
  }
};
</script>

<style scoped>
.chart-container {
  height: 100%;
  position: relative;
  background-color: #fff;
  border-radius: 32px;
  -webkit-box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  padding: 30px;
}

.chart-html {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  height: 100%;
  position: relative;
  background-repeat: no-repeat;
  background-position: top center;
  background-size: auto 150px;
  background-color: transparent;
  justify-content: center;
  padding-bottom: 50px;
}

.chart-columns {
  padding-left: 45px;
  padding-right: 45px;
  display: flex;
  justify-content: space-around;
  align-items: flex-end;
  width: 100%;
  height: 100%;
  border-bottom: 2px solid #176867;
  background-image: url('/images/vocabulary/bg-chart-overview.svg');
  background-repeat: no-repeat;
  background-position: bottom;
  background-size: contain;
}

.chart-column {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 20%;
  max-width: 75px;
  position: relative;
  transition: all 0.5s cubic-bezier(0.34, 1.56, 0.64, 1);
}

.column-value {
  font-size: 18px;
  font-weight: bold;
  color: rgba(31, 114, 116, 1);
  margin-bottom: 5px;
  position: absolute;
  top: -40px;
  width: 100%;
  text-align: center;
  fill: rgba(255, 255, 255, 0.60);
  filter: drop-shadow(0px 5.518px 5.518px rgba(97, 124, 154, 0.15));
  backdrop-filter: blur(3.6785595417022705px);
  background: white;
  border-radius: 8px;
}

.column-value::after {
  content: "";
  position: absolute;
  top: 100%;
  left: 50%;
  margin-left: -5px;
  border-width: 5px;
  border-style: solid;
  border-color: white transparent transparent transparent;
}

.column-bar {
  height: 100%;
  width: 100%;
  border-radius: 8px 8px 0 0;
  position: relative;
  overflow: hidden;
  transition: all 0.6s cubic-bezier(0.34, 1.56, 0.64, 1);
  transform-origin: bottom center;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}

.learned-bar {
  background-color: rgba(255, 106, 0, 0.31);
}

.temporary-bar {
  background-color: rgba(255, 217, 0, 0.42);
}

.memorized-bar {
  background-color: rgba(128, 255, 0, 0.37);
}

.mastered-bar {
  background: url("/images/vocabulary/bg-mastered-bar-column-chart-overview.svg"), linear-gradient(32deg, rgba(0, 255, 115, 0.50) 32.16%, rgba(0, 255, 84, 0.50) 50.05%, rgba(0, 255, 22, 0.50) 71.77%, rgba(255, 255, 0, 0.50) 96.05%);
  background-position: top center;
  background-repeat: no-repeat;
}


.column-label {
  font-size: 22px;
  color: rgba(7, 64, 63, 1);
  text-align: center;
  position: absolute;
  bottom: -35px;
  width: max-content;
  font-family: "Beanbag_Dungmori_Rounded_Medium";
}
</style>
