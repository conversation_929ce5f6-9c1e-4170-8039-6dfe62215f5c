<template>
  <div>
    <div class="flashcard-wrap">
      <!--    overview-->
      <div v-if="currentView === TYPES_VIEW.OVERVIEW"
           class="flex flex-col justify-center items-center w-[90%] max-w-[1000px]">
        <div style="background: linear-gradient(to bottom, #FFFFFF, #F4F5FA);"
             class="w-full text-center p-4 rounded-3xl mb-6">
          <div class="img mb-7">
            <img src="/images/vocabulary/img-over-view-course.svg" alt=""/>
          </div>
          <div class="title text-center text-[#07403F] ">
            <div class="font-beanbag-medium text-[20px] uppercase">Bộ từ vựng</div>
            <div class="font-zuume-semibold text-[48px] text-bold uppercase">jlpt {{ categoryData.level }}</div>
            <div class="font-averta-regular text-[20px]">Họ<PERSON> và ghi nhớ {{ categoryData.data.word_count }} từ vựng thuộc
              cấp độ {{ categoryData.level }}
            </div>
          </div>
        </div>

        <div class="w-full">
          <!-- progress -->
          <!-- thay đổi thành style kiểu grid để thanh progress ở các phần đã học, tạm nhớ, ghi nhớ, thuộc lòng đều nhau -->

          <div class="max-w-[420px] mx-auto progress-item grid grid-cols-3 gap-4 items-center ">
            <div class="font-averta-regular text-[20px] text-[#1E1E1E]">Đã học</div>
            <progress-bar
                :percent="((categoryData.data.learned + categoryData.data.temporary + categoryData.data.memorized + categoryData.data.mastered) / categoryData.data.word_count) * 100"
                :color="((categoryData.data.learned + categoryData.data.temporary + categoryData.data.memorized + categoryData.data.mastered) / categoryData.data.word_count) * 100 < 85 ? '#E8B931' : '#57D061'"/>
            <div class="font-averta-regular text-[20px] text-[#757575] text-end">
              {{
                Math.round(((categoryData.data.learned + categoryData.data.temporary + categoryData.data.memorized + categoryData.data.mastered) / categoryData.data.word_count) * 100)
              }}%・{{
                categoryData.data.learned + categoryData.data.temporary + categoryData.data.memorized + categoryData.data.mastered
              }}
              từ
            </div>
            <div class="font-averta-regular text-[20px] text-[#1E1E1E]">Tạm nhớ</div>
            <progress-bar
                :percent="((categoryData.data.temporary + categoryData.data.memorized + categoryData.data.mastered) / categoryData.data.word_count) * 100"
                :color="((categoryData.data.temporary + categoryData.data.memorized + categoryData.data.mastered) / categoryData.data.word_count) * 100 < 85 ? '#E8B931' : '#57D061'"/>
            <div class="font-averta-regular text-[20px] text-[#757575] text-end">
              {{
                Math.round(((categoryData.data.temporary + categoryData.data.memorized + categoryData.data.mastered) / categoryData.data.word_count) * 100)
              }}%・{{
                categoryData.data.temporary + categoryData.data.memorized + categoryData.data.mastered
              }}
              từ
            </div>
            <div class="font-averta-regular text-[20px] text-[#1E1E1E]"> Ghi nhớ</div>
            <progress-bar
                :percent="((categoryData.data.memorized + categoryData.data.mastered) / categoryData.data.word_count) * 100"
                :color="((categoryData.data.memorized + categoryData.data.mastered) / categoryData.data.word_count) * 100 < 85 ? '#E8B931' : '#57D061'"
            />
            <div class="font-averta-regular text-[20px] text-[#757575] text-end">
              {{
                Math.round(((categoryData.data.memorized + categoryData.data.mastered) / categoryData.data.word_count) * 100)
              }}%・{{
                categoryData.data.memorized + categoryData.data.mastered
              }}
              từ
            </div>
            <div class="font-averta-regular text-[20px] text-[#1E1E1E]">Thuộc lòng</div>
            <progress-bar
                :percent="((categoryData.data.mastered) / categoryData.data.word_count) * 100"
                :color="((categoryData.data.mastered) / categoryData.data.word_count) * 100 < 85 ? '#E8B931' : '#57D061'"
            />
            <div class="font-averta-regular text-[20px] text-[#757575] text-end">
              {{
                Math.round(((categoryData.data.mastered) / categoryData.data.word_count) * 100)
              }}%・{{ categoryData.data.mastered }}
              từ
            </div>
          </div>
        </div>

        <div class="w-[340px]">
          <div
              class="w-full text-xl text-[#07403F] font-beanbag rounded-full p-3 mt-10 flex justify-between items-center border-[1px] border-[#07403F] cursor-pointer"
              @click="currentView = TYPES_VIEW.SEARCH">
            <div class="text-[#07403F] font-averta-regular text-xl">
              Học bộ từ
            </div>
            <div>
              <svg width="8" height="8" viewBox="0 0 8 8" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M0.5 4H7.5M7.5 4L4 0.5M7.5 4L4 7.5" stroke="#07403F" stroke-linecap="round"
                      stroke-linejoin="round"/>
              </svg>
            </div>
          </div>
          <div v-if="!categoryData.hasCourseOwner"
               class="text-center font-averta-regular text-xl italic mt-3 text-[#EF6D13]">
            *Học thử 300 từ đầu tiên trong bộ từ vựng JLPT N5
          </div>
        </div>

        <button
            class="bg-[#57D061] text-xl text-[#07403F] font-beanbag rounded-full py-3 uppercase shadow-lg shadow-[#4C5D7052] mt-12 w-[340px] cursor-pointer"
            @click="startStudy()"
        >
          Bắt đầu
        </button>
      </div>

      <div v-if="currentView === TYPES_VIEW.SEARCH">
        <div class="flex justify-between items-center mb-10">
          <div class="flex items-center">
            <div class="vocabulary-search-btn-back h-[65px] w-[65px]" @click="currentView = TYPES_VIEW.OVERVIEW">
              <i class="fas fa-arrow-left"></i>
            </div>
            <div class="vocabulary-section-favorite-title">
              <div class="flex font-beanbag-medium text-[24px] text-[#07403F] items-center">
                {{ categoryData.name }}
                <i class="fas fa-heart text-[#FF7C79] ml-2"></i>
              </div>
              <div class="font-beanbag-regular text-[20px] text-[#07403F]">{{ categoryData.data.word_count }} từ</div>
            </div>
          </div>
          <div
              class="vocabulary-favorite-search-bar flex items-center rounded-full bg-white shadow-md h-[65px] px-[20px] transition-all duration-300 flex-1 min-w-0 max-w-[400px]">
            <div class="search-icon">
              <i class="fas fa-search"></i>
            </div>
            <input
                type="text"
                v-model="searchQuery"
                placeholder="Nhập từ vựng muốn tìm kiếm"
                class="search-input leading-[65px]"
            />
          </div>
        </div>

        <div class="search-result list-favorite" v-if="searchResults.length > 0">
          <div class="search-result-items">
            <div class="search-result-item cursor-pointer relative" v-for="(item, index) in searchResults" :key="index">
              <div class="search-result-item-content border-b border-[#D0D3DA] border-bottom-[1px] pt-3 pb-3">
                <div class="search-result-item-title font-gen-jyuu-gothic-regular text-black text-[20px]">
                  <div v-html="item.value.word"></div>
                </div>
                <div class="search-result-item-description font-gen-jyuu-gothic-regular text-[18px] text-[#757575]">
                  <div v-html="item.value.meaning"></div>
                </div>
                <div
                    class="search-result-item-specialized w-fit font-averta-regular text-[16px] text-[#FFFFFF] bg-[#B3B3B3] rounded-full px-3 py-1">
                  {{ item.course_name }}
                </div>
              </div>
              <div v-if="!idsFree.includes(item.id) && !categoryData.hasCourseOwner"
                   class="absolute h-full top-0 left-0 w-full flex items-center"
                   style="background: linear-gradient(to right, rgba(234, 246, 235, 1), rgba(255, 255, 255, 0));">
                <svg class="m-[20px]" width="24" height="24" viewBox="0 0 24 24" fill="none"
                     xmlns="http://www.w3.org/2000/svg">
                  <path
                      d="M12 1.25C15.0376 1.25 17.5 3.71243 17.5 6.75V8.52734C19.0676 9.0071 20.2509 10.3866 20.4756 12.0557C20.6237 13.1559 20.75 14.312 20.75 15.5C20.75 16.688 20.6237 17.8441 20.4756 18.9443C20.204 20.9613 18.5327 22.5558 16.4746 22.6504C15.0462 22.7161 13.5958 22.75 12 22.75C10.4042 22.75 8.95376 22.7161 7.52539 22.6504C5.46733 22.5558 3.79598 20.9613 3.52441 18.9443C3.37629 17.8441 3.25 16.688 3.25 15.5C3.25 14.312 3.37629 13.1559 3.52441 12.0557C3.74914 10.3866 4.93236 9.0071 6.5 8.52734V6.75C6.5 3.71243 8.96243 1.25 12 1.25ZM11.9932 14C11.1685 14.0001 10.5 14.6716 10.5 15.5C10.5 16.3284 11.1685 16.9999 11.9932 17H12.0068C12.8315 16.9999 13.5 16.3284 13.5 15.5C13.5 14.6716 12.8315 14.0001 12.0068 14H11.9932ZM12 3.25C10.067 3.25 8.5 4.817 8.5 6.75V8.31055C9.61773 8.27087 10.7654 8.25 12 8.25C13.2346 8.25 14.3823 8.27087 15.5 8.31055V6.75C15.5 4.817 13.933 3.25 12 3.25Z"
                      fill="#757575"/>
                </svg>
              </div>
            </div>
          </div>
        </div>

        <button
            class="fixed bottom-[56px] left-[50%] transform bg-[#57D061] text-xl text-[#07403F] font-beanbag rounded-full py-3 uppercase shadow-lg shadow-[#4C5D7052] w-[340px] cursor-pointer"
            @click="startStudy()"
        >
          bắt đầu
        </button>
      </div>

      <div v-if="currentView === TYPES_VIEW.STACKED_CARD" class="flashcards-wrap" v-loading="isLoadDataFlashcard">

        <div class="mx-auto w-[80%] max-w-[648px] relative">
          <div
              :style="{ background: currentCard?.is_relearn ? '#14AE5C' : (currentCard?.is_test ? '#FF7C79' : '#4E87FF') }"
              class="tag_card rounded-full px-2 py-1 font-beanbag-medium text-white text-base flex items-center mb-4"
              style="line-height: 1; width: fit-content; align-self: flex-start;">
            <span v-if="currentCard?.is_test">Kiểm tra</span>
            <span v-else-if="currentCard?.is_relearn">Từ vựng ôn lại</span>
            <span v-else>Từ mới</span>
          </div>
          <div class="card_setting cursor-pointer popover_setting absolute right-[-88px] top-[-15px] group z-50">
            <svg width="48" height="48" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M18 44H30C40 44 44 40 44 30V18C44 8 40 4 30 4H18C8 4 4 8 4 18V30C4 40 8 44 18 44Z"
                    stroke="#757575"
                    stroke-width="3" stroke-linecap="round" stroke-linejoin="round"/>
              <path d="M31.1401 37V29.2" stroke="#757575" stroke-width="3" stroke-miterlimit="10" stroke-linecap="round"
                    stroke-linejoin="round"/>
              <path d="M31.1401 14.9V11" stroke="#757575" stroke-width="3" stroke-miterlimit="10" stroke-linecap="round"
                    stroke-linejoin="round"/>
              <path
                  d="M31.1399 25.2999C34.0118 25.2999 36.3399 22.9718 36.3399 20.0999C36.3399 17.228 34.0118 14.8999 31.1399 14.8999C28.2681 14.8999 25.9399 17.228 25.9399 20.0999C25.9399 22.9718 28.2681 25.2999 31.1399 25.2999Z"
                  stroke="#757575" stroke-width="3" stroke-miterlimit="10" stroke-linecap="round"
                  stroke-linejoin="round"/>
              <path d="M16.8599 37.0001V33.1001" stroke="#757575" stroke-width="3" stroke-miterlimit="10"
                    stroke-linecap="round" stroke-linejoin="round"/>
              <path d="M16.8599 18.8V11" stroke="#757575" stroke-width="3" stroke-miterlimit="10" stroke-linecap="round"
                    stroke-linejoin="round"/>
              <path
                  d="M16.8602 33.0999C19.732 33.0999 22.0602 30.7718 22.0602 27.9C22.0602 25.0281 19.732 22.7 16.8602 22.7C13.9883 22.7 11.6602 25.0281 11.6602 27.9C11.6602 30.7718 13.9883 33.0999 16.8602 33.0999Z"
                  stroke="#757575" stroke-width="3" stroke-miterlimit="10" stroke-linecap="round"
                  stroke-linejoin="round"/>
            </svg>
            <div
                class="popover_setting_content hidden group-hover:block absolute right-[-30px] top-full bg-white shadow-lg rounded-[40px] p-[21px] min-w-[225px] z-10">
              <div class="relative z-10 bg-white">
                <!--                <div class="popover_setting_content_item flex justify-between items-center py-2">-->
                <!--                  <div class="popover_setting_content_item_title font-averta-regular text-[#07403F] text-base">-->
                <!--                    Xáo trộn thẻ-->
                <!--                  </div>-->
                <!--                  <label class="shuffle-switch">-->
                <!--                    <input type="checkbox" v-model="isShuffle" @change="handleShuffleChange">-->
                <!--                    <span class="shuffle-slider round">-->
                <!--                    <span class="slider-circle"></span>-->
                <!--                  </span>-->
                <!--                  </label>-->
                <!--                </div>-->
                <div class="popover_setting_content_item flex justify-between items-center py-2">
                  <div class="popover_setting_content_item_title font-averta-regular text-[#07403F] text-base">
                    Mặt trước thẻ
                  </div>
                  <label class="language-switch" :disabled="categoryData.hasCourseOwner === 0 && countSwipeCard === limitCardFree">
                    <input :disabled="categoryData.hasCourseOwner === 0 && countSwipeCard === limitCardFree"
                           type="checkbox" v-model="isJapanese" @change="handleLanguageChange">
                    <span class="language-slider round">
                    <span class="slider-text">{{ isJapanese ? 'JA' : 'VN' }}</span>
                    <span class="flag" :class="isJapanese ? 'ja' : 'vi'"></span>
                  </span>
                  </label>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="cards-wrap content mx-auto w-[80%] max-w-[648px]">

          <div class="content-wrap mx-auto mb-6">
            <div id="stacked-cards-block" class="stackedcards stackedcards--animatable a_cursor--pointer">
              <div class="stackedcards-container" style="margin-bottom: 20px;">
                <div
                    v-for="(card, index) in dataFlashCard"
                    :data-id="card.id"
                    class="card-item"
                    :class="{
                  'stackedcards-active': index === 0,
                  'stackedcards-top': true,
                  'stackedcards--animatable': true,
                  'stackedcards-origin-top': true
                }"
                >
                  <div class="card-inner"
                       :class="{ 'flip': !isJapanese && !card.is_test && !(card.hasCourseOwner === 0), 'noFlip': card.is_test || card.hasCourseOwner }"
                       :data-id="card.id">
                    <template v-if="card.take_break === 1">
                      <div class="card__face card__face--jp card__face--front noFlip" style="padding: unset">
                        <img class="rounded-[32px] h-full w-auto"
                             :src="`/images/vocabulary/take-break/${card.img_break}.svg`" alt=""/>
                      </div>
                    </template>
                    <template v-else-if="card.hasCourseOwner === 0">
                      <div class="card__face card__face--jp card__face--front text-wrap noFlip text-center">
                        <div>
                          <svg class="m-[20px]" width="40" height="40" viewBox="0 0 24 24" fill="none"
                               xmlns="http://www.w3.org/2000/svg">
                            <path
                                d="M12 1.25C15.0376 1.25 17.5 3.71243 17.5 6.75V8.52734C19.0676 9.0071 20.2509 10.3866 20.4756 12.0557C20.6237 13.1559 20.75 14.312 20.75 15.5C20.75 16.688 20.6237 17.8441 20.4756 18.9443C20.204 20.9613 18.5327 22.5558 16.4746 22.6504C15.0462 22.7161 13.5958 22.75 12 22.75C10.4042 22.75 8.95376 22.7161 7.52539 22.6504C5.46733 22.5558 3.79598 20.9613 3.52441 18.9443C3.37629 17.8441 3.25 16.688 3.25 15.5C3.25 14.312 3.37629 13.1559 3.52441 12.0557C3.74914 10.3866 4.93236 9.0071 6.5 8.52734V6.75C6.5 3.71243 8.96243 1.25 12 1.25ZM11.9932 14C11.1685 14.0001 10.5 14.6716 10.5 15.5C10.5 16.3284 11.1685 16.9999 11.9932 17H12.0068C12.8315 16.9999 13.5 16.3284 13.5 15.5C13.5 14.6716 12.8315 14.0001 12.0068 14H11.9932ZM12 3.25C10.067 3.25 8.5 4.817 8.5 6.75V8.31055C9.61773 8.27087 10.7654 8.25 12 8.25C13.2346 8.25 14.3823 8.27087 15.5 8.31055V6.75C15.5 4.817 13.933 3.25 12 3.25Z"
                                fill="#757575"/>
                          </svg>
                        </div>
                        <div class="font-gen-jyuu-gothic-medium text-base text-center text-[#07403F]">
                          Sở hữu khóa học để mở toàn bộ
                        </div>
                        <div class="font-gen-jyuu-gothic-medium text-2xl text-center text-[#07403F]">
                          {{ categoryData.data.word_count }} từ vựng
                        </div>
                        <div class="img text-center">
                          <img src="/images/vocabulary/card-not-has-course.svg" alt=""/>
                        </div>
                        <button
                            class="bg-[#57D061] text-xl text-[#07403F] font-beanbag rounded-full py-3 uppercase shadow-lg shadow-[#4C5D7052] mt-12 w-[340px] cursor-pointer">
                          Mở khóa toàn bộ
                        </button>
                      </div>
                    </template>
                    <template v-else>
                      <div class="card__face card__face--jp card__face--front" :class="{ 'noFlip': card.is_test }"
                           style="width: 100%; height: 100%;">
                        <div class="card-wrap p-4 h-[90%] overflow-y-auto relative" :class="{ 'noFlip': card.is_test }">
                          <div class="card_header flex items-center">
                            <div v-if="card.value && card.value.audio"
                                 class="card_audio noFlip w-[48px] h-[48px] rounded-full bg-[#E1EBFF] flex items-center justify-center mr-4 cursor-pointer"
                                 @click="playAudio(card.id, card.value.audio)"
                            >
                              <svg class="noFlip" width="24" height="24" viewBox="0 0 24 24" fill="none"
                                   xmlns="http://www.w3.org/2000/svg">
                                <path
                                    d="M18 16.7503C17.84 16.7503 17.69 16.7003 17.55 16.6003C17.22 16.3503 17.15 15.8803 17.4 15.5503C18.97 13.4603 18.97 10.5403 17.4 8.45027C17.15 8.12027 17.22 7.65027 17.55 7.40027C17.88 7.15027 18.35 7.22027 18.6 7.55027C20.56 10.1703 20.56 13.8303 18.6 16.4503C18.45 16.6503 18.23 16.7503 18 16.7503Z"
                                    fill="#4E87FF"/>
                                <path
                                    d="M19.83 19.2503C19.67 19.2503 19.52 19.2003 19.38 19.1003C19.05 18.8503 18.98 18.3803 19.23 18.0503C21.9 14.4903 21.9 9.51027 19.23 5.95027C18.98 5.62027 19.05 5.15027 19.38 4.90027C19.71 4.65027 20.18 4.72027 20.43 5.05027C23.5 9.14027 23.5 14.8603 20.43 18.9503C20.29 19.1503 20.06 19.2503 19.83 19.2503Z"
                                    fill="#4E87FF"/>
                                <path opacity="0.4"
                                      d="M15.75 7.40972V16.5897C15.75 18.3097 15.13 19.5997 14.02 20.2197C13.57 20.4697 13.07 20.5897 12.55 20.5897C11.75 20.5897 10.89 20.3197 10.01 19.7697L7.09 17.9397C6.89 17.8197 6.66 17.7497 6.43 17.7497H5.5V6.24972H6.43C6.66 6.24972 6.89 6.17972 7.09 6.05972L10.01 4.22972C11.47 3.31972 12.9 3.15972 14.02 3.77972C15.13 4.39972 15.75 5.68972 15.75 7.40972Z"
                                      fill="#4E87FF"/>
                                <path
                                    d="M5.5 6.25V17.75H5C2.58 17.75 1.25 16.42 1.25 14V10C1.25 7.58 2.58 6.25 5 6.25H5.5Z"
                                    fill="#4E87FF"/>
                              </svg>
                            </div>
                            <div class="card_title font-gen-jyuu-gothic-medium text-2xl text-[#757575]"
                                 v-html="card.value.word_stress">
                            </div>
                          </div>
                          <div class="card_content min-h-[calc(100%-34px)] flex flex-col"
                               :class="{ 'noFlip': card.is_test }">
                            <div
                                class="content-text font-gen-jyuu-gothic-medium text-[56px] text-[#07403F] items-center justify-center flex text-center"
                                style="flex-grow: 1;"
                                v-html="card.value.word">
                            </div>

                            <div v-if="card.is_test"
                                 class="text-center font-gen-jyuu-gothic-medium text-[20px] text-[#07403F]">
                              có nghĩa là gì?
                            </div>

                            <div v-if="card.value.front_image  && !card.is_test"
                                 class="content-img text-center p-[40px]">
                              <img
                                  :src="`https://video-test.dungmori.com/images/${card.value.front_image}`">
                            </div>


                            <div v-if="card.value.example.length && !card.is_test" class="example-wrap">
                              <p class="w-fit font-beanbag text-base text-[#07403F] bg-[#CEFFD8] rounded-[10px] p-2 mb-2">
                                Ví dụ
                              </p>
                              <div class="list-example">
                                <template v-for="(example, index) in card.value.example">
                                  <div class="example-item flex items-start mb-1">
                                    <svg v-if="example.audio" class="w-[36px] h-[36px] noFlip cursor-pointer" width="24"
                                         height="24" viewBox="0 0 24 24" fill="none"
                                         xmlns="http://www.w3.org/2000/svg"
                                         @click="playAudio(card.id + '_example_' + index, example.audio)">
                                      <path
                                          d="M18 16.7503C17.84 16.7503 17.69 16.7003 17.55 16.6003C17.22 16.3503 17.15 15.8803 17.4 15.5503C18.97 13.4603 18.97 10.5403 17.4 8.45027C17.15 8.12027 17.22 7.65027 17.55 7.40027C17.88 7.15027 18.35 7.22027 18.6 7.55027C20.56 10.1703 20.56 13.8303 18.6 16.4503C18.45 16.6503 18.23 16.7503 18 16.7503Z"
                                          fill="#4E87FF"/>
                                      <path
                                          d="M19.83 19.2503C19.67 19.2503 19.52 19.2003 19.38 19.1003C19.05 18.8503 18.98 18.3803 19.23 18.0503C21.9 14.4903 21.9 9.51027 19.23 5.95027C18.98 5.62027 19.05 5.15027 19.38 4.90027C19.71 4.65027 20.18 4.72027 20.43 5.05027C23.5 9.14027 23.5 14.8603 20.43 18.9503C20.29 19.1503 20.06 19.2503 19.83 19.2503Z"
                                          fill="#4E87FF"/>
                                      <path opacity="0.4"
                                            d="M15.75 7.40972V16.5897C15.75 18.3097 15.13 19.5997 14.02 20.2197C13.57 20.4697 13.07 20.5897 12.55 20.5897C11.75 20.5897 10.89 20.3197 10.01 19.7697L7.09 17.9397C6.89 17.8197 6.66 17.7497 6.43 17.7497H5.5V6.24972H6.43C6.66 6.24972 6.89 6.17972 7.09 6.05972L10.01 4.22972C11.47 3.31972 12.9 3.15972 14.02 3.77972C15.13 4.39972 15.75 5.68972 15.75 7.40972Z"
                                            fill="#4E87FF"/>
                                      <path
                                          d="M5.5 6.25V17.75H5C2.58 17.75 1.25 16.42 1.25 14V10C1.25 7.58 2.58 6.25 5 6.25H5.5Z"
                                          fill="#4E87FF"/>
                                    </svg>
                                    <div class="ml-2 font-beanbag-regular text-2xl flex items-start w-[90%]">
                                      {{ index + 1 }}. <span class="ml-2" v-html="example.example"></span>
                                    </div>
                                  </div>
                                </template>
                              </div>
                            </div>

                            <div v-if="card.is_test" class="test-wrap flex flex-col noFlip mb-5">
                              <!-- radio chon dap an-->
                              <div v-for="(item, index) in card.value.quiz_question" :key="index" class="radio-wrap">
                                <input
                                    class="input-radio-test"
                                    :class="{'false': item.index}"
                                    type="radio"
                                    :id="`${card.id}-${index}`"
                                    name="select"
                                    :value="index"
                                    @click="selectAnswer(card.id, item.index)"
                                    :disabled="item.index !== selectedAnswerIndex && selectedAnswerIndex !== null"
                                >
                                <label :for="`${card.id}-${index}`" class="label-radio-test">
                                  <div class="number-radio-test">{{ index + 1 }}</div>
                                  <p>{{ item.question }}</p>
                                </label>
                              </div>
                            </div>
                          </div>
                        </div>
                        <div v-if="!card.is_test"
                             class="card-footer noFlip flex justify-end border-t-[1px] border-[#D0D3DA]">
                          <svg class="m-3 noFlip" width="33" height="28" viewBox="0 0 33 28" fill="none"
                               @click="likeCard(card.id)"
                               xmlns="http://www.w3.org/2000/svg">
                            <path
                                d="M23.8333 0.5C28.6377 0.5 32.4997 4.21737 32.5 8.78727C32.4819 14.1271 29.8923 18.3826 26.6876 21.5156C23.4787 24.6525 19.6885 26.627 17.3726 27.393C17.163 27.4593 16.8527 27.5 16.5137 27.5C16.1782 27.5 15.8562 27.4601 15.6229 27.3915C13.3062 26.6243 9.50962 24.6502 6.29845 21.5153C3.08901 18.3822 0.5 14.127 0.5 8.78779C0.5 4.21765 4.36212 0.5 9.16667 0.5C12.0183 0.5 14.5145 1.79375 16.1076 3.81099L16.5 4.30783L16.8924 3.81099C18.4855 1.79375 20.9817 0.5 23.8333 0.5Z"
                                :fill="card.isLike ? '#FF7C79' : '#ffffff'" stroke="#FF7C79"/>
                          </svg>
                        </div>
                      </div>
                      <div class="card__face card__face--vi card__face--back" style="width: 100%; height: 100%;">
                        <div class="card-wrap-back p-4 h-[60%] overflow-y-auto flex flex-col relative">
                          <div
                              class="card_content_back font-beanbag-medium text-5xl items-center justify-center flex text-[#07403F] mb-5"
                              style="flex-grow: 1"
                          >
                            <div class="text-center" v-html="card.value.meaning"></div>
                          </div>
                          <div class="font-beanbag-medium text-[#757575] text-xl text-center text-[#07403F]"
                               v-html="card.value.kanji_meaning"></div>
                          <div class="card_img_back p-[40px] content-img text-center" v-if="card.value.back_image">
                            <img
                                :src="`https://video-test.dungmori.com/images/${card.value.back_image}`">
                          </div>
                          <div v-if="card.value.meaning_example.length" class="example-wrap">
                            <p class="w-fit font-beanbag text-base text-[#07403F] bg-[#CEFFD8] rounded-[10px] p-2 mb-2">
                              Ví dụ
                            </p>
                            <div class="list-example">
                              <template v-for="(meaning_example, index) in card.value.meaning_example">
                                <div class="example-item flex items-center mb-1">
                                  <div class="ml-2 font-averta-regular text-2xl flex items-start">
                                    {{ index + 1 }}. <span class="ml-1" v-html="meaning_example"></span>
                                  </div>
                                </div>
                              </template>
                            </div>
                          </div>
                        </div>
                        <div class="how-remember-wrap px-4 h-[40%] flex flex-col">
                          <div class="how-remember-wrap-header flex  mb-5">
                            <div class="font-beanbag-medium text-[#757575] text-xl">
                              Cách nhớ
                            </div>
                            <div class="border-b-[1px] border-[#D0D3DA] flex-1 m-[7px]"></div>
                          </div>
                          <div v-if="card.comment && card.comment.user_info"
                               class="how-remember-wrap-content grid grid-cols-[40px_auto] gap-4"
                               style="">
                            <div class="how-remember-wrap-avatar w-[28px]">
                              <img class="rounded-full"
                                   :src="`/cdn/avatar/small/${card.comment.user_info.avatar}`">
                            </div>
                            <div class="how-remember-wrap-info flex text-[#073A3B]">
                          <span class="font-averta-bold">
                            {{ card.comment.user_info.name }}・
                          </span>
                              <span class="font-averta-regular">
                            {{ card.comment.time_created }}
                          </span>
                              <svg v-if="card.comment.pin" width="16" height="16" viewBox="0 0 16 16" fill="none"
                                   xmlns="http://www.w3.org/2000/svg">
                                <path
                                    d="M4.54059 9.86604C4.49626 9.83523 4.45013 9.81231 4.41581 9.77792C3.83372 9.198 3.25271 8.617 2.67312 8.03457C2.59482 7.95577 2.51759 7.87338 2.45323 7.78312C2.23084 7.47256 2.23799 7.15555 2.48076 6.86004C2.75322 6.52871 3.11005 6.30626 3.49405 6.13218C4.1902 5.81697 4.90958 5.70163 5.65114 5.95953C5.851 6.02902 6.03657 6.14078 6.21856 6.22854C6.924 5.52396 7.63981 4.80935 8.36242 4.08758C8.13788 3.75446 8.06279 3.37477 8.17649 2.96821C8.44966 1.99177 9.64601 1.67476 10.3836 2.38543C10.7887 2.77586 11.1827 3.17776 11.5803 3.57608C12.2554 4.25271 12.9347 4.92469 13.6019 5.60885C14.4236 6.45133 13.8447 7.6316 13.0213 7.84616C12.6641 7.93929 12.3312 7.88628 12.0212 7.69106C11.9354 7.63697 11.8721 7.63733 11.7981 7.71148C11.1216 8.39277 10.4426 9.07191 9.76472 9.75177C9.75864 9.75786 9.75542 9.76682 9.74648 9.78186C9.75363 9.79691 9.76078 9.81732 9.77223 9.83523C10.3089 10.6745 10.3089 11.5489 9.90023 12.4272C9.74755 12.7553 9.5248 13.0526 9.31814 13.3527C9.24735 13.4555 9.13579 13.5365 9.0296 13.6074C8.81114 13.7532 8.57373 13.7629 8.34561 13.635C8.22297 13.5662 8.10677 13.4767 8.00701 13.3775C7.36128 12.7352 6.71877 12.089 6.08019 11.4392C5.99331 11.3508 5.94289 11.3429 5.84278 11.4246C4.85559 12.2341 3.86411 13.0382 2.87299 13.8431C2.60555 14.0605 2.32702 14.0509 2.12286 13.822C1.99378 13.6773 1.96304 13.515 2.04277 13.3384C2.07745 13.2618 2.12286 13.1887 2.17256 13.1206C2.9209 12.0915 3.67104 11.0635 4.42046 10.0351C4.45407 9.9889 4.4866 9.94198 4.53988 9.8664L4.54059 9.86604Z"
                                    fill="#B3B3B3"/>
                              </svg>
                            </div>
                            <div class="col-start-2 font-averta-regular text-[#07403F]" style="display: -webkit-box;
                            -webkit-line-clamp: 2;
                            -webkit-box-orient: vertical;
                            overflow: hidden;
                            text-overflow: ellipsis;"
                            >
                              {{ card.comment.content }}
                            </div>
                            <div class="col-start-2 flex justify-between items-center">
                              <div class="font-averta-regular text-[#009951] flex">
                                <div class="flex items-center mr-5">
                                  <svg class="mr-1 noFlip" width="13" height="12" viewBox="0 0 13 12" fill="none"
                                       xmlns="http://www.w3.org/2000/svg">
                                    <path
                                        d="M9.38889 0.723633C11.1046 0.723633 12.4996 2.1159 12.5 3.84852C12.493 5.95749 11.518 7.64456 10.2933 8.89844C9.06415 10.1569 7.61344 10.9461 6.74434 11.2477C6.70298 11.2611 6.61724 11.2756 6.50542 11.2756C6.39725 11.2756 6.30516 11.262 6.25054 11.2459C5.38046 10.9429 3.92943 10.1541 2.70106 8.89818C1.47455 7.64419 0.5 5.95749 0.5 3.8492C0.5 2.11626 1.89522 0.723633 3.61111 0.723633C4.6312 0.723633 5.52625 1.20724 6.10079 1.96912L6.5 2.49851L6.89921 1.96912C7.47375 1.20724 8.3688 0.723633 9.38889 0.723633Z"
                                        :fill="card.comment.comment_like.length ? '#009951' : 'none'" stroke="#009951"/>
                                  </svg>
                                  {{ card.comment.count_like }}
                                </div>
                                <div v-if="card.comment && card.comment.replies">
                                  {{ card.comment.replies.length }} Trả lời
                                </div>
                              </div>
                              <div class="underline decoration-solid text-[#009951] cursor-pointer noFlip"
                                   @click="toggleCommentTab('open')">
                                Xem thêm >>
                              </div>
                            </div>
                          </div>
                          <div v-else
                               class="items-center flex grow "
                               style="flex-grow: 1;">
                            <div
                                class="underline decoration-solid font-averta-regular text-[#009951] cursor-pointer noFlip"
                                @click="toggleCommentTab('open')">
                              Đóng góp cách nhớ của bạn >>
                            </div>
                          </div>
                        </div>
                      </div>
                    </template>
                  </div>
                </div>
              </div>

              <!--              <div v-else class="stackedcards-container" style="margin-bottom: 20px;">-->
              <!--                <div-->
              <!--                    v-for="i in 3"-->
              <!--                    class="card-item"-->
              <!--                    :class="{-->
              <!--                      'stackedcards-active': i === 0,-->
              <!--                      'stackedcards-top': true,-->
              <!--                      'stackedcards&#45;&#45;animatable': true,-->
              <!--                      'stackedcards-origin-top': true-->
              <!--                    }"-->
              <!--                >-->
              <!--                  <div class="card-inner noFlip">-->
              <!--                    <div class="card__face card__face&#45;&#45;jp card__face&#45;&#45;front text-wrap noFlip text-center">-->
              <!--                      <div>-->
              <!--                        <svg class="m-[20px]" width="40" height="40" viewBox="0 0 24 24" fill="none"-->
              <!--                             xmlns="http://www.w3.org/2000/svg">-->
              <!--                          <path-->
              <!--                              d="M12 1.25C15.0376 1.25 17.5 3.71243 17.5 6.75V8.52734C19.0676 9.0071 20.2509 10.3866 20.4756 12.0557C20.6237 13.1559 20.75 14.312 20.75 15.5C20.75 16.688 20.6237 17.8441 20.4756 18.9443C20.204 20.9613 18.5327 22.5558 16.4746 22.6504C15.0462 22.7161 13.5958 22.75 12 22.75C10.4042 22.75 8.95376 22.7161 7.52539 22.6504C5.46733 22.5558 3.79598 20.9613 3.52441 18.9443C3.37629 17.8441 3.25 16.688 3.25 15.5C3.25 14.312 3.37629 13.1559 3.52441 12.0557C3.74914 10.3866 4.93236 9.0071 6.5 8.52734V6.75C6.5 3.71243 8.96243 1.25 12 1.25ZM11.9932 14C11.1685 14.0001 10.5 14.6716 10.5 15.5C10.5 16.3284 11.1685 16.9999 11.9932 17H12.0068C12.8315 16.9999 13.5 16.3284 13.5 15.5C13.5 14.6716 12.8315 14.0001 12.0068 14H11.9932ZM12 3.25C10.067 3.25 8.5 4.817 8.5 6.75V8.31055C9.61773 8.27087 10.7654 8.25 12 8.25C13.2346 8.25 14.3823 8.27087 15.5 8.31055V6.75C15.5 4.817 13.933 3.25 12 3.25Z"-->
              <!--                              fill="#757575"/>-->
              <!--                        </svg>-->
              <!--                      </div>-->
              <!--                      <div class="font-gen-jyuu-gothic-medium text-base text-center text-[#07403F]">-->
              <!--                        Sở hữu khóa học để mở toàn bộ-->
              <!--                      </div>-->
              <!--                      <div class="font-gen-jyuu-gothic-medium text-2xl text-center text-[#07403F]">-->
              <!--                        3000 từ vựng-->
              <!--                      </div>-->
              <!--                      <div class="img text-center">-->
              <!--                        <img src="/images/vocabulary/card-not-has-course.svg" alt=""/>-->
              <!--                      </div>-->
              <!--                      <button-->
              <!--                          class="bg-[#57D061] text-xl text-[#07403F] font-beanbag rounded-full py-3 uppercase shadow-lg shadow-[#4C5D7052] mt-12 w-[340px] cursor-pointer">-->
              <!--                        Mở khóa toàn bộ-->
              <!--                      </button>-->
              <!--                    </div>-->
              <!--                  </div>-->
              <!--                </div>-->
              <!--              </div>-->

              <div
                  class="stackedcards--animatable stackedcards-overlay left stackedcards-origin-top font-averta-bold text-[#975102]">
                Chưa nhớ
              </div>
              <div
                  class="stackedcards--animatable stackedcards-overlay right stackedcards-origin-top font-averta-bold text-[#02542D]">
                Đã học
              </div>
            </div>
          </div>


          <div class="group-actions flex items-center"
               :class="currentCard && currentCard.take_break === 1 ? 'justify-center' : 'justify-between'"
          >
            <template v-if="currentCard && currentCard.take_break === 1">
              <button
                  class="bg-[#8FFFA6] text-xl text-[#07403F] font-beanbag rounded-full py-3 uppercase shadow-lg shadow-[#4C5D7052] mt-12 w-[340px] cursor-pointer"
                  @click="swipeCard('right')"
              >
                Đi tiếp nào!
              </button>
            </template>

            <template v-else>
              <button
                  :disabled="(currentCard && currentCard.is_test) || (!categoryData.hasCourseOwner && countSwipeCard === limitCardFree)"
                  class="w-[37%] flex justify-center items-center a-cursor-pointer rounded-full py-[21px] px-[43px] font-beanbag-medium text-2xl drop-shadow-2xl"
                  :class="categoryData.hasCourseOwner ? 'left-action ' : ''"
                  :style="categoryData.hasCourseOwner || (!categoryData.hasCourseOwner && countSwipeCard < limitCardFree) ? {background: '#FFF1BB', color: '#07403F'} : {background: '#D9D9D9', color: '#B3B3B3'}"
                  @click="swipeCard('left')"
              >
                <svg class="mr-2" width="23" height="18" viewBox="0 0 23 18" fill="none"
                     xmlns="http://www.w3.org/2000/svg">
                  <path
                      d="M21.2842 7.80392C21.9448 7.80392 22.4803 8.33943 22.4803 9C22.4803 9.66057 21.9448 10.1961 21.2842 10.1961V7.80392ZM1.30128 9.84575C0.834185 9.37866 0.834185 8.62134 1.30128 8.15425L8.91306 0.542469C9.38015 0.0753727 10.1375 0.0753727 10.6046 0.542469C11.0717 1.00957 11.0717 1.76688 10.6046 2.23398L3.83854 9L10.6046 15.766C11.0717 16.2331 11.0717 16.9904 10.6046 17.4575C10.1375 17.9246 9.38015 17.9246 8.91306 17.4575L1.30128 9.84575ZM21.2842 10.1961H2.14703V7.80392H21.2842V10.1961Z"
                      :fill="categoryData.hasCourseOwner || (!categoryData.hasCourseOwner && countSwipeCard < limitCardFree) ? '#07403F' : '#B3B3B3'"/>
                </svg>
                Chưa nhớ
              </button>
              <button
                  :disabled="(currentCard && (currentCard.is_test || (swipeCardList.length > 0 && swipeCardList[swipeCardList.length - 1].is_test))) || (!categoryData.hasCourseOwner && countSwipeCard === limitCardFree)"
                  class="rounded-full drop-shadow-2xl p-4 cursor-pointer" @click="undo"
                  :style="categoryData.hasCourseOwner || (!categoryData.hasCourseOwner && countSwipeCard < limitCardFree) ? {background: '#FFFFFF'} : {background: '#D9D9D9'}"
              >
                <svg width="40" height="40" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path
                      d="M11.8833 30.5168H25.2166C29.8166 30.5168 33.5499 26.7834 33.5499 22.1834C33.5499 17.5834 29.8166 13.8501 25.2166 13.8501H6.88327"
                      :stroke="categoryData.hasCourseOwner || (!categoryData.hasCourseOwner && countSwipeCard < limitCardFree) ? '#07403F' : '#B3B3B3'"
                      stroke-width="2.5"
                      stroke-miterlimit="10" stroke-linecap="round"
                      stroke-linejoin="round"/>
                  <path d="M10.7166 18.0167L6.44993 13.7501L10.7166 9.4834"
                        :stroke="categoryData.hasCourseOwner || (!categoryData.hasCourseOwner && countSwipeCard < limitCardFree) ? '#07403F' : '#B3B3B3'"
                        stroke-width="2.5"
                        stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
              </button>
              <button
                  :disabled="(currentCard && currentCard.is_test) || (!categoryData.hasCourseOwner && countSwipeCard === limitCardFree)"
                  class="w-[37%] flex justify-center items-center a-cursor-pointer rounded-full py-[21px] px-[43px] font-beanbag-medium text-2xl drop-shadow-2xl"
                  :style="categoryData.hasCourseOwner || (!categoryData.hasCourseOwner && countSwipeCard < limitCardFree) ? {background: '#CEFFD8', color: '#07403F'} : {background: '#D9D9D9', color: '#B3B3B3'}"
                  :class="categoryData.hasCourseOwner || (!categoryData.hasCourseOwner && countSwipeCard < limitCardFree) ? 'right-action ' : ''"
                  @click="swipeCard('right')"
              >
                Đã học
                <svg class="ml-2" width="22" height="18" viewBox="0 0 22 18" fill="none"
                     xmlns="http://www.w3.org/2000/svg">
                  <path
                      d="M1.19994 7.8C0.537194 7.8 -6.41346e-05 8.33726 -6.41346e-05 9C-6.41346e-05 9.66274 0.537194 10.2 1.19994 10.2V7.8ZM21.2485 9.84853C21.7171 9.3799 21.7171 8.6201 21.2485 8.15147L13.6117 0.514718C13.1431 0.0460892 12.3833 0.0460892 11.9147 0.514718C11.446 0.983348 11.446 1.74315 11.9147 2.21177L18.7029 9L11.9147 15.7882C11.446 16.2569 11.446 17.0167 11.9147 17.4853C12.3833 17.9539 13.1431 17.9539 13.6117 17.4853L21.2485 9.84853ZM1.19994 10.2H20.3999V7.8H1.19994V10.2Z"
                      :fill="categoryData.hasCourseOwner || (!categoryData.hasCourseOwner && countSwipeCard < limitCardFree) ? '#07403F' : '#B3B3B3'"/>
                </svg>
              </button>
            </template>
          </div>

        </div>
      </div>


      <div v-if="currentView === TYPES_VIEW.RESULT" class="w-[90%] max-w-[1000px] mx-auto h-[450px] my-auto">
        <ChartVocabulary :options="{showTextMaxLearnWord: true}" :chartData="chartData"/>
        <div class="flex items-center justify-center relative z-10">
          <button
              class="w-[340px] bg-[#FFF193] text-xl text-[#07403F] font-beanbag rounded-full py-3 uppercase shadow-lg shadow-[#4C5D7052] mt-12"
              @click="currentView = TYPES_VIEW.OVERVIEW"
          >
            Học lại
          </button>
          <button
              class="w-[340px] bg-[#57D061] text-xl text-[#07403F] font-beanbag rounded-full py-3 uppercase shadow-lg shadow-[#4C5D7052] mt-12 ml-10"
              @click="startStudy()"
          >
            Học bộ từ khác
          </button>
        </div>


        <div class="img-fix-botton fixed bottom-0 w-[calc(100%-280px)] right-0 text-center z-[1]">
          <img src="/images/vocabulary/img-bot-result-1.svg" alt=""/>
        </div>
      </div>


      <div id="lesson-list" v-if="currentView === TYPES_VIEW.STACKED_CARD">
        <Container :hasLesson="false" @active-changed="handleContainerActive($event)"></Container>
      </div>
    </div>
  </div>
</template>

<script>

import ProgressBar from "../../component/ProgressBar.vue";
import axios from "axios";
import StackedCards, {removeFontSizeStyle, sortFlcVocabulary} from "../../module/flashcard";
import ChartVocabulary from "./Chart.vue";
import _ from "lodash";
import Container from "../../course/components/Container.vue";

const TYPES_VIEW = {
  OVERVIEW: 1,
  SEARCH: 2,
  STACKED_CARD: 3,
  RESULT: 4,
}

export default {
  components: {
    ChartVocabulary,
    ProgressBar,
    // FlashCardModule
    Container,
  },
  props: {
    courseId: {
      type: Number,
      required: true,
      default: 0
    },
    categoryData: {
      type: Object,
      default: () => {
        return {
          name: '',
          data: {
            learned: 0,
            temporary: 0,
            memorized: 0,
            mastered: 0
          },
          word_count: 0,
          selected: false,
          id: 0,
        }
      }
    }
  },
  name: "FlashCard",
  data() {
    return {
      listVocabulary: [],
      TYPES_VIEW: TYPES_VIEW,
      currentView: TYPES_VIEW.OVERVIEW,
      searchResults: [],
      searchQuery: '',
      dataFlashCard: [
        {
          "id": 1,
          "lesson_id": 1,
          "type": 17,
          "take_break": 0,
          "value": {
            "word": "一",
            "word_stress": "",
            "word_type": "Động từ",
            "audio": null,
            "front_image": null,
            "example": [],
            "meaning": "Từ điển",
            "back_image": null,
            "meaning_example": [],
            "quiz_question": null,
            "kanji_meaning": null
          },
        }
      ],
      isJapanese: true,
      currentCard: null,
      chartData: {
        learned: 0,
        temporary: 0,
        memorized: 0,
        mastered: 0
      },
      isLoadDataFlashcard: false,
      arrCardLearned: [],
      swipeCardList: [],
      selectedAnswerIndex: null,
      allCards: [],
      idsFree: [103631, 103632, 103612, 103613],
      isActiveContainerCmt: false,
      countSwipeCard: 0,
      completedCards: [],
      isShuffle: false,
      limitCardFree: 2,
    };
  },
  created() {
    this.getListVocabulary();
    console.log(`this.categoryData: `, this.categoryData);
  },
  watch: {
    searchQuery(newValue, oldValue) {
      console.log(`searchQuery: `, newValue);
      if (newValue) {
        this.searchWord(newValue);
      } else {
        this.searchResults = this.allCards;
      }
    },
    async currentView(newValue, oldValue) {
      console.log(`currentView: `, newValue);
      if (newValue === TYPES_VIEW.SEARCH) {
        if (this.allCards.length === 0) {
          this.allCards = await this.getAllFlashCard();
          this.searchResults = this.allCards;
        }
      }

      if (newValue === TYPES_VIEW.RESULT) {
        $("#default").css("margin-top", "0");
      } else {
        $("#default").css("margin-top", "58px");
      }
    }
  },
  methods: {
    getListVocabulary() {
      this.listVocabulary = this.listVocabulary.filter(item => item.course_id === this.courseId);
    },
    playAudio(card_id, audioUrl) {
      console.log(`Play audio for card id: ${card_id}, url: ${audioUrl}`);
    },
    toggleCommentTab(handle = 'open', extraData = {}) {
      console.log(`mo tab cmt with handle: ${handle}`);

      let currentCard = this.dataFlashCard[0];

      let eventData = {
        handle: handle,
        flashcard: currentCard,
        ...extraData
      };

      this.$emit('open-comment-tab', eventData);

      let customEvent = new CustomEvent('open-comment-tab', {detail: eventData});
      document.dispatchEvent(customEvent);
    },
    initStackedCards() {
      if (this.stackedCardsInstance) {
        this.stackedCardsInstance.destroyEventListenersCards();
      }
      this.$nextTick(() => {
        this.stackedCardsInstance = new StackedCards({
          visibleItems: 3,
          margin: 22,
          rotate: true,
          useOverlays: true
        });

        this.updateCurrentFlashcard();
      });
    },

    updateCurrentFlashcard() {
      this.currentCard = this.dataFlashCard[0];
      // Đợi một chút để đảm bảo stackedCardsInstance đã cập nhật currentPosition
      // this.$nextTick(() => {
      //   // Nếu danh sách thẻ flashcard đã được lọc và chỉ có 1 thẻ
      //   if (this.dataFlashCard.length === 1) {
      //     const currentCard = this.dataFlashCard[0];
      //     console.log('Only one flashcard, using it as current:', currentCard);
      //
      //     // Tìm component Container và gọi phương thức setCurrentFlashcard
      //     const containerComponent = this.$parent.$children.find(child => child.$options.name === 'container');
      //     if (containerComponent) {
      //       containerComponent.setCurrentFlashcard(currentCard);
      //     } else {
      //       console.error('Container component not found');
      //     }
      //
      //     return;
      //   }
      //
      //   // Xử lý bình thường nếu có nhiều thẻ
      //   if (this.stackedCardsInstance) {
      //     const currentCard = this.dataFlashCard[0];
      //
      //     console.log('Current flashcard:', currentCard);
      //
      //     if (currentCard) {
      //       console.log('Current flashcard updated:', currentCard);
      //
      //       // Tìm component Container và gọi phương thức setCurrentFlashcard
      //       const containerComponent = this.$children.find(child => child.$options.name === 'container');
      //       if (containerComponent) {
      //         containerComponent.setCurrentFlashcard(currentCard);
      //       } else {
      //         console.error('Container component not found');
      //       }
      //     }
      //   }
      // });
    },

    swipeCard(type) {
      if (!this.stackedCardsInstance || !this.dataFlashCard[0]) return;

      console.log(`this.categoryData: `, this.categoryData);
      if (this.categoryData.hasCourseOwner === false) {
        this.countSwipeCard++;
        this.currentCard = this.dataFlashCard[this.countSwipeCard];
        console.log(`this.currentCard: `, this.currentCard);
        if (type === 'left') {
          this.stackedCardsInstance.swipeLeft();
        } else {
          this.stackedCardsInstance.swipeRight();
        }
        this.currentCard = this.dataFlashCard[0];
        return;
      }


      console.log(`countSwipeCard: `, this.countSwipeCard);
      if (this.dataFlashCard[0].id === 0) {
        this.dataFlashCard = this.dataFlashCard.filter(card => card.id !== 0);
        // lấy phần tử đầu tiên của mảng dataFlashCard push vào mảng swipeCardList và xóa khỏi mảng dataFlashCard
        this.swipeCardList.push(this.dataFlashCard[0]);
        this.dataFlashCard.shift();
        this.initStackedCards();
        this.countSwipeCard = 0;
        return;
      } else if (this.countSwipeCard && this.countSwipeCard % 5 === 0) {
        console.log(`this.swipeCardList: `, this.swipeCardList);
        let ratio = this.swipeCardList.filter(card => card.type_swipe === 'right').length / this.swipeCardList.length;
        console.log(`ratio: `, ratio);
        let card_unshift = {
          id: 0,
          lesson_id: 1,
          type: 17,
          take_break: 1,
        };
        if (ratio >= 0.8 || ratio <= 0.2) {
          let rd = ratio >= 0.8 ? Math.floor(Math.random() * 4) + 1 : Math.floor(Math.random() * 5) + 1;
          card_unshift.img_break = `${ratio >= 0.8 ? 'right' : 'left'}-${rd}`;
          this.dataFlashCard.unshift(card_unshift);
          let cards = document.querySelectorAll('.card-inner');
          cards.forEach(card => {
            card.classList.remove('flip');
          });
          this.initStackedCards();
          return;
        }
      } else if (!this.dataFlashCard[0].is_relearn) {
        this.countSwipeCard++;
      }

      let currentCard = this.dataFlashCard[0];
      currentCard.type_swipe = type;
      currentCard.t = Math.floor(Date.now() / 1000); // Luôn cập nhật thời gian
      currentCard.is_relearn = false;

      // Xử lý thẻ kiểm tra khi vuốt trái
      if (type === 'left' && currentCard.is_test) {
        currentCard.is_test = false;
        currentCard.is_relearn = true;
        this.stackedCardsInstance.swipeLeft();
        this.dataFlashCard[0] = currentCard;
        setTimeout(() => {
          this.initStackedCards();
          this.currentCard = this.dataFlashCard[0];
        }, 500);
        return;
      }

      // Cập nhật trạng thái thẻ
      if (type === 'left') {
        this.stackedCardsInstance.swipeLeft();
        currentCard.box = (currentCard.box || 0); // Giữ nguyên box
      } else {
        currentCard.box = (currentCard.box || 0) + 1; // Tăng box
        this.stackedCardsInstance.swipeRight();
      }
      currentCard.p = currentCard.box;

      // kiểm tra trong mảng swipeCardList có thẻ nào trùng với currentCard không, nếu có thì xóa
      this.swipeCardList = this.swipeCardList.filter(card => card.id !== currentCard.id);
      this.swipeCardList.push(currentCard);
      console.log(`this.swipeCardList: `, this.swipeCardList);

      this.dataFlashCard = this.dataFlashCard.filter(card => card.id !== currentCard.id);

      // Gộp và sắp xếp thẻ
      let allCards = [...this.dataFlashCard, ...this.arrCardLearned, ...this.swipeCardList];
      // Loại bỏ trùng lặp theo ID, giữ phiên bản mới nhất
      allCards = _.uniqBy(allCards, 'id');
      let {sortCards, completedCards} = sortFlcVocabulary(allCards);
      // this.completedCards  = completedCards;
      console.log(`sortCards: `, sortCards);

      // Kiểm tra xem tất cả thẻ có ở box = 5 không
      const allMastered = allCards.every(card => card.box >= 5);

      let arrD = [];
      let currentTime = Math.floor(Date.now() / 1000);
      console.log(`currentTime: `, currentTime);
      sortCards.forEach(card => {
        arrD.push({id: card.id, box: card.box, p: card.p, t: card.t, ti: currentTime - card.t});
      });
      console.log(`arrD: `, arrD);

      // Cập nhật mảng thẻ
      this.dataFlashCard = sortCards.slice(0, 6);
      this.arrCardLearned = sortCards.slice(6);
      // this.swipeCardList = this.swipeCardList.filter(
      //     item => !sortCards.some(card => card.id === item.id)
      // );
      console.log(`this.swipeCardList: `, this.swipeCardList);

      // Đặt lại giao diện thẻ
      setTimeout(() => {
        let container = document.querySelector('.stackedcards-container');
        if (container?.firstChild) {
          Object.assign(container.firstChild.style, {
            transform: 'none',
            transition: 'none'
          });
        }

        this.$nextTick(() => {
          if (this.isJapanese) {
            let cards = document.querySelectorAll('.card-inner');
            cards.forEach(card => {
              card.classList.remove('flip');
            });
          }
          // Chuyển sang kết quả nếu tất cả thẻ đã thành thạo hoặc không còn thẻ
          if (!this.dataFlashCard.length || allMastered) {
            this.saveDataFlashcard();
            this.currentView = TYPES_VIEW.RESULT;
            return;
          }

          this.initStackedCards();
          this.currentCard = this.dataFlashCard[0] || null;
        });
      }, 400);
    },

    undo() {
      if (!this.stackedCardsInstance || !this.dataFlashCard[0]) return;
      console.log(`undo: `);
      // thêm phần tử vừa học ở mảng swipeCardList vào vị trí đầu tiên của mảng dataFlashCard
      this.dataFlashCard.unshift(this.swipeCardList.pop());
      this.initStackedCards();
    },

    async startStudy() {
      this.currentView = TYPES_VIEW.STACKED_CARD;
      // if (!this.categoryData.hasCourseOwner) {
      //   this.initStackedCards();
      //   return;
      // }

      this.isLoadDataFlashcard = true;

      if (this.allCards.length === 0) {
        this.allCards = await this.getAllFlashCard();
      }

      let {sortCards, completedCards} = sortFlcVocabulary(this.allCards);
      this.arrCardLearned = sortCards;
      this.completedCards = completedCards;

      let arrD = [];
      let currentTime = Math.floor(Date.now() / 1000);
      console.log(`currentTime: `, currentTime);
      this.arrCardLearned.forEach(card => {
        arrD.push({id: card.id, box: card.box, p: card.p, t: card.t, ti: currentTime - card.t});
      });

      if (this.arrCardLearned.length === 0) {
        this.currentView = TYPES_VIEW.RESULT;
        this.updateDataChart(completedCards);
        return;
      }

      console.log(`arrD: `, arrD);
      console.log(`this.arrCardLearned: `, this.arrCardLearned);

      // Lấy 6 thẻ đầu tiên trong mảng arrCardLearned và gán vào mảng dataFlashCard và xóa 6 thẻ đầu tiên trong mảng arrCardLearned
      this.dataFlashCard = this.arrCardLearned.slice(0, 6);
      this.arrCardLearned = this.arrCardLearned.slice(6);

      // kiểm tra user có sở hữu khóa học ko. nếu không chèn vào vị trí thứ 3 của mảng dataFlashCard 1 thẻ đặc biệt
      if (!this.categoryData.hasCourseOwner) {
        let card_unshift = {
          id: 0,
          hasCourseOwner: 0,
          take_break: 0,
          "value": {
            "word": "一",
            "word_stress": "",
            "word_type": "Động từ",
            "audio": null,
            "front_image": null,
            "example": [],
            "meaning": "Từ điển",
            "back_image": null,
            "meaning_example": [],
            "quiz_question": null,
            "kanji_meaning": null
          },
        };
        this.dataFlashCard.splice(2, 0, card_unshift);
      }
      console.log(`dataFlashCard: `, this.dataFlashCard);
      console.log(`arrCardLearned: `, this.arrCardLearned);
      this.initStackedCards();
      this.isLoadDataFlashcard = false;

      console.log(`this.dataFlashCard: `, this.dataFlashCard);
      this.currentCard = this.dataFlashCard[0];
      console.log(`this.currentCard: `, this.currentCard);
    },

    async getAllFlashCard() {
      let arrCard = [];
      let [history_learn_flc, flashcards] = await Promise.all([
        this.getHistory(),
        this.getFlashCardByCourse()
      ]);
      console.log(`history_learn_flc: `, history_learn_flc);
      flashcards.forEach(flc => {
        if (flc.value.quiz_question && flc.value.quiz_question.length > 0) {
          flc.value.quiz_question = flc.value.quiz_question.reduce((acc, cur, index) => {
            acc[index] = {
              index: index,
              question: cur
            };
            return acc;
          }, {});
          flc.value.quiz_question = _.shuffle(flc.value.quiz_question);
        }

        let flc_find = history_learn_flc.find(h => h.i === flc.id);
        if (flc_find) {
          flc = {...flc, ...flc_find};
        } else {
          flc.t = null;
          flc.p = null;
        }
        arrCard.push(flc);
      });
      console.log(`arrCard: `, arrCard);
      return arrCard;
    },

    async getHistory() {
      let history_learn_flc = [];
      let params = {
        course_id: this.courseId,
        type: 'normal',
        query: 'find'
      };

      await axios.get('/vocabulary/get-course', {params})
          .then(response => {
            console.log(`response: `, response);
            if (response.data.data) {
              history_learn_flc = JSON.parse(response.data.data.data);
            }
          })
          .catch(error => {
            console.error(error);
          });
      return history_learn_flc;
    },

    async getFlashCardByCourse() {
      let flashcards = [];
      let params = {
        course_id: this.courseId,
        type: 'normal'
      };

      // return [];

      await axios.get('/vocabulary/get-flashcard-by-course', {params})
          .then(response => {
            console.log(`response: `, response);
            flashcards = response.data.data;
            flashcards.map(flashcard => {
              flashcard.value = JSON.parse(flashcard.value);
              flashcard.value.word = removeFontSizeStyle(flashcard.value.word);
              return flashcard;
            });
          })
          .catch(error => {
            console.error(error);
          });
      return flashcards;
    },

    async getFlcById(ids) {
      let flashcards = [];
      let params = {
        ids: ids
      };

      await axios.get('/vocabulary/get-flashcard-by-id', {params})
          .then(response => {
            console.log(`response: `, response);
            flashcards = response.data.data;
          })
          .catch(error => {
            console.error(error);
          });
      return flashcards;
    },

    selectAnswer(id, index) {
      // Reset selectedAnswerIndex nếu đang ở card mới
      if (this.currentCard && this.currentCard.id !== id) {
        this.selectedAnswerIndex = null;
      }

      // Nếu đã chọn đáp án rồi thì không làm gì
      if (this.selectedAnswerIndex !== null) return;

      this.selectedAnswerIndex = index;
      console.log(`Selected answer ${index} for card ${id}`);

      setTimeout(() => {
        // Reset sau khi xử lý xong
        this.selectedAnswerIndex = null;
        //xoa checked tất cả input
        document.querySelectorAll('.input-radio-test').forEach(input => {
          input.checked = false;
        });

        if (!index) {
          this.swipeCard('right');
        } else {
          this.swipeCard('left');
        }
      }, 700);
    },

    searchWord(searchQuery) {
      this.searchResults = this.allCards.filter(card => card.value.word.toLowerCase().includes(searchQuery.toLowerCase()));
    },

    async saveDataFlashcard() {
      if (this.swipeCardList.length === 0) return;
      let allCards = [...this.dataFlashCard, ...this.arrCardLearned, ...this.swipeCardList, ...this.completedCards];
      console.log(`allCards: `, allCards);
      // lặp qua mảng allCards xóa các phàn tử có id trùng nhau và lấy phần tử có box lớn nhất
      let maxBoxById = {};
      allCards.forEach(card => {
        if (!maxBoxById[card.id] || card.box > maxBoxById[card.id].box) {
          maxBoxById[card.id] = card;
        }
      });
      allCards = Object.values(maxBoxById);


      if (!allCards.length || this.currentView !== TYPES_VIEW.STACKED_CARD) return;
      let dataSave = this.updateDataChart(allCards);

      console.log(`dataSave: `, dataSave);

      if (dataSave.length === 0) return;

      await axios.post('/vocabulary/save-flashcard', {
        course_id: this.courseId,
        data: dataSave
      }).then(response => {
        console.log(`response: `, response);
      }).catch(error => {
        console.error(error);
      })
    },

    handleBeforeUnload() {
      this.saveDataFlashcard();
    },

    updateDataChart(allCards) {
      let chartData = {
        non: 0,
        learned: 0,
        temporary: 0,
        memorized: 0,
        mastered: 0,
        super_mastered: 0,
        totalWords: 0
      };
      let dataSave = [];
      allCards.forEach(card => {
        if (card.p === 1) {
          chartData.learned += 1;
        }

        if (card.p === 2) {
          chartData.temporary += 1;
        }

        if (card.p === 3) {
          chartData.memorized += 1;
        }

        if (card.p === 4) {
          chartData.mastered += 1;
        }

        if (card.p === 5) {
          chartData.super_mastered += 1;
        }

        chartData.totalWords += 1;
        if (card.t) {
          dataSave.push({
            i: card.id,
            p: card.p,
            t: card.t
          });
        }
      });
      console.log(`chartData: `, chartData);
      this.chartData = chartData;
      return dataSave;
    },

    likeCard(id) {
      axios.post('/vocabulary/like-card', {
        id: id,
        course_id: this.courseId,
        t: Math.floor(Date.now() / 1000)
      })
          .then(response => {
            console.log(`response: `, response);
            this.dataFlashCard[0].isLike = response.data.isLike;
          })
          .catch(error => {
            console.error(error);
          })
    },

    handleContainerActive(active) {
      console.log(`Container active changed: `, active);
      this.isActiveContainerCmt = active;
    },

    handleShuffleChange() {
      console.log(`handleShuffleChange: `, this.isShuffle);
    },

    handleLanguageChange() {
      console.log(`handleLanguageChange: `, this.isJapanese);
    },
  },
  mounted() {
    window.addEventListener('beforeunload', this.handleBeforeUnload);
  },
  async beforeDestroy() {
    console.log(`beforeDestroy`);
    $("#default").css("margin-top", "58px");
    await this.saveDataFlashcard();
    window.removeEventListener('beforeunload', this.handleBeforeUnload);
  }
}
</script>

<style scoped>
.input-radio-test[type="radio"] {
  display: none;

  &:not(:disabled) ~ label {
    cursor: pointer;
  }

  &:disabled ~ label {
    color: hsla(150, 5%, 75%, 1);
    border-color: hsla(150, 5%, 75%, 1);
    box-shadow: none;
    cursor: not-allowed;
  }
}

.label-radio-test {
  height: 100%;
  display: flex;
  align-items: center;
  background: white;
  border-radius: 9999px;
  padding: 1rem 5px;
  text-align: center;
  position: relative;
}

.number-radio-test {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: white;
  border-radius: 9999px;
  color: #757575;
  font-family: 'Beanbag_Dungmori_Rounded_Medium';
  font-size: 16px;
  margin-right: 10px;
  border: 3px solid #D9D9D9;
  box-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
}

.input-radio-test[type="radio"]:checked + .label-radio-test {
  background: #95FF99;
  color: #212121;
  box-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  z-index: 1;
}

.input-radio-test[type="radio"]:checked + .label-radio-test .number-radio-test {
  border: unset;
  box-shadow: unset;
}

.input-radio-test.false[type="radio"]:checked + .label-radio-test {
  background: #FF7C79;
  color: white;
  box-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  z-index: 1;
}

</style>
