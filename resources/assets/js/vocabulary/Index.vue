<template>
  <div class="vocabulary-container">
    <!-- Left Navigation Menu -->
    <div class="nav-menu" id="navMenu">
      <div class="nav-menu-header">
        <div class="nav-menu-title cursor-pointer font-beanbag-medium text-[20px] text-[#07403F]"
             @click="currentView = typesView.OVERVIEW"><PERSON>ho từ vựng
        </div>
        <div class="toggle-menu" onclick="toggleMenu()">
          <svg width="32" height="31" viewBox="0 0 32 31" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path
                d="M12.6667 6V26M8.22222 6H23.7778C25.0051 6 26 6.99492 26 8.22222V23.7778C26 25.0051 25.0051 26 23.7778 26H8.22222C6.99492 26 6 25.0051 6 23.7778V8.22222C6 6.99492 6.99492 6 8.22222 6Z"
                stroke="#07403F" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
        </div>
      </div>

      <div class="nav-menu-items">
        <div class="nav-menu-item" onclick="toggleSubmenu('sub-menu-jlpt')">
          <span class="nav-menu-item-text font-beanbag-medium text-[20px] text-[#07403F]">JLPT</span>
          <i class="fas fa-chevron-down ml-auto"></i>
        </div>
        <div class="nav-submenu" id="sub-menu-jlpt">
          <!-- click scroll to section by id -->
          <div v-for="(vocabulary_jlpt, index) in jlpt"
               class="nav-submenu-item font-beanbag-regular text-[18px] text-black"
               @click="scrollToSection('jlpt-' + index, vocabulary_jlpt.id)"
          >
            <span class="text-black">Từ vựng {{ vocabulary_jlpt.name }}</span>
          </div>
        </div>

        <div v-if="specialized" class="nav-menu-item" onclick="toggleSubmenu('sub-menu-specialized')">
          <span class="nav-menu-item-text font-beanbag-medium text-[20px] text-[#07403F]">Chuyên ngành</span>
          <i class="fas fa-chevron-down ml-auto"></i>
        </div>
        <div v-if="specialized" class="nav-submenu" id="sub-menu-specialized">
          <div v-for="(vocabulary_specialized, index) in specialized"
               class="nav-submenu-item font-beanbag-regular text-[18px] text-black">
            <span class="text-black">{{ vocabulary_specialized.name }}</span>
          </div>
        </div>

        <div class="nav-menu-item justify-between" @click="scrollToSection('vocabulary-card-favorite')">
          <span class="nav-menu-item-text font-beanbag-medium text-[20px] text-[#07403F]">Yêu thích</span>
          <i class="fas fa-heart text-[#FF7C79]"></i>
        </div>
      </div>
    </div>

    <!-- Content Area -->
    <div class="content-area">

      <!-- Overview Content -->
      <div class="content-section active max-w-[1096px] mx-auto mt-[58px]" id="default">
        <!-- Vocabulary Search Bar -->
        <div v-if="currentView !== typesView.FAVORITE && currentView !== typesView.SPECIALIZED"
             class="vocabulary-search-container relative z-10">
          <div v-if="currentView === typesView.SEARCH"
               class="decoration-search absolute top-[-15px] left-1/2 transform -translate-y-1/2 z-1">
            <img src="/images/vocabulary/img-decoration-search-1.svg" alt="decoration-search">
          </div>
          <div v-if="currentView === typesView.SEARCH" class="vocabulary-search-btn-back h-[80px] w-[80px]"
               @click="currentView = typesView.OVERVIEW; isLoadingSearch = false; searchQuery = ''">
            <svg class="mr-[4px]" width="18" height="30" viewBox="0 0 18 30" fill="none"
                 xmlns="http://www.w3.org/2000/svg">
              <path d="M15.25 2.5L2.75 15L15.25 27.5" stroke="#1E1E1E" stroke-width="3.57143" stroke-linecap="round"
                    stroke-linejoin="round"/>
            </svg>
          </div>
          <div
              class="vocabulary-search-bar flex items-center rounded-full bg-white shadow-md h-[80px] px-[20px] transition-all duration-300 flex-1 min-w-0 z-10">
            <div class="search-icon">
              <svg width="37" height="38" viewBox="0 0 37 38" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path
                    d="M31.6697 32.5732L25.1095 26.013M28.6535 17.4924C28.6535 24.1555 23.252 29.557 16.5889 29.557C9.92576 29.557 4.52423 24.1555 4.52423 17.4924C4.52423 10.8293 9.92576 5.42773 16.5889 5.42773C23.252 5.42773 28.6535 10.8293 28.6535 17.4924Z"
                    stroke="#5A5A5A" stroke-width="2.01077" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </div>
            <input
                type="text"
                v-model="searchQuery"
                placeholder="Nhập từ vựng muốn tìm kiếm"
                class="search-input"
                @focus="searchFocused = true"
                @blur="searchFocused = false"
            />
            <div class="search-clear cursor-pointer bg-[#757575] px-[10px] py-[5px] rounded-full" v-if="searchQuery"
                 @click="searchQuery = ''">
              <i class="fas fa-times text-white"></i>
            </div>
          </div>
        </div>
        <!--        <div @click="getCourse()">-->
        <!--          get course-->
        <!--        </div>-->

        <!-- Overview Content -->
        <template v-if="currentView === typesView.OVERVIEW">
          <div class="section-content overview-content">
            <!-- Chart Overview -->
            <div v-if="authUser.id" v-loading="isLoadingChart" class="chart-overview-container flex mb-[66px]">
              <div class="w-2/3 h-full relative">
                <!--                button show dialog info-->
                <div class="text-right absolute top-[25px] right-[25px] z-[1]">
                  <button
                      class="btn-info-chart bg-[#FFB98F] text-white rounded-full w-[32px] h-[32px] flex items-center justify-center"
                      @click="showDialogInfo = true">
                    <span class="text-[20px] font-beanbag-medium">i</span>
                  </button>
                </div>

                <!-- HTML/CSS Chart -->
                <ChartVocabulary :chartData="chartData"/>

              </div>
              <div class="chart-header w-1/3">
                <div class="flex justify-between items-center border-b-[1px] pb-3"
                     style="border-bottom-style: dashed; border-bottom-color: #176867">
                  <div class="chart-title font-beanbag-medium text-[20px] text-[#176867]">Tổng số từ vựng</div>
                  <div
                      class="total-word rounded-full text-[20px] font-beanbag-medium text-[#176867] px-[15px] py-[2px] bg-[#CEFFD8]">
                    {{ chartData.totalWords }}
                  </div>
                </div>
                <div class="chart-filters pt-3">
                  <!--                khi danh sach dai để chế độ scroll danh sach categories-->
                  <div class="filter-checkboxes overflow-y-auto h-[310px]">
                    <div v-for="(category, index) in categories" :key="index" class="flex items-center justify-between">
                      <label class="checkbox-container">
                        <input type="checkbox" v-model="category.selected">
                        <span class="checkbox-label font-beanbag-medium font-medium">{{ category.name }}</span>
                      </label>
                      <div class="font-beanbag-medium text-[18px] text-[#176867]">{{ category.data.word_count }} từ
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!--JLPT-->
            <div class="vocabulary-section" id="section-jlpt">
              <div class="vocabulary-section-title font-beanbag-medium text-[#07403F] text-[32px]">
                JLPT
                <span class="badge badge-vip font-beanbag-medium text-[18px]">VIP</span>
              </div>
              <div class="vocabulary-cards">
                <div :id="`jlpt-${index}`" class="vocabulary-card min-h-[260px]"
                     :class="item.word_count === 0 ? 'vocabulary-card-incoming' : ''"
                     v-for="(item, index) in jlpt" :key="index" :data-key="index"
                     @click="openCourseFlashCardDetail(item.id)">
                  <div class="vocabulary-card-content">
                    <div class="vocabulary-card-status" v-if="item.word_count > 0">
                      <span class="badge badge-new font-gen-jyuu-gothic-medium text-[12px]">NEW</span>
                    </div>
                    <div class="vocabulary-line bg-[#57D061]"></div>
                    <div class="vocabulary-card-title font-gen-jyuu-gothic-medium text-bold text-[20px]">
                      Từ vựng {{ item.name }}
                    </div>
                    <div class="vocabulary-card-count font-gen-jyuu-gothic text-[20px]">
                      {{ item.word_count }} từ vựng
                    </div>
                  </div>
                  <div v-if="item.word_count > 0" class="vocabulary-card-name font-zuume-semibold">
                    <h1>
                      {{ item.name }}
                    </h1>
                  </div>
                  <div v-else class="vocabulary-card-incoming-text">
                    <p class="text-center text-[23px] font-gen-jyuu-gothic-medium text-[#757575]">Sắp ra mắt</p>
                  </div>
                </div>
              </div>
            </div>

            <!--Chuyên ngành-->
            <div v-if="specialized" class="vocabulary-section" id="section-specialized">
              <div class="vocabulary-section-title">
                Chuyên ngành
                <span class="badge badge-free">Free</span>
              </div>
              <div class="vocabulary-cards">
                <div class="vocabulary-card"
                     :class="item.status === 'incoming' ? 'vocabulary-card-incoming' : ''"
                     v-for="(item, index) in specialized.data" :key="index"
                     :data-key="index">
                  <div class="vocabulary-card-content">
                    <div class="vocabulary-card-status" v-if="item.status === 'hot'">
                      <span class="badge badge-new font-gen-jyuu-gothic-medium text-[12px]">HOT</span>
                    </div>
                    <div class="vocabulary-line bg-[#4E87FF]"></div>
                    <div class="vocabulary-card-title font-gen-jyuu-gothic-medium text-bold text-[20px] leading-[20px]">
                      {{ item.name }}
                    </div>
                    <div class="vocabulary-card-count font-gen-jyuu-gothic text-[20px]">
                      {{ item.word_count }} từ vựng
                    </div>
                  </div>
                  <img v-if="item.status !== 'incoming'" src="/images/vocabulary/test.png" :alt="item.name"
                       class="vocabulary-card-image">
                  <div v-else class="vocabulary-card-incoming-text">
                    <p class="text-center text-[23px] font-gen-jyuu-gothic-medium text-[#757575]">Sắp ra mắt</p>
                  </div>
                </div>
              </div>
            </div>

            <!--Yêu thích-->
            <div class="vocabulary-section" id="section-favorite">
              <div class="vocabulary-section-title font-beanbag-medium text-[#07403F] text-[32px]">
                Từ vựng yêu thích
                <i class="fas fa-heart text-[#FF7C79] ml-2"></i>
              </div>
              <div v-loading="isLoadingFavorite" id="vocabulary-card-favorite" class="vocabulary-card card-favorite"
                   @click="openViewFavorite()">
                <div class="vocabulary-card-content-favorite">
                  <div
                      class="absolute h-[50px] bg-[#FF7C79] top-[40px] left-[25px] w-[3px] bg-[#FF7C79] rounded-full"></div>
                  <div class="w-full  flex justify-between">
                    <div class="flex flex-col justify-around">
                      <div class="pt-[23px] pl-[32px] pb-[13px]">
                        <div class="text-[26px]  font-gen-jyuu-gothic-medium text-bold ">
                          Từ vựng của tôi
                        </div>
                        <div class="vocabulary-card-count font-gen-jyuu-gothic text-[20px]">
                          {{ favorite ? favorite.data.length : 0 }} từ vựng
                        </div>
                      </div>
                      <div class="pl-[32px] pb-[23px]">
                        <div class="font-gen-jyuu-gothic text-[20px]">Từ thêm gần đây</div>
                        <div class="favorite-word-new text-[24px]">
                          <span v-html="favorite ? favorite.lastFlashcard.value.word : ''"></span>
                        </div>
                        <div class="time-add-new-word font-beanbag-regular text-[20px] text-[#757575]">
                          {{ favorite ? favorite.data[0].convert_time : '' }}
                        </div>
                      </div>
                    </div>
                    <div>
                      <img src="/images/vocabulary/bg-favorite-overview.svg" alt=""
                           class="h-full object-cover">
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </template>

        <!-- Search Content -->
        <template v-if="currentView === typesView.SEARCH">
          <div class="section-content search-content" v-loading="isLoadingSearch">
            <!-- Hiển thị kết quả tìm kiếm nếu có -->
            <div class="search-result" v-if="searchResults.length > 0">
              <div class="search-result-items">
                <div class="search-result-item" v-for="(item, index) in searchResults" :key="index">
                  <div class="search-result-item-content border-b border-[#D0D3DA] border-bottom-[1px] mb-3 pb-3">
                    <div class="search-result-item-title font-gen-jyuu-gothic-medium text-black text-[20px]">
                      <div v-html="item.value.word"></div>
                    </div>
                    <div class="search-result-item-description font-gen-jyuu-gothic text-[18px] text-[#757575]">
                      {{ item.value.meaning }}
                    </div>
                    <div
                        class="search-result-item-specialized w-fit font-averta-regular text-[16px] text-[#FFFFFF] bg-[#B3B3B3] rounded-full px-3 py-1">
                      {{ item.course_name }}
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Hiển thị khi không có kết quả tìm kiếm hoặc searchQuery rỗng -->
            <div class="search-not-found" v-else>
              <div class="search-not-found-image">
                <img src="/images/vocabulary/bg-search-not-found.svg" alt="Không tìm thấy kết quả">
              </div>
              <!--              <div class="search-not-found-text" v-if="searchQuery">-->
              <!--                Không tìm thấy kết quả nào cho "{{ searchQuery }}"-->
              <!--              </div>-->
              <!--              <div class="search-not-found-text" v-else>-->
              <!--                Nhập từ vựng bạn muốn tìm kiếm-->
              <!--              </div>-->
            </div>
          </div>
        </template>

        <!-- Specialized Content -->
        <template v-if="currentView === typesView.SPECIALIZED">
          <div class="section-content specialized-content">
            <flash-card :course-id="courseSpecializedActive"
                        :category-data="categories.find(category => category.id === courseSpecializedActive)"></flash-card>
          </div>
        </template>

        <!-- Favorite Content -->
        <template v-if="currentView === typesView.FAVORITE">
          <div class="section-content favorite-content">
            <!--            header view favorite gồm 3 phần: nút back, title, ô search cùng nằm thẳng hàng. nút back và title sát trai, ô search sát phải-->
            <div class="flex justify-between items-center mb-10">
              <div class="flex items-center">
                <div class="vocabulary-search-btn-back h-[65px] w-[65px]"
                     @click="currentView = typesView.OVERVIEW; isLoadingFavorite = false; searchResults = []; searchQuery = ''">
                  <i class="fas fa-arrow-left"></i>
                </div>
                <div class="vocabulary-section-favorite-title">
                  <div class="flex font-beanbag-medium text-[24px] text-[#07403F] items-center">
                    Từ vựng yêu thích
                    <i class="fas fa-heart text-[#FF7C79] ml-2"></i>
                  </div>
                  <div class="font-beanbag-regular text-[20px] text-[#07403F]">{{ allFavoriteFlashcards.length }} từ
                  </div>
                </div>
              </div>
              <div
                  class="vocabulary-favorite-search-bar flex items-center rounded-full bg-white shadow-md h-[65px] px-[20px] transition-all duration-300 flex-1 min-w-0 max-w-[400px]">
                <div class="search-icon">
                  <i class="fas fa-search"></i>
                </div>
                <input
                    type="text"
                    v-model="searchQueryFavorite"
                    placeholder="Nhập từ vựng muốn tìm kiếm"
                    class="search-input leading-[65px]"
                />
              </div>
            </div>

            <!-- category favorite-->
            <div class="flex items-center mb-10">
              <div
                  class="bg-[#CCF8D1] py-2 px-3 rounded-full flex font-beanbag-medium text-[20px] text-[#07403F] items-center mr-5">
                <i class="fas fa-list mr-2"></i>
                Danh sách
              </div>
              <div class="font-beanbag-regular text-[20px] text-[#B3B3B3] flex items-center">
                <img src="/images/icons/tag-flashcard.svg" alt="tag">
                Thẻ
              </div>
            </div>

            <div class="search-result list-favorite" v-if="favoriteFlashcards.length > 0" v-loading="isLoadingFavorite">
              <div class="search-result-items">
                <div class="search-result-item cursor-pointer" v-for="(item, index) in favoriteFlashcards" :key="index"
                     @click="openFlashcardPopup(index)">
                  <div class="search-result-item-content border-b border-[#D0D3DA] border-bottom-[1px] mb-3 pb-3">
                    <div class="search-result-item-title font-gen-jyuu-gothic-medium text-black text-[20px]">
                      <span v-html="item.value.word"></span>
                    </div>
                    <div class="search-result-item-description font-gen-jyuu-gothic text-[18px] text-[#757575]">
                      <span v-html="item.value.meaning"></span>
                    </div>
                    <div
                        class="search-result-item-specialized w-fit font-averta-regular text-[16px] text-[#FFFFFF] bg-[#B3B3B3] rounded-full px-3 py-1">
                      {{ item.course_name }}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </template>
      </div>
    </div>

    <el-dialog
        :visible.sync="showDialogInfo"
        max-width="1097px"
        top="8vh"
        width="80%"
        height="80vh"
        center
        :show-close="false"
        :close-on-click-modal="true"
        class="dialog-vocabulary-info">
      <!-- Slot tùy chỉnh tiêu đề -->
      <template #title>
        <div
            class="custom-title pt-[29px] pl-[43px] pr-[38px] flex justify-between items-end rounded-[32px]">
          <div class="flex items-center gap-2">
            <div class="text-[#073A3B] font-beanbag text-[24px] bg-[#CCF8D1] px-5 py-1 rounded-full">
              Rừng ngôn từ
            </div>
            <div class="text-[#0C403F] text-[27px]">
              言葉の森
            </div>
          </div>

          <el-button icon="el-icon-close" circle @click="showDialogInfo = false"></el-button>
        </div>
      </template>

      <div
          class="dialog-wrapper overflow-y-scroll h-[60vh] pl-[43px] py-[12px] pr-[38px] mx-[43px] my-[12px] custom-scrollbar">
        <div class="font-beanbag-regular text-[#07403F] text-[20px] mb-3 break-normal">
          Là nơi thống kê chi tiết số lượng từ vựng nằm ở các <span
            class="italic font-beanbag-medium break-normal">mức độ ghi nhớ</span>
          khác nhau thông qua phương pháp học <span class="italic font-beanbag-medium break-normal">Lặp lại ngắt quãng (Spaced Repetition)</span>,
          giúp bạn hoàn toàn làm chủ quá trình học của bản thân!
        </div>
        <div class="text-center mb-5">
          <img src="/images/vocabulary/img-popup-info.svg" alt="Thông tin">
        </div>
        <div class="mb-5">
          <ul class="list-disc font-beanbag-regular text-[#07403F] text-[20px]">
            <li class=" py-[5px]" style="list-style: disc"><span class="font-beanbag-medium bg-[#FFD1B0] rounded-[8px]">Đã học</span>:
              Những từ bạn mới được học.
            </li>
            <li class=" py-[5px]" style="list-style: disc"><span class="font-beanbag-medium bg-[#FFEF94] rounded-[8px]">Tạm nhớ</span>:
              Những từ bạn vẫn nhớ sau 1 thời gian ngắn không sử dụng tới.
            </li>
            <li class=" py-[5px]" style="list-style: disc"><span class="font-beanbag-medium bg-[#D0FCA1] rounded-[8px]">Ghi nhớ</span>:
              Từ vựng đã được lưu vào trí nhớ dài hạn của bạn rồi!
            </li>
            <li class=" py-[5px]" style="list-style: disc"><span class="font-beanbag-medium bg-[#80FBA6] rounded-[8px]">Thuộc lòng</span>:
              Tuyệt vời! Những từ vựng này giờ là của bạn!
            </li>
          </ul>
        </div>
        <div class="font-beanbag-regular text-[#07403F] text-[20px]">
          Chăm chỉ học tập, bạn sẽ có được một <span class="font-beanbag-medium italic">Rừng ngôn từ</span> thật phong
          phú, đa dạng! Cùng học bạn nhé!
        </div>

        <!--        btn ở giữa khi click đóng dialog-->
        <div class="flex justify-center mt-5">
          <button
              class="px-12 py-4 flex items-center justify-center bg-[#57D061] rounded-full cursor-pointer drop-shadow"
              @click="showDialogInfo = false">
            <span class="text-btn font-beanbag-medium text-[#07403F] text-xl mr-1 text-[20px]">
              Mình đã hiểu!
            </span>
          </button>
        </div>
      </div>
    </el-dialog>


    <!-- Flashcard Popup -->
    <div class="flashcard-popup-overlay" v-if="showFlashcardPopup" @click.self="closeFlashcardPopup">
      <div class="flashcard-popup">
        <div class="flashcard-popup-content">
          <button class="flashcard-nav-button prev" @click="prevFlashcard" :disabled="currentFlashcardIndex === 0">
            <i class="fas fa-chevron-left"></i>
          </button>

          <div class="flashcards-wrap cursor-pointer" style="width: 600px;">
            <div
                v-for="(card, index) in favoriteFlashcards"
                :key="card.id"
                :data-id="card.id"
                class="card-item"
                v-show="index === currentFlashcardIndex"
                :class="{
                  'stackedcards-active': index === 0,
                  'stackedcards-top': true,
                  'stackedcards--animatable': true,
                  'stackedcards-origin-top': true
                }">
              <div class="card-inner" :id="`card-inner-${card.id}`" @click="flipCard(card.id, $event)">
                <div class="card__face card__face--jp card__face--front" :class="{'noFlip': card.is_test}">
                  <div class="card-wrap p-4 h-[90%] overflow-y-auto relative">
                    <div class="card_header flex items-center">
                      <div
                          class="card_audio noFlip w-[48px] h-[48px] rounded-full bg-[#E1EBFF] flex items-center justify-center mr-4 cursor-pointer"
                          @click="playAudio('audio')"
                      >
                        <svg class="noFlip" width="24" height="24" viewBox="0 0 24 24" fill="none"
                             xmlns="http://www.w3.org/2000/svg">
                          <path
                              d="M18 16.7503C17.84 16.7503 17.69 16.7003 17.55 16.6003C17.22 16.3503 17.15 15.8803 17.4 15.5503C18.97 13.4603 18.97 10.5403 17.4 8.45027C17.15 8.12027 17.22 7.65027 17.55 7.40027C17.88 7.15027 18.35 7.22027 18.6 7.55027C20.56 10.1703 20.56 13.8303 18.6 16.4503C18.45 16.6503 18.23 16.7503 18 16.7503Z"
                              fill="#4E87FF"/>
                          <path
                              d="M19.83 19.2503C19.67 19.2503 19.52 19.2003 19.38 19.1003C19.05 18.8503 18.98 18.3803 19.23 18.0503C21.9 14.4903 21.9 9.51027 19.23 5.95027C18.98 5.62027 19.05 5.15027 19.38 4.90027C19.71 4.65027 20.18 4.72027 20.43 5.05027C23.5 9.14027 23.5 14.8603 20.43 18.9503C20.29 19.1503 20.06 19.2503 19.83 19.2503Z"
                              fill="#4E87FF"/>
                          <path opacity="0.4"
                                d="M15.75 7.40972V16.5897C15.75 18.3097 15.13 19.5997 14.02 20.2197C13.57 20.4697 13.07 20.5897 12.55 20.5897C11.75 20.5897 10.89 20.3197 10.01 19.7697L7.09 17.9397C6.89 17.8197 6.66 17.7497 6.43 17.7497H5.5V6.24972H6.43C6.66 6.24972 6.89 6.17972 7.09 6.05972L10.01 4.22972C11.47 3.31972 12.9 3.15972 14.02 3.77972C15.13 4.39972 15.75 5.68972 15.75 7.40972Z"
                                fill="#4E87FF"/>
                          <path
                              d="M5.5 6.25V17.75H5C2.58 17.75 1.25 16.42 1.25 14V10C1.25 7.58 2.58 6.25 5 6.25H5.5Z"
                              fill="#4E87FF"/>
                        </svg>
                      </div>
                      <div class="card_title font-gen-jyuu-gothic-medium text-2xl text-[#757575]"
                           v-html="card.value.word_stress">
                      </div>
                    </div>
                    <div class="card_content min-h-[calc(100%-34px)] flex flex-col">
                      <div
                          class="content-text font-gen-jyuu-gothic-medium text-[56px] text-[#07403F] items-center justify-center flex"
                          style="flex-grow: 1;"
                          v-html="card.value.word">
                      </div>
                      <div v-if="card.value.front_image" class="content-img text-center p-[40px]">
                        <img
                            :src="`https://video-test.dungmori.com/images/${card.value.front_image}`">
                      </div>
                      <div v-if="card.value.example.length" class="example-wrap">
                        <p class="w-fit font-beanbag text-base text-[#07403F] bg-[#CEFFD8] rounded-[10px] p-2 mb-2">
                          Ví dụ
                        </p>
                        <div class="list-example">
                          <template v-for="(example, index) in card.value.example">
                            <div class="example-item mb-1">
                              <svg v-if="example.audio" class="w-[36px] h-[36px] noFlip cursor-pointer" width="24"
                                   height="24" viewBox="0 0 24 24" fill="none"
                                   xmlns="http://www.w3.org/2000/svg"
                                   @click="playAudio(card.id + '_example_' + index, example.audio)">
                                <path
                                    d="M18 16.7503C17.84 16.7503 17.69 16.7003 17.55 16.6003C17.22 16.3503 17.15 15.8803 17.4 15.5503C18.97 13.4603 18.97 10.5403 17.4 8.45027C17.15 8.12027 17.22 7.65027 17.55 7.40027C17.88 7.15027 18.35 7.22027 18.6 7.55027C20.56 10.1703 20.56 13.8303 18.6 16.4503C18.45 16.6503 18.23 16.7503 18 16.7503Z"
                                    fill="#4E87FF"/>
                                <path
                                    d="M19.83 19.2503C19.67 19.2503 19.52 19.2003 19.38 19.1003C19.05 18.8503 18.98 18.3803 19.23 18.0503C21.9 14.4903 21.9 9.51027 19.23 5.95027C18.98 5.62027 19.05 5.15027 19.38 4.90027C19.71 4.65027 20.18 4.72027 20.43 5.05027C23.5 9.14027 23.5 14.8603 20.43 18.9503C20.29 19.1503 20.06 19.2503 19.83 19.2503Z"
                                    fill="#4E87FF"/>
                                <path opacity="0.4"
                                      d="M15.75 7.40972V16.5897C15.75 18.3097 15.13 19.5997 14.02 20.2197C13.57 20.4697 13.07 20.5897 12.55 20.5897C11.75 20.5897 10.89 20.3197 10.01 19.7697L7.09 17.9397C6.89 17.8197 6.66 17.7497 6.43 17.7497H5.5V6.24972H6.43C6.66 6.24972 6.89 6.17972 7.09 6.05972L10.01 4.22972C11.47 3.31972 12.9 3.15972 14.02 3.77972C15.13 4.39972 15.75 5.68972 15.75 7.40972Z"
                                      fill="#4E87FF"/>
                                <path
                                    d="M5.5 6.25V17.75H5C2.58 17.75 1.25 16.42 1.25 14V10C1.25 7.58 2.58 6.25 5 6.25H5.5Z"
                                    fill="#4E87FF"/>
                              </svg>
                              <div class="ml-2 font-beanbag-regular text-2xl flex items-start text-balance">
                                {{ index + 1 }}. <span class="ml-2" v-html="example.example"></span>
                              </div>
                            </div>
                          </template>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="card-footer noFlip flex justify-end border-t-[1px] border-[#D0D3DA]">
                    <svg class="m-3 noFlip" width="33" height="28" viewBox="0 0 33 28" fill="none"
                         xmlns="http://www.w3.org/2000/svg">
                      <path
                          d="M23.8333 0.5C28.6377 0.5 32.4997 4.21737 32.5 8.78727C32.4819 14.1271 29.8923 18.3826 26.6876 21.5156C23.4787 24.6525 19.6885 26.627 17.3726 27.393C17.163 27.4593 16.8527 27.5 16.5137 27.5C16.1782 27.5 15.8562 27.4601 15.6229 27.3915C13.3062 26.6243 9.50962 24.6502 6.29845 21.5153C3.08901 18.3822 0.5 14.127 0.5 8.78779C0.5 4.21765 4.36212 0.5 9.16667 0.5C12.0183 0.5 14.5145 1.79375 16.1076 3.81099L16.5 4.30783L16.8924 3.81099C18.4855 1.79375 20.9817 0.5 23.8333 0.5Z"
                          fill="#FF7C79" stroke="#FF7C79"/>
                    </svg>
                  </div>
                </div>
                <div class="card__face card__face--vi card__face--back">
                  <div class="card-wrap-back p-4 h-[60%] overflow-y-auto flex flex-col relative">
                    <div
                        class="card_content_back font-beanbag-medium text-5xl items-center justify-center flex text-[#07403F] mb-5"
                        style="flex-grow: 1"
                    >
                      <div class="text-center" v-html="card.value.meaning"></div>
                    </div>
                    <div class="font-beanbag-medium text-[#757575] text-xl text-center text-[#07403F]"
                         v-html="card.value.kanji_meaning"></div>
                    <div class="card_img_back p-[40px] content-img text-center" v-if="card.value.back_image">
                      <img
                          :src="`https://video-test.dungmori.com/images/${card.value.back_image}`">
                    </div>
                    <div v-if="card.value.meaning_example.length" class="example-wrap">
                      <p class="w-fit font-beanbag text-base text-[#07403F] bg-[#CEFFD8] rounded-[10px] p-2 mb-2">
                        Ví dụ
                      </p>
                      <div class="list-example">
                        <template v-for="(meaning_example, index) in card.value.meaning_example">
                          <div class="example-item flex items-center mb-1">
                            <div class="ml-2 font-averta-regular text-2xl flex items-start">
                              {{ index + 1 }}. <span class="ml-1" v-html="meaning_example"></span>
                            </div>
                          </div>
                        </template>
                      </div>
                    </div>
                  </div>
                  <div class="how-remember-wrap px-4 h-[40%] flex flex-col">
                    <div class="how-remember-wrap-header flex  mb-5">
                      <div class="font-beanbag-medium text-[#757575] text-xl">
                        Cách nhớ
                      </div>
                      <div class="border-b-[1px] border-[#D0D3DA] flex-1 m-[7px]"></div>
                    </div>
                    <div v-if="card.comment && card.comment.user_info"
                         class="how-remember-wrap-content grid grid-cols-[40px_auto] gap-4"
                         style="">
                      <div class="how-remember-wrap-avatar w-[28px]">
                        <img class="rounded-full"
                             :src="`/cdn/avatar/small/${card.comment.user_info.avatar}`">
                      </div>
                      <div class="how-remember-wrap-info flex text-[#073A3B]">
                          <span class="font-averta-bold">
                            {{ card.comment.user_info.name }}・
                          </span>
                        <span class="font-averta-regular">
                            {{ card.comment.time_created }}
                          </span>
                        <svg v-if="card.comment.pin" width="16" height="16" viewBox="0 0 16 16" fill="none"
                             xmlns="http://www.w3.org/2000/svg">
                          <path
                              d="M4.54059 9.86604C4.49626 9.83523 4.45013 9.81231 4.41581 9.77792C3.83372 9.198 3.25271 8.617 2.67312 8.03457C2.59482 7.95577 2.51759 7.87338 2.45323 7.78312C2.23084 7.47256 2.23799 7.15555 2.48076 6.86004C2.75322 6.52871 3.11005 6.30626 3.49405 6.13218C4.1902 5.81697 4.90958 5.70163 5.65114 5.95953C5.851 6.02902 6.03657 6.14078 6.21856 6.22854C6.924 5.52396 7.63981 4.80935 8.36242 4.08758C8.13788 3.75446 8.06279 3.37477 8.17649 2.96821C8.44966 1.99177 9.64601 1.67476 10.3836 2.38543C10.7887 2.77586 11.1827 3.17776 11.5803 3.57608C12.2554 4.25271 12.9347 4.92469 13.6019 5.60885C14.4236 6.45133 13.8447 7.6316 13.0213 7.84616C12.6641 7.93929 12.3312 7.88628 12.0212 7.69106C11.9354 7.63697 11.8721 7.63733 11.7981 7.71148C11.1216 8.39277 10.4426 9.07191 9.76472 9.75177C9.75864 9.75786 9.75542 9.76682 9.74648 9.78186C9.75363 9.79691 9.76078 9.81732 9.77223 9.83523C10.3089 10.6745 10.3089 11.5489 9.90023 12.4272C9.74755 12.7553 9.5248 13.0526 9.31814 13.3527C9.24735 13.4555 9.13579 13.5365 9.0296 13.6074C8.81114 13.7532 8.57373 13.7629 8.34561 13.635C8.22297 13.5662 8.10677 13.4767 8.00701 13.3775C7.36128 12.7352 6.71877 12.089 6.08019 11.4392C5.99331 11.3508 5.94289 11.3429 5.84278 11.4246C4.85559 12.2341 3.86411 13.0382 2.87299 13.8431C2.60555 14.0605 2.32702 14.0509 2.12286 13.822C1.99378 13.6773 1.96304 13.515 2.04277 13.3384C2.07745 13.2618 2.12286 13.1887 2.17256 13.1206C2.9209 12.0915 3.67104 11.0635 4.42046 10.0351C4.45407 9.9889 4.4866 9.94198 4.53988 9.8664L4.54059 9.86604Z"
                              fill="#B3B3B3"/>
                        </svg>
                      </div>
                      <div class="col-start-2 font-averta-regular text-[#07403F]" style="display: -webkit-box;
                            -webkit-line-clamp: 2;
                            -webkit-box-orient: vertical;
                            overflow: hidden;
                            text-overflow: ellipsis;"
                      >
                        {{ card.comment.content }}
                      </div>
                      <div class="col-start-2 flex justify-between items-center">
                        <div class="font-averta-regular text-[#009951] flex">
                          <div class="flex items-center mr-5">
                            <svg class="mr-1 noFlip" width="13" height="12" viewBox="0 0 13 12" fill="none"
                                 xmlns="http://www.w3.org/2000/svg">
                              <path
                                  d="M9.38889 0.723633C11.1046 0.723633 12.4996 2.1159 12.5 3.84852C12.493 5.95749 11.518 7.64456 10.2933 8.89844C9.06415 10.1569 7.61344 10.9461 6.74434 11.2477C6.70298 11.2611 6.61724 11.2756 6.50542 11.2756C6.39725 11.2756 6.30516 11.262 6.25054 11.2459C5.38046 10.9429 3.92943 10.1541 2.70106 8.89818C1.47455 7.64419 0.5 5.95749 0.5 3.8492C0.5 2.11626 1.89522 0.723633 3.61111 0.723633C4.6312 0.723633 5.52625 1.20724 6.10079 1.96912L6.5 2.49851L6.89921 1.96912C7.47375 1.20724 8.3688 0.723633 9.38889 0.723633Z"
                                  :fill="card.comment.comment_like.length ? '#009951' : 'none'" stroke="#009951"/>
                            </svg>
                            {{ card.comment.count_like }}
                          </div>
                          <div v-if="card.comment && card.comment.replies">
                            {{ card.comment.replies.length }} Trả lời
                          </div>
                        </div>
                        <div class="underline decoration-solid text-[#009951] cursor-pointer noFlip"
                             @click="toggleCommentTab('open')">
                          Xem thêm >>
                        </div>
                      </div>
                    </div>
                    <div v-else
                         class="items-center flex grow "
                         style="flex-grow: 1;">
                      <div
                          class="underline decoration-solid font-averta-regular text-[#009951] cursor-pointer noFlip"
                          @click="toggleCommentTab('open')">
                        Đóng góp cách nhớ của bạn >>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!--          <div class="flashcard-container" @click="flipFlashcard">-->
          <!--            <div class="flashcard" :class="{ 'flipped': isFlashcardFlipped }">-->
          <!--              <div class="flashcard-front">-->
          <!--                <div class="flashcard-word">{{ currentFlashcard.word }}</div>-->
          <!--                <div class="flashcard-reading">{{ currentFlashcard.reading }}</div>-->
          <!--              </div>-->
          <!--              <div class="flashcard-back">-->
          <!--                <div class="flashcard-meaning">{{ currentFlashcard.meaning }}</div>-->
          <!--                <div class="flashcard-example" v-if="currentFlashcard.example">-->
          <!--                  <div class="example-jp">{{ currentFlashcard.example.japanese }}</div>-->
          <!--                  <div class="example-vi">{{ currentFlashcard.example.vietnamese }}</div>-->
          <!--                </div>-->
          <!--              </div>-->
          <!--            </div>-->
          <!--          </div>-->

          <button class="flashcard-nav-button next" @click="nextFlashcard"
                  :disabled="currentFlashcardIndex === favoriteFlashcards.length - 1">
            <i class="fas fa-chevron-right"></i>
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import SearchBar from "../course/components/SearchBar.vue";
import FlashCard from "./components/FlashCard.vue";
import ChartVocabulary from "./components/Chart.vue";
import axios from "axios";
import _ from "lodash";
import {removeFontSizeStyle} from "./../module/flashcard.js";

const TYPES_VIEW = {
  OVERVIEW: 1,
  SEARCH: 2,
  SPECIALIZED: 3,
  FAVORITE: 4,
}

export default {
  name: "vocabulary",
  components: {ChartVocabulary, FlashCard, SearchBar},
  props: {
    jlpt: {
      type: Object,
      required: true,
      default: () => {
      }
    },
    specialized: {
      type: Object,
      required: true,
      default: () => {
      }
    },
    favorite: {
      type: Object,
      required: true,
      default: () => []
    }
  },
  watch: {
    searchFocused(newValue, oldValue) {
      if (newValue) {
        this.currentView = TYPES_VIEW.SEARCH;
      }
    },
    searchQuery(newValue, oldValue) {
      console.log(`searchQuery: `, newValue);
      if (newValue) {
        this.currentView = TYPES_VIEW.SEARCH;
        this.searchWord();
      } else {
        this.searchResults = [];
      }
    },
    searchQueryFavorite(newValue, oldValue) {
      if (newValue) {
        // this.currentView = TYPES_VIEW.SEARCH;
        this.searchWordFavorite();
      } else {
        this.favoriteFlashcards = this.allFavoriteFlashcards;
      }
    },
    currentView(newValue, oldValue) {
      console.log(`currentView: `, newValue);
      if (newValue === TYPES_VIEW.OVERVIEW) {
        this.getOverviewFavorite();
        this.getCourse();
      }
    }
  },
  data() {
    return {
      searchQuery: '',
      searchFocused: false,
      searchQueryFavorite: '',
      showFlashcardPopup: false,
      isFlashcardFlipped: false,
      currentFlashcardIndex: 0,
      favoriteFlashcards: [],
      allFavoriteFlashcards: [],
      currentView: TYPES_VIEW.OVERVIEW,
      typesView: TYPES_VIEW,
      chart: null,
      categories: [],
      searchResults: [],
      showDialogInfo: false,
      card: {
        "id": 103569,
        "lesson_id": 10846,
        "exam_part_id": null,
        "skill": 1,
        "mondai_id": null,
        "type": 17,
        "type_ld": null,
        "server": null,
        "video_name": null,
        "video_title": null,
        "value": {
          "word": "<p>わたし</p>",
          "word_stress": "<p>わ<span style=\"text-decoration:overline\">たしr</span></p>",
          "word_type": "Động từ",
          "meaning": "<p>3</p>",
          "audio": null,
          "front_image": null,
          "back_image": null,
          "example": [
            {
              "example": "<p><span style=\"color:#ef6d13\">わたし</span>はきょうしです。</p>",
              "audio": null
            }
          ],
          "meaning_example": [
            "<p><span style=\"color:#ef6d13\">T&ocirc;i</span> l&agrave; gi&aacute;o vi&ecirc;n.</p>"
          ],
          "quiz_question": [],
          "kanji_meaning": null
        },
        "suggest": null,
        "explain": null,
        "explain_mp3": null,
        "value_data": null,
        "grade": "",
        "sort": 3,
        "show": 1,
        "is_quiz": 0,
        "video_full": 0,
        "updated_at": "2025-04-25T01:45:14.000000Z",
        "created_at": "2025-04-10T01:40:02.000000Z",
        "answers": [],
        "comment": {
          "id": 463617,
          "content": "hi",
          "user_id": 540309,
          "count_like": 0,
          "created_at": "2025-05-16 09:54:28",
          "pin": 0,
          "parent_id": 0,
          "table_id": 103569,
          "table_name": "flashcard",
          "user_info": {
            "email": "<EMAIL>",
            "name": "Dinhsuu",
            "avatar": "1711587724_0_76076.jpeg",
            "userId": 540309
          },
          "time_created": "16/05/2025 09:54",
          "replies": [
            {
              "id": 463618,
              "table_id": 103569,
              "table_name": "flashcard",
              "user_id": 540308,
              "admin_log": null,
              "kwadmin_id": null,
              "content": "122",
              "img": null,
              "audio": null,
              "rate": 0,
              "is_tester": 0,
              "parent_id": 463617,
              "count_like": 0,
              "ulikes": null,
              "readed": 0,
              "tag_data": null,
              "status": 0,
              "pin": 0,
              "is_correct": 0,
              "updated_at": "2025-05-16 09:54:44",
              "created_at": "2025-05-16 09:54:44",
              "user_info": {
                "email": "<EMAIL>",
                "name": "Thu Vũ",
                "avatar": "1711587724_0_76076.jpeg",
                "userId": 540308
              },
              "time_created": "16/05/2025 09:54",
              "replies": null,
              "table_info": {
                "id": "",
                "course_id": "",
                "name": "",
                "SEOurl": "",
                "course_url": ""
              }
            }
          ],
          "table_info": {
            "id": "",
            "course_id": "",
            "name": "",
            "SEOurl": "",
            "course_url": ""
          },
          "comment_like": []
        }
      },
      courseSpecializedActive: null,
      isLoadingChart: false,
      isLoadingSearch: false,
      isLoadingFavorite: false,
      favorite: [],
      authUser: authUser
    }
  },
  computed: {
    chartData() {
      const data = {
        learned: 0,
        temporary: 0,
        memorized: 0,
        mastered: 0,
        super_mastered: 0,
        totalWords: 0,
      };

      // Cộng dồn dữ liệu từ các danh mục được chọn
      console.log('categories: ', this.categories);
      this.categories.forEach(category => {
        if (category.selected) {
          console.log(`category: `, category);

          data.learned = category.data.learned + data.learned + category.data.temporary + category.data.memorized + category.data.mastered;
          data.temporary = category.data.temporary + data.temporary + category.data.memorized + category.data.mastered;
          data.memorized = category.data.memorized + data.memorized + category.data.mastered;
          data.mastered = category.data.mastered + data.mastered;
          data.super_mastered = category.data.super_mastered + data.super_mastered;
          data.totalWords = category.data.word_count ? category.data.word_count + data.totalWords : data.totalWords;
        }
      });
      console.log(`chartData: `, data);
      return data;
    },

    // Lấy flashcard hiện tại
    // currentFlashcard() {
    //   return this.favoriteFlashcards[this.currentFlashcardIndex] || {};
    // }
  },
  methods: {
    // Mở popup flashcard
    openFlashcardPopup(index = 0) {
      this.currentFlashcardIndex = index;
      this.isFlashcardFlipped = false;
      this.showFlashcardPopup = true;
      document.body.style.overflow = 'hidden'; // Ngăn cuộn trang khi popup mở
    },

    // Đóng popup flashcard
    closeFlashcardPopup() {
      this.showFlashcardPopup = false;
      document.body.style.overflow = ''; // Cho phép cuộn trang trở lại
    },

    // Lật flashcard
    flipCard(id, event) {
      console.log(`id: `, id);
      // Kiểm tra xem phần tử được click hoặc cha của nó có class noFlip, card_audio, hoặc là thẻ button không
      const isNonFlippable = event.target.closest('.noFlip, .card_audio, button');
      if (isNonFlippable) {
        console.log('Click on non-flippable element, skipping flip');
        return; // Không lật thẻ nếu click vào phần tử không cho phép
      }

      const cardElement = document.querySelector(`#card-inner-${id}`);
      if (cardElement) {
        cardElement.classList.toggle('flip');
      }
    },

    // Chuyển đến flashcard trước
    prevFlashcard() {
      if (this.currentFlashcardIndex > 0) {
        this.currentFlashcardIndex--;
        this.isFlashcardFlipped = false;
      }
    },

    // Chuyển đến flashcard tiếp theo
    nextFlashcard() {
      if (this.currentFlashcardIndex < this.favoriteFlashcards.length - 1) {
        this.currentFlashcardIndex++;
        this.isFlashcardFlipped = false;
      }
    },

    // Thêm phương thức initCategories
    initCategories() {
      // Xóa danh sách categories hiện tại
      this.categories = [];

      // Thêm các danh mục JLPT
      if (this.jlpt) {
        this.jlpt.forEach(item => {
          console.log('item: ', item);
          const data = {
            learned: 0,
            temporary: 0,
            memorized: 0,
            mastered: 0,
            super_mastered: 0,
            word_count: item.word_count
          };

          this.categories.push({
            name: `Từ vựng ${item.name}`,
            level: item.name,
            id: item.id,
            selected: item.name.includes('N5'), // Mặc định chọn N5
            data: data,
            ...Object.fromEntries(
                Object.entries(item).filter(([key]) => key !== 'name')
            )
          });
        });
      }

      // Thêm các danh mục chuyên ngành
      if (this.specialized && this.specialized.data) {
        this.list_vocabulary.specialized.data.forEach(item => {
          const data = {
            learned: 0,
            temporary: 0,
            memorized: 0,
            mastered: 0,
            super_mastered: 0,
          };

          this.categories.push({
            name: item.name,
            id: item.id,
            selected: false,
            data: data,
            ...Object.fromEntries(
                Object.entries(item).filter(([key]) => key !== 'name')
            )
          });
        });
      }

      console.log('categories: ', this.categories);
    },

    playAudio(id = null, audioUrl = '') {
      console.log(`Play audio for id: ${id}, url: ${audioUrl}`);
      console.log(`this.$root: `, this.$root);
      if (this.$root.toggleMp3) {
        this.$root.toggleMp3(id, 'flashcard', `https://video-test.dungmori.com/audio/${audioUrl}`);
      } else {
        console.error('toggleMp3 method not found in root instance');
      }
    },

    openCourseFlashCardDetail(courseId) {
      // kiểm tra đã đăng nhập chưa. chưa thì hiện popup bắt đăng nhập
      if (!this.authUser.id) {
        //emit sang parent để hiện popup đăng nhập
        this.$emit('open-login-popup');
        return;
      }
      console.log(`courseId: `, courseId);
      let course = this.categories.find(category => category.id === courseId);
      console.log(`course: `, course);
      if (!course || !course.word_count || course.word_count === 0) {
        // this.$message.error('Course not found or no words');
        // return;
      }
      console.log(`openCourseFlashCardDetail: `, courseId);
      this.courseSpecializedActive = courseId;
      this.currentView = this.typesView.SPECIALIZED;
    },

    getCourse() {
      this.isLoadingChart = true;
      console.log(`getCourse--------------------->`);
      let params = {
        course_id: this.categories.map(category => category.id),
        type: 'normal'
      };
      axios.get('/vocabulary/get-course', {params})
          .then(response => {
            this.isLoadingChart = false;
            console.log(`response: `, response);
            let courses = response.data.data;
            courses.forEach(course => {
              console.log(`course: `, course);
              let data = JSON.parse(course.data);
              console.log(`data: `, data);

              let groupedData = _.groupBy(data, 'p');
              console.log(`groupedData: `, groupedData);

              let category = this.categories.find(category => category.id === course.course_id);
              console.log(`category: `, category);
              if (category) {
                category.data.learned = groupedData[1] ? groupedData[1].length : 0;
                category.data.temporary = groupedData[2] ? groupedData[2].length : 0;
                category.data.memorized = groupedData[3] ? groupedData[3].length : 0;
                category.data.mastered = groupedData[4] ? groupedData[4].length : 0;
                category.data.super_mastered = groupedData[5] ? groupedData[5].length : 0;
              }
              console.log(`category: `, category);
              console.log(`categories: `, this.categories);

              // kích hoạt lại chartData để cập nhật lại chart
              this.chartData = this.chartData;
            });
          })
          .catch(error => {
            console.error(error);
            this.isLoadingChart = false;
          });
    },

    getOverviewFavorite() {
      this.isLoadingFavorite = true;
      axios.get('/vocabulary/get-overview-favorite')
          .then(response => {
            this.isLoadingFavorite = false;
            console.log('response: ', response);
            this.favorite = response.data.data;
          })
          .catch(error => {
            console.error(error);
            this.isLoadingFavorite = false;
          });
    },

    scrollToSection(sectionId, courseId) {
      console.log(`scrollToSection: `, sectionId, courseId);
      console.log(`this.courseSpecializedActive: `, this.courseSpecializedActive);
      console.log(`this.currentView: `, this.currentView);
      if (sectionId === 'vocabulary-card-favorite' && (this.currentView === this.typesView.SPECIALIZED || this.currentView === this.typesView.SEARCH)) {
        this.openViewFavorite();
        return;
      }

      if (courseId !== this.courseSpecializedActive && (this.currentView === this.typesView.SPECIALIZED || this.currentView === this.typesView.FAVORITE)) {
        this.openCourseFlashCardDetail(courseId);
        return;
      }
      const element = document.getElementById(sectionId);
      if (element) {
        // scroll sao cho element nằm ở giữa viewport
        element.scrollIntoView({behavior: 'smooth', block: 'center'});
        // thêm hiệu ứng highlight cho element
        element.classList.add('highlight');
        setTimeout(() => {
          // khi tắt hiệu ứng highlight thì cho animation để không bị mất hiệu ứng đột ngột
          element.classList.remove('highlight');
        }, 4000);
      }
    },

    searchWord() {
      let searchQuery = this.searchQuery.trim();
      if (searchQuery === '') {
        this.$message.error('Vui lòng nhập từ vựng để tìm kiếm');
        return;
      }
      this.isLoadingSearch = true;
      axios.get('/vocabulary/search-word', {params: {searchQuery}})
          .then(response => {
            this.isLoadingSearch = false;
            this.searchResults = response.data.data.map(item => {
              item.value.word = removeFontSizeStyle(item.value.word);
              return item;
            });
          })
          .catch(error => {
            console.error(error);
            this.isLoadingSearch = false;
          });
    },

    openViewFavorite() {
      this.currentView = this.typesView.FAVORITE;
      this.isLoadingFavorite = true;
      axios.get('/vocabulary/get-favorite')
          .then(response => {
            this.isLoadingFavorite = false;
            console.log('response: ', response);
            let flashcards = response.data.data.flashcards;
            flashcards.map(flashcard => {
              flashcard.value = JSON.parse(flashcard.value);
              flashcard.value.word = removeFontSizeStyle(flashcard.value.word);
              return flashcard;
            });

            this.favoriteFlashcards = flashcards;
            this.allFavoriteFlashcards = flashcards;

            console.log('favoriteFlashcards: ', this.favoriteFlashcards);
          })
          .catch(error => {
            console.error(error);
            this.isLoadingFavorite = false;
          });
    },

    searchWordFavorite() {
      this.isLoadingFavorite = true;
      this.favoriteFlashcards = this.allFavoriteFlashcards.filter(flashcard => {
        return flashcard.value.word.toLowerCase().includes(this.searchQueryFavorite.toLowerCase()) || flashcard.value.meaning.toLowerCase().includes(this.searchQueryFavorite.toLowerCase());
      });
      this.isLoadingFavorite = false;
    }
  },
  mounted() {
    // Không cần khởi tạo gì đặc biệt vì biểu đồ HTML/CSS tự động render
    console.log('Component mounted, chart data:', this.chartData);
  },
  created() {
    console.log("jlpt", this.jlpt);

    // Khởi tạo danh sách categories từ list_vocabulary
    this.initCategories();

    this.getCourse();

    // this.getOverviewFavorite();
  }
}
</script>