<template>
  <div class="w-full">
    <div class="flex justify-between items-center mt-4 w-full">
      <div class="flex items-center gap-3">
        <el-select v-model="teacherType" @change="getList" style="width: 200px">
          <el-option value="online" label="Online"></el-option>
          <el-option value="offline" label="Offline"></el-option>
        </el-select>
        <el-date-picker
          v-model="curDate"
          type="month"
          placeholder="Chọn tháng"
          value-format="yyyy-MM-dd"
          :clearable="false"
          @change="getList"
        >
        </el-date-picker>
        <el-input
          v-model="filters.name"
          placeholder="Tìm kiếm bằng tên"
          @keyup.enter.native="getList"
        ></el-input>
        <el-select v-model="timeFrame" style="width: 500px" @change="getList">
          <el-option value="monthly" label="<PERSON><PERSON><PERSON> tháng - cu<PERSON><PERSON> tháng"></el-option>
          <el-option
            value="26,25"
            label="26 tháng trước - 25 tháng này"
          ></el-option>
        </el-select>

        <el-button type="primary" @click="exportExcel">Xuất excel</el-button>
      </div>
      <strong>Đơn vị: VNĐ</strong>
    </div>
    <el-table
      v-loading="loading"
      :data="tableData"
      border
      height="75vh"
      style="width: 100%; margin-top: 10px"
      header-cell-class-name="user-table__header"
      @cell-click="cellClick"
    >
      <el-table-column prop="id" label="ID" width="80" fixed></el-table-column>
      <el-table-column prop="name" label="Họ và tên" width="200" fixed>
      </el-table-column>
      <el-table-column prop="base_salary" label="Lương cơ bản" width="90" fixed>
        <template slot-scope="scope">
          <div v-for="salary in scope.row.salaries">
            {{ salary.base_salary | decimal }}
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="base_salary" label="Cấp độ" width="80">
        <template slot-scope="scope">
          <div v-for="salary in scope.row.salaries">{{ salary.level }}</div>
        </template>
      </el-table-column>
      <el-table-column prop="base_salary" label="Lớp" width="300">
        <template slot-scope="scope">
          <el-tooltip
            v-for="salary in scope.row.salaries"
            v-if="salary.group || salary.course"
            :key="salary.id"
            class="item"
            effect="dark"
            :content="teacherType === 'online'
            ? salary.group?.name
            : `${salary.course?.department?.name || '-' } | ${salary.course?.info?.periods[0]?.period_code || '-' } | ${ salary.course?.info?.room?.room_name || '-'  } | ${salary.course?.date_start || '-' } - ${salary.course?.date_end || '-' } `"
            placement="right"
          >
            <div class="truncate ... cursor-pointer text-center">
              {{ teacherType === 'online' ? salary.group?.name : `${salary.course?.department?.name || '-'} | ${salary.course?.info?.periods[0]?.period_code || '-'} | ${ salary.course?.info?.room?.room_name || '-' } | ${salary.course?.date_start || '-'} - ${salary.course?.date_end || '-'}` }}
            </div>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column
        prop="join_date"
        label="Ngày vào làm"
        :formatter="dateReverse"
        width="100"
      >
      </el-table-column>
      <el-table-column prop="seniorityMonth" label="Thâm niên" width="100">
      </el-table-column>
      <el-table-column prop="totalGroup" label="Tổng số lớp" width="100">
      </el-table-column>
      <el-table-column prop="latest_increase" label="Áp dụng từ" width="150">
        <template slot-scope="scope">
          <div v-for="salary in scope.row.salaries">
            {{ salary.valid_from | dateTimeToDate }}
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="work_days_count" label="Số ngày công" width="60">
        <template slot-scope="scope">
          <div v-for="salary in scope.row.salaries">
            {{ salary.work_days.length }}
          </div>
        </template>
      </el-table-column>
      <el-table-column
        prop="work_days_salary"
        label="Lương theo số ngày công"
        width="100"
      >
        <template slot-scope="scope">
          <div v-for="salary in scope.row.salaries">
            {{ (salary.work_days.length * salary.base_salary) | decimal }}
            {{ salary.currency | currency }}
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="ref_rewards" label="Thưởng HVLL" width="90">
        <template slot-scope="scope">
          <div v-for="salary in scope.row.salaries">
            {{ salary.ref_rewards | decimal }} {{ salary.currency | currency }}
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="rewards" label="Thưởng" width="90">
        <template slot-scope="scope">
          <div v-for="salary in scope.row.salaries">
            {{ salary.rewards | decimal }} {{ salary.currency | currency }}
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="faults" label="Lỗi phạt" width="90">
        <template slot-scope="scope">
          <div v-for="salary in scope.row.salaries">
            {{ salary.faults | decimal }} {{ salary.currency | currency }}
          </div>
        </template>
      </el-table-column>
      <el-table-column
        prop="salary"
        label="Lương, phụ cấp thực nhận"
        width="100"
      >
        <template slot-scope="scope">
          <div v-for="salary in scope.row.salaries">
            {{
              (salary.work_days.length * salary.base_salary +
                salary.ref_rewards +
                salary.rewards -
                salary.faults)
                | decimal
            }}
            {{ salary.currency | currency }}
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="fixed_salary" label="Thuế">
        <template slot-scope="scope">
          <div v-for="salary in scope.row.salaries">
            {{
              salary.currency === "yen"
                ? Math.round(
                    ((salary.work_days.length * salary.base_salary +
                      salary.ref_rewards +
                      salary.rewards -
                      salary.faults) *
                      (scope.row.tax?.value || 0)) /
                      100
                  )
                : (Math.round(
                    ((salary.work_days.length * salary.base_salary +
                      salary.ref_rewards +
                      salary.rewards -
                      salary.faults) *
                      (scope.row.tax?.value || 0)) /
                      100 /
                      1000
                  ) *
                    1000)
                  | decimal
            }}
            {{ salary.currency | currency }}
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="fixed_salary" label="Lương thực trả" width="100">
        <template slot-scope="scope">
          <div v-for="salary in scope.row.salaries">
            {{
              ((
                salary.work_days.length * salary.base_salary +
                salary.ref_rewards +
                salary.rewards -
                salary.faults
              ).toFixed(0) -
                (salary.currency === "yen"
                  ? Math.round(
                      ((salary.work_days.length * salary.base_salary +
                        salary.ref_rewards +
                        salary.rewards -
                        salary.faults) *
                        (scope.row.tax?.value || 0)) /
                        100
                    )
                  : (Math.round(
                      ((salary.work_days.length * salary.base_salary +
                        salary.ref_rewards +
                        salary.rewards -
                        salary.faults) *
                        (scope.row.tax?.value || 0)) /
                        100 /
                        1000
                    ) *
                      1000)
                    | decimal))
                | decimal
            }}
            {{ salary.currency | currency }}
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="note" label="Ghi chú" width="100">
      </el-table-column>
      <el-table-column
        prop="note_by_accountant"
        label="Ghi chú bởi kế toán"
        width="100"
      >
        <template slot-scope="scope">
          <div>
            <span>{{ scope.row.note_by_accountant }}</span>
            <i class="el-icon-edit-outline" @click="editNote(scope.row)"></i>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <el-drawer
      :title="currentUser ? currentUser.name : ''"
      :visible.sync="salaryDialog"
      direction="ltr"
      size="1200px"
    >
      <salary-management
        v-if="salaryDialog"
        :user="currentUser"
        :time-frame="timeFrame"
        :teacher-type="teacherType"
        @submit="salaryDialog = false"
      />
    </el-drawer>
    <el-drawer
      :title="currentUser ? currentUser.name : ''"
      :visible.sync="workDayDialog"
      direction="ltr"
      size="900px"
    >
      <work-day-management
        v-if="workDayDialog"
        :user="currentUser"
        :time-frame="timeFrame"
        :teacher-type="teacherType"
        @submit="workDayDialog = false"
      />
    </el-drawer>
    <div style="margin-top: 10px">
      <el-pagination
        background
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page.sync="page"
        :page-sizes="[10, 20, 50, 100, 500, 1000, 5000, 10000]"
        :page-size.sync="perPage"
        layout="total, sizes, prev, pager, next, jumper"
        :total="totalResult"
      >
      </el-pagination>
    </div>
  </div>
</template>
<script>
import { mapGetters, mapActions } from "vuex";
import { find, sumBy, groupBy, map, cloneDeep, orderBy } from "lodash";
import axios from "axios";
import moment from "moment";
import SalaryManagement from "../components/SalaryManagement";
import WorkDayManagement from "../components/WorkDayManagement";

export default {
  components: { WorkDayManagement, SalaryManagement },
  data() {
    return {
      page: 1,
      perPage: 20,
      loading: false,
      vendor: "dmr",
      filters: {
        name: "",
      },
      currentUser: null,
      salaryDialog: false,
      workDayDialog: false,
      userDialog: false,
      timeFrame: "26,25",
      teacherType: "online"
    };
  },
  computed: {
    ...mapGetters("teacher", ["users", "totalResult", "currentDate"]),
    ...mapGetters("payoff", ["payoffList"]),
    curDate: {
      get() {
        return this.currentDate;
      },
      set(newValue) {
        this.changeCurrentDate(newValue);
      },
    },
    tableData() {
      return this.users.map((user) => {
        user.name = `${user.last_name} ${user.first_name}`;
        user.base_salary = 0;
        user.latest_increase = null;
        user.work_days_count = user.work_days.length || 0;
        user.work_days_salary = user.work_days_count * user.base_salary;
        user.rewards = 0;
        user.faults = 0;
        user.latest_salaries.forEach((salary) => {
          const check = find(user.salaries, function (o) {
            return o.id === salary.id;
          });
          if (!check) user.salaries.push(salary);
        });
        let workDays = cloneDeep(user.work_days);
        let salaries = orderBy(
          cloneDeep(user.salaries),
          ["level", "valid_from"],
          ["asc", "desc"]
        );
        salaries = salaries.map((salary) => {
          salary.work_days = workDays.filter((day) => {
            if (this.teacherType === 'online') {
              return (
                day.group &&
                day.group_id == salary.group_id &&
                moment(day.date_attendance).isSameOrAfter(salary.valid_from)
              );
            } else {
              return (
                day.course &&
                day.course_id == salary.course_id &&
                moment(day.date_attendance).isSameOrAfter(salary.valid_from)
              );
            }

          });
          const ids = salary.work_days.map((o) => o.id);
          workDays = workDays.filter((o) => !ids.includes(o.id));
          salary.ref_rewards = 0;
          salary.rewards = 0;
          salary.faults = 0;
          salary.work_days.forEach((day) => {
            if (day.ref_reward) {
              salary.ref_rewards += day.ref_reward;
            }
            if (day.reward) {
              const reward = find(this.payoffList, ["id", day.reward]);
              if (reward) {
                salary.rewards +=
                  salary.currency === "vnd" ? reward.price : reward.yen_price;
              }
            }
            if (day.fault) {
              const fault = find(this.payoffList, ["id", day.fault]);
              if (fault) {
                salary.faults +=
                  salary.currency === "vnd" ? fault.price : fault.yen_price;
              }
            }
          });
          return salary;
        });
        user.salaries = orderBy(
          cloneDeep(salaries),
          ["level", "valid_from"],
          ["asc", "asc"]
        );
        user.salary = user.work_days_salary + user.rewards - user.faults;
        user.fixed_salary = user.salary.toFixed(0);
        // đếm số lượng nhóm
        let countGroup = 0;
        if (user.groups) {
          user.groups.forEach((item) => {
            if (item.group) {
              countGroup += 1;
            }
          });
        }
        user.totalGroup = countGroup;
        user.joinDate = user.join_date;
        user.seniorityMonth = this.differenceTime(user.join_date);
        return user;
      });
    },
  },
  async mounted() {
    this.setPage("Quản lý chấm công");
    await this.getPayoffList();
    await this.getList();
  },
  methods: {
    ...mapActions("ui", ["setPage"]),
    ...mapActions("teacher", [
      "getUserList",
      "changeCurrentDate",
      "updateUsers",
    ]),
    ...mapActions("payoff", ["getPayoffList"]),
    decimal(row, column) {
      return new Intl.NumberFormat("vi-VN").format(row[column.property]);
    },
    dateTimeToDate(row, column) {
      return row[column.property]
        ? moment(row[column.property]).format("DD-MM-YYYY")
        : "--";
    },
    dateReverse(row, column) {
      return row[column.property]
        ? moment(row[column.property], "YYYY-MM-DD").format("DD-MM-YYYY")
        : "--";
    },
    async getList() {
      this.loading = true;
      const data = {
        ...this.filters,
        teacherType: this.teacherType,
        curDate: this.currentDate,
        timeFrame: this.timeFrame,
        page: this.page,
        perPage: this.perPage,
      };
      await this.getUserList(data);
      this.loading = false;
    },
    async handleCurrentChange(page) {
      await this.getList();
    },
    async handleSizeChange(perPage) {
      await this.getList();
    },
    async exportExcel() {
      this.loading = true;
      const data = {
        ...this.filters,
        curDate: this.currentDate,
        timeFrame: this.timeFrame,
        teacherType: this.teacherType,
        page: this.page,
        perPage: this.perPage,
      };
      axios
        .post(
          window.location.origin + "/backend/school/api/v1/user/export",
          data,
          {
            responseType: "blob",
          }
        )
        .then((response) => {
          const url = URL.createObjectURL(
            new Blob([response.data], {
              type: "application/vnd.ms-excel",
            })
          );
          const link = document.createElement("a");
          link.href = url;
          link.setAttribute("download", "cham_cong_" + Date.now() + ".xlsx");
          document.body.appendChild(link);
          link.click();
        });
      this.loading = false;
    },
    cellClick(row, column, cell, event) {
      this.currentUser = { ...row };
      if (column["property"] === "base_salary") {
        this.salaryDialog = true;
      }
      if (column["property"] === "work_days_count") {
        this.workDayDialog = true;
      }
      if (column["property"] === "name") {
        this.userDialog = true;
      }
    },
    differenceTime(joinDate) {
      var currentTime = moment();
      var joinTime = moment(joinDate);

      var diffMonths = currentTime.diff(joinTime, "months");

      if (diffMonths) {
        return diffMonths + " Tháng";
      }
      return "--";
    },
    editNote(user) {
      var vm = this;
      this.$prompt("Nhập note", "Nhập note", {
        inputValue: user.note_by_accountant,
        confirmButtonText: "OK",
        cancelButtonText: "Cancel",
      })
        .then(({ value }) => {
          axios
            .post(
              window.location.origin + "/backend/school/api/v1/user/edit-note",
              {
                id: user.id,
                noteByAccountant: value,
              }
            )
            .then(() => {
              const users = vm.users.map((u) => {
                if (u.id === user.id) {
                  u.note_by_accountant = value;
                }
                return u;
              });
              this.updateUsers(users);
            });

          this.$message({
            type: "success",
            message: "Thành công",
          });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "Lỗi",
          });
        });
    },
  },
};
</script>
<style>
.el-table .cell {
  word-break: break-word;
}

.el-table .el-table__cell {
  vertical-align: baseline !important;
}

.user-table__header {
  background-color: rgb(217 249 157) !important;
  color: black;
}
</style>
