<!doctype html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport"
          content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <link rel="icon" href="{{asset('assets/img/new_home/06-2024/dungmori-fav.png')}}"/>
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <link rel="preload" href="{{asset('css/base.css')}}?{{filemtime('css/base.css')}}" as="style"
          onload="this.onload=null;this.rel='stylesheet'" media="screen">
    <link rel="preload" href="{{asset('css/plugins.css')}}?{{filemtime('css/plugins.css')}}" as="style"
          onload="this.onload=null;this.rel='stylesheet'" media="screen">
    <link rel="stylesheet" href="{{asset('assets/css/styles.css')}}?{{filemtime('assets/css/styles.css')}}"
          media="screen">

    <link rel="stylesheet" href="{{ asset('css/course/basic.css') }}">
    <link rel="stylesheet" href="{{ asset('/plugin/element-ui/element-ui.min.css') }}">

    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">


    <link rel="stylesheet" href="{{ asset('css/module/flashcard.css') }}">

    <title>Vocabulary</title>
    <script type="text/javascript">
        let specialized = {};
        let jlpt = {!! json_encode($jlpt) !!};
        let favorite = {!! json_encode($favorite) !!};
        console.log(`specialized: =====================>`, specialized);
        
        console.log(`jlpt: =====================>`, jlpt);
        const authUser = {
            id: "{{ auth()->check() ? auth()->user()->id : '' }}",
            name: "{{ auth()->check() ? auth()->user()->name : '' }}",
            avatar: "{{ auth()->check() && auth()->user()->avatar ? url('cdn/avatar/default/' . auth()->user()->avatar) : '/images/icons/default-user.png' }}",
            isTester: "{{ auth()->check() ? auth()->user()->is_tester : false }}",
        }
    </script>

</head>
<body>
<div id="vocabulary_page">
    @include('frontend._layouts.menu')

    <div id="vocabulary_content">
        <vocabulary :jlpt="jlpt" :favorite="favorite"></vocabulary>
    </div>
</div>



<script src="https://code.jquery.com/jquery-3.7.1.js" integrity="sha256-eKhayi8LEQwp4NKxN+CfCh+3qOVUtJn3QNZ0TciWLP4=" crossorigin="anonymous"></script>
<script src="{{asset('plugin/vue/vue.min.js')}}" type="text/javascript"></script>
<script src="{{ asset('/plugin/axios/axios.min.js') }}"></script>
<script src="{{ asset('/plugin/element-ui/element-ui.min.js') }}"></script>
<script src="{{ asset('/plugin/element-ui/vi.js') }}"></script>
<script src="{{ asset('/assets/js/vocabulary/vocabulary.js') }}?{{ filemtime('assets/js/vocabulary/vocabulary.js') }}"></script>
<script src="{{asset('assets/js/headlibs.js')}}?{{filemtime('assets/js/headlibs.js')}}"></script>

<script>
    // Toggle menu collapse
    function toggleMenu() {
        const navMenu = document.getElementById('navMenu');
        navMenu.classList.toggle('collapsed');
    }

    // Toggle submenu
    function toggleSubmenu(submenuId) {
        const submenu = document.getElementById(submenuId);
        const isOpen = submenu.classList.toggle('open');

        // Toggle arrow direction for the clicked menu item
        const menuItem = event.currentTarget;
        const arrow = menuItem.querySelector('.fa-chevron-down');

        if (isOpen) {
            // Menu is open, arrow points down
            arrow.classList.add('open');
        } else {
            // Menu is closed, arrow points right
            arrow.classList.remove('open');
        }

        // Close other submenus
        const allSubmenus = document.querySelectorAll('.nav-submenu');
        allSubmenus.forEach(menu => {
            if (menu.id !== submenuId && menu.classList.contains('open')) {
                menu.classList.remove('open');

                // Find the parent menu item for this submenu and rotate its arrow
                const submenuId = menu.id;
                const parentMenuItem = document.querySelector(`.nav-menu-item[onclick*="'${submenuId}'"]`);
                if (parentMenuItem) {
                    const arrow = parentMenuItem.querySelector('.fa-chevron-down');
                    if (arrow) {
                        arrow.classList.remove('open');
                    }
                }
            }
        });
    }

    // Show content section

    // Mobile menu toggle
    document.addEventListener('DOMContentLoaded', function () {
        const mobileMenuToggle = document.querySelector('.toggle-menu');
        if (mobileMenuToggle) {
            mobileMenuToggle.addEventListener('click', function () {
                if (window.innerWidth <= 768) {
                    document.getElementById('navMenu').classList.toggle('open');
                }
            });
        }
    });
</script>

</body>
</html>