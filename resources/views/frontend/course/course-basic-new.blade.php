@extends('frontend._layouts.default')
@section('title')
    <PERSON><PERSON><PERSON><PERSON> h<PERSON><PERSON> {{ $course->name }} - Dung<PERSON>i
@stop
@section('description')
    Dungmori YouTuber d<PERSON><PERSON> tiếng <PERSON>hật với phong cách gần gũi dễ hiểu nhất
@stop
@section('keywords')
    Dungmori YouTuber d<PERSON><PERSON> tiếng <PERSON>hật với phong cách gần gũi dễ hiểu nhất
@stop
@section('image')
    {{ url('cdn/course/default') }}/{{ $course->avatar_name }}
@stop
@section('author')
    DUNGMORI
@stop

@section('header-css')
    <link rel="stylesheet" href="{{ asset('css/course/basic.css') }}">
    <link href="https://vjs.zencdn.net/8.16.1/video-js.css" rel="stylesheet"/>
    <link rel="stylesheet" href="{{ asset('/plugin/element-ui/element-ui.min.css') }}">
    <link rel="stylesheet" href="//unpkg.com/element-ui@2.15.13/lib/theme-chalk/index.css">
@stop

@section('content')
    <div id="course_basic_vue" style="display: block;" v-cloak>
        @php
            $assetVersion = '1.0.1';
            $isLocked = !$isUnlock;
            $firstStage = $stages->first();
            $firstStageCategoryIds = $firstStage->pluck('id');
            $totalGroup = $groups->whereIn('lesson_category_id', $firstStageCategoryIds)->count();
            $groupIds = $groups->whereIn('lesson_category_id', $firstStageCategoryIds)->pluck('id');
            $totalPercent = $groups->whereIn('lesson_category_id', $firstStageCategoryIds)->sum('progress');
            $totalPercentStage = $totalGroup > 0 ? round($totalPercent / $totalGroup) : 0;

            $completedIntroduction = $totalPercentStage >= 85;

            $completedStages = 0;
            $completedRequireStages = 0;
            $requireStages = 0;
            foreach ($stages as $stageName => $stage) {
                $stageCategories = $categories->whereIn('stage', $stage->pluck('stage')->toArray());
                $completedRequireCategories = 0;
                $completedNonRequireCategories = 0;
                $requiredCategories = 0;
                foreach ($stageCategories as $category) {
                    $categoryGroups = $groups->where('lesson_category_id', $category->id);
                    $completedRequireGroups = 0;
                    $completedNonRequireGroups = 0;
                    $requiredGroups = 0;

                    foreach ($categoryGroups as $group) {
                        $groupIsRequired = $group->lessons->where('require', 1)->count();
                        if ($groupIsRequired) {
                            $requiredGroups++;
                        }

                        if ($groupIsRequired && $group->is_pass) {
                            $completedRequireGroups++;
                        }

                        if (!$groupIsRequired && $group->is_pass) {
                            $completedNonRequireGroups++;
                        }
                    }
                    if ($requiredGroups > 0) {
                        $requiredCategories++;
                        if ($completedRequireGroups == $requiredGroups) {
                            $completedRequireCategories += 1;
                        }
                    } else {
                        if ($completedNonRequireGroups == $categoryGroups->count()) {
                            $completedNonRequireCategories += 1;
                        }
                    }
                }
                if ($requiredCategories > 0) {
                    $requireStages++;
                    $stagePercent = round($completedRequireCategories * 100 / $requiredCategories);
                    if ($stagePercent == 100) {
                        $completedRequireStages += $stagePercent;
                    }
                } else {
                    $stagePercent = $stageCategories->count() > 0 ? round($completedNonRequireCategories * 100 / $stageCategories->count()) : 0;
                }
            }
            $courseCompletedPercent = $requireStages ? round($completedRequireStages / $requireStages) : 0;

            if($courseExpireDay) {
                $date = \Carbon\Carbon::parse($courseExpireDay)->format('d/m/Y');
                $colorCourseExpire =
                    \Carbon\Carbon::parse($courseExpireDay)->addDays(-7) > \Carbon\Carbon::parse(date('Y-m-d H:i:s'))
                        ? 'bg-[#C1EACA]'
                        : 'text-[#975102] bg-[#FFF1C2]';

                $text = 'Khóa học còn hạn đến ';
                if ((\Carbon\Carbon::parse($courseExpireDay)->addDays(-7) > \Carbon\Carbon::now() && $courseOwner->watch_expired_day == null) || ($courseOwner->watch_expired_day != null && \Carbon\Carbon::parse($courseOwner->code_active_at)->addDay()->equalTo($courseOwner->watch_expired_day))) {
                    $text = 'Thời hạn học thử còn ';
                }
            }
        @endphp
        @include('frontend.course.components.modal-teacher-info')
        <div class="course-basic">
            <div id="course-basic-tutorial">
                @include('frontend.course.components.course-tutorial')
            </div>
            <div class="pb-20 bg-[#F4F5FA] pt-7">
                <div class="bg-cover bg-no-repeat sp:!bg-none"
                     style="{{ $completedIntroduction ? "background-image: url('/images/lessons/banner-paid.png')" : '' }}">
                    <div class="container max-w-[1100px] mx-auto">
                        <div class="grid grid-cols-3 sp:grid-cols-1">
                            <div class="col-span-2 left-col {{ $completedIntroduction ? 'invisible sp:hidden' : '' }}">
                                @if($course->SEOurl == 'luyen-de-n4' || $course->SEOurl == 'luyen-de-n5')
                                    <div class="img">
                                        <img src="{{ asset("/images/course/LD.png") }}">
                                    </div>
                                @else
                                    <div class="video">
                                        <video id="my-video-intro" class="video-js h-[413px] sp:h-[250px]" controls
                                               preload="auto"
                                               poster="@if ($course->id === 47) /images/course/chuhann5thumb.jpg @else /images/course/poster-intro-n4-n5.jpg @endif"
                                               data-setup="{}">
                                            <source
                                                    @if($course->id == 40)
                                                        src="https://vn.dungmori.com/720p/GioithieuN4.mp4/index.m3u8"
                                                    @else
                                                        src="https://vn.dungmori.com/720p/GIOI-THIEU-KHOA-HOC.mp4/index.m3u8"
                                                    @endif
                                                    type="application/x-mpegURL"/>
                                        </video>
                                    </div>
                                @endif
                                {{--                                @if (!empty($learningLesson->video))--}}
                                {{--                                @endif--}}
                            </div>
                            {{-- COURSE INFO --}}
                            <div class="right-col text-[#212121] sp:px-6 z-50">
                                <div class="desktop:text-right sp:text-left relative">
                                    @if ($course->price_option === 0)
                                        <div class="bg-[#07403F] px-3 py-1 text-white rounded-full font-beanbag-bold absolute desktop:top-1 top-4 desktop:left-8 desktop:right-auto left-auto right-0">MIỄN PHÍ</div>
                                    @endif
                                    <div class="text-2xl font-averta-semibold text-[#07403F] sp:hidden">
                                        Tiếng Nhật GUNGUN
                                    </div>
                                    <div class="flex items-center justify-end sp:justify-between whitespace-nowrap mb-[7px]">
                                        <div class="relative">
                                            <span
                                                    class="@if ($course->id === 47) text-[89px] @else text-[98px] @endif sp:text-[48px] text-[#07403F] font-zuume-semibold relative">{{ $course->name }}
                                            </span>
                                            <span
                                                    class="absolute bg-[#EF6D13] text-white text-[6px] px-2 py-[1px] rounded-full top-0 right-[-30px] desktop:hidden">
                                                NEW
                                            </span>
                                        </div>
                                        @if($isLocked)
                                            @if ($course->price_option === 1)
                                                <button
                                                    class="desktop:hidden relative top-[-5px] bg-[#EF6D13] shadow-md ml-8 flex items-center justify-center rounded-full text-white max-w-[100%] py-3 px-4 text-base font-beanbag uppercase"
                                                    @click="buyNow({{ $course->id }})">
                                                    <span class="relative top-[2px]">
                                                        MUA NGAY
                                                    </span>
                                                </button>
                                            @endif
                                        @elseif (!$isLocked && $courseOwner && $courseOwner->code_active_at != null && !($courseOwner->watch_expired_day != null && \Carbon\Carbon::parse($courseOwner->watch_expired_day)->isAfter(\Carbon\Carbon::parse(date('Y-m-d H:i:s'))) && \Carbon\Carbon::parse($courseOwner->code_active_at)->addDay()->lessThan($courseOwner->watch_expired_day)))
                                            <div class="desktop:hidden">
                                                <a class="cursor-pointer flex py-[18px] px-[10px] justify-center items-baseline bg-[#EF6D13] hover:text-white rounded-full shadow-2xl text-white"
                                                   @click="handleClickPromotionFooter($event, 'link')"
                                                >
                                                    <div class="relative font-beanbag text-[12px] leading-[16px] mr-[63px] uppercase">
                                                        Nhận ưu đãi
                                                        <div
                                                                class="absolute top-[-7px] left-[80px] -rotate-6 pr-[10px] pl-[9px] py-[2px] text-[#07403F] font-beanbag text-[14px] leading-[16px]"
                                                        >
                                                            <div class="relative w-[56px]">
                                                                <img class=""
                                                                     src="{{ asset('/images/lessons/svg/bg-tag-promotion.svg') }}">
                                                            </div>
                                                            <div class="absolute top-[5px] left-[20px]">
                                                                @{{ activeStep == 1 ? '-30%' : (activeStep == 2 ? '-20%'
                                                                : '-10%') }}
                                                            </div>
                                                        </div>
                                                    </div>
                                                </a>
                                            </div>
                                        @endif
                                    </div>
                                    <div class="font-averta-regular text-base relative top-[-25px] sp:top-[-10px]">
                                        ぐんぐん上達！- Tiến bộ nhanh chóng cùng DUNGMORI!
                                        {{--                                        Nhập môn tiếng Nhật cùng DUNGMORI!--}}
                                    </div>
                                    @if(in_array($course->id, [30, 41]))
                                        <div class="font-averta-regular text-base relative top-[-25px] sp:top-[-10px]">
                                            Với kho đề thi phong phú, sát thực tế, khóa "Luyện đề" là trợ thủ đắc lực số 1 giúp bạn tổng ôn kiến thức và rèn luyện kĩ năng làm bài trước mỗi kì thi!
                                        </div>
                                    @endif
                                    @if(in_array($course->id, [39, 40, 44, 45, 46, 47]))
                                        <div
                                                class="flex items-center justify-end sp:justify-start relative top-[-20px] sp:top-[-8px]">
                                            <div class="flex items-center">
                                                @for ($i = 5; $i > 0; $i--)
                                                    <img class="mr-[3px] w-4"
                                                         src="{{ asset('/images/icons/star.png') }}?v={{ $assetVersion }}"
                                                         alt="star">
                                                @endfor
                                                <span class="ml-2 text-[15px] text-[#EF6D13]">5/5</span>
                                            </div>
                                            @if ($course->price_option === 1)
                                                <div class="text-[#6F727D] text-[15px] ml-3 underline">
                                                    (5999 đánh giá)
                                                </div>
                                            @endif
                                        </div>
                                    @endif

                                    <div class="font-averta-regular sp:mt-5">
                                        <div class="text-base font-averta-semibold">
                                            Giáo viên phụ trách
                                        </div>
                                        @if(in_array($course->id, [39, 40, 44, 45, 46, 47]))
                                            <div class="flex items-center justify-end sp:justify-start mt-4">
                                                @if (strpos($course->author_id, '"47"'))
                                                <div class="bg-white rounded-full p-2 flex items-center justify-center mr-2 cursor-pointer btn-teacher-info"
                                                     data-id="#teacher-1">
                                                    <img class="flex-none rounded-full w-[32px] h-[32px]"
                                                         src="{{ asset('/images/lessons/thay-dung.png') }}?v=4"
                                                         alt="thầy Dũng">
                                                    <span class="ml-1 text-md whitespace-nowrap">Thầy Dũng Mori</span>
                                                </div>
                                                @endif
                                                @if (strpos($course->author_id, '"48"'))
                                                <div class="bg-white rounded-full p-2 flex items-center justify-center cursor-pointer btn-teacher-info"
                                                     data-id="#teacher-2">
                                                    <img class="flex-none rounded-full w-[32px] h-[32px]"
                                                         src="{{ asset('/images/lessons/co-thanh.png') }}?v=4"
                                                         alt="cô Thanh">
                                                    <span class="ml-1 text-md whitespace-nowrap">Cô Phương Thanh</span>
                                                </div>
                                                @endif
                                            </div>
                                            @if (strpos($course->author_id, '"49"'))
                                            <div class="flex items-center justify-end sp:justify-start mt-4">
                                                <div class="bg-white rounded-full p-2 flex items-center justify-center cursor-pointer btn-teacher-info"
                                                     data-id="#teacher-3">
                                                    <img class="flex-none rounded-full w-[32px] h-[32px] object-cover"
                                                         src="{{ asset('/images/lessons/co-phan-ha.png') }}?v=4"
                                                         alt="cô Thanh">
                                                    <span class="ml-1 text-md whitespace-nowrap">Cô Phan Hà</span>
                                                </div>
                                            </div>
                                            @endif
                                        @elseif($course->id === 30)
                                            <div class="flex items-center justify-end sp:justify-start mt-4">
                                                <div class="bg-white rounded-full p-2 flex items-center justify-center mr-2 cursor-pointer btn-teacher-info"
                                                     data-id="#teacher-4">
                                                    <img class="flex-none rounded-full w-[32px] h-[32px] object-cover"
                                                         src="{{ asset('/images/lessons/thay-truong.png') }}?v=4"
                                                         alt="thầy Dũng">
                                                    <span class="ml-1 text-md whitespace-nowrap">Thầy Minh Trường</span>
                                                </div>
                                            </div>
                                        @elseif($course->id === 41)
                                            <div class="flex items-center justify-end sp:justify-start mt-4">
                                                <div class="bg-white rounded-full p-2 flex items-center justify-center cursor-pointer btn-teacher-info"
                                                     data-id="#teacher-5">
                                                    <img class="flex-none rounded-full w-[32px] h-[32px] object-cover"
                                                         src="{{ asset('/images/lessons/co-ninh.png') }}?v=4"
                                                         alt="cô Thanh">
                                                    <span class="ml-1 text-md whitespace-nowrap">Cô Nguyễn Ninh</span>
                                                </div>
                                            </div>
                                        @endif
                                    </div>

                                    @if (!$isLocked)
                                        <div class="inline-flex mt-[15px] select-none border {{ $courseCompletedPercent < 85 ? 'border-[#BF6A02]' : ($courseCompletedPercent == 100 ? 'border-transparent' : '') }} {{ $courseCompletedPercent < 85 ? 'text-[#BF6A02]' : ($courseCompletedPercent == 100 ? 'bg-[#57D061] text-[#07403F]' : 'text-[#009951]') }} rounded-[13px] px-[7px] font-averta-regular text-[14px]">
                                            Bạn đã hoàn thành {{ $courseCompletedPercent }}%
                                        </div>
                                    @endif

                                    @if ($course->price_option === 0 && $course->id === 47)
                                        {{-- Chữ Hán N5 --}}
                                        <a href="{{url('/khoa-hoc/so-cap-n5')}}">
                                            <div class="font-beanbag-bold text-white py-1.5 text-center mt-4 text-lg desktop:ml-8 rounded-full bg-[#EF6D13]">
                                                NÂNG CẤP LÊN KHÓA N5
                                            </div>
                                        </a>
                                    @else
                                    <div class="right-col text-[#212121] sp:px-6 z-50">
                                        @if ($isLocked)
                                            <div class="sp:mt-4 sp:hidden">
                                                <div class="bg-white rounded-3xl shadow-lg w-[310px] ml-auto mb-4 text-center p-2 mt-3">
                                                    <div class="flex items-start text-[#07403F] text-3xl justify-center text-bold font-beanbag">
                                                        <div>
                                                            {{ number_format($course->price) }}
                                                            {{--                                                @if($course->id === 39)--}}
                                                            {{--                                                    1,290,000--}}
                                                            {{--                                                @else--}}
                                                            {{--                                                    1,890,000--}}
                                                            {{--                                                @endif--}}
                                                        </div>
                                                        <div class="text-base">
                                                            VNĐ
                                                        </div>
                                                    </div>
                                                    <div class="text-xl font-beanbag-regular text-[#07403F]">
                                                        Thời gian học <span class="text-bold font-beanbag"> {{ in_array($course->id, [39, 40, 44, 45, 46, 47]) ? 8 : 2  }} tháng</span>
                                                    </div>
                                                </div>

                                                <button
                                                    class="bg-[#EF6D13] shadow-md ml-auto flex items-center justify-center rounded-full text-white max-w-[100%] w-[310px] py-3 text-base font-beanbag uppercase"
                                                    @click="buyNow({{ $course->id }})">
                                                    MUA NGAY
                                                    {{--                                                <img class="ml-2 relative top-[-2px] w-14"--}}
                                                    {{--                                                     src="{{ asset('/images/icons/sale-off.png') }}?v={{ $assetVersion }}"--}}
                                                    {{--                                                     alt="sale-off">--}}
                                                </button>
                                            </div>
                                        @elseif($courseExpireDay)
                                            <div
                                                class="flex items-center justify-end sp:justify-start font-averta-regular mt-4 whitespace-nowrap sp:hidden">
                                                <div class="{{ $colorCourseExpire }} px-4 py-1 rounded-full text-[15px]">
                                                    @if ((\Carbon\Carbon::parse($courseExpireDay)->addDays(-7) > \Carbon\Carbon::now() && $courseOwner->watch_expired_day == null) || ($courseOwner->watch_expired_day != null && \Carbon\Carbon::parse($courseOwner->code_active_at)->addDay()->equalTo($courseOwner->watch_expired_day)))
                                                        {{ $text }} <b class="font-averta-semibold">
                                                            @{{ formatTime(currentCourseTimeLeft) }}
                                                        </b>
                                                    @else
                                                        {{ $text }} <b class="font-averta-semibold">
                                                            {{ $date }}
                                                        </b>
                                                    @endif

                                                </div>

                                                @if (!$isTryLearn && (!((\Carbon\Carbon::parse($courseExpireDay)->addDays(-7) > \Carbon\Carbon::parse(date('Y-m-d H:i:s')) && $courseOwner->watch_expired_day == null) || ($courseOwner->watch_expired_day != null && \Carbon\Carbon::parse($courseOwner->code_active_at)->addDay()->equalTo($courseOwner->watch_expired_day)))))
                                                    <a onclick="showChatbox()"
                                                       class="mobile:hidden ml-2 text-base text-[#07403F] !underline cursor-pointer">Gia
                                                        hạn></a>
                                                @endif

                                            </div>
                                            @if (!$isLocked && $courseOwner && $courseOwner->code_active_at != null && !($courseOwner->watch_expired_day != null && \Carbon\Carbon::parse($courseOwner->watch_expired_day)->isAfter(\Carbon\Carbon::parse(date('Y-m-d H:i:s'))) && \Carbon\Carbon::parse($courseOwner->code_active_at)->addDay()->lessThan($courseOwner->watch_expired_day)))
                                                <div class="mt-[20px] sp:hidden" style="justify-items: end">
                                                    <a class="max-w-[306px] cursor-pointer flex py-[14px] px-[56px] justify-center items-baseline bg-[#EF6D13] hover:text-white rounded-full shadow-2xl text-white"
                                                       @click="handleClickPromotionFooter($event, 'link')"
                                                    >
                                                        <div class="relative font-beanbag text-[16px] leading-[16px] mr-[60px] uppercase">
                                                            Nhận ưu đãi 1
                                                            <div
                                                                class="absolute top-[-7px] left-[110px] -rotate-6 pr-[10px] pl-[9px] py-[2px] text-[#07403F] font-beanbag text-[14px] leading-[16px]"
                                                            >
                                                                <div class="relative w-[56px]">
                                                                    <img class=""
                                                                         src="{{ asset('/images/lessons/svg/bg-tag-promotion.svg') }}">
                                                                </div>
                                                                <div class="absolute top-[5px] left-[20px]">
                                                                    @{{ activeStep == 1 ? '-30%' : (activeStep == 2 ? '-20%'
                                                                    : '-10%') }}
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </a>
                                                </div>
                                            @endif
                                        @endif
                                    </div>
                                    @endif
                                    {{--                                    <div class="flex justify-end sp:justify-start items-center mt-4 font-averta-regular">--}}
                                    {{--                                        <img src="{{ asset('/images/icons/volume-low.png') }}" alt="speaker">--}}
                                    {{--                                        <span>Tiếng Việt</span>--}}
                                    {{--                                        <span class="ml-1">•</span>--}}
                                    {{--                                        <img class="mx-1" src="{{ asset('/images/icons/calendar.png') }}"--}}
                                    {{--                                             alt="calendar">--}}
                                    {{--                                        <span>Cập nhật cuối--}}
                                    {{--                                        {{ \Carbon\Carbon::parse($course->updated_at)->format('m/Y') }}</span>--}}
                                    {{--                                    </div>--}}

                                </div>
                            </div>
                            {{-- END COURSE INFO --}}
                        </div>
                    </div>
                </div>

                {{-- COURSE LIST --}}
                <div class="container max-w-[1100px] mx-auto pt-4 mt-7 sp:mt-3 font-averta-regular sp:px-6">
                    <div class="sp:grid-cols-1 @if ($course->price_option === 1) grid grid-cols-3 @endif">
                        <div class="col-span-2 left-col">
                            <div id="step-content-2">
                                @include('frontend.course.components.first-lesson')
                            </div>
                            {{-- END FIRST LESSON --}}

                            @if ($course->price_option === 1)
                                <div class="desktop:hidden mt-5">
                                    @include('frontend.course.components.course-new-purpose', ['courseId' => $course->id])
                                </div>
                            @endif

                            <div class="mt-7" id="step-content-3">
                                @include('frontend.course.components.lesson-list')
                            </div>
                        </div>

                        {{-- PURPOSE AND FEEDBACK --}}
                        @if ($course->price_option === 1)
                        <div class="right-col text-[#212121] pl-14 sp:pl-0">
                            <div class="sp:hidden">
                                @include('frontend.course.components.course-new-purpose', ['courseId' => $course->id])
                            </div>
                            @if(in_array($course->id, [39, 40, 44, 45, 46, 47]))
                                <div class="mt-5 sp:hidden">
                                    <div class="font-beanbag text-xl text-[#1E1E1E]">
                                        Đánh giá của học viên
                                    </div>

                                    <div class="grid grid-cols-1 gap-1">
                                        @if($course->id == 39)
                                            @foreach ($feedbacks->take(5) as $feedback)
                                                <div>
                                                    <div class="flex items-center my-2">
                                                        <div class="flex-none">
                                                            <div class="bg-white rounded-full p-1 w-[40px] h-[40px]">
                                                                <div class="bg-[#DAFBDE] rounded-full">
                                                                    <img class="rounded-full"
                                                                         src="{{ $feedback->users->avatar !== null ? asset('/cdn/avatar/small/' . $feedback->users->avatar) : asset('/images/icons/user-default-logo.png') }}"
                                                                         alt="user-default-logo.png">
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="font-averta-regular ml-2">
                                                            <div class="font-semibold">{{ $feedback->users->name }}</div>
                                                            <div class="flex items-center">
                                                                @for ($i = 5; $i > 0; $i--)
                                                                    <img class="mr-[1px] w-4"
                                                                         src="{{ asset('/images/icons/star.png') }}" alt="star">
                                                                @endfor
                                                                <span
                                                                        class="text-[#737680] ml-4 text-[10px] relative top-[2px]">{{ $feedback->date }}</span>
                                                            </div>

                                                        </div>
                                                    </div>
                                                    <div class="font-averta-regular my-2 text-justify">{{ $feedback->content }}</div>
                                                </div>
                                            @endforeach
                                        @else
                                            @foreach ($feedbacks->reverse()->take(4) as $feedback)
                                                <div>
                                                    <div class="flex items-center my-2">
                                                        <div class="flex-none">
                                                            <div class="bg-white rounded-full p-1 w-[40px] h-[40px]">
                                                                <div class="bg-[#DAFBDE] rounded-full">
                                                                    <img class="rounded-full"
                                                                         src="{{ $feedback->users->avatar !== null ? asset('/cdn/avatar/small/' . $feedback->users->avatar) : asset('/images/icons/user-default-logo.png') }}"
                                                                         alt="user-default-logo.png">
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="font-averta-regular ml-2">
                                                            <div class="font-semibold">{{ $feedback->users->name }}</div>
                                                            <div class="flex items-center">
                                                                @for ($i = 5; $i > 0; $i--)
                                                                    <img class="mr-[1px] w-4"
                                                                         src="{{ asset('/images/icons/star.png') }}" alt="star">
                                                                @endfor
                                                                <span
                                                                        class="text-[#737680] ml-4 text-[10px] relative top-[2px]">{{ $feedback->date }}</span>
                                                            </div>

                                                        </div>
                                                    </div>
                                                    <div class="font-averta-regular my-2 text-justify">{{ $feedback->content }}</div>
                                                </div>
                                            @endforeach
                                        @endif



                                        {{--                                    @foreach ($feedbacks->slice(4) as $feedback)--}}
                                        {{--                                        <div class="hidden feedback-more-item">--}}
                                        {{--                                            <div class="flex items-center my-2">--}}
                                        {{--                                                <div class="flex-none">--}}
                                        {{--                                                    <div class="bg-white rounded-full p-1 w-[40px] h-[40px]">--}}
                                        {{--                                                        <div class="bg-[#DAFBDE] rounded-full">--}}
                                        {{--                                                            <img class="rounded-full"--}}
                                        {{--                                                                 src="{{ asset('/images/icons/user-default-logo.png') }}"--}}
                                        {{--                                                                 alt="user-default-logo.png">--}}
                                        {{--                                                        </div>--}}
                                        {{--                                                    </div>--}}
                                        {{--                                                </div>--}}
                                        {{--                                                <div class="font-averta-regular ml-2">--}}
                                        {{--                                                    <div class="font-semibold">{{ $feedback->users->name }}</div>--}}
                                        {{--                                                    <div class="flex items-center">--}}
                                        {{--                                                        @for ($i = 5; $i > 0; $i--)--}}
                                        {{--                                                            <img class="mr-[1px] w-4"--}}
                                        {{--                                                                 src="{{ asset('/images/icons/star.png') }}" alt="star">--}}
                                        {{--                                                        @endfor--}}
                                        {{--                                                        <span--}}
                                        {{--                                                                class="text-[#737680] ml-4 text-[10px] relative top-[2px]">{{ $feedback->date }}</span>--}}
                                        {{--                                                    </div>--}}

                                        {{--                                                </div>--}}
                                        {{--                                            </div>--}}
                                        {{--                                            <div class="font-averta-regular my-2 text-justify">{{ $feedback->content }}</div>--}}
                                        {{--                                        </div>--}}
                                        {{--                                    @endforeach--}}
                                    </div>
                                    {{--                                <div class="underline text-[#EF6D13] text-center text-base cursor-pointer btn-more-feedback">--}}
                                    {{--                                    Xem thêm >>--}}
                                    {{--                                </div>--}}
                                </div>
                            @endif
                        </div>
                        @endif
                        {{-- END PURPOSE AND FEEDBACK --}}

                    </div>

                    @if(in_array($course->id, [39, 40, 44, 45, 46, 47]) && $course->price_option === 1)
                        {{-- FEEDBACK MOBILE --}}
                        <div class="mt-5 desktop:hidden">
                            <div class="font-beanbag text-xl text-[#1E1E1E]">
                                Đánh giá của học viên
                            </div>

                            <div class="grid grid-cols-1 gap-1">
                                @foreach ($feedbacks->take(2) as $feedback)
                                    <div>
                                        <div class="flex items-center my-2">
                                            <div class="flex-none">
                                                <div class="bg-white rounded-full p-1 w-[40px] h-[40px]">
                                                    <div class="bg-[#DAFBDE] rounded-full">
                                                        <img class="rounded-full"
                                                             src="{{ asset('/images/icons/user-default-logo.png') }}"
                                                             alt="user-default-logo.png">
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="font-averta-regular ml-2">
                                                <div class="font-semibold">{{ $feedback->users->name }}</div>
                                                <div class="flex items-center">
                                                    @for ($i = 5; $i > 0; $i--)
                                                        <img class="mr-[1px] w-4"
                                                             src="{{ asset('/images/icons/star.png') }}"
                                                             alt="star">
                                                    @endfor
                                                    <span
                                                            class="text-[#737680] ml-4 text-[10px] relative top-[2px]">{{ $feedback->date }}</span>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="font-averta-regular my-2 text-justify">{{ $feedback->content }}</div>
                                    </div>
                                @endforeach
                                @foreach ($feedbacks->slice(2) as $feedback)
                                    <div class="hidden feedback-more-item">
                                        <div class="flex items-center my-2">
                                            <div class="flex-none">
                                                <div class="bg-white rounded-full p-1 w-[40px] h-[40px]">
                                                    <div class="bg-[#DAFBDE] rounded-full">
                                                        <img class="rounded-full"
                                                             src="{{ asset('/images/icons/user-default-logo.png') }}"
                                                             alt="user-default-logo.png">
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="font-averta-regular ml-2">
                                                <div class="font-semibold">{{ $feedback->users->name }}</div>
                                                <div class="flex items-center">
                                                    @for ($i = 5; $i > 0; $i--)
                                                        <img class="mr-[1px] w-4"
                                                             src="{{ asset('/images/icons/star.png') }}"
                                                             alt="star">
                                                    @endfor
                                                    <span
                                                            class="text-[#737680] ml-4 text-[10px] relative top-[2px]">{{ $feedback->date }}</span>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="font-averta-regular my-2 text-justify">{{ $feedback->content }}</div>
                                    </div>
                                @endforeach
                            </div>
                            <div class="underline text-[#EF6D13] text-right text-base cursor-pointer btn-more-feedback">
                                Xem thêm >>
                            </div>
                        </div>
                        {{-- END FEEDBACK MOBILE --}}
                    @endif
                </div>

                {{-- APP INTRO --}}
                <div class="bg-[#F0FFF1] py-40 relative mt-[160px] mb-[50px] sp:mt-10 sp:mb-10 sp:py-10">
                    <div class="container max-w-[1100px] mx-auto sp:px-6">
                        <div class="grid grid-cols-3 sp:grid-cols-1">
                            <div class="col-span-2 flex items-center justify-end sp:block">
                                <div
                                        class="text-[48px] text-[#07403F] flex-none font-beanbag w-auto sp:text-center sp:leading-[50px]">
                                    Học mọi lúc, <br>
                                    mọi nơi
                                </div>
                                <div class="flex-none hyphen bg-[#57D061] h-[90%] w-[8px] rounded-full mx-6 sp:hidden"></div>
                                <div>
                                    <div class="font-averta-semibold text-[18px] ml-3 sp:text-justify sp:mt-5">
                                        Kho tàng kiến thức nằm ngay trong túi của bạn!
                                        Một chạm mở <b class="font-averta-bold">App DUNGMORI</b>, bạn có thể bắt đầu giờ
                                        học
                                        ngay
                                        dù
                                        đang ở bất kì đâu!
                                    </div>
                                    <div class="flex justify-center items-center mt-5 sp:grid sp:grid-cols-1 sp:gap-2">
                                        <a class="w-1/2 sp:w-full sp:text-center"
                                           href="https://apps.apple.com/us/app/dungmori/id1486123836" target="_blank">
                                            <img class="sp:w-[170px]"
                                                 src="{{ asset('/images/icons/apple-store.png') }}?v={{ $assetVersion }}"
                                                 alt="apple-store.png">
                                        </a>
                                        <a class="w-1/2 desktop:ml-3 sp:w-full sp:text-center"
                                           href="https://play.google.com/store/apps/details?id=com.dungmori.dungmoriapp&pli=1"
                                           target="_blank">
                                            <img class="sp:w-[170px]"
                                                 src="{{ asset('/images/icons/google-play.png') }}?v={{ $assetVersion }}"
                                                 alt="google-play.png">
                                        </a>
                                    </div>
                                </div>
                            </div>
                            <div class="col-span-1">
                                <img class="absolute sp:relative top-1/2 transform -translate-y-1/2 ml-4 sp:ml-0 w-[360px] mt-[10px]"
                                     src="{{ asset('/images/lessons/phone.png') }}?v={{ $assetVersion }}"
                                     alt="phone.png">
                            </div>
                        </div>
                    </div>
                </div>
                {{-- END APP INTRO --}}
            </div>
        </div>

        @if (!$isLocked && $courseOwner && $courseOwner->code_active_at != null && !($courseOwner->watch_expired_day != null && \Carbon\Carbon::parse($courseOwner->watch_expired_day)->isAfter(\Carbon\Carbon::parse(date('Y-m-d H:i:s'))) && \Carbon\Carbon::parse($courseOwner->code_active_at)->addDay()->lessThan($courseOwner->watch_expired_day)))
            <div class="footer-promotion bg-[#F4F5FA] fixed bottom-[115px] md:bottom-[0] w-full z-[100]">
                <div class="max-w-[1170px] p-[6px] md:p-[13px] mx-auto text-center">
                    <div class="grid grid-cols-3 gap-5">
                        <div
                                @click="handleClickPromotionFooter($event)"
                                :class="{'promotion-active': activeStep === 1 && currentTimeLeft !== 0, 'expired': activeStep > 1}"
                                class="md:flex promotion-item md:items-center pb-[5px] pt-[13px] px-[12px] md:p-[11px] rounded-[10.87px]"
                        >
                            <div class="group-promotion-reduce flex items-start sp:justify-center">
                                <div class="text-reduce uppercase font-zuume-semibold  text-[28px] leading-[31px] md:text-[20px] md:leading-[20px]">
                                    giảm
                                </div>
                                <div class="value-reduce font-zuume-semibold text-[28px] leading-[31px] md:text-[57px] md:leading-[57px] sp:ml-[4px] md:mt-[-3px] md:mb-[-11px]">
                                    30%
                                </div>
                            </div>
                            <div class="group-promotion-time leading-1">
                                <div class="time-stop flex items-baseline justify-center">
                                    <div class="mt-[1px]">
                                        <svg class="w-[8px] h-[8px] md:w-[14px] md:h-[14px]" width="14" height="14"
                                             viewBox="0 0 14 14" fill="none"
                                             xmlns="http://www.w3.org/2000/svg">
                                            <path d="M11.9192 7.03407C11.9192 9.58701 9.84727 11.659 7.29433 11.659C4.74139 11.659 2.66943 9.58701 2.66943 7.03407C2.66943 4.48113 4.74139 2.40918 7.29433 2.40918C9.84727 2.40918 11.9192 4.48113 11.9192 7.03407Z"
                                                  stroke="#B3B3B3" stroke-width="1.1487" stroke-linecap="round"
                                                  stroke-linejoin="round"/>
                                            <path d="M7.29443 4.25781V6.90061" stroke="#B3B3B3" stroke-width="1.1487"
                                                  stroke-linecap="round" stroke-linejoin="round"/>
                                            <path d="M5.7085 1.08398H8.87985" stroke="#B3B3B3" stroke-width="1.1487"
                                                  stroke-miterlimit="10" stroke-linecap="round"
                                                  stroke-linejoin="round"/>
                                        </svg>
                                    </div>
                                    <div class="text-[#B3B3B3] text-bold font-bold text-[16px] leading-[16px] font-averta-regular ml-[6px] sp:text-[10px]">
                                        00:00:00
                                    </div>
                                </div>
                                <div class="time-active">
                                    <div class="text-[14px]  leading-[14px] text-bold flex justify-end text-white sp:hidden">
                                        Ưu đãi còn
                                    </div>
                                    <div class="flex time-active-group sp:items-center sp:justify-center">
                                        <svg class="w-[8px] h-[8px] md:w-[24px] md:h-[24px]" width="24" height="24"
                                             viewBox="0 0 24 24" fill="none"
                                             xmlns="http://www.w3.org/2000/svg">
                                            <path d="M20.75 13.2568C20.75 18.0868 16.83 22.0068 12 22.0068C7.17 22.0068 3.25 18.0868 3.25 13.2568C3.25 8.42684 7.17 4.50684 12 4.50684C16.83 4.50684 20.75 8.42684 20.75 13.2568Z"
                                                  stroke="#FFE53A" stroke-width="1.81666" stroke-linecap="round"
                                                  stroke-linejoin="round"/>
                                            <path d="M12 8.00391V13.0039" stroke="#FFE53A" stroke-width="1.81666"
                                                  stroke-linecap="round" stroke-linejoin="round"/>
                                            <path d="M8.99951 1.99902H14.9995" stroke="#FFE53A" stroke-width="1.81666"
                                                  stroke-miterlimit="10" stroke-linecap="round"
                                                  stroke-linejoin="round"/>
                                        </svg>
                                        <div class="ml-[8px] text-[10px] leading-1 md:text-[24px] md:leading-[24px] text-bold text-[#FFE53A]">
                                            @{{ formatTime(currentTimeLeft) }}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div
                                @click="handleClickPromotionFooter($event)"
                                :class="{'promotion-active': activeStep === 2 && currentTimeLeft !== 0, 'expired': activeStep > 2}"
                                class="md:flex promotion-item md:items-center pb-[5px] pt-[13px] px-[12px] md:p-[11px] rounded-[10.87px]"
                        >
                            <div class="group-promotion-reduce flex items-start sp:justify-center">
                                <div class="text-reduce uppercase font-zuume-semibold  text-[28px] leading-[31px] md:text-[20px] md:leading-[20px]">
                                    giảm
                                </div>
                                <div class="value-reduce font-zuume-semibold text-[28px] leading-[31px] md:text-[57px] md:leading-[57px] sp:ml-[4px] md:mt-[-3px] md:mb-[-11px]">
                                    20%
                                </div>
                            </div>
                            <div class="group-promotion-time  leading-1">
                                <div class="time-stop flex items-baseline justify-center">
                                    <div class="mt-[1px]">
                                        <svg class="w-[8px] h-[8px] md:w-[14px] md:h-[14px]" width="14" height="14"
                                             viewBox="0 0 14 14" fill="none"
                                             xmlns="http://www.w3.org/2000/svg">
                                            <path d="M11.9192 7.03407C11.9192 9.58701 9.84727 11.659 7.29433 11.659C4.74139 11.659 2.66943 9.58701 2.66943 7.03407C2.66943 4.48113 4.74139 2.40918 7.29433 2.40918C9.84727 2.40918 11.9192 4.48113 11.9192 7.03407Z"
                                                  stroke="#B3B3B3" stroke-width="1.1487" stroke-linecap="round"
                                                  stroke-linejoin="round"/>
                                            <path d="M7.29443 4.25781V6.90061" stroke="#B3B3B3" stroke-width="1.1487"
                                                  stroke-linecap="round" stroke-linejoin="round"/>
                                            <path d="M5.7085 1.08398H8.87985" stroke="#B3B3B3" stroke-width="1.1487"
                                                  stroke-miterlimit="10" stroke-linecap="round"
                                                  stroke-linejoin="round"/>
                                        </svg>
                                    </div>
                                    <div class="text-[#B3B3B3] text-bold font-bold text-[16px] leading-[16px] font-averta-regular ml-[6px]  sp:text-[10px]">
                                        00:00:00
                                    </div>
                                </div>
                                <div class="time-active">
                                    <div class="text-[14px]  leading-[14px] text-bold flex justify-end text-white sp:hidden">
                                        Ưu đãi còn
                                    </div>
                                    <div class="flex time-active-group sp:items-center sp:justify-center">
                                        <svg class="w-[8px] h-[8px] md:w-[24px] md:h-[24px]" width="24" height="24"
                                             viewBox="0 0 24 24" fill="none"
                                             xmlns="http://www.w3.org/2000/svg">
                                            <path d="M20.75 13.2568C20.75 18.0868 16.83 22.0068 12 22.0068C7.17 22.0068 3.25 18.0868 3.25 13.2568C3.25 8.42684 7.17 4.50684 12 4.50684C16.83 4.50684 20.75 8.42684 20.75 13.2568Z"
                                                  stroke="#FFE53A" stroke-width="1.81666" stroke-linecap="round"
                                                  stroke-linejoin="round"/>
                                            <path d="M12 8.00391V13.0039" stroke="#FFE53A" stroke-width="1.81666"
                                                  stroke-linecap="round" stroke-linejoin="round"/>
                                            <path d="M8.99951 1.99902H14.9995" stroke="#FFE53A" stroke-width="1.81666"
                                                  stroke-miterlimit="10" stroke-linecap="round"
                                                  stroke-linejoin="round"/>
                                        </svg>
                                        <div class="ml-[8px] text-[10px] leading-1 md:text-[24px] md:leading-[24px] text-bold text-[#FFE53A]">
                                            @{{ formatTime(currentTimeLeft) }}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div
                                @click="handleClickPromotionFooter($event)"
                                class="md:flex promotion-item md:items-center pb-[5px] pt-[13px] px-[12px] md:p-[11px] rounded-[10.87px]"
                                :class="{'promotion-active': activeStep === 3 && currentTimeLeft !== 0, 'expired': activeStep === 3 && currentTimeLeft === 0}"
                        >
                            <div class="group-promotion-reduce flex items-start sp:justify-center">
                                <div class="text-reduce uppercase font-zuume-semibold  text-[28px] leading-[31px] md:text-[20px] md:leading-[20px]">
                                    giảm
                                </div>
                                <div class="value-reduce font-zuume-semibold text-[28px] leading-[31px] md:text-[57px] md:leading-[57px] sp:ml-[4px] md:mt-[-3px] md:mb-[-11px]">
                                    10%
                                </div>
                            </div>
                            <div class="group-promotion-time  leading-1">
                                <div class="time-stop flex items-baseline justify-center">
                                    <div class="mt-[1px]">
                                        <svg class="w-[8px] h-[8px] md:w-[14px] md:h-[14px]" width="14" height="14"
                                             viewBox="0 0 14 14" fill="none"
                                             xmlns="http://www.w3.org/2000/svg">
                                            <path d="M11.9192 7.03407C11.9192 9.58701 9.84727 11.659 7.29433 11.659C4.74139 11.659 2.66943 9.58701 2.66943 7.03407C2.66943 4.48113 4.74139 2.40918 7.29433 2.40918C9.84727 2.40918 11.9192 4.48113 11.9192 7.03407Z"
                                                  stroke="#B3B3B3" stroke-width="1.1487" stroke-linecap="round"
                                                  stroke-linejoin="round"/>
                                            <path d="M7.29443 4.25781V6.90061" stroke="#B3B3B3" stroke-width="1.1487"
                                                  stroke-linecap="round" stroke-linejoin="round"/>
                                            <path d="M5.7085 1.08398H8.87985" stroke="#B3B3B3" stroke-width="1.1487"
                                                  stroke-miterlimit="10" stroke-linecap="round"
                                                  stroke-linejoin="round"/>
                                        </svg>
                                    </div>
                                    <div class="text-[#B3B3B3] text-bold font-bold text-[16px] leading-[16px] font-averta-regular ml-[6px]  sp:text-[10px]">
                                        00:00:00
                                    </div>
                                </div>
                                <div class="time-active">
                                    <div class="text-[14px]  leading-[14px] text-bold flex justify-end text-white sp:hidden">
                                        Ưu đãi còn
                                    </div>
                                    <div class="flex time-active-group sp:items-center sp:justify-center">
                                        <svg class="w-[8px] h-[8px] md:w-[24px] md:h-[24px]" width="24" height="24"
                                             viewBox="0 0 24 24" fill="none"
                                             xmlns="http://www.w3.org/2000/svg">
                                            <path d="M20.75 13.2568C20.75 18.0868 16.83 22.0068 12 22.0068C7.17 22.0068 3.25 18.0868 3.25 13.2568C3.25 8.42684 7.17 4.50684 12 4.50684C16.83 4.50684 20.75 8.42684 20.75 13.2568Z"
                                                  stroke="#FFE53A" stroke-width="1.81666" stroke-linecap="round"
                                                  stroke-linejoin="round"/>
                                            <path d="M12 8.00391V13.0039" stroke="#FFE53A" stroke-width="1.81666"
                                                  stroke-linecap="round" stroke-linejoin="round"/>
                                            <path d="M8.99951 1.99902H14.9995" stroke="#FFE53A" stroke-width="1.81666"
                                                  stroke-miterlimit="10" stroke-linecap="round"
                                                  stroke-linejoin="round"/>
                                        </svg>
                                        <div class="ml-[8px] text-[10px] leading-1 md:text-[24px] md:leading-[24px] text-bold text-[#FFE53A]">
                                            @{{ formatTime(currentTimeLeft) }}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        @endif
    </div>

@stop

@section('footer-js')
    <script src="{{ asset('assets/js/course/basic.js') }}"></script>
    <script src="{{ asset('/plugin/element-ui/element-ui.min.js') }}"></script>
    <script src="{{ asset('/plugin/element-ui/vi.js') }}"></script>
    <script src="https://vjs.zencdn.net/8.16.1/video.min.js"></script>

    <script type="text/javascript">
        // try {
        //     let apiLesson = axios.create({
        //         baseURL: "/khoa-hoc",
        //         headers: {
        //             "Content-Type": "application/json",
        //             'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        //         },
        //         withCredentials: true,
        //     });
        //
        //     console.log(`apiLesson: `, apiLesson)
        // } catch (e) {
        //     console.log(e)
        // }

        let course = @json($course);
        let courseOwner = @json($courseOwner);
        let key_stage_open = @json($stageOpen);

        var checkFocusTab = true;
        $(document).ready(function(){
            window.onfocus = function() { checkFocusTab = true; };
            window.onblur = function() { checkFocusTab = false; };
        });

        if (courseOwner) {
            var auth = @json(Auth::user());
            if (auth && !auth.is_tester && !auth.is_assistant) {
                setInterval(() => {
                    if (checkFocusTab) {
                        switch (courseOwner.course_id) {
                            case 39:
                                ga('send', 'event', courseOwner.code, 'hoc_n5', 'thoi_gian_hoc');
                                break;
                            case 40:
                                ga('send', 'event', courseOwner.code, 'hoc_n4', 'thoi_gian_hoc');
                                break;
                            case 3:
                                ga('send', 'event', courseOwner.code, 'hoc_n3', 'thoi_gian_hoc');
                                break;
                            case 16:
                                ga('send', 'event', courseOwner.code, 'hoc_n2', 'thoi_gian_hoc');
                                break;
                            case 17:
                                ga('send', 'event', courseOwner.code, 'hoc_n1', 'thoi_gian_hoc');
                                break;
                        }
                    }
                }, 60000); // 60s
            }
        }

        new Vue({
            el: "#course_basic_vue",
            data: {
                totalTime: 24 * 60 * 60, // 24 giờ tính bằng giây
                timeElapsed: 0, // Thời gian đã trôi qua (tính bằng giây)
                activeStep: 0, // Bắt đầu từ mốc 0
                timer: null, // Để lưu setInterval
                courseOwner: courseOwner,
                linkPromotion: {
                    1: "https://m.me/102856559258931?ref=IAP30", // 30%
                    2: "https://m.me/102856559258931?ref=IAP20", // 20%
                    3: "https://m.me/102856559258931?ref=IAP10", // 10%
                },
                key_stage_open: key_stage_open
            },
            methods: {
                handleClickPromotionFooter(event, type = 'btn') {
                    const clickedElement = event.currentTarget;
                    var text = "buy_n" + (this.courseOwner.course_id == 39 ? "5" : "4") + "_" + (this.activeStep == 1 ? "30" : (this.activeStep == 2 ? "20" : (this.activeStep == 3 ? "10" : "")));
                    if (type === 'link' || clickedElement.classList.contains('promotion-active')) {
                        ga('send', 'event', 'hoc_thu_cate', text, 'buy_label');
                        window.open(this.linkPromotion[this.activeStep], '_blank');
                    }
                },

                buyNow(courseId) {
                    var text = "buy_n" + (courseId == 39 ? "5" : "4") + "_0";
                    ga('send', 'event', 'hoc_thu_cate', text, 'buy_label');
                    window.open('https://m.me/1595926847401625?ref=2352890', '_blank');
                },

                startTimer() {
                    this.timer = setInterval(() => {
                        this.timeElapsed++; // Tăng thời gian đã trôi qua

                        // Cập nhật mốc hiện tại dựa trên thời gian đã trôi qua
                        if (this.timeElapsed <= 15 * 60) {
                            this.activeStep = 1; // Mốc 1: 15 phút đầu
                        } else if (this.timeElapsed <= 15 * 60 + 90 * 60) {
                            this.activeStep = 2; // Mốc 2: 1h30 tiếp theo
                        } else {
                            this.activeStep = 3; // Mốc 3: Thời gian còn lại
                        }
                    }, 1000);
                },
                stopTimer() {
                    clearInterval(this.timer);
                },

                formatTime(seconds) {
                    const hours = Math.floor(seconds / 3600);
                    const minutes = Math.floor((seconds % 3600) / 60);
                    const secs = seconds % 60;

                    return `${hours.toString().padStart(2, '0')}:${minutes
                        .toString()
                        .padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
                },
                initializePromotion(startTime = null) {
                    if (startTime) {
                        // Nếu có thời gian kích hoạt mã từ trước
                        this.startTime = startTime;
                        this.timeElapsed = Number((Date.now() / 1000 + 24 * 60 * 60).toFixed(0)) - startTime
                    } else {
                        // Nếu không có, thiết lập thời gian kích hoạt mới
                        this.startTime = Math.floor(Date.now() / 1000); // Lưu timestamp hiện tại
                        localStorage.setItem("promotionStartTime", this.startTime); // Lưu thời gian kích hoạt vào localStorage
                    }
                    this.startTimer(); // Bắt đầu đếm ngược
                },
                setCookieVideo() {
                    axios.get(@json(url("/video/get-link?url=https://vn.dungmori.com/720p/GIOI-THIEU-KHOA-HOC.mp4/index.m3u8")), {
                        headers: {
                            "Content-Type": "application/json",
                            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                        },
                    }).then(res => {
                        console.log(`res: `, res)
                    }).catch(err => {
                        console.log(`err: `, err)
                    });
                },
                scrollToLastLesson() {
                    if (this.key_stage_open) {
                        this.$nextTick(() => {
                            const lessonElement = $("#step-content-3").find(`#content-stage-${this.key_stage_open}`);
                            if (lessonElement) {
                                window.scrollTo({
                                    top: lessonElement.offset().top,
                                    behavior: 'smooth'
                                });
                            }
                        });
                    }
                }
            },
            beforeCreate () {
                // this.setCookieVideo();
            },
            created() {
                // this.setCookieVideo();
            },
            watch: {},
            mounted() {
                if (this.courseOwner !== null && this.courseOwner.code_active_at !== null) {
                    const serverTimestamp = new Date(this.courseOwner.code_active_at.replace(" ", "T")).getTime() / 1000; // Chuyển thành timestamp

                    if (1000 * (serverTimestamp + 24 * 60 * 60) > Date.now()) {
                        this.initializePromotion(Number(serverTimestamp + 24 * 60 * 60));
                    }
                }
                // this.scrollToLastLesson();
            },
            computed: {
                // Tính toán thời gian còn lại cho từng mốc
                currentTimeLeft() {
                    if (this.timeElapsed !== 0) {
                        if (this.activeStep === 1) {
                            return Math.max(15 * 60 - this.timeElapsed, 0); // Mốc 1: 15 phút
                        } else if (this.activeStep === 2) {
                            return Math.max(90 * 60 - (this.timeElapsed - 15 * 60), 0); // Mốc 2: 1h30p
                        } else {
                            return Math.max(this.totalTime - this.timeElapsed, 0); // Mốc 3: Phần còn lại
                        }
                    } else {
                        return 0;
                    }
                },
                currentCourseTimeLeft() {
                    if (courseOwner.code_active_at !== null) {
                        return Math.max(this.totalTime - this.timeElapsed, 0);
                    } else {
                        return 0;
                    }
                }
            },
            beforeDestroy() {
                this.stopTimer(); // Dừng đếm ngược khi component bị hủy
            },
        })
    </script>
@stop
