@if(in_array($course->id, [39, 40, 44, 45, 46, 47]))
    {{-- LESSON LIST --}}
    <div class="font-beanbag text-xl text-[#1E1E1E]">
        <PERSON><PERSON><PERSON><PERSON> trình học
    </div>
    <div class="flex items-center">
        @php
            $hours = convertMinutesToHours($lessons->sum('expect_time'));
        @endphp
        <span class="text-[#1E1E1E]">
            {{ count($stages) }} Chương・{{ $lessons->whereIn('type', ['video', 'video_test'])->whereIn('group_id', $groups->whereIn('lesson_category_id', $stages->flatten()->pluck('id'))->pluck('id'))->count() }}
            videos bài
            giảng・{{ $hours[0] . ' giờ' }} {{ $hours[1] . ' phút' }}</span>
    </div>
@endif

<div class="stages grid grid-cols-1 gap-4 pt-5 relative">
    @php
        $key = 0;
    @endphp
    @foreach ($stages as $stageName => $stage)
        @php
            $key++;
            $categoryIds = $stage->pluck('id');
            $isOpen = isset(\request()->stage) ? in_array(\request()->stage, $stage->pluck('id')->toArray()) : ($course->SEOurl == 'luyen-de-n4' || $course->SEOurl == 'luyen-de-n5' ? true : $categoryIds->contains($learningLesson->lesson_category_id));
            $totalGroup = $groups->whereIn('lesson_category_id', $categoryIds)->count();
            $groupIds = $groups->whereIn('lesson_category_id', $categoryIds)->pluck('id');
            $totalPercent = $groups->whereIn('lesson_category_id', $categoryIds)->sum('progress');
            $totalPercentStage = $totalGroup > 0 ? round($totalPercent / $totalGroup) : 0;
            $isDisable = !$categories->whereIn('id', $categoryIds)->where('status', 1)->first();
        @endphp
        @if($isDisable)
            <div class="stage rounded-[24px] bg-white {{ $key > 4 && $stageOpen <= 4 ? 'hidden' : 'shadow-[0px_0px_18px_0px]' }}">
                <div class="head disabled p-4 rounded-[24px]" style="background-image: url('{{ url("images/course/bg-category-coming-soon.svg") }}')">
                    <div class="flex items-center">
                        <div class="opacity-50 learning flex-none bg-[#57D0618C] w-[58px] h-[58px] overflow-hidden rounded-[16px]">
                            <img class="max-h-full ml-[11px]" src="{{ asset('/images/icons/learning.png') }}"
                                 alt="learning">
                        </div>
                        <div class="opacity-50 schedule flex-none bg-[#CED1D1] w-[58px] h-[58px] overflow-hidden rounded-[16px]">
                            <img class="max-h-full mt-[5px]" src="{{ asset('/images/icons/schedule.png') }}"
                                 alt="schedule">
                        </div>
                        <div class="opacity-50 ml-4">
                            <div class="text-[#073A3B] font-averta-semibold text-xl">
                                {{ $stageName }}</div>
                            @if(in_array($course->id, [39, 40, 44, 45, 46, 47]))
                                <div class="text-md text-[#212121]">
                                    {{ $lessons->whereIn('type', ['video', 'video_test'])->whereIn('group_id', $groupIds)->count() }}
                                    videos・{{ $lessons->whereIn('type', ['test', 'video_test'])->whereIn('group_id', $groupIds)->count() }}
                                    bài
                                    tập・{{ $lessons->whereIn('type', ['last_exam', 'exam'])->whereIn('group_id', $groupIds)->count() }}
                                    bài
                                    Test
                                </div>
                            @endif
                        </div>
                        <div class="ml-auto text-[#757575] text-base font-averta-bold font-bold">
                            Sắp ra mắt
                        </div>
                    </div>
                </div>
            </div>
        @else
            <div class="stage rounded-[24px] bg-white {{ $key > 4 && $stageOpen <= 4 ? 'hidden' : 'shadow-[0px_0px_18px_0px_#]' }}"  id="content-stage-{{ $key }}">
                <div class="head {{ $isOpen ? 'is-open' : '' }} p-4 rounded-[24px]">
                    <div class="flex items-center">
                        <div class="learning flex-none bg-[#57D0618C] w-[58px] h-[58px] overflow-hidden rounded-[16px]">
                            <img class="max-h-full ml-[11px]" src="{{ asset('/images/icons/learning.png') }}"
                                 alt="learning">
                        </div>
                        <div class="schedule flex-none bg-[#CED1D1] w-[58px] h-[58px] overflow-hidden rounded-[16px]">
                            <img class="max-h-full mt-[5px]" src="{{ asset('/images/icons/schedule.png') }}"
                                 alt="schedule">
                        </div>
                        <div class="ml-4">
                            <div class="text-[#073A3B] font-averta-semibold text-xl">
                                {{ $stageName }}</div>
                            @if(in_array($course->id, [39, 40, 44, 45, 46, 47]))
                                <div class="text-md text-[#212121]">
                                    {{ $lessons->whereIn('type', ['video', 'video_test'])->whereIn('group_id', $groupIds)->count() }}
                                    videos・{{ $lessons->whereIn('type', ['test', 'video_test'])->whereIn('group_id', $groupIds)->count() }}
                                    bài
                                    tập・{{ $lessons->whereIn('type', ['last_exam', 'exam'])->whereIn('group_id', $groupIds)->count() }}
                                    bài
                                    Test
                                </div>
                            @endif
                            @if(in_array($course->id, [30, 41]))
                                <div class="text-md text-[#212121]">
                                    {{ $lessons->whereIn('type', ['last_exam', 'exam'])->whereIn('group_id', $groupIds)->count() }}
                                    bài Test
                                </div>
                            @endif
                        </div>
                        <div
                                class="ml-auto flex-none bg-white w-[40px] h-[40px] rounded-full flex items-center justify-center cursor-pointer">
                            <img class="arrow-icon"
                                 src="{{ asset('/images/icons/arrow-down.png') }}?v={{ $assetVersion }}" alt="arrow">
                        </div>
                    </div>
                    <div class="flex items-center mt-[2px]">
                        @php
                            $stageCategories = $categories->whereIn('stage', $stage->pluck('stage'));
                            $completedRequireCategories = 0;
                            $completedNonRequireCategories = 0;
                            $requiredCategories = 0;
                            foreach ($stageCategories as $category) {
                                $categoryGroups = $groups->where('lesson_category_id', $category->id);
                                $completedRequireGroups = 0;
                                $completedNonRequireGroups = 0;
                                $requiredGroups = 0;

                                foreach ($categoryGroups as $group) {
                                    $groupIsRequired = $group->lessons->where('require', 1)->count();

                                    if ($groupIsRequired) {
                                        $requiredGroups++;
                                    }

                                    if ($groupIsRequired && $group->is_pass) {
                                        $completedRequireGroups++;
                                    }

                                    if (!$groupIsRequired && $group->is_pass) {
                                        $completedNonRequireGroups++;
                                    }
                                }

                                if ($requiredGroups > 0) {
                                    $requiredCategories++;
                                    if ($completedRequireGroups == $requiredGroups) {
                                        $completedRequireCategories += 1;
                                    }
                                } else {
                                    if ($completedNonRequireGroups == $categoryGroups->count()) {
                                        $completedNonRequireCategories += 1;
                                    }
                                }
                            }
                            if ($requiredCategories > 0) {
                                $stagePercent = round($completedRequireCategories * 100 / $requiredCategories);
                            } else {
                                $stagePercent = round($completedNonRequireCategories * 100 / $stageCategories->count());
                            }
                        @endphp
                        <div class="w-[58px] text-center font-averta-bold flex-shrink-0">{{ $stagePercent }}%</div>
                        <div class="ml-4 relative h-[4px] bg-[#F5F5F5] rounded-[16px] w-full">
                            <div class="absolute rounded-[16px] left-0 bottom-0 h-[4px] {{ $stagePercent < 85 ? 'bg-[#E8B931]' : 'bg-[#57D061]' }}" style="width: {{ $stagePercent }}%;"></div>
                        </div>
                    </div>
                    {{--                    <div class="flex items-center mt-1">--}}
                    {{--                    <div class="flex-none w-[58px] text-[#073A3B] text-center font-averta-semibold">--}}
                    {{--                        {{ $totalPercentStage }}%--}}
                    {{--                    </div>--}}
                    {{--                        <div class="ml-4 h-[5px] w-full bg-white rounded-full overflow-hidden">--}}
                    {{--                            <div class="h-full {{ $totalPercentStage < 85 ? 'bg-[#E8B931]' : 'bg-[#57D06199]' }}"--}}
                    {{--                                 style="width: {{ $totalPercentStage }}%"></div>--}}
                    {{--                        </div>--}}
                    {{--                    </div>--}}
                </div>
                <div class="p-6 {{ !$isOpen ? 'hidden' : '' }}">
                    @foreach ($categories->whereIn('id', $categoryIds) as $category)
                        <div
                                class="lesson-group-item my-2 {{ ($isOpen && (\request()->stage == $category->id || $learningLesson->lesson_category_id == $category->id)) || ($course->SEOurl == 'luyen-de-n4' || $course->SEOurl == 'luyen-de-n5') ? 'is-open' : '' }}">
                            <div class="flex justify-stretch justify-items-stretch items-center cursor-pointer lesson-group-item__head pr-[8px]">
                                <div class="flex-none max-w-[80%] md:max-w-[90%] font-averta-semibold text-xl text-[#073A3B]">
                                    {{ $category->title }}
                                </div>
                                <div class="ml-2 mr-4 md:ml-4 md:mr-10 flex-grow h-[2px] bg-[#D0D3DA]"></div>
                                <div class="w-2">
                                    <img class="arrow-icon"
                                         src="{{ asset('/images/icons/arrow-right.png') }}?v={{ $assetVersion }}"
                                         alt="arrow">
                                </div>
                            </div>
                            <div class="py-6 grid grid-cols-1 gap-4 group-list">
                                @foreach ($groups->where('lesson_category_id', $category->id) as $group)
                                    @php
                                        $isLocked =
                                            !$isUnlock &&
                                            $group->lessons
                                                ->where('group_id', $group->id)
                                                ->where('price_option', 2)
                                                ->first();
                                        $isTrial =
                                            !$group->lessons
                                                ->where('group_id', $group->id)
                                                ->where('price_option', 2)
                                                ->first() && !$isUnlock;
                                    @endphp
                                    <div class="flex justify-between">
                                        <div class="flex items-center">
                                            <div class="flex-none">
                                                <img class="w-[16px]"
                                                     src="{{ asset('/images/icons/play2.png') }}?v={{ $assetVersion }}"
                                                     alt="play button">
                                            </div>
                                            <div class="ml-4 {{ isset($group['is_pass']) && $group['is_pass'] ? 'opacity-[0.5]' : '' }} {{ $isLocked ? 'text-[#757575]' : 'text-[#1E1E1E]' }}">
                                                @if ($isTrial)
                                                    <div class="mb-1">
                                                    <span
                                                            class="text-white bg-[#57D061] font-averta-regular rounded-full py-0.5 px-5">Học
                                                        thử</span>
                                                    </div>
                                                @endif
                                                <div class="text-xl cursor-pointer">
                                                    <a class="{{ $isLocked ? 'text-[#757575]' : 'text-[#1E1E1E]' }}"
                                                       href="{{ !empty($isTutorial) ? 'javscript:void(0);' : route('frontend.lesson.group', ['courseSlug' => $course->SEOurl, 'groupId' => $group->id]) }}">
                                                        <span class="font-averta-semibold">{{ $group['name'] }}</span>
                                                    </a>
                                                </div>
                                                <div class="flex items-center gap-2">
                                                    <div class="flex-none flex items-center">
                                                        <svg width="12" height="13" viewBox="0 0 12 13" fill="none"
                                                             xmlns="http://www.w3.org/2000/svg">
                                                            <path d="M10 4.625V9.5C10 11 9.105 11.5 8 11.5H4C2.895 11.5 2 11 2 9.5V4.625C2 3 2.895 2.625 4 2.625C4 2.935 4.12499 3.215 4.32999 3.42C4.53499 3.625 4.815 3.75 5.125 3.75H6.875C7.495 3.75 8 3.245 8 2.625C9.105 2.625 10 3 10 4.625Z"
                                                                  stroke="{{ $isLocked ? '#757575' : '#57D061' }}"
                                                                  stroke-linecap="round" stroke-linejoin="round"/>
                                                            <path d="M8 2.625C8 3.245 7.495 3.75 6.875 3.75H5.125C4.815 3.75 4.53499 3.625 4.32999 3.42C4.12499 3.215 4 2.935 4 2.625C4 2.005 4.505 1.5 5.125 1.5H6.875C7.185 1.5 7.46501 1.625 7.67001 1.83C7.87501 2.035 8 2.315 8 2.625Z"
                                                                  stroke="{{ $isLocked ? '#757575' : '#57D061' }}"
                                                                  stroke-linecap="round" stroke-linejoin="round"/>
                                                            <path d="M4 7H6"
                                                                  stroke="{{ $isLocked ? '#757575' : '#57D061' }}"
                                                                  stroke-linecap="round" stroke-linejoin="round"/>
                                                            <path d="M4 9H8"
                                                                  stroke="{{ $isLocked ? '#757575' : '#57D061' }}"
                                                                  stroke-linecap="round" stroke-linejoin="round"/>
                                                        </svg>
                                                    </div>
                                                    <div class="text-base {{ $isLocked ? 'text-[#757575]' : 'text-[#1E1E1E]' }}">
                                                        {{ $lessons->where('group_id', $group['id'])->count() }}
                                                        @if(in_array($course->id, [39, 40, 44, 45, 46, 47]))
                                                            bài giảng
                                                        @else
                                                            bài test
                                                        @endif
                                                    </div>
                                                    <div class="flex-none flex items-center ml-2">
                                                        <svg width="12" height="13" viewBox="0 0 12 13" fill="none"
                                                             xmlns="http://www.w3.org/2000/svg">
                                                            <path d="M11 6.24121C11 9.00121 8.76 11.2412 6 11.2412C3.24 11.2412 1 9.00121 1 6.24121C1 3.48121 3.24 1.24121 6 1.24121C8.76 1.24121 11 3.48121 11 6.24121Z"
                                                                  stroke="{{ $isLocked ? '#757575' : '#57D061' }}"
                                                                  stroke-linecap="round" stroke-linejoin="round"/>
                                                            <path d="M7.85543 7.83109L6.30543 6.90609C6.03543 6.74609 5.81543 6.36109 5.81543 6.04609V3.99609"
                                                                  stroke="{{ $isLocked ? '#757575' : '#57D061' }}"
                                                                  stroke-linecap="round" stroke-linejoin="round"/>
                                                        </svg>
                                                    </div>
                                                    <div class="text-base {{ $isLocked ? 'text-[#757575]' : 'text-[#1E1E1E]' }}">
                                                        {{ $lessons->where('group_id', $group['id'])->sum('expect_time') }}
                                                        phút
                                                    </div>
                                                </div>
                                            </div>
{{--                                            @if(isset($group['is_pass']) && $group['is_pass'])--}}
{{--                                                <div class="ml-auto pl-10 flex-none">--}}
{{--                                                    <img src="{{ asset('/images/icons/check1.png') }}" alt="check1">--}}
{{--                                                </div>--}}
{{--                                            @endif--}}
                                        </div>

                                        @if ($isLocked)
                                            <div class="ml-auto pl-10 flex-none pr-[2px]">
                                                <img class="w-[20px]" src="{{ asset('/images/icons/lock.png') }}"
                                                     alt="lock">
                                            </div>
                                        @else
                                            <div class="mr-[4px] {{ isset($group['is_pass']) && $group['is_pass'] ? 'opacity-[1]' : 'opacity-[0]' }}">
                                                <svg width="14" height="10" viewBox="0 0 14 10" fill="none"
                                                     xmlns="http://www.w3.org/2000/svg">
                                                    <path d="M12.4191 1L5.08577 8.33333L1.75244 5" stroke="#57D061"
                                                          stroke-width="2" stroke-linecap="round"
                                                          stroke-linejoin="round"/>
                                                </svg>
                                            </div>
                                        @endif
                                    </div>
                                @endforeach
                            </div>
                        </div>
                    @endforeach
                </div>
            </div>
        @endif
    @endforeach
    @if (count($stages) > 4 && $stageOpen <= 4)
        <div class="overlay-foot h-[120px] rounded-[24px] absolute bottom-[-20px] w-full"
             style="background: linear-gradient(180deg, rgba(217, 217, 217, 0) 0%, #F4F5FA 60.02%);">
        </div>
    @endif
</div>

@if (count($stages) > 4 && $stageOpen <= 4)
    <div class="text-center mt-5 btn-show-all-stage">
        <a
                class="text-[#EF6D13] font-averta-semibold text-xl cursor-pointer underline hover:text-[#EF6D13] hover:underline">Xem
            chi
            tiết</a>
    </div>
@endif

{{-- END LESSON LIST --}}
