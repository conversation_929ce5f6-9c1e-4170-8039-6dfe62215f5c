@extends('backend._default.dashboard')

@section('description') <PERSON><PERSON> liệu tỷ lệ học lên @stop
@section('keywords') user @stop
@section('author') dungmori.com @stop
@section('title') Admin | Tỷ lệ học lên @stop

@section('assets')
    <link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datetimepicker/3.1.3/css/bootstrap-datetimepicker.min.css" rel="stylesheet" type="text/css" />
    <link type="text/css" rel="stylesheet" href="{{ asset('assets/backend/css/filterable_list.css') }}?{{filemtime('assets/backend/css/filterable_list.css')}}">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.24.0/moment-with-locales.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datetimepicker/3.1.3/js/bootstrap-datetimepicker.min.js"></script>
    <!-- Vue select-->
    {{-- <script src="{{ asset('/plugin/vue-select/vue-select.js') }}"></script>
    <link rel="stylesheet" href="{{ asset('/plugin/vue-select/vue-select.css') }}"> --}}

    <script src="https://unpkg.com/vue-select@3.0.0"></script>
    <link rel="stylesheet" href="https://unpkg.com/vue-select@3.0.0/dist/vue-select.css">
@stop

@section('content')
    <div id="learnUpRatio">
        <div class="row bg-title">
            <h4 class="page-title pull-left">
                Tỷ lệ học lên lớp VIP (tổng) (chỉ tính các lớp đã kết thúc)
            </h4>
        </div>

        <div class="flex justify-end mt-5">
            <div class="form-group">
                <label>&nbsp;</label>
                <button class="btn btn-info form-control" style="color: #fff" @click="reset()">Reset</button>
            </div>
            <div class="form-group ml-5">
                <label>Loại</label>
                <select id="type" class="form-control" v-model="groupType">
                    <option value="all">Tất cả</option>
                    <option value="vip15">VIP 15</option>
                    <option value="vip500">VIP 500</option>
                </select>
            </div>
            {{-- <div class="form-group ml-5">
                <label>Lớp</label>
                <v-select
                    :options="groupsFilter"
                    label="label"
                    :reduce="(groupsFilter) => groupsFilter.id"
                    v-model="classSearch"
                    :clearable="false"
                >
                </v-select>
            </div> --}}
            <div class="form-group ml-5">
                <label>Giáo viên</label>
                <v-select
                    v-model="classSearch"
                    :options="teachers"
                    label="name"
                    :reduce="(teachers) => teachers.group_ids"
                    :clearable="false"
                >
                </v-select>
            </div>
            <div class="form-group ml-5">
                <label>Từ ngày (lớp gốc)</label>
                <input type="text" class="form-control" name="time_from_base" id="time_from_base" placeholder="Chọn thời gian">
            </div>
            <div class="form-group ml-5">
                <label>Đến ngày (lớp gốc)</label>
                <input type="text" class="form-control" name="time_to_base" id="time_to_base" placeholder="Chọn thời gian">
            </div>
            <div class="form-group ml-5">
                <label>Từ ngày (lớp đích)</label>
                <input type="text" class="form-control" name="time_from" id="time_from" placeholder="Chọn thời gian">
            </div>
            <div class="form-group ml-5">
                <label>Đến ngày (lớp đích)</label>
                <input type="text" class="form-control" name="time_to" id="time_to" placeholder="Chọn thời gian">
            </div>
            <div class="form-group ml-5">
                <label>&nbsp;</label>
                <button class="btn btn-info form-control" style="color: #fff" @click="update()">Cập nhật</button>
            </div>
        </div>

        <div>
            <table class="table">
                <tr>
                    <th class="text-center">Khóa học</th>
                    <th class="text-center">Tổng số học viên</th>
                    <th class="text-center">HS học lên & tỷ lệ</th>
                    <th class="text-center">Theo combo</th>
                    <th class="text-center">Khóa lẻ hoặc khác combo</th>
                </tr>
                <tr>
                    <td class="text-center">N5</td>
                    <td class="text-center">@{{ ratio.n5 }}</td>
                    <td class="text-center">@{{ ratio.n4Up }} (@{{ Math.round(ratio.n4UpRatio * 100) / 100 }}%)</td>
                    <td class="text-center">@{{ ratio.n4UpByCombo }}</td>
                    <td class="text-center">@{{ ratio.n4UpBySingle }}</td>
                </tr>
                <tr>
                    <td class="text-center">N4</td>
                    <td class="text-center">@{{ ratio.n4 }}</td>
                    <td class="text-center">@{{ ratio.n3Up }} (@{{ Math.round(ratio.n3UpRatio * 100) / 100 }}%)</td>
                    <td class="text-center">@{{ ratio.n3UpByCombo }}</td>
                    <td class="text-center">@{{ ratio.n3UpBySingle }}</td>
                </tr>
                <tr>
                    <td class="text-center">N3</td>
                    <td class="text-center">@{{ ratio.n3 }}</td>
                    <td class="text-center">@{{ ratio.n2Up }} (@{{ Math.round(ratio.n2UpRatio * 100) / 100 }}%)</td>
                    <td class="text-center">@{{ ratio.n2UpByCombo }}</td>
                    <td class="text-center">@{{ ratio.n2UpBySingle }}</td>
                </tr>
                <tr>
                    <td class="text-center">N2</td>
                    <td class="text-center">@{{ ratio.n2 }}</td>
                    <td class="text-center">@{{ ratio.n1Up }} (@{{ Math.round(ratio.n1UpRatio * 100) / 100 }}%)</td>
                    <td class="text-center">@{{ ratio.n1UpByCombo }}</td>
                    <td class="text-center">@{{ ratio.n1UpBySingle }}</td>
                </tr>
                <tr>
                    <td class="text-center">N1</td>
                    <td class="text-center">@{{ ratio.n1 }}</td>
                    <td class="text-center">-</td>
                    <td class="text-center">-</td>
                    <td class="text-center">-</td>
                </tr>
                <tr>
                    <td class="text-center">Tổng</td>
                    <td class="text-center">@{{ ratio.n5 + ratio.n4 + ratio.n3 + ratio.n2 + ratio.n1 }}</td>
                    <td class="text-center">@{{ ratio.n4Up + ratio.n3Up + ratio.n2Up + ratio.n1Up }}</td>
                    <td class="text-center">@{{ ratio.n4UpByCombo + ratio.n3UpByCombo + ratio.n2UpByCombo + ratio.n1UpByCombo }}</td>
                    <td class="text-center">@{{ ratio.n4UpBySingle + ratio.n3UpBySingle + ratio.n2UpBySingle + ratio.n1UpBySingle }}</td>
                </tr>
            </table>

            <h3>N5</h3>
            <table class="table">
                <tr>
                    <th class="text-center">ID</th>
                    <th class="text-center" width="50%">Lớp</th>
                    <th class="text-center">Tổng số học viên</th>
                    <th class="text-center">HS học lên & tỷ lệ</th>
                </tr>
                <tr v-for="(c, i) in ratio.n5NumberStudent">
                    <td class="text-center">@{{ c.group_id }}</td>
                    <td class="text-center" width="50%">@{{ groups.find((g) => g.id == c.group_id).name }}</td>
                    <td class="text-center">@{{ c.count }}</td>
                    <td class="text-center cursor-pointer" @click="showDetailStudents(c.group_id, 'N5')">@{{ calculateNumberStudent(ratio.n5LearnUpRatioAll, c.group_id) }} (@{{ calculateRatioUp(ratio.n5LearnUpRatioAll, c) }}%)</td>
                </tr>
            </table>
            <h3>N4</h3>
            <table class="table">
                <tr>
                    <th class="text-center">ID</th>
                    <th class="text-center" width="50%">Lớp</th>
                    <th class="text-center">Tổng số học viên</th>
                    <th class="text-center">HS học lên & tỷ lệ</th>
                </tr>
                <tr v-for="(c, i) in ratio.n4NumberStudent">
                    <td class="text-center">@{{ c.group_id }}</td>
                    <td class="text-center" width="50%">@{{ groups.find((g) => g.id == c.group_id).name }}</td>
                    <td class="text-center">@{{ c.count }}</td>
                    <td class="text-center cursor-pointer" @click="showDetailStudents(c.group_id, 'N4')">@{{ calculateNumberStudent(ratio.n4LearnUpRatioAll, c.group_id) }} (@{{ calculateRatioUp(ratio.n4LearnUpRatioAll, c) }}%)</td>
                </tr>
            </table>
            <h3>N3</h3>
            <table class="table">
                <tr>
                    <th class="text-center">ID</th>
                    <th class="text-center" width="50%">Lớp</th>
                    <th class="text-center">Tổng số học viên</th>
                    <th class="text-center">HS học lên & tỷ lệ</th>
                </tr>
                <tr v-for="(c, i) in ratio.n3NumberStudent">
                    <td class="text-center">@{{ c.group_id }}</td>
                    <td class="text-center" width="50%">@{{ groups.find((g) => g.id == c.group_id).name }}</td>
                    <td class="text-center">@{{ c.count }}</td>
                    <td class="text-center cursor-pointer" @click="showDetailStudents(c.group_id, 'N3')">@{{ calculateNumberStudent(ratio.n3LearnUpRatioAll, c.group_id) }} (@{{ calculateRatioUp(ratio.n3LearnUpRatioAll, c) }}%)</td>
                </tr>
            </table>
            <h3>N2</h3>
            <table class="table">
                <tr>
                    <th class="text-center">ID</th>
                    <th class="text-center" width="50%">Lớp</th>
                    <th class="text-center">Tổng số học viên</th>
                    <th class="text-center">HS học lên & tỷ lệ</th>
                </tr>
                <tr v-for="(c, i) in ratio.n2NumberStudent">
                    <td class="text-center">@{{ c.group_id }}</td>
                    <td class="text-center" width="50%">@{{ groups.find((g) => g.id == c.group_id).name }}</td>
                    <td class="text-center">@{{ c.count }}</td>
                    <td class="text-center cursor-pointer" @click="showDetailStudents(c.group_id, 'N2')">@{{ calculateNumberStudent(ratio.n2LearnUpRatioAll, c.group_id) }} (@{{ calculateRatioUp(ratio.n2LearnUpRatioAll, c) }}%)</td>
                </tr>
            </table>
            <h3>N1</h3>
            <table class="table">
                <tr>
                    <th class="text-center">ID</th>
                    <th class="text-center" width="50%">Lớp</th>
                    <th class="text-center">Tổng số học viên</th>
                    <th class="text-center">HS học lên & tỷ lệ</th>
                </tr>
                <tr v-for="(c, i) in ratio.n1NumberStudent">
                    <td class="text-center">@{{ c.group_id }}</td>
                    <td class="text-center" width="50%">@{{ groups.find((g) => g.id == c.group_id).name }}</td>
                    <td class="text-center">@{{ c.count }}</td>
                    <td class="text-center">-</td>
                </tr>
            </table>
        </div>

        <div id="detail_students" class="modal fade show-people" role="dialog" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header flex">
                        <h4 class="modal-title flex-1">Danh sách</h4>
                        <button type="button" class="close" data-dismiss="modal">&times;</button>
                    </div>
                    <div class="modal-body">
                        <div
                            v-for="(student, index) in students"
                            :key="student.id"
                            class="flex items-center my-1"
                            v-bind:style="index == 0 ? 'border: 0' : ''"
                        >
                            <img
                                v-if="student.avatar"
                                class="w-10 h-10 mr-2 rounded-full"
                                :src="'{{ url('cdn/avatar/small') }}' + '/' + student.avatar"
                            />
                            <img
                                v-else
                                class="w-10 h-10 mr-2 rounded-full"
                                src="{{ url('assets/img/default-avatar.jpg') }}"
                            />
                            <div class="flex-1 member-name">
                                <b>@{{ student.name }}</b><br>
                                <span>@{{ student.email }}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="{{ asset('/plugin/moment/moment.js') }}"></script>
    <script src="{{ asset('/plugin/deparam/deparam.min.js') }}"></script>
    <script>
        Vue.component("v-select", VueSelect.VueSelect);
        var ratio = <?php echo $result; ?>;
        var vipClass = <?php echo $vipClass; ?>;
        var teachers = <?php echo $teachers; ?>;
        for (var i = 0; i < teachers.length; i++) {
            teachers[i].name = teachers[i].last_name + ' ' + teachers[i].first_name;
            teachers[i].group_ids = teachers[i].group_ids.split(',');
        }
        teachers.unshift({ group_ids: 'all', name: 'Tất cả' });
        // for (var i = 0; i < vipClass.length; i++) {
        //     vipClass[i].label = `(${vipClass[i].vip_level}) ${vipClass[i].name} `;
        // }
        var vipChart = new Vue({
            el: '#learnUpRatio',
            data: {
                ratio: ratio,
                groups: [...vipClass],
                groupType: 'all',
                classSearch: 'all',
                teachers: teachers,
                students: []
            },
            computed: {
                groupsFilter() {
                    var vm = this;
                    var temp = [...this.groups];
                    var result1 = temp.filter((t) => vm.groupType === 'all' || t.type === vm.groupType);
                    var result = [...result1];
                    result.forEach((group) => {
                        group.label = `(${group.vip_level}) ${group.name} `;
                        return group;
                    });
                    result.unshift({ id: 'all', label: 'Tất cả' });
                    return result;
                }
            },
            methods: {
                update() {
                    var vm = this;
                    var timeFromBase = $('#time_from_base').val() || 'all';
                    var timeToBase = $('#time_to_base').val() || 'all';
                    var timeFrom = $('#time_from').val() || 'all';
                    var timeTo = $('#time_to').val() || 'all';
                    $.ajax({
                        type: 'get',
                        url: '/backend/vip/ratio-data/' + vm.groupType + '/' + timeFromBase + '/' + timeToBase + '/' + timeFrom + '/' + timeTo + '/' + vm.classSearch,
                        success: function(response) {
                            vm.ratio = JSON.parse(response);
                        }
                    });
                },
                reset() {
                    this.ratio = ratio;
                    this.groups = [...vipClass];
                    this.groupType = 'all';
                    this.classSearch = 'all';
                    $('#time_from_base').val('');
                    $('#time_to_base').val('');
                    $('#time_from').val('');
                    $('#time_to').val('');
                },
                calculateNumberStudent(ratio, groupId) {
                    return ratio.find((t) => t.group_id == groupId)?.count ?? 0;
                },
                calculateRatioUp(ratio, c) {
                    return Math.round(this.calculateNumberStudent(ratio, c.group_id) * 100 / c.count * 100) / 100;
                },
                showDetailStudents(groupId, level) {
                    $('#detail_students').modal('toggle');
                    var vm = this;
                    var timeFromBase = $('#time_from_base').val() || 'all';
                    var timeToBase = $('#time_to_base').val() || 'all';
                    var timeFrom = $('#time_from').val() || 'all';
                    var timeTo = $('#time_to').val() || 'all';
                    $.ajax({
                        type: 'get',
                        url: '/backend/vip/detail-students/' + groupId + '/' + vm.groupType + '/' + level + '/' + timeFromBase + '/' + timeToBase + '/' + timeFrom + '/' + timeTo,
                        success: function(response) {
                            vm.students = response;
                        }
                    });
                }
            },
        });
    </script>
    <script>
        $(function () {
            $('#time_from_base').datetimepicker({
                language: 'vi',
                format: 'DD-MM-YYYY HH:mm'
            });
            $('#time_to_base').datetimepicker({
                language: 'vi',
                format: 'DD-MM-YYYY HH:mm'
            });
            $('#time_from').datetimepicker({
                language: 'vi',
                format: 'DD-MM-YYYY HH:mm'
            });
            $('#time_to').datetimepicker({
                language: 'vi',
                format: 'DD-MM-YYYY HH:mm'
            });
        });
    </script>

    <style>
        #learnUpRatio .vs__dropdown-toggle {
            min-height: 39px;
        }
        #learnUpRatio .v-select {
            width: 400px;
        }
    </style>
@stop
