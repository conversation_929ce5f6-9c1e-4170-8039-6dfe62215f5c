@charset "UTF-8";
.opacity-0-important {
  opacity: 0 !important;
}

.text-result p span, .text-result {
  font-size: 24px !important;
}

.pulse-container-flashcard {
  position: absolute;
  top: -40px;
  width: 100%;
  height: -webkit-fill-available;
  overflow: hidden;
}

.pulse-circle-flashcard {
  top: -130px;
  left: 25%;
  width: 52%;
  height: 272px;
  border-radius: 50%;
  position: absolute;
  -webkit-transition: -webkit-transform 0.1s ease-in-out;
  transition: -webkit-transform 0.1s ease-in-out;
  transition: transform 0.1s ease-in-out;
  transition: transform 0.1s ease-in-out, -webkit-transform 0.1s ease-in-out;
  -webkit-filter: blur(35px);
          filter: blur(35px);
}

.card-wrap::after {
  content: "";
  position: fixed;
  bottom: 71px;
  left: 0;
  right: 0;
  height: 30px;
  background: -webkit-gradient(linear, left top, left bottom, from(transparent), to(white));
  background: -webkit-linear-gradient(top, transparent, white);
  background: linear-gradient(to bottom, transparent, white);
  pointer-events: none;
  -webkit-transition: opacity 0.3s ease;
  transition: opacity 0.3s ease;
  width: calc(100% - 2rem);
  margin-left: 1rem;
}

.card-wrap-back::after {
  content: "";
  position: fixed;
  bottom: 224px;
  left: 0;
  right: 0;
  height: 30px;
  background: -webkit-gradient(linear, left top, left bottom, from(transparent), to(white));
  background: -webkit-linear-gradient(top, transparent, white);
  background: linear-gradient(to bottom, transparent, white);
  pointer-events: none;
  -webkit-transition: opacity 0.3s ease;
  transition: opacity 0.3s ease;
  width: calc(100% - 2rem);
  margin-left: 1rem;
}

/* Class để ẩn phần mờ */
.card-wrap.fade-hidden::after {
  opacity: 0;
}

.card-wrap-back::-webkit-scrollbar, .card-wrap::-webkit-scrollbar {
  width: 6px;
  border-radius: 10px;
}

.card-wrap-back::-webkit-scrollbar-track, .card-wrap::-webkit-scrollbar-track {
  border-radius: 6px;
}

.card-wrap-back::-webkit-scrollbar-thumb, .card-wrap::-webkit-scrollbar-thumb {
  background: #D9D9D9;
  border-radius: 10px;
}

.card-wrap-back::-webkit-scrollbar-thumb:hover, .card-wrap::-webkit-scrollbar-thumb:hover {
  background: #8e8d8d;
}

.language-switch, .shuffle-switch {
  position: relative;
  display: inline-block;
  width: 64px;
  height: 32px;
}

.language-switch input, .shuffle-switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.language-slider, .shuffle-slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  -webkit-transition: 0.4s;
  transition: 0.4s;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -webkit-align-items: center;
      -ms-flex-align: center;
          align-items: center;
  padding: 0 8px;
}

.language-slider {
  background-color: #CCF8D1;
}

.shuffle-slider {
  background-color: #B3B3B3;
}

.language-slider .slider-text {
  font-family: Beanbag_Dungmori_Rounded_Medium;
  color: #757575;
  font-size: 12px;
  font-weight: bold;
  position: absolute;
  right: 8px;
}

.language-slider .flag {
  position: absolute;
  height: 26px;
  width: 26px;
  left: -6px;
  bottom: 0;
  -webkit-transition: 0.4s;
  transition: 0.4s;
  border-radius: 50%;
  background-size: cover;
  background-position: center;
}

.flag.vi {
  background-image: url("/path-to-your-vietnam-flag.png");
  background: #FF0000;
  position: relative;
}

.flag.vi::after {
  content: "★";
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
      -ms-transform: translate(-50%, -50%);
          transform: translate(-50%, -50%);
  color: #FFFF00;
  font-size: 14px;
}

.flag.ja {
  background-image: url("/path-to-your-japan-flag.png"); /* Thay đổi đường dẫn tới ảnh cờ Nhật */
  /* Hoặc sử dụng CSS để vẽ cờ */
  background: #FFFFFF;
  position: relative;
}

.flag.ja::after {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
      -ms-transform: translate(-50%, -50%);
          transform: translate(-50%, -50%);
  width: 12px;
  height: 12px;
  background: #FF0000;
  border-radius: 50%;
}

input:checked + .language-slider, .shuffle-switch input:checked + .shuffle-slider {
  background-color: #CCF8D1;
}

input:checked + .language-slider .flag {
  -webkit-transform: translateX(32px);
      -ms-transform: translateX(32px);
          transform: translateX(32px);
}

input:checked + .language-slider .slider-text {
  left: 8px;
  right: auto;
}

.language-slider.round, .shuffle-slider.round {
  border-radius: 34px;
}

/* Thêm hiệu ứng hover */
.language-switch:hover .language-slider, .shuffle-switch:hover .shuffle-slider {
  opacity: 0.9;
}

.shuffle-slider .slider-circle {
  position: absolute;
  content: "";
  height: 26px;
  width: 26px;
  left: 4px;
  bottom: 3px;
  background-color: #D9D9D9;
  -webkit-transition: 0.4s;
  transition: 0.4s;
  border-radius: 50%;
}

.shuffle-switch input:checked + .shuffle-slider .slider-circle {
  -webkit-transform: translateX(30px);
      -ms-transform: translateX(30px);
          transform: translateX(30px);
  background-color: #07403F;
}

/* Popover styles */
.popover_setting_content {
  margin-top: 10px;
}

.popover_setting_content::before {
  content: "";
  position: absolute;
  margin: 15px;
  top: -24px;
  right: 30px;
  width: 16px;
  height: 16px;
  background-color: white;
  -webkit-transform: rotate(45deg);
      -ms-transform: rotate(45deg);
          transform: rotate(45deg);
  -webkit-box-shadow: -2px -2px 5px rgba(0, 0, 0, 0.06);
          box-shadow: -2px -2px 5px rgba(0, 0, 0, 0.06);
}

.popover_setting_content > div {
  background-color: white;
  position: relative;
  z-index: 1;
}

.popover_setting_content_item {
  -webkit-transition: background-color 0.2s ease;
  transition: background-color 0.2s ease;
}

.flashcards-wrap {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-flow: column;
      -ms-flex-flow: column;
          flex-flow: column;
  -webkit-box-align: center;
  -webkit-align-items: center;
      -ms-flex-align: center;
          align-items: center;
  position: relative;
  /* elements on stacked cards */
}
.flashcards-wrap .a_cursor--pointer {
  cursor: pointer;
}
.flashcards-wrap .content {
  width: 80%;
}
.flashcards-wrap .no-transition {
  -webkit-transition: none !important;
  transition: none !important;
}
.flashcards-wrap .stackedcards.init {
  opacity: 0;
  /* set về 0 để tạo hiệu ứng hiện ra từ hư vô thật ma mị */
}
.flashcards-wrap .stackedcards {
  position: relative;
}
.flashcards-wrap .stackedcards * {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}
.flashcards-wrap .stackedcards--animatable {
  -webkit-transition: all 400ms ease;
  transition: all 400ms ease;
}
.flashcards-wrap .stackedcards .stackedcards-container > *,
.flashcards-wrap .stackedcards-overlay {
  position: absolute;
  width: 100%;
  height: 100%;
  will-change: transform, opacity;
  top: 0;
  border-radius: 10px 10px 0 0;
}
.flashcards-wrap .stackedcards-overlay.left > div,
.flashcards-wrap .stackedcards-overlay.right > div,
.flashcards-wrap .stackedcards-overlay.top > div {
  width: 100%;
  height: 100%;
  -webkit-box-align: center;
  -ms-flex-align: center;
  -webkit-align-items: center;
          align-items: center;
  display: -webkit-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
          justify-content: center;
}
.flashcards-wrap .stackedcards-overlay.left,
.flashcards-wrap .stackedcards-overlay.right,
.flashcards-wrap .stackedcards-overlay.top {
  -webkit-box-align: center;
  -ms-flex-align: center;
  -webkit-align-items: center;
          align-items: center;
  display: -webkit-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
          justify-content: center;
  left: 0;
  opacity: 0;
  top: -44px;
  height: 100%;
  font-size: 24px;
  font-weight: 700;
}
.flashcards-wrap .stackedcards-overlay.top {
  background: transparent;
  color: transparent;
}
.flashcards-wrap .stackedcards-overlay.right {
  height: 50px;
  background: #CEFFD8;
}
.flashcards-wrap .stackedcards-overlay.left {
  height: 50px;
  background: #FFF193;
}
.flashcards-wrap .stackedcards-overlay.left:empty,
.flashcards-wrap .stackedcards-overlay.right:empty,
.flashcards-wrap .stackedcards-overlay.top:empty {
  display: none !important;
}
.flashcards-wrap .stackedcards-overlay-hidden {
  display: none;
}
.flashcards-wrap .stackedcards-origin-top {
  -webkit-transform-origin: top;
  -ms-transform-origin: top;
  transform-origin: top;
}
.flashcards-wrap .stackedcards-top {
  background: transparent;
  height: 100%;
}
.flashcards-wrap .stackedcards .stackedcards-container > :nth-child(1) {
  position: relative;
  display: block;
}
.flashcards-wrap .card-item {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -webkit-align-items: center;
      -ms-flex-align: center;
          align-items: center;
  width: 100%;
  height: 100%;
}
.flashcards-wrap .card-item .card-inner.flip {
  -webkit-transform: rotateY(180deg);
          transform: rotateY(180deg);
}
.flashcards-wrap .card-item .card-inner {
  position: relative;
  -webkit-transition: -webkit-transform 0.6s;
  transition: -webkit-transform 0.6s;
  transition: transform 0.6s;
  transition: transform 0.6s, -webkit-transform 0.6s;
  width: 100%;
  min-height: 550px;
  transform-style: preserve-3d;
  -webkit-transform-style: preserve-3d;
}
.flashcards-wrap .card-item .card-inner .card__face {
  padding: 20px 20px 20px 20px;
  -webkit-box-shadow: 0 6px 12px 0 rgba(0, 0, 0, 0.3);
          box-shadow: 0 6px 12px 0 rgba(0, 0, 0, 0.3);
  border-radius: 32px;
  position: absolute;
  height: 100%;
  width: 100%;
  backface-visibility: hidden;
  -moz-backface-visibility: hidden;
  -webkit-backface-visibility: hidden;
  background-color: #FFF;
}
.flashcards-wrap .card-item .card-inner .card__face .card-inner.flip {
  -webkit-transform: rotateY(180deg);
          transform: rotateY(180deg);
}
.flashcards-wrap .card-item .card-inner .card__face--back {
  -webkit-transform: rotateY(180deg);
  transform: rotateY(180deg);
}

.left-action, .right-action {
  -webkit-transition: background-color 0.3s ease;
  transition: background-color 0.3s ease;
}

.left-action:hover, .left-action:active, .left-action.active {
  background-color: #FFE271;
}

.right-action:hover, .right-action:active, .right-action.active {
  background-color: #98FFAE;
}

/* Vocabulary Search Bar */
.vocabulary-search-container {
  width: 100%;
  margin: 0 auto 30px;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -webkit-align-items: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-transition: all 0.3s ease-in-out;
  transition: all 0.3s ease-in-out; /* Animation cho container */
}

.vocabulary-search-bar:hover, .vocabulary-favorite-search-bar:hover {
  -webkit-box-shadow: 0 1px 6px rgba(32, 33, 36, 0.4);
          box-shadow: 0 1px 6px rgba(32, 33, 36, 0.4);
}

.vocabulary-search-bar:focus-within, .vocabulary-favorite-search-bar:focus-within {
  -webkit-box-shadow: 0 1px 10px rgba(32, 33, 36, 0.4);
          box-shadow: 0 1px 10px rgba(32, 33, 36, 0.4);
}

/* Style cho button quay lại */
.vocabulary-search-btn-back {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -webkit-align-items: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
      -ms-flex-pack: center;
          justify-content: center;
  background-color: #fff;
  border-radius: 50%;
  -webkit-box-shadow: 0 1px 6px rgba(32, 33, 36, 0.28);
          box-shadow: 0 1px 6px rgba(32, 33, 36, 0.28);
  margin-right: 15px;
  -webkit-transition: all 0.2s;
  transition: all 0.2s;
  cursor: pointer;
}

.vocabulary-search-btn-back:hover {
  -webkit-box-shadow: 0 1px 6px rgba(32, 33, 36, 0.4);
          box-shadow: 0 1px 6px rgba(32, 33, 36, 0.4);
  background-color: rgba(7, 64, 63, 0.05);
}

.vocabulary-search-btn-back i {
  font-size: 22px;
  color: #07403F;
}

.search-icon {
  color: #9aa0a6;
  margin-right: 12px;
  font-size: 22px;
}

.search-input {
  -webkit-box-flex: 1;
  -webkit-flex: 1;
      -ms-flex: 1;
          flex: 1;
  border: none;
  outline: none;
  font-size: 16px;
  color: #202124;
  background: transparent;
  width: 100%; /* Đảm bảo input lấp đầy không gian */
  -webkit-transition: all 0.3s ease-in-out;
  transition: all 0.3s ease-in-out; /* Animation mượt mà khi thay đổi kích thước */
}

.search-input::-webkit-input-placeholder {
  color: #9aa0a6;
  font-family: "Averta-Regular";
  font-size: 20px;
}

.search-input::-moz-placeholder {
  color: #9aa0a6;
  font-family: "Averta-Regular";
  font-size: 20px;
}

.search-input:-ms-input-placeholder {
  color: #9aa0a6;
  font-family: "Averta-Regular";
  font-size: 20px;
}

.search-input::-ms-input-placeholder {
  color: #9aa0a6;
  font-family: "Averta-Regular";
  font-size: 20px;
}

.search-input::placeholder {
  color: #9aa0a6;
  font-family: "Averta-Regular";
  font-size: 20px;
}

/* Chart Overview Styles */
.chart-overview-container {
  height: 430px;
}

.chart-header {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
      -ms-flex-direction: column;
          flex-direction: column;
  margin-left: 12px;
  background: rgb(240, 255, 241);
  border-radius: 32px;
  padding: 20px;
  -webkit-box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.checkbox-container {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -webkit-align-items: center;
      -ms-flex-align: center;
          align-items: center;
  cursor: pointer;
}

/* Ẩn checkbox mặc định */
.checkbox-container input[type=checkbox] {
  position: absolute;
  opacity: 0;
  cursor: pointer;
  height: 0;
  width: 0;
}

/* Cập nhật style cho checkbox container */
.filter-checkboxes .checkbox-container {
  position: relative;
  padding-left: 28px;
  margin-bottom: 8px;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -webkit-align-items: center;
      -ms-flex-align: center;
          align-items: center;
  cursor: pointer;
}

/* Tạo checkbox giả hình tròn */
.filter-checkboxes .checkbox-container::before {
  content: "";
  position: absolute;
  left: 0;
  top: 50%;
  -webkit-transform: translateY(-50%);
      -ms-transform: translateY(-50%);
          transform: translateY(-50%);
  width: 18px;
  height: 18px;
  border: 2px solid #407845;
  border-radius: 50%; /* Làm tròn checkbox */
  background-color: #fff;
  -webkit-transition: all 0.2s ease;
  transition: all 0.2s ease;
}

/* Tạo dấu check bên trong */
.filter-checkboxes .checkbox-container input[type=checkbox]:checked ~ .checkbox-label::after {
  content: "";
  position: absolute;
  left: -24px;
  top: 50%;
  -webkit-transform: translateY(-50%);
      -ms-transform: translateY(-50%);
          transform: translateY(-50%);
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background-color: #407845;
  -webkit-transition: all 0.2s ease;
  transition: all 0.2s ease;
}

/* Hiệu ứng hover */
.filter-checkboxes .checkbox-container:hover::before {
  border-color: #2c5530;
}

.filter-checkboxes .checkbox-label {
  color: #176867;
  position: relative;
  cursor: pointer;
}

@media (max-width: 768px) {
  .chart-header {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -webkit-flex-direction: column;
        -ms-flex-direction: column;
            flex-direction: column;
  }
  .chart-filters {
    margin-top: 10px;
  }
  .filter-checkboxes {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -webkit-flex-direction: column;
        -ms-flex-direction: column;
            flex-direction: column;
    gap: 8px;
  }
}
/* Style cho thanh scrollbar */
.filter-checkboxes::-webkit-scrollbar {
  width: 6px; /* Độ rộng thanh scrollbar */
}

.filter-checkboxes::-webkit-scrollbar-track {
  background: transparent; /* Không có background */
}

.filter-checkboxes::-webkit-scrollbar-thumb {
  background-color: #407845; /* Màu của thanh scrollbar */
  border-radius: 10px; /* Bo tròn thanh scrollbar */
  border: none; /* Không có border */
}

/* Ẩn các nút ở 2 đầu trên dưới */
.filter-checkboxes::-webkit-scrollbar-button {
  display: none; /* Ẩn các nút ở 2 đầu */
}

/* Style cho Firefox */
.filter-checkboxes {
  scrollbar-width: thin; /* Độ rộng thanh scrollbar trên Firefox */
  scrollbar-color: #407845 transparent; /* Màu của thanh scrollbar và background trên Firefox */
}

.dialog-wrapper.custom-scrollbar {
  overflow-y: auto;
  overflow-x: hidden;
  scrollbar-width: thin;
  scrollbar-color: #57D061 transparent;
}

.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: transparent;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background-color: #407845;
  border-radius: 10px;
  border: none;
}

/* Style cho phần không tìm thấy kết quả */
.search-not-found {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
      -ms-flex-direction: column;
          flex-direction: column;
  -webkit-box-align: center;
  -webkit-align-items: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
      -ms-flex-pack: center;
          justify-content: center;
  padding: 40px 20px;
  text-align: center;
  min-height: 400px;
}

.search-not-found-image {
  margin-bottom: 20px;
  max-width: 250px;
  width: 100%;
}

.search-not-found-image img {
  width: 100%;
  height: auto;
}

.search-not-found-text {
  font-size: 18px;
  color: #757575;
  font-family: "Averta-Regular";
  max-width: 400px;
  line-height: 1.5;
}

.highlight {
  /* bóng xung quanh element và lập lòe kiểu blur nhấp nháy */
  -webkit-box-shadow: 0 0 10px #57D061;
          box-shadow: 0 0 10px #57D061;
  -webkit-animation: pulse 2s infinite;
          animation: pulse 2s infinite;
}

@-webkit-keyframes pulse {
  0% {
    -webkit-box-shadow: 0 0 10px #57D061;
            box-shadow: 0 0 10px #57D061;
  }
  50% {
    -webkit-box-shadow: 0 0 24px #57D061;
            box-shadow: 0 0 24px #57D061;
  }
  100% {
    -webkit-box-shadow: 0 0 10px #57D061;
            box-shadow: 0 0 10px #57D061;
  }
}

@keyframes pulse {
  0% {
    -webkit-box-shadow: 0 0 10px #57D061;
            box-shadow: 0 0 10px #57D061;
  }
  50% {
    -webkit-box-shadow: 0 0 24px #57D061;
            box-shadow: 0 0 24px #57D061;
  }
  100% {
    -webkit-box-shadow: 0 0 10px #57D061;
            box-shadow: 0 0 10px #57D061;
  }
}
.dialog-vocabulary-info .el-dialog {
  background: url("/images/vocabulary/bg-info-popup.svg"), #FFFFFF !important;
  background-position: top center !important;
  background-repeat: no-repeat !important;
  border-radius: 32px; /* Bo viền */
  max-width: 1224px !important;
}

/* Flashcard Popup Styles */
.flashcard-popup-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -webkit-align-items: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
      -ms-flex-pack: center;
          justify-content: center;
  z-index: 1000;
}

.flashcard-popup {
  width: 90%;
  max-width: 800px;
  max-height: 90vh;
}

.flashcard-popup-content {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -webkit-align-items: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
      -ms-flex-pack: justify;
          justify-content: space-between;
  padding: 30px;
  -webkit-box-flex: 1;
  -webkit-flex: 1;
      -ms-flex: 1;
          flex: 1;
}

.flashcard-nav-button {
  background-color: #CCF8D1;
  color: #07403F;
  border: none;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -webkit-align-items: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
      -ms-flex-pack: center;
          justify-content: center;
  font-size: 18px;
  cursor: pointer;
  -webkit-transition: background-color 0.2s, -webkit-transform 0.2s;
  transition: background-color 0.2s, -webkit-transform 0.2s;
  transition: background-color 0.2s, transform 0.2s;
  transition: background-color 0.2s, transform 0.2s, -webkit-transform 0.2s;
}

.flashcard-nav-button:hover {
  background-color: #0a5e5c;
  -webkit-transform: scale(1.05);
      -ms-transform: scale(1.05);
          transform: scale(1.05);
}

.flashcard-nav-button:disabled {
  background-color: #ccc;
  cursor: not-allowed;
  -webkit-transform: none;
      -ms-transform: none;
          transform: none;
}

.flashcard-container {
  -webkit-box-flex: 1;
  -webkit-flex: 1;
      -ms-flex: 1;
          flex: 1;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-align: center;
  -webkit-align-items: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-perspective: 1000px;
          perspective: 1000px;
  margin: 0 20px;
  cursor: pointer;
}

.flashcard {
  width: 100%;
  height: 400px;
  position: relative;
  -webkit-transform-style: preserve-3d;
          transform-style: preserve-3d;
  -webkit-transition: -webkit-transform 0.6s;
  transition: -webkit-transform 0.6s;
  transition: transform 0.6s;
  transition: transform 0.6s, -webkit-transform 0.6s;
}

.flashcard.flipped {
  -webkit-transform: rotateY(180deg);
          transform: rotateY(180deg);
}

.flashcard-front, .flashcard-back {
  position: absolute;
  width: 100%;
  height: 100%;
  -webkit-backface-visibility: hidden;
          backface-visibility: hidden;
  border-radius: 16px;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
      -ms-flex-direction: column;
          flex-direction: column;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-align: center;
  -webkit-align-items: center;
      -ms-flex-align: center;
          align-items: center;
  padding: 30px;
  -webkit-box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
          box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
}

.flashcard-front {
  background-color: #f8f9fa;
  border: 2px solid #07403F;
}

.flashcard-back {
  background-color: #07403F;
  color: white;
  -webkit-transform: rotateY(180deg);
          transform: rotateY(180deg);
}

.flashcard-word {
  font-size: 48px;
  font-weight: bold;
  margin-bottom: 20px;
  text-align: center;
}

.flashcard-reading {
  font-size: 24px;
  color: #666;
  text-align: center;
}

.flashcard-meaning {
  font-size: 36px;
  font-weight: bold;
  margin-bottom: 30px;
  text-align: center;
}

.flashcard-example {
  width: 100%;
  max-width: 500px;
}

.example-jp {
  font-size: 20px;
  margin-bottom: 10px;
  text-align: center;
}

.example-vi {
  font-size: 18px;
  color: #ccc;
  text-align: center;
}

.vocabulary-container {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  min-height: 100vh;
}

.nav-menu {
  padding-top: 76px;
  width: 280px;
  background-color: #f8f9fa;
  border-right: 1px solid #e9ecef;
  -webkit-transition: all 0.3s ease;
  transition: all 0.3s ease;
  overflow: hidden;
}

.nav-menu.collapsed {
  /* width: 60px; Độ rộng khi thu gọn */
  background-color: #F4F5FA;
  border-right: none;
}

#navMenu .nav-menu-header {
  padding: 15px 35px;
  border-bottom: 1px solid #e9ecef;
  white-space: nowrap;
  -webkit-transition: all 0.3s ease;
  transition: all 0.3s ease;
}

.nav-menu.collapsed .nav-menu-header {
  margin-top: 15px;
  margin-left: 15px;
  height: 48px;
  border-bottom: none;
  padding: 10px;
  background-color: white;
  border-radius: 12px;
  cursor: pointer;
  -webkit-box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
          box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  -webkit-transition: all 0.3s ease;
  transition: all 0.3s ease;
}

.nav-menu-items {
  max-height: calc(100vh - 120px);
  overflow-y: auto;
  -webkit-transition: all 0.3s ease;
  transition: all 0.3s ease;
}

.nav-menu.collapsed .nav-menu-items {
  max-height: 0;
  opacity: 0;
  visibility: hidden;
  -webkit-transition: all 0.3s ease, opacity 0.2s ease, visibility 0s linear 0.3s;
  transition: all 0.3s ease, opacity 0.2s ease, visibility 0s linear 0.3s;
}

/* Thêm hiệu ứng mượt mà khi hover */
.nav-menu.collapsed .nav-menu-header:hover {
  background-color: #e9ecef;
}

.nav-menu-header {
  padding: 15px;
  border-bottom: 1px solid #e9ecef;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
      -ms-flex-pack: justify;
          justify-content: space-between;
  -webkit-box-align: center;
  -webkit-align-items: center;
      -ms-flex-align: center;
          align-items: center;
}

.nav-menu-title {
  font-size: 18px;
  font-weight: bold;
  color: #07403F;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.toggle-menu {
  cursor: pointer;
  color: #07403F;
}

.nav-menu-items {
  padding: 20px;
}

.nav-menu-item {
  padding: 10px 15px;
  cursor: pointer;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -webkit-align-items: center;
      -ms-flex-align: center;
          align-items: center;
  color: #495057;
  -webkit-transition: all 0.2s;
  transition: all 0.2s;
  border-radius: 12px;
}

.nav-menu-item:hover {
  background-color: #e9ecef;
}

.nav-menu-item.active {
  background-color: #CCF8D1;
  color: #07403F;
  font-weight: bold;
}

.nav-menu-item i {
  margin-right: 5px;
  width: 20px;
  text-align: center;
}

.nav-menu-item-text {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.nav-submenu {
  padding-left: 30px;
  max-height: 0;
  overflow: hidden;
  -webkit-transition: max-height 0.3s ease-in-out;
  transition: max-height 0.3s ease-in-out;
}

.nav-submenu.open {
  max-height: 500px;
}

.fa-chevron-down {
  -webkit-transition: -webkit-transform 0.3s ease;
  transition: -webkit-transform 0.3s ease;
  transition: transform 0.3s ease;
  transition: transform 0.3s ease, -webkit-transform 0.3s ease;
  -webkit-transform: rotate(-90deg);
      -ms-transform: rotate(-90deg);
          transform: rotate(-90deg); /* Default: arrow points right */
}

.fa-chevron-down.open {
  -webkit-transform: rotate(0deg);
      -ms-transform: rotate(0deg);
          transform: rotate(0deg); /* When open: arrow points down */
}

.nav-submenu-item {
  padding: 8px 15px;
  cursor: pointer;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -webkit-align-items: center;
      -ms-flex-align: center;
          align-items: center;
  color: #6c757d;
  -webkit-transition: all 0.2s;
  transition: all 0.2s;
  border-radius: 12px;
}

.nav-submenu-item:hover {
  background-color: #e9ecef;
}

.nav-submenu-item.active {
  color: #07403F;
  font-weight: bold;
}

.content-area {
  -webkit-box-flex: 1;
  -webkit-flex: 1;
      -ms-flex: 1;
          flex: 1;
  padding: 20px;
  background-color: #F4F5FA;
  background-image: url("/images/vocabulary/vocabulary-pattern.svg");
  background-repeat: no-repeat;
  background-position: top center;
  background-size: auto;
  position: relative;
  padding-top: 76px;
}

/* Overlay để làm mờ hình nền và giúp nội dung dễ đọc hơn */
.content-area::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 0;
}

.content-section {
  display: none;
  position: relative;
  z-index: 1;
}

/* Card styles */
.vocabulary-cards {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: wrap;
      -ms-flex-wrap: wrap;
          flex-wrap: wrap;
  gap: 20px;
  margin-top: 20px;
}

.vocabulary-card {
  width: calc(20% - 16px);
  background-color: #fff;
  border-radius: 24px;
  -webkit-box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
          box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  -webkit-transition: all 0.3s ease;
  transition: all 0.3s ease;
  position: relative;
  cursor: pointer;
  /*min-height: 265px;*/
}

.vocabulary-card:hover {
  -webkit-transform: translateY(-5px);
      -ms-transform: translateY(-5px);
          transform: translateY(-5px);
  -webkit-box-shadow: 0 8px 16px rgba(0, 0, 0, 0.15);
          box-shadow: 0 8px 16px rgba(0, 0, 0, 0.15);
}

.vocabulary-card-image {
  width: 100%;
  height: 130px;
  -o-object-fit: cover;
     object-fit: cover;
  border-bottom: 1px solid #eee;
}

.vocabulary-card-content {
  height: 120px;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
      -ms-flex-direction: column;
          flex-direction: column;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
      -ms-flex-pack: center;
          justify-content: center;
  padding-left: 20px;
  padding-right: 15px;
}

.vocabulary-card-name {
  text-align: right;
  padding: 20px 20px 0 20px;
}

.vocabulary-card-name h1 {
  font-size: 110px;
  background: -webkit-gradient(linear, left top, left bottom, from(white), to(#57D061));
  background: -webkit-linear-gradient(white, #57D061);
  background: linear-gradient(white, #57D061);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  font-weight: bold;
}

.vocabulary-card + .vocabulary-card-incoming {
  background-image: url("/images/vocabulary/bg-incoming.svg");
  background-repeat: no-repeat;
  background-size: cover;
  background-position: center;
}

/*khi class vocabulary-card có thêm class card-favorite thi width = 100%*/
.vocabulary-card.card-favorite {
  width: 100%;
}

.vocabulary-card-incoming-text p {
  /*// style cho text  nam duoi cung*/
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  text-align: center;
  font-size: 20px;
  font-weight: bold;
  margin: 0;
  padding: 28px 0;
}

.vocabulary-card-title {
  font-size: 16px;
  font-weight: bold;
  color: #07403F;
  /*margin-bottom: 8px;*/
  /*height: 40px;*/
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.vocabulary-card-count {
  font-size: 14px;
  color: #6c757d;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -webkit-align-items: center;
      -ms-flex-align: center;
          align-items: center;
}

.vocabulary-card-count i {
  margin-right: 5px;
  font-size: 12px;
}

.vocabulary-card-badge {
  position: absolute;
  top: 10px;
  right: 10px;
  padding: 4px 8px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: bold;
  text-transform: uppercase;
}

.badge-vip {
  background-color: #E84C4C;
  color: white;
}

.badge-free {
  background-color: #28a745;
  color: #fff;
}

.badge-favorite {
  background-color: #dc3545;
  color: #fff;
}

.badge-new {
  background-color: #EF6D13;
  color: #fff;
  margin: 12px 0 0 14px;
}

.vocabulary-card-status {
  position: absolute;
  top: 0;
  left: 0;
}

.vocabulary-line {
  position: absolute;
  left: 10px;
  height: 41px;
  width: 2px;
  /*background: #57D061;*/
  border-radius: 50px;
}

/* Responsive */
@media (max-width: 1200px) {
  .vocabulary-card {
    width: calc(25% - 15px);
  }
}
@media (max-width: 992px) {
  .vocabulary-card {
    width: calc(33.333% - 14px);
  }
}
@media (max-width: 768px) {
  .vocabulary-card {
    width: calc(50% - 10px);
  }
}
@media (max-width: 576px) {
  .vocabulary-card {
    width: 100%;
  }
}
/* Overview styles */
.overview-content {
  padding: 0 !important;
}

.vocabulary-section {
  margin-bottom: 50px;
  padding: 0 20px;
}

.vocabulary-section-title {
  font-weight: bold;
  color: #07403F;
  margin-bottom: 20px;
  padding-bottom: 10px;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -webkit-align-items: center;
      -ms-flex-align: center;
          align-items: center;
}

.vocabulary-section-title .badge {
  margin-left: 10px;
  font-size: 12px;
  padding: 4px 8px;
  border-radius: 20px;
  text-transform: uppercase;
}

.view-all-btn {
  text-align: center;
  margin-top: 20px;
}

.view-all-btn button {
  background-color: #07403F;
  color: white;
  border: none;
  padding: 8px 20px;
  border-radius: 5px;
  cursor: pointer;
  font-size: 14px;
  -webkit-transition: all 0.3s ease;
  transition: all 0.3s ease;
}

.view-all-btn button:hover {
  background-color: #0a5e5c;
  -webkit-transform: translateY(-2px);
      -ms-transform: translateY(-2px);
          transform: translateY(-2px);
}

.view-all-btn i {
  margin-left: 5px;
}

.content-section.active {
  display: block;
}

.section-title {
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 20px;
  color: #07403F;
}

.section-content {
  /*background-color: rgba(255, 255, 255, 0.9);*/
  padding: 25px;
  /*border-radius: 10px;*/
  min-height: 300px;
  /*box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);*/
  /*backdrop-filter: blur(10px);*/
  /*-webkit-backdrop-filter: blur(10px);*/
  /*border: 1px solid rgba(255, 255, 255, 0.5);*/
  -webkit-transition: all 0.3s ease;
  transition: all 0.3s ease;
}

.section-content:hover {
  /*box-shadow: 0 12px 28px rgba(0, 0, 0, 0.15);*/
  /*transform: translateY(-2px);*/
}

/* Responsive */
@media (max-width: 768px) {
  .nav-menu {
    position: fixed;
    height: 100%;
    z-index: 1000;
    left: 0;
    top: 60px;
    -webkit-transform: translateX(-100%);
        -ms-transform: translateX(-100%);
            transform: translateX(-100%);
  }
  .nav-menu.open {
    -webkit-transform: translateX(0);
        -ms-transform: translateX(0);
            transform: translateX(0);
  }
  .content-area {
    width: 100%;
  }
}
ruby {
  line-height: 2.2;
}
