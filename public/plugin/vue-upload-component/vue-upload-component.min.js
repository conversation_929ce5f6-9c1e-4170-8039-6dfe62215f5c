/*!
 * Name: vue-upload-component
 * Version: 2.8.22
 * Author: LianYue
 */
!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?module.exports=t():"function"==typeof define&&define.amd?define(t):e.VueUploadComponent=t()}(this,function(){"use strict";var r=function(t){var i=new XMLHttpRequest;return i.open(t.method||"GET",t.url),i.responseType="json",t.headers&&Object.keys(t.headers).forEach(function(e){i.setRequestHeader(e,t.headers[e])}),i};function e(e){var n,t,i=r(e);return n=i,t=e.body,new Promise(function(e,i){n.onload=function(){if(200<=n.status&&n.status<300){var t;try{t=JSON.parse(n.response)}catch(e){t=n.response}e(t)}else i(n.response)},n.onerror=function(){return i(n.response)},n.send(JSON.stringify(t))})}var t=function(){function n(e,t){for(var i=0;i<t.length;i++){var n=t[i];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}return function(e,t,i){return t&&n(e.prototype,t),i&&n(e,i),e}}();var i=function(){function i(e,t){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,i),this.file=e,this.options=t,this.chunks=[],this.sessionId=null,this.chunkSize=null,this.speedInterval=null}return t(i,[{key:"createChunks",value:function(){this.chunks=[];for(var e=0,t=this.chunkSize;e<this.fileSize;)this.chunks.push({blob:this.file.file.slice(e,t),startOffset:e,active:!1,retries:this.maxRetries}),t=(e=t)+this.chunkSize}},{key:"updateFileProgress",value:function(){this.file.progress=this.progress}},{key:"pause",value:function(){this.file.active=!1,this.stopChunks()}},{key:"stopChunks",value:function(){this.chunksUploading.forEach(function(e){e.xhr.abort(),e.active=!1}),this.stopSpeedCalc()}},{key:"resume",value:function(){this.file.active=!0,this.startChunking()}},{key:"upload",value:function(){var i=this;return this.promise=new Promise(function(e,t){i.resolve=e,i.reject=t}),this.start(),this.promise}},{key:"start",value:function(){var t=this;e({method:"POST",headers:Object.assign({},this.headers,{"Content-Type":"application/json"}),url:this.action,body:Object.assign(this.startBody,{phase:"start",mime_type:this.fileType,size:this.fileSize,name:this.fileName})}).then(function(e){if("success"!==e.status)return t.file.response=e,t.reject("server");t.sessionId=e.data.session_id,t.chunkSize=e.data.end_offset,t.createChunks(),t.startChunking()}).catch(function(e){t.file.response=e,t.reject("server")})}},{key:"startChunking",value:function(){for(var e=0;e<this.maxActiveChunks;e++)this.uploadNextChunk();this.startSpeedCalc()}},{key:"uploadNextChunk",value:function(){if(this.file.active){if(this.hasChunksToUpload)return this.uploadChunk(this.chunksToUpload[0]);if(0===this.chunksUploading.length)return this.finish()}}},{key:"uploadChunk",value:function(t){var i=this;t.progress=0,t.active=!0,this.updateFileProgress(),t.xhr=r({method:"POST",headers:this.headers,url:this.action}),t.xhr.upload.addEventListener("progress",function(e){e.lengthComputable&&(t.progress=Math.round(e.loaded/e.total*100))},!1),function(n,e){var t=new FormData;for(var i in e)t.append(i,e[i]);return new Promise(function(e,i){n.onload=function(){if(200<=n.status&&n.status<300){var t;try{t=JSON.parse(n.response)}catch(e){t=n.response}e(t)}else i(n.response)},n.onerror=function(){return i(n.response)},n.send(t)})}(t.xhr,Object.assign(this.uploadBody,{phase:"upload",session_id:this.sessionId,start_offset:t.startOffset,chunk:t.blob})).then(function(e){if(t.active=!1,"success"===e.status)t.uploaded=!0;else if(t.retries--<=0)return i.stopChunks(),i.reject("upload");i.uploadNextChunk()}).catch(function(){if(t.active=!1,t.retries--<=0)return i.stopChunks(),i.reject("upload");i.uploadNextChunk()})}},{key:"finish",value:function(){var t=this;this.updateFileProgress(),this.stopSpeedCalc(),e({method:"POST",headers:Object.assign({},this.headers,{"Content-Type":"application/json"}),url:this.action,body:Object.assign(this.finishBody,{phase:"finish",session_id:this.sessionId})}).then(function(e){if("success"!==(t.file.response=e).status)return t.reject("server");t.resolve(e)}).catch(function(e){t.file.response=e,t.reject("server")})}},{key:"startSpeedCalc",value:function(){var t=this,i=this.file.speed=0;this.speedInterval||(this.speedInterval=window.setInterval(function(){var e=t.progress/100*t.fileSize;t.file.speed=e-i,i=e},1e3))}},{key:"stopSpeedCalc",value:function(){this.speedInterval&&window.clearInterval(this.speedInterval),this.speedInterval=null,this.file.speed=0}},{key:"maxRetries",get:function(){return parseInt(this.options.maxRetries,10)}},{key:"maxActiveChunks",get:function(){return parseInt(this.options.maxActive,10)}},{key:"fileType",get:function(){return this.file.type}},{key:"fileSize",get:function(){return this.file.size}},{key:"fileName",get:function(){return this.file.name}},{key:"action",get:function(){return this.options.action||null}},{key:"startBody",get:function(){return this.options.startBody||{}}},{key:"uploadBody",get:function(){return this.options.uploadBody||{}}},{key:"finishBody",get:function(){return this.options.finishBody||{}}},{key:"headers",get:function(){return this.options.headers||{}}},{key:"readyToUpload",get:function(){return!!this.chunks}},{key:"progress",get:function(){var i=this,e=this.chunksUploaded.length/this.chunks.length*100,t=this.chunksUploading.reduce(function(e,t){return e+(0|t.progress)/i.chunks.length},0);return Math.min(e+t,100)}},{key:"chunksToUpload",get:function(){return this.chunks.filter(function(e){return!e.active&&!e.uploaded})}},{key:"hasChunksToUpload",get:function(){return 0<this.chunksToUpload.length}},{key:"chunksUploading",get:function(){return this.chunks.filter(function(e){return!!e.xhr&&!!e.active})}},{key:"chunksUploaded",get:function(){return this.chunks.filter(function(e){return!!e.uploaded})}}]),i}();var n=function(e,t,i,n,r,s,o,a,u,c){"boolean"!=typeof o&&(u=a,a=o,o=!1);var l,d="function"==typeof i?i.options:i;if(e&&e.render&&(d.render=e.render,d.staticRenderFns=e.staticRenderFns,d._compiled=!0,r&&(d.functional=!0)),n&&(d._scopeId=n),s?(l=function(e){(e=e||this.$vnode&&this.$vnode.ssrContext||this.parent&&this.parent.$vnode&&this.parent.$vnode.ssrContext)||"undefined"==typeof __VUE_SSR_CONTEXT__||(e=__VUE_SSR_CONTEXT__),t&&t.call(this,u(e)),e&&e._registeredComponents&&e._registeredComponents.add(s)},d._ssrRegister=l):t&&(l=o?function(){t.call(this,c(this.$root.$options.shadowRoot))}:function(e){t.call(this,a(e))}),l)if(d.functional){var h=d.render;d.render=function(e,t){return l.call(t),h(e,t)}}else{var f=d.beforeCreate;d.beforeCreate=f?[].concat(f,l):[l]}return i},s=n({render:function(){var e=this,t=e.$createElement;return(e._self._c||t)("input",{attrs:{type:"file",name:e.$parent.name,id:e.$parent.inputId||e.$parent.name,accept:e.$parent.accept,capture:e.$parent.capture,disabled:e.$parent.disabled,webkitdirectory:!(!e.$parent.directory||!e.$parent.features.directory)||void 0,directory:!(!e.$parent.directory||!e.$parent.features.directory)||void 0,multiple:e.$parent.multiple&&e.$parent.features.html5},on:{change:e.change}})},staticRenderFns:[]},void 0,{methods:{change:function(e){this.$parent.addInputFile(e.target),e.target.files?(e.target.value="",e.target.files.length&&!/safari/i.test(navigator.userAgent)&&(e.target.type="",e.target.type="file")):(this.$destroy(),new this.constructor({parent:this.$parent,el:this.$el}))}}},void 0,!1,void 0,void 0,void 0),f=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var i=arguments[t];for(var n in i)Object.prototype.hasOwnProperty.call(i,n)&&(e[n]=i[n])}return e},o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e};function c(e){if(Array.isArray(e)){for(var t=0,i=Array(e.length);t<e.length;t++)i[t]=e[t];return i}return Array.from(e)}var a={headers:{},action:"",minSize:1048576,maxActive:3,maxRetries:5,handler:i},u={components:{InputFile:s},props:{inputId:{type:String},name:{type:String,default:"file"},accept:{type:String},capture:{},disabled:{},multiple:{type:Boolean},maximum:{type:Number,default:function(){return this.multiple?0:1}},addIndex:{type:[Boolean,Number]},directory:{type:Boolean},postAction:{type:String},putAction:{type:String},customAction:{type:Function},headers:{type:Object,default:Object},data:{type:Object,default:Object},timeout:{type:Number,default:0},drop:{default:!1},dropDirectory:{type:Boolean,default:!0},size:{type:Number,default:0},extensions:{default:Array},value:{type:Array,default:Array},thread:{type:Number,default:1},chunkEnabled:{type:Boolean,default:!1},chunk:{type:Object,default:function(){return a}}},data:function(){return{files:this.value,features:{html5:!0,directory:!1,drop:!1},active:!1,dropActive:!1,uploading:0,destroy:!1}},mounted:function(){var e=document.createElement("input");if(e.type="file",e.multiple=!0,window.FormData&&e.files?("boolean"!=typeof e.webkitdirectory&&"boolean"!=typeof e.directory||(this.features.directory=!0),this.features.html5&&void 0!==e.ondrop&&(this.features.drop=!0)):this.features.html5=!1,this.maps={},this.files)for(var t=0;t<this.files.length;t++){var i=this.files[t];this.maps[i.id]=i}this.$nextTick(function(){var e=this;this.$parent?(this.$parent.$forceUpdate(),this.$parent.$nextTick(function(){e.watchDrop(e.drop)})):this.watchDrop(this.drop)})},beforeDestroy:function(){this.destroy=!0,this.active=!1,this.watchDrop(!1)},computed:{uploaded:function(){for(var e=void 0,t=0;t<this.files.length;t++)if((e=this.files[t]).fileObject&&!e.error&&!e.success)return!1;return!0},chunkOptions:function(){return Object.assign(a,this.chunk)},className:function(){return["file-uploads",this.features.html5?"file-uploads-html5":"file-uploads-html4",this.features.directory&&this.directory?"file-uploads-directory":void 0,this.features.drop&&this.drop?"file-uploads-drop":void 0,this.disabled?"file-uploads-disabled":void 0]}},watch:{active:function(e){this.watchActive(e)},dropActive:function(e){this.watchDropActive(e),this.$parent&&this.$parent.$forceUpdate()},drop:function(e){this.watchDrop(e)},value:function(e){if(this.files!==e){this.files=e;var t=this.maps;this.maps={};for(var i=0;i<this.files.length;i++){var n=this.files[i];this.maps[n.id]=n}for(var r in this.maps){var s=this.maps[r],o=t[r];s!==o&&this.emitFile(s,o)}for(var a in t)this.maps[a]||this.emitFile(void 0,t[a])}}},methods:{clear:function(){if(this.files.length){var e=this.files;this.files=[],this.maps={},this.emitInput();for(var t=0;t<e.length;t++)this.emitFile(void 0,e[t])}return!0},get:function(e){return!!e&&("object"===(void 0===e?"undefined":o(e))?this.maps[e.id]||!1:this.maps[e]||!1)},add:function(e){var t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:this.addIndex,i=e,n=i instanceof Array;n||(i=[i]);for(var r=[],s=0;s<i.length;s++){var o=i[s];this.features.html5&&o instanceof Blob&&(o={file:o,size:o.size,name:o.webkitRelativePath||o.relativePath||o.name||"unknown",type:o.type});var a=!1;if(!1===o.fileObject||(o.fileObject?a=!0:"undefined"!=typeof Element&&o.el instanceof Element?a=!0:"undefined"!=typeof Blob&&o.file instanceof Blob&&(a=!0)),a&&((o=f({fileObject:!0,size:-1,name:"Filename",type:"",active:!1,error:"",success:!1,putAction:this.putAction,postAction:this.postAction,timeout:this.timeout},o,{response:{},progress:"0.00",speed:0})).data=f({},this.data,o.data?o.data:{}),o.headers=f({},this.headers,o.headers?o.headers:{})),o.id||(o.id=Math.random().toString(36).substr(2)),!this.emitFilter(o,void 0)){if(1<this.maximum&&r.length+this.files.length>=this.maximum)break;if(r.push(o),1===this.maximum)break}}if(!r.length)return!1;1===this.maximum&&this.clear();var u=void 0;if(!0===t||0===t)u=r.concat(this.files);else if(t){var c;(c=u=this.files.concat([])).splice.apply(c,[t,0].concat(r))}else u=this.files.concat(r);this.files=u;for(var l=0;l<r.length;l++){var d=r[l];this.maps[d.id]=d}this.emitInput();for(var h=0;h<r.length;h++)this.emitFile(r[h],void 0);return n?r:r[0]},addInputFile:function(e){var t=[];if(e.files)for(var i=0;i<e.files.length;i++){var n=e.files[i];t.push({size:n.size,name:n.webkitRelativePath||n.relativePath||n.name,type:n.type,file:n})}else{var r=e.value.replace(/\\/g,"/").split("/");delete e.__vuex__,t.push({name:r[r.length-1],el:e})}return this.add(t)},addDataTransfer:function(e){var r=this,s=[];if(e.items&&e.items.length){for(var o=[],t=0;t<e.items.length;t++){var i=e.items[t];(i=i.getAsEntry?i.getAsEntry()||i.getAsFile():i.webkitGetAsEntry&&i.webkitGetAsEntry()||i.getAsFile())&&o.push(i)}return new Promise(function(n,e){!function t(i){var e=o[i];if(!e||0<r.maximum&&s.length>=r.maximum)return n(r.add(s));r.getEntry(e).then(function(e){s.push.apply(s,c(e)),t(i+1)})}(0)})}if(e.files.length){for(var n=0;n<e.files.length&&(s.push(e.files[n]),!(0<this.maximum&&s.length>=this.maximum));n++);return Promise.resolve(this.add(s))}return Promise.resolve([])},getEntry:function(o){var a=this,u=1<arguments.length&&void 0!==arguments[1]?arguments[1]:"";return new Promise(function(r,e){if(o.isFile)o.file(function(e){r([{size:e.size,name:u+e.name,type:e.type,file:e}])});else if(o.isDirectory&&a.dropDirectory){var s=[],t=o.createReader();!function n(){t.readEntries(function(e){!function t(i){return!e[i]&&0===i||0<a.maximum&&s.length>=a.maximum?r(s):e[i]?void a.getEntry(e[i],u+o.name+"/").then(function(e){s.push.apply(s,c(e)),t(i+1)}):n()}(0)})}()}else r([])})},replace:function(e,t){var i=this.get(e),n=this.get(t);if(!i||!n||i===n)return!1;var r=this.files.concat([]),s=r.indexOf(i),o=r.indexOf(n);return-1!==s&&-1!==o&&(r[s]=n,r[o]=i,this.files=r,this.emitInput(),!0)},remove:function(e){var t=this.get(e);if(t){if(this.emitFilter(void 0,t))return!1;var i=this.files.concat([]),n=i.indexOf(t);if(-1===n)return console.error("remove",t),!1;i.splice(n,1),this.files=i,delete this.maps[t.id],this.emitInput(),this.emitFile(void 0,t)}return t},update:function(e,t){var i=this.get(e);if(i){var n=f({},i,t);if(!i.fileObject||!i.active||n.active||n.error||n.success||(n.error="abort"),this.emitFilter(n,i))return!1;var r=this.files.concat([]),s=r.indexOf(i);return-1===s?(console.error("update",i),!1):(r.splice(s,1,n),this.files=r,delete this.maps[i.id],this.maps[n.id]=n,this.emitInput(),this.emitFile(n,i),n)}return!1},emitFilter:function(e,t){var i=!1;return this.$emit("input-filter",e,t,function(){return i=!0}),i},emitFile:function(i,e){this.$emit("input-file",i,e),!(i&&i.fileObject&&i.active)||e&&e.active?i&&i.fileObject&&i.active||!e||!e.fileObject||!e.active||this.uploading--:(this.uploading++,this.$nextTick(function(){var t=this;setTimeout(function(){t.upload(i).then(function(){(i=t.get(i))&&i.fileObject&&t.update(i,{active:!1,success:!i.error})}).catch(function(e){t.update(i,{active:!1,success:!1,error:e.code||e.error||e.message||e})})},parseInt(50*Math.random()+50,10))})),!this.active||Boolean(i)===Boolean(e)&&i.active===e.active||this.watchActive(!0)},emitInput:function(){this.$emit("input",this.files)},upload:function(e){var t=this.get(e);if(!t)return Promise.reject("not_exists");if(!t.fileObject)return Promise.reject("file_object");if(t.error)return Promise.reject(t.error);if(t.success)return Promise.resolve(t);var i=this.extensions;if(i&&(i.length||void 0===i.length)&&("object"===(void 0===i?"undefined":o(i))&&i instanceof RegExp||("string"==typeof i&&(i=i.split(",").map(function(e){return e.trim()}).filter(function(e){return e})),i=new RegExp("\\.("+i.join("|").replace(/\./g,"\\.")+")$","i")),-1===t.name.search(i)))return Promise.reject("extension");if(0<this.size&&0<=t.size&&t.size>this.size)return Promise.reject("size");if(this.customAction)return this.customAction(t,this);if(this.features.html5){if(this.shouldUseChunkUpload(t))return this.uploadChunk(t);if(t.putAction)return this.uploadPut(t);if(t.postAction)return this.uploadHtml5(t)}return t.postAction?this.uploadHtml4(t):Promise.reject("No action configured")},shouldUseChunkUpload:function(e){return this.chunkEnabled&&!!this.chunkOptions.handler&&e.size>this.chunkOptions.minSize},uploadChunk:function(e){var t=this.chunkOptions.handler;return e.chunk=new t(e,this.chunkOptions),e.chunk.upload()},uploadPut:function(e){var t=[],i=void 0;for(var n in e.data)null!=(i=e.data[n])&&t.push(encodeURIComponent(n)+"="+encodeURIComponent(i));var r=t.length?(-1===e.putAction.indexOf("?")?"?":"&")+t.join("&"):"",s=new XMLHttpRequest;return s.open("PUT",e.putAction+r),this.uploadXhr(s,e,e.file)},uploadHtml5:function(e){var t=new window.FormData,i=void 0;for(var n in e.data)(i=e.data[n])&&"object"===(void 0===i?"undefined":o(i))&&"function"!=typeof i.toString?i instanceof File?t.append(n,i,i.name):t.append(n,JSON.stringify(i)):null!=i&&t.append(n,i);t.append(this.name,e.file,e.file.filename||e.name);var r=new XMLHttpRequest;return r.open("POST",e.postAction),this.uploadXhr(r,e,t)},uploadXhr:function(o,e,i){var a=this,u=e,n=0,r=0;o.upload.onprogress=function(e){if(u=a.get(u),e.lengthComputable&&u&&u.fileObject&&u.active){var t=Math.round(Date.now()/1e3);t!==n&&(n=t,u=a.update(u,{progress:(e.loaded/e.total*100).toFixed(2),speed:e.loaded-r}),r=e.loaded)}};var c=setInterval(function(){if(!(u=a.get(u))||!u.fileObject||u.success||u.error||!u.active){c&&(clearInterval(c),c=!1);try{o.abort(),o.timeout=1}catch(e){}}},100);return new Promise(function(n,r){var s=void 0,e=function(e){if(!s){if(s=!0,c&&(clearInterval(c),c=!1),!(u=a.get(u)))return r("not_exists");if(!u.fileObject)return r("file_object");if(u.error)return r(u.error);if(!u.active)return r("abort");if(u.success)return n(u);var t={};switch(e.type){case"timeout":case"abort":t.error=e.type;break;case"error":o.status?500<=o.status?t.error="server":400<=o.status&&(t.error="denied"):t.error="network";break;default:500<=o.status?t.error="server":400<=o.status?t.error="denied":t.progress="100.00"}if(o.responseText){var i=o.getResponseHeader("Content-Type");i&&-1!==i.indexOf("/json")?t.response=JSON.parse(o.responseText):t.response=o.responseText}return(u=a.update(u,t)).error?r(u.error):n(u)}};for(var t in o.onload=e,o.onerror=e,o.onabort=e,o.ontimeout=e,u.timeout&&(o.timeout=u.timeout),u.headers)o.setRequestHeader(t,u.headers[t]);u=a.update(u,{xhr:o}),o.send(i)})},uploadHtml4:function(e){var a=this,u=e,c=function(e){27===e.keyCode&&e.preventDefault()},l=document.createElement("iframe");l.id="upload-iframe-"+u.id,l.name="upload-iframe-"+u.id,l.src="about:blank",l.setAttribute("style","width:1px;height:1px;top:-999em;position:absolute; margin-top:-999em;");var t=document.createElement("form");t.action=u.postAction,t.name="upload-form-"+u.id,t.setAttribute("method","POST"),t.setAttribute("target","upload-iframe-"+u.id),t.setAttribute("enctype","multipart/form-data");var i=void 0,n=void 0;for(var r in u.data)(i=u.data[r])&&"object"===(void 0===i?"undefined":o(i))&&"function"!=typeof i.toString&&(i=JSON.stringify(i)),null!=i&&((n=document.createElement("input")).type="hidden",n.name=r,n.value=i,t.appendChild(n));t.appendChild(u.el),document.body.appendChild(l).appendChild(t);return new Promise(function(s,o){setTimeout(function(){if(!(u=a.update(u,{iframe:l})))return o("not_exists");var n=setInterval(function(){(u=a.get(u))&&u.fileObject&&!u.success&&!u.error&&u.active||(n&&(clearInterval(n),n=!1),l.onabort({type:u?"abort":"not_exists"}))},100),r=void 0,e=function(e){if(!r){if(r=!0,n&&(clearInterval(n),n=!1),document.body.removeEventListener("keydown",c),!(u=a.get(u)))return o("not_exists");if(!u.fileObject)return o("file_object");if(u.error)return o(u.error);if(!u.active)return o("abort");if(u.success)return s(u);var t=function(){var t=void 0;try{l.contentWindow&&(t=l.contentWindow.document)}catch(e){}if(!t)try{t=l.contentDocument?l.contentDocument:l.document}catch(e){t=l.document}return t&&t.body?t.body.innerHTML:null}(),i={};switch(e.type){case"abort":i.error="abort";break;case"error":u.error?i.error=u.error:i.error=null===t?"network":"denied";break;default:u.error?i.error=u.error:null===i?i.error="network":i.progress="100.00"}if(null!==t){if(t&&"{"===t.substr(0,1)&&"}"===t.substr(t.length-1,1))try{t=JSON.parse(t)}catch(e){}i.response=t}return(u=a.update(u,i)).error?o(u.error):s(u)}};l.onload=e,l.onerror=e,l.onabort=e,document.body.addEventListener("keydown",c),t.submit()},50)}).then(function(e){return l.parentNode&&l.parentNode.removeChild(l),e}).catch(function(e){return l.parentNode&&l.parentNode.removeChild(l),e})},watchActive:function(e){for(var t=void 0,i=0;t=this.files[i];)if(i++,t.fileObject)if(e&&!this.destroy){if(this.uploading>=this.thread||this.uploading&&!this.features.html5)break;t.active||t.error||t.success||this.update(t,{active:!0})}else t.active&&this.update(t,{active:!1});else;0===this.uploading&&(this.active=!1)},watchDrop:function(e){var t=e;if(this.features.drop){if(this.dropElement)try{document.removeEventListener("dragenter",this.onDocumentDragenter,!1),document.removeEventListener("dragleave",this.onDocumentDragleave,!1),document.removeEventListener("dragover",this.onDocumentDragover,!1),document.removeEventListener("drop",this.onDocumentDrop,!1),this.dropElement.removeEventListener("dragover",this.onDragover,!1),this.dropElement.removeEventListener("drop",this.onDrop,!1)}catch(e){}t?"string"==typeof t?t=document.querySelector(t)||this.$root.$el.querySelector(t):!0===t&&(t=this.$parent.$el):t=!1,this.dropElement=t,this.dropElement&&(document.addEventListener("dragenter",this.onDocumentDragenter,!1),document.addEventListener("dragleave",this.onDocumentDragleave,!1),document.addEventListener("dragover",this.onDocumentDragover,!1),document.addEventListener("drop",this.onDocumentDrop,!1),this.dropElement.addEventListener("dragover",this.onDragover,!1),this.dropElement.addEventListener("drop",this.onDrop,!1))}},watchDropActive:function(e,t){e!==t&&(this.dropTimeout&&(clearTimeout(this.dropTimeout),this.dropTimeout=null),e&&(this.dropTimeout=setTimeout(this.onDocumentDrop,1e3)))},onDocumentDragenter:function(e){if(!this.dropActive&&e.dataTransfer){var t=e.dataTransfer;t.files&&t.files.length?this.dropActive=!0:t.types?t.types.indexOf&&-1!==t.types.indexOf("Files")?this.dropActive=!0:t.types.contains&&t.types.contains("Files")&&(this.dropActive=!0):this.dropActive=!0,this.dropActive&&this.watchDropActive(!0)}},onDocumentDragleave:function(e){this.dropActive&&(e.target===e.explicitOriginalTarget||!e.fromElement&&(e.clientX<=0||e.clientY<=0||e.clientX>=window.innerWidth||e.clientY>=window.innerHeight))&&(this.dropActive=!1,this.watchDropActive(!1))},onDocumentDragover:function(){this.watchDropActive(!0)},onDocumentDrop:function(){this.dropActive=!1,this.watchDropActive(!1)},onDragover:function(e){e.preventDefault()},onDrop:function(e){e.preventDefault(),e.dataTransfer&&this.addDataTransfer(e.dataTransfer)}}},l="undefined"!=typeof navigator&&/msie [6-9]\\b/.test(navigator.userAgent.toLowerCase());var d=document.head||document.getElementsByTagName("head")[0],h={};var p,m=n({render:function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("span",{class:e.className},[e._t("default"),e._v(" "),i("label",{attrs:{for:e.inputId||e.name}}),e._v(" "),i("input-file")],2)},staticRenderFns:[]},function(e){e&&e("data-v-939ffe40_0",{source:".file-uploads{overflow:hidden;position:relative;text-align:center;display:inline-block}.file-uploads.file-uploads-html4 input,.file-uploads.file-uploads-html5 label{background:#fff;opacity:0;font-size:20em;z-index:1;top:0;left:0;right:0;bottom:0;position:absolute;width:100%;height:100%}.file-uploads.file-uploads-html4 label,.file-uploads.file-uploads-html5 input{background:rgba(255,255,255,0);overflow:hidden;position:fixed;width:1px;height:1px;z-index:-1;opacity:0}",map:void 0,media:void 0})},u,void 0,!1,void 0,function(e){return function(e,t){return function(e,t){var i=l?t.media||"default":e,n=h[i]||(h[i]={ids:new Set,styles:[]});if(!n.ids.has(e)){n.ids.add(e);var r=t.source;if(t.map&&(r+="\n/*# sourceURL="+t.map.sources[0]+" */",r+="\n/*# sourceMappingURL=data:application/json;base64,"+btoa(unescape(encodeURIComponent(JSON.stringify(t.map))))+" */"),n.element||(n.element=document.createElement("style"),n.element.type="text/css",t.media&&n.element.setAttribute("media",t.media),d.appendChild(n.element)),"styleSheet"in n.element)n.styles.push(r),n.element.styleSheet.cssText=n.styles.filter(Boolean).join("\n");else{var s=n.ids.size-1,o=document.createTextNode(r),a=n.element.childNodes;a[s]&&n.element.removeChild(a[s]),a.length?n.element.insertBefore(o,a[s]):n.element.appendChild(o)}}}(e,t)}},void 0);return(p=Object.freeze({default:m}))&&p.default||p});
//# sourceMappingURL=vue-upload-component.min.js.map
