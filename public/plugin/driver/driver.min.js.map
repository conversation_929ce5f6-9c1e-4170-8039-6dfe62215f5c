{"version": 3, "file": "driver.min.js", "sources": ["webpack://Driver/webpack/universalModuleDefinition"], "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"Driver\"] = factory();\n\telse\n\t\troot[\"Driver\"] = factory();\n})(window, function() {\nreturn "], "mappings": "AAAA", "sourceRoot": ""}