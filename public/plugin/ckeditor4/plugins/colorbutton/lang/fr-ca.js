/*
Copyright (c) 2003-2019, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-oss-license
*/
CKEDITOR.plugins.setLang( 'colorbutton', 'fr-ca', {
	auto: 'Automatique',
	bgColorTitle: 'Couleur de fond',
	colors: {
		'000': 'Noir',
		'800000': '<PERSON><PERSON>',
		'8B4513': 'Brun foncé',
		'2F4F4F': 'Gris ardoise foncé',
		'008080': 'Sarcelle',
		'000080': 'Marine',
		'4B0082': 'Indigo',
		'696969': 'Gris foncé',
		B22222: 'Rouge brique',
		A52A2A: 'Brun',
		DAA520: 'Doré',
		'006400': 'Vert foncé',
		'40E0D0': 'Turquoise',
		'0000CD': 'Bleu',
		'800080': 'Mauve',
		'808080': 'Gris',
		F00: 'Rouge',
		FF8C00: 'Orange foncé',
		FFD700: 'Or',
		'008000': 'Vert',
		'0FF': 'Cyan',
		'00F': 'Bleu',
		EE82EE: 'Violet',
		A9A9A9: 'Gris pâle',
		FFA07A: 'Saumon clair',
		FFA500: 'Orange',
		FFFF00: 'Jaune',
		'00FF00': 'Vert lime',
		AFEEEE: 'Turquoise pâle',
		ADD8E6: 'Bleu pâle',
		DDA0DD: 'Prune',
		D3D3D3: 'Gris pâle',
		FFF0F5: 'Bleu lavande',
		FAEBD7: 'Blanc antique',
		FFFFE0: 'Jaune pâle',
		F0FFF0: 'Miel doré',
		F0FFFF: 'Azure',
		F0F8FF: 'Bleu alice',
		E6E6FA: 'Lavande',
		FFF: 'Blanc',
		'1ABC9C': 'Strong Cyan', // MISSING
		'2ECC71': 'Emerald', // MISSING
		'3498DB': 'Bright Blue', // MISSING
		'9B59B6': 'Amethyst', // MISSING
		'4E5F70': 'Grayish Blue', // MISSING
		'F1C40F': 'Vivid Yellow', // MISSING
		'16A085': 'Dark Cyan', // MISSING
		'27AE60': 'Dark Emerald', // MISSING
		'2980B9': 'Strong Blue', // MISSING
		'8E44AD': 'Dark Violet', // MISSING
		'2C3E50': 'Desaturated Blue', // MISSING
		'F39C12': 'Orange', // MISSING
		'E67E22': 'Carrot', // MISSING
		'E74C3C': 'Pale Red', // MISSING
		'ECF0F1': 'Bright Silver', // MISSING
		'95A5A6': 'Light Grayish Cyan', // MISSING
		'DDD': 'Light Gray', // MISSING
		'D35400': 'Pumpkin', // MISSING
		'C0392B': 'Strong Red', // MISSING
		'BDC3C7': 'Silver', // MISSING
		'7F8C8D': 'Grayish Cyan', // MISSING
		'999': 'Dark Gray' // MISSING
	},
	more: 'Plus de couleurs...',
	panelTitle: 'Couleurs',
	textColorTitle: 'Couleur de texte'
} );
