/*
Copyright (c) 2003-2019, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-oss-license
*/
CKEDITOR.plugins.setLang( 'colorbutton', 'pt', {
	auto: 'Automático',
	bgColorTitle: 'Cor de fundo',
	colors: {
		'000': 'Black',
		'800000': '<PERSON><PERSON><PERSON>',
		'8B4513': 'Saddle Brown',
		'2F4F4F': 'Cinza lousa escuro',
		'008080': 'Teal',
		'000080': 'Azul naval',
		'4B0082': 'Indigo',
		'696969': 'Cinza escuro',
		B22222: 'Vermelho tijolo',
		A52A2A: 'Castanho',
		DAA520: '<PERSON><PERSON><PERSON> (daa520)',
		'006400': 'Verde escuro (006400)',
		'40E0D0': 'Turquesa',
		'0000CD': 'Azul médio (0000cd)',
		'800080': 'Purple',
		'808080': 'Cinza',
		F00: 'Vermel<PERSON>',
		FF8C00: 'Laranja escuro',
		FFD700: '<PERSON><PERSON><PERSON>',
		'008000': '<PERSON>',
		'0FF': 'Ciano',
		'00F': 'Azul',
		EE82EE: 'Violeta',
		A9A9A9: 'Cinza fosco',
		FFA07A: 'Salmão claro',
		FFA500: 'Laranja',
		FFFF00: 'Amarelo',
		'00FF00': 'Limão (Verde espectro)',
		AFEEEE: 'Turquesa pálida (afeeee)',
		ADD8E6: 'Light Blue',
		DDA0DD: 'Ameixa',
		D3D3D3: 'Cinza claro',
		FFF0F5: 'Lavanda avermelhada',
		FAEBD7: 'Branco velho',
		FFFFE0: 'Amarelo claro',
		F0FFF0: 'Maná (f0fff0)',
		F0FFFF: 'Azul celeste',
		F0F8FF: 'Azul Alice (f0f8ff)',
		E6E6FA: 'Lavanda',
		FFF: 'Branco',
		'1ABC9C': 'Ciano forte',
		'2ECC71': 'Esmeralda',
		'3498DB': 'Azul brilhante',
		'9B59B6': 'Amethyst', // MISSING
		'4E5F70': 'Azul acinzentado',
		'F1C40F': 'Amarelo vívido',
		'16A085': 'Ciano escuro',
		'27AE60': 'Esmeralda escuro',
		'2980B9': 'Azul forte',
		'8E44AD': 'Violeta escuro',
		'2C3E50': 'Desaturated Blue', // MISSING
		'F39C12': 'Laranja',
		'E67E22': 'Cenoura',
		'E74C3C': 'Vermelho claro',
		'ECF0F1': 'Prateado brilhante',
		'95A5A6': 'Ciano acinzentado claro',
		'DDD': 'Cinza claro',
		'D35400': 'Abóbora',
		'C0392B': 'Strong Red', // MISSING
		'BDC3C7': 'Prateado',
		'7F8C8D': 'Ciano acinzentado',
		'999': 'Cinza escuro'
	},
	more: 'Mais cores...',
	panelTitle: 'Cores',
	textColorTitle: 'Cor do texto'
} );
