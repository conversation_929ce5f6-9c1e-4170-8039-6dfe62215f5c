/**
 * @license Copyright (c) 2003-2019, CKSource - <PERSON><PERSON>. All rights reserved.
 * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-oss-license
 */
CKEDITOR.plugins.setLang( 'filetools', 'de-ch', {
	loadError: 'Während dem Lesen der Datei ist ein Fehler aufgetreten.',
	networkError: 'Während dem Hochladen der Datei ist ein Netzwerkfehler aufgetreten.',
	httpError404: 'Während dem Hochladen der Datei ist ein HTTP-Fehler aufgetreten (404: Datei nicht gefunden).',
	httpError403: 'Während dem Hochladen der Datei ist ein HTTP-Fehler aufgetreten (403: Verboten).',
	httpError: 'Während dem Hochladen der Datei ist ein HTTP-Fehler aufgetreten (Fehlerstatus: %1).',
	noUrlError: 'Hochlade-URL ist nicht definiert.',
	responseError: 'Falsche Antwort des Servers.'
} );
