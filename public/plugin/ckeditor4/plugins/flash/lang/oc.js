/*
Copyright (c) 2003-2019, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-oss-license
*/
CKEDITOR.plugins.setLang( 'flash', 'oc', {
	access: 'Accès als escripts',
	accessAlways: 'Totjorn',
	accessNever: 'Pas jamai',
	accessSameDomain: '<PERSON><PERSON><PERSON> domeni',
	alignAbsBottom: 'Bas absolut',
	alignAbsMiddle: 'Mitan absolut',
	alignBaseline: 'Linha de basa',
	alignTextTop: 'Naut del tèxte',
	bgcolor: 'Color de rèireplan',
	chkFull: 'Permetre l\'ecran complet',
	chkLoop: 'Bocla',
	chkMenu: 'Activar lo menú Flash',
	chkPlay: 'Legir automaticament',
	flashvars: 'Variablas per Flash',
	hSpace: 'Espaçament orizontal',
	properties: 'Proprietats del Flash',
	propertiesTab: 'Proprietats',
	quality: 'Qualitat',
	qualityAutoHigh: 'Nauta automatica',
	qualityAutoLow: 'Bassa automatica',
	qualityBest: 'Maximala',
	qualityHigh: 'Nauta',
	qualityLow: 'Bassa',
	qualityMedium: 'Mejana',
	scale: 'Escala',
	scaleAll: 'Afichar tot',
	scaleFit: 'Adaptacion automatica',
	scaleNoBorder: 'Pas cap de bordadura',
	title: 'Proprietats del Flash',
	vSpace: 'Espaçament vertical',
	validateHSpace: 'L\'espaçament orizontal deu èsser un nombre.',
	validateSrc: 'L\'URL deu èsser indicada.',
	validateVSpace: 'L\'espaçament vertical deu èsser un nombre.',
	windowMode: 'Mòde fenèstra',
	windowModeOpaque: 'Opac',
	windowModeTransparent: 'Transparent',
	windowModeWindow: 'Fenèstra'
} );
