/*
Copyright (c) 2003-2019, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-oss-license
*/
CKEDITOR.plugins.setLang( 'flash', 'sv', {
	access: 'Script-tillgång',
	accessAlways: 'Alltid',
	accessNever: 'Aldrig',
	accessSameDomain: '<PERSON>ma domän',
	alignAbsBottom: 'Absolut nederkant',
	alignAbsMiddle: 'Absolut centrering',
	alignBaseline: 'Baslinje',
	alignTextTop: 'Text överkant',
	bgcolor: 'Bakgrundsfärg',
	chkFull: 'Tillåt helskärm',
	chkLoop: 'Upprepa/Loopa',
	chkMenu: 'Aktivera Flashmeny',
	chkPlay: 'Automatisk uppspelning',
	flashvars: 'Variabler för Flash',
	hSpace: 'Horis. marginal',
	properties: 'Flashegenskaper',
	propertiesTab: 'Egenskaper',
	quality: 'Kvalitet',
	qualityAutoHigh: 'Auto Hög',
	qualityAutoLow: 'Auto Låg',
	qualityBest: 'Bäst',
	qualityHigh: 'Hög',
	qualityLow: 'Låg',
	qualityMedium: 'Medium',
	scale: 'Skala',
	scaleAll: 'Visa allt',
	scaleFit: 'Exakt passning',
	scaleNoBorder: 'Ingen ram',
	title: 'Flashegenskaper',
	vSpace: 'Vert. marginal',
	validateHSpace: 'HSpace måste vara ett nummer.',
	validateSrc: 'Var god ange länkens URL',
	validateVSpace: 'VSpace måste vara ett nummer.',
	windowMode: 'Fönsterläge',
	windowModeOpaque: 'Opaque',
	windowModeTransparent: 'Transparent',
	windowModeWindow: 'Fönster'
} );
