/*
Copyright (c) 2003-2019, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-oss-license
*/
CKEDITOR.plugins.setLang( 'table', 'zh-cn', {
	border: '边框',
	caption: '标题',
	cell: {
		menu: '单元格',
		insertBefore: '在左侧插入单元格',
		insertAfter: '在右侧插入单元格',
		deleteCell: '删除单元格',
		merge: '合并单元格',
		mergeRight: '向右合并单元格',
		mergeDown: '向下合并单元格',
		splitHorizontal: '水平拆分单元格',
		splitVertical: '垂直拆分单元格',
		title: '单元格属性',
		cellType: '单元格类型',
		rowSpan: '纵跨行数',
		colSpan: '横跨列数',
		wordWrap: '自动换行',
		hAlign: '水平对齐',
		vAlign: '垂直对齐',
		alignBaseline: '基线',
		bgColor: '背景颜色',
		borderColor: '边框颜色',
		data: '数据',
		header: '表头',
		yes: '是',
		no: '否',
		invalidWidth: '单元格宽度必须为数字格式',
		invalidHeight: '单元格高度必须为数字格式',
		invalidRowSpan: '行跨度必须为整数格式',
		invalidColSpan: '列跨度必须为整数格式',
		chooseColor: '选择'
	},
	cellPad: '边距',
	cellSpace: '间距',
	column: {
		menu: '列',
		insertBefore: '在左侧插入列',
		insertAfter: '在右侧插入列',
		deleteColumn: '删除列'
	},
	columns: '列数',
	deleteTable: '删除表格',
	headers: '标题单元格',
	headersBoth: '第一列和第一行',
	headersColumn: '第一列',
	headersNone: '无',
	headersRow: '第一行',
	heightUnit: 'height unit', // MISSING
	invalidBorder: '边框粗细必须为数字格式',
	invalidCellPadding: '单元格填充必须为数字格式',
	invalidCellSpacing: '单元格间距必须为数字格式',
	invalidCols: '指定的行数必须大于零',
	invalidHeight: '表格高度必须为数字格式',
	invalidRows: '指定的列数必须大于零',
	invalidWidth: '表格宽度必须为数字格式',
	menu: '表格属性',
	row: {
		menu: '行',
		insertBefore: '在上方插入行',
		insertAfter: '在下方插入行',
		deleteRow: '删除行'
	},
	rows: '行数',
	summary: '摘要',
	title: '表格属性',
	toolbar: '表格',
	widthPc: '百分比',
	widthPx: '像素',
	widthUnit: '宽度单位'
} );
