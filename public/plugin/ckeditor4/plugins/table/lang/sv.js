/*
Copyright (c) 2003-2019, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-oss-license
*/
CKEDITOR.plugins.setLang( 'table', 'sv', {
	border: 'Kantstorlek',
	caption: 'Rubrik',
	cell: {
		menu: 'Cell',
		insertBefore: '<PERSON>ägg till cell före',
		insertAfter: '<PERSON>ägg till cell efter',
		deleteCell: 'Radera celler',
		merge: 'Sammanfoga celler',
		mergeRight: 'Sammanfoga höger',
		mergeDown: 'Sammanfoga ner',
		splitHorizontal: 'Dela cell horisontellt',
		splitVertical: 'Dela cell vertikalt',
		title: 'Egenskaper för cell',
		cellType: 'Celltyp',
		rowSpan: 'Rad spann',
		colSpan: 'Kolumnen spann',
		wordWrap: 'Radbrytning',
		hAlign: 'Horisontell justering',
		vAlign: 'Vertikal justering',
		alignBaseline: 'Ba<PERSON><PERSON>je',
		bgColor: '<PERSON><PERSON><PERSON><PERSON>',
		borderColor: '<PERSON><PERSON><PERSON>rg',
		data: 'Data',
		header: 'R<PERSON>rik',
		yes: 'Ja',
		no: 'Nej',
		invalidWidth: 'Cellens bredd måste vara ett nummer.',
		invalidHeight: 'Cellens höjd måste vara ett nummer.',
		invalidRowSpan: 'Radutvidgning måste vara ett heltal.',
		invalidColSpan: 'Kolumn måste vara ett heltal.',
		chooseColor: 'Välj'
	},
	cellPad: 'Cellutfyllnad',
	cellSpace: 'Cellavstånd',
	column: {
		menu: 'Kolumn',
		insertBefore: 'Lägg till kolumn före',
		insertAfter: 'Lägg till kolumn efter',
		deleteColumn: 'Radera kolumn'
	},
	columns: 'Kolumner',
	deleteTable: 'Radera tabell',
	headers: 'Rubriker',
	headersBoth: 'Båda',
	headersColumn: 'Första kolumnen',
	headersNone: 'Ingen',
	headersRow: 'Första raden',
	heightUnit: 'height unit', // MISSING
	invalidBorder: 'Ram måste vara ett nummer.',
	invalidCellPadding: 'Luft i cell måste vara ett nummer.',
	invalidCellSpacing: 'Luft i cell måste vara ett nummer.',
	invalidCols: 'Antal kolumner måste vara ett nummer större än 0.',
	invalidHeight: 'Tabellens höjd måste vara ett nummer.',
	invalidRows: 'Antal rader måste vara större än 0.',
	invalidWidth: 'Tabell måste vara ett nummer.',
	menu: 'Tabellegenskaper',
	row: {
		menu: 'Rad',
		insertBefore: 'Lägg till rad före',
		insertAfter: 'Lägg till rad efter',
		deleteRow: 'Radera rad'
	},
	rows: 'Rader',
	summary: 'Sammanfattning',
	title: 'Tabellegenskaper',
	toolbar: 'Tabell',
	widthPc: 'procent',
	widthPx: 'pixlar',
	widthUnit: 'enhet bredd'
} );
