"use strict";function _typeof(e){"@babel/helpers - typeof";return(_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function _createForOfIteratorHelper(e,t){var r="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!r){if(Array.isArray(e)||(r=_unsupportedIterableToArray(e))||t&&e&&"number"==typeof e.length){r&&(e=r);var n=0,o=function(){};return{s:o,n:function(){return n>=e.length?{done:!0}:{done:!1,value:e[n++]}},e:function(e){throw e},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,i=!0,s=!1;return{s:function(){r=r.call(e)},n:function(){var e=r.next();return i=e.done,e},e:function(e){s=!0,a=e},f:function(){try{i||null==r["return"]||r["return"]()}finally{if(s)throw a}}}}function _toConsumableArray(e){return _arrayWithoutHoles(e)||_iterableToArray(e)||_unsupportedIterableToArray(e)||_nonIterableSpread()}function _nonIterableSpread(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function _unsupportedIterableToArray(e,t){if(e){if("string"==typeof e)return _arrayLikeToArray(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?_arrayLikeToArray(e,t):void 0}}function _iterableToArray(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}function _arrayWithoutHoles(e){if(Array.isArray(e))return _arrayLikeToArray(e)}function _arrayLikeToArray(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function ownKeys(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function _objectSpread(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?ownKeys(Object(r),!0).forEach(function(t){_defineProperty(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):ownKeys(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function _defineProperty(e,t,r){return t=_toPropertyKey(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function _toPropertyKey(e){var t=_toPrimitive(e,"string");return"symbol"===_typeof(t)?t:String(t)}function _toPrimitive(e,t){if("object"!==_typeof(e)||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!==_typeof(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}function _regeneratorRuntime(){function e(e,t,r){return Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}),e[t]}function t(e,t,r,o){var a=t&&t.prototype instanceof n?t:n,i=Object.create(a.prototype),s=new f(o||[]);return v(i,"_invoke",{value:c(e,r,s)}),i}function r(e,t,r){try{return{type:"normal",arg:e.call(t,r)}}catch(n){return{type:"throw",arg:n}}}function n(){}function o(){}function a(){}function i(t){["next","throw","return"].forEach(function(r){e(t,r,function(e){return this._invoke(r,e)})})}function s(e,t){function n(o,a,i,s){var c=r(e[o],e,a);if("throw"!==c.type){var d=c.arg,u=d.value;return u&&"object"==_typeof(u)&&y.call(u,"__await")?t.resolve(u.__await).then(function(e){n("next",e,i,s)},function(e){n("throw",e,i,s)}):t.resolve(u).then(function(e){d.value=e,i(d)},function(e){return n("throw",e,i,s)})}s(c.arg)}var o;v(this,"_invoke",{value:function(e,r){function a(){return new t(function(t,o){n(e,r,t,o)})}return o=o?o.then(a,a):a()}})}function c(e,t,n){var o="suspendedStart";return function(a,i){if("executing"===o)throw new Error("Generator is already running");if("completed"===o){if("throw"===a)throw i;return g()}for(n.method=a,n.arg=i;;){var s=n.delegate;if(s){var c=d(s,n);if(c){if(c===x)continue;return c}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if("suspendedStart"===o)throw o="completed",n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);o="executing";var u=r(e,t,n);if("normal"===u.type){if(o=n.done?"completed":"suspendedYield",u.arg===x)continue;return{value:u.arg,done:n.done}}"throw"===u.type&&(o="completed",n.method="throw",n.arg=u.arg)}}}function d(e,t){var n=t.method,o=e.iterator[n];if(void 0===o)return t.delegate=null,"throw"===n&&e.iterator["return"]&&(t.method="return",t.arg=void 0,d(e,t),"throw"===t.method)||"return"!==n&&(t.method="throw",t.arg=new TypeError("The iterator does not provide a '"+n+"' method")),x;var a=r(o,e.iterator,t.arg);if("throw"===a.type)return t.method="throw",t.arg=a.arg,t.delegate=null,x;var i=a.arg;return i?i.done?(t[e.resultName]=i.value,t.next=e.nextLoc,"return"!==t.method&&(t.method="next",t.arg=void 0),t.delegate=null,x):i:(t.method="throw",t.arg=new TypeError("iterator result is not an object"),t.delegate=null,x)}function u(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function l(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function f(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(u,this),this.reset(!0)}function p(e){if(e){var t=e[w];if(t)return t.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var r=-1,n=function o(){for(;++r<e.length;)if(y.call(e,r))return o.value=e[r],o.done=!1,o;return o.value=void 0,o.done=!0,o};return n.next=n}}return{next:g}}function g(){return{value:void 0,done:!0}}_regeneratorRuntime=function(){return m};var m={},h=Object.prototype,y=h.hasOwnProperty,v=Object.defineProperty||function(e,t,r){e[t]=r.value},_="function"==typeof Symbol?Symbol:{},w=_.iterator||"@@iterator",S=_.asyncIterator||"@@asyncIterator",b=_.toStringTag||"@@toStringTag";try{e({},"")}catch(k){e=function(e,t,r){return e[t]=r}}m.wrap=t;var x={},I={};e(I,w,function(){return this});var T=Object.getPrototypeOf,$=T&&T(T(p([])));$&&$!==h&&y.call($,w)&&(I=$);var N=a.prototype=n.prototype=Object.create(I);return o.prototype=a,v(N,"constructor",{value:a,configurable:!0}),v(a,"constructor",{value:o,configurable:!0}),o.displayName=e(a,b,"GeneratorFunction"),m.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===o||"GeneratorFunction"===(t.displayName||t.name))},m.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,a):(t.__proto__=a,e(t,b,"GeneratorFunction")),t.prototype=Object.create(N),t},m.awrap=function(e){return{__await:e}},i(s.prototype),e(s.prototype,S,function(){return this}),m.AsyncIterator=s,m.async=function(e,r,n,o,a){void 0===a&&(a=Promise);var i=new s(t(e,r,n,o),a);return m.isGeneratorFunction(r)?i:i.next().then(function(e){return e.done?e.value:i.next()})},i(N),e(N,b,"Generator"),e(N,w,function(){return this}),e(N,"toString",function(){return"[object Generator]"}),m.keys=function(e){var t=Object(e),r=[];for(var n in t)r.push(n);return r.reverse(),function o(){for(;r.length;){var e=r.pop();if(e in t)return o.value=e,o.done=!1,o}return o.done=!0,o}},m.values=p,f.prototype={constructor:f,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(l),!e)for(var t in this)"t"===t.charAt(0)&&y.call(this,t)&&!isNaN(+t.slice(1))&&(this[t]=void 0)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(e){function t(t,n){return a.type="throw",a.arg=e,r.next=t,n&&(r.method="next",r.arg=void 0),!!n}if(this.done)throw e;for(var r=this,n=this.tryEntries.length-1;n>=0;--n){var o=this.tryEntries[n],a=o.completion;if("root"===o.tryLoc)return t("end");if(o.tryLoc<=this.prev){var i=y.call(o,"catchLoc"),s=y.call(o,"finallyLoc");if(i&&s){if(this.prev<o.catchLoc)return t(o.catchLoc,!0);if(this.prev<o.finallyLoc)return t(o.finallyLoc)}else if(i){if(this.prev<o.catchLoc)return t(o.catchLoc,!0)}else{if(!s)throw new Error("try statement without catch or finally");if(this.prev<o.finallyLoc)return t(o.finallyLoc)}}}},abrupt:function(e,t){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.tryLoc<=this.prev&&y.call(n,"finallyLoc")&&this.prev<n.finallyLoc){var o=n;break}}o&&("break"===e||"continue"===e)&&o.tryLoc<=t&&t<=o.finallyLoc&&(o=null);var a=o?o.completion:{};return a.type=e,a.arg=t,o?(this.method="next",this.next=o.finallyLoc,x):this.complete(a)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),x},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.finallyLoc===e)return this.complete(r.completion,r.afterLoc),l(r),x}},"catch":function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.tryLoc===e){var n=r.completion;if("throw"===n.type){var o=n.arg;l(r)}return o}}throw new Error("illegal catch attempt")},delegateYield:function(e,t,r){return this.delegate={iterator:p(e),resultName:t,nextLoc:r},"next"===this.method&&(this.arg=void 0),x}},m}function asyncGeneratorStep(e,t,r,n,o,a,i){try{var s=e[a](i),c=s.value}catch(d){return void r(d)}s.done?t(c):Promise.resolve(c).then(n,o)}function _asyncToGenerator(e){return function(){var t=this,r=arguments;return new Promise(function(n,o){function a(e){asyncGeneratorStep(s,n,o,a,i,"next",e)}function i(e){asyncGeneratorStep(s,n,o,a,i,"throw",e)}var s=e.apply(t,r);a(void 0)})}}function auto_grow(e){e.style.height="5px",e.style.height=e.scrollHeight+"px"}function submitMess(e){var t=e.keyCode?e.keyCode:e.which;13!=t||e.shiftKey||(app.sendMessage(),setTimeout(function(){app.message=""},20))}var hashids=new Hashids("",6,"0123456789ABCDEF");$.ajaxSetup({headers:{"X-CSRF-TOKEN":document.querySelector('meta[name="csrf-token"]').getAttribute("content")}});var app=new Vue({el:"#chat_container",data:{currentFilter:null,keywords:"",currentCourses:[],today:today,tags:tags,senders:senders,page:1,currentSender:null,messages:[],message:"",socket:null,senderPage:0,senderLastPage:null,messagePage:0,mesageLastPage:null,firstMessageId:null,userIsTyping:!1,imageUrl:"",firstUpdate:!0,messageId:null,loadSenderFinish:!1,rootMessage:null,filter:{},filterTag:null,filterFrom:null,filterTo:null,quickReplies:[],quickReplyKeyword:"",studentNotes:[],studentNoteKey:""},created:function(){this.mergeTag(this.senders)},watch:{filterFrom:{handler:function(e){e.length>2&&this.filterChatByTags()},deep:!0},filterTo:{handler:function(e){e.length>2&&this.filterChatByTags()},deep:!0}},computed:{notes:function(){var e=this;return this.studentNotes.filter(function(t){return t.content.includes(e.studentNoteKey)})}},methods:{mergeTag:function(e){var t=this;e.forEach(function(e){if(e.tags=[],null!=e.tag_ids){var r=e.tag_ids;r=r.map(Number);for(var n=0;n<r.length;n++){var o=_.find(t.tags,["id",r[n]]);o&&e.tags.push(o)}}}),t.$forceUpdate()},initChatAtFirstTime:function(){function e(e){return t.apply(this,arguments)}var t=_asyncToGenerator(_regeneratorRuntime().mark(function r(e){var t;return _regeneratorRuntime().wrap(function(r){for(;;)switch(r.prev=r.next){case 0:if(t=this,!t.currentSender||e.id!==t.currentSender.id){r.next=3;break}return r.abrupt("return");case 3:return null!=t.currentSender&&(t.socket.removeAllListeners("received_enc_"+t.currentSender.id+"_0"),t.socket.removeAllListeners("received_"+t.currentSender.id+"_0"),t.socket.removeAllListeners("sent_enc_"+t.currentSender.id+"_"+adminId),t.socket.removeAllListeners("sent_"+t.currentSender.id+"_"+adminId),t.socket.removeAllListeners("typing_"+t.currentSender.id+"_"+t.currentSender.creator_id),t.socket.removeAllListeners("stopTyping_"+t.currentSender.id+"_"+t.currentSender.creator_id)),t.messages=[],t.currentSender=e,t.message="",t.messagePage=0,t.mesageLastPage=null,t.firstMessageId=null,t.userIsTyping=!1,t.firstUpdate=!0,t.messageId=null,t.listen(),r.next=16,t.loadByPage();case 16:t.senders=t.senders.map(function(e){return e.id==t.currentSender.id&&(e.status=1),e}),$.post(window.location.origin+"/backend/chat/get-user-courses",{id:e.creator_id},function(e){t.currentCourses=e});case 18:case"end":return r.stop()}},r,this)}));return e}(),openChat:function(){function e(e){return t.apply(this,arguments)}var t=_asyncToGenerator(_regeneratorRuntime().mark(function r(e){var t;return _regeneratorRuntime().wrap(function(r){for(;;)switch(r.prev=r.next){case 0:if(t=this,!t.currentSender||e.id!==t.currentSender.id){r.next=3;break}return r.abrupt("return");case 3:return null!=t.currentSender&&(t.socket.removeAllListeners("received_enc_"+t.currentSender.id+"_0"),t.socket.removeAllListeners("received_"+t.currentSender.id+"_0"),t.socket.removeAllListeners("sent_enc_"+t.currentSender.id+"_"+adminId),t.socket.removeAllListeners("sent_"+t.currentSender.id+"_"+adminId),t.socket.removeAllListeners("typing_"+t.currentSender.id+"_"+t.currentSender.creator_id),t.socket.removeAllListeners("stopTyping_"+t.currentSender.id+"_"+t.currentSender.creator_id)),t.messages=[],t.currentSender=e,t.message="",t.messagePage=0,t.mesageLastPage=null,t.firstMessageId=null,t.userIsTyping=!1,t.firstUpdate=!0,t.messageId=null,t.rootMessage=null,t.getStudentNotes(),t.listen(),r.next=18,t.loadByPage();case 18:t.senders=t.senders.map(function(e){return e.id==t.currentSender.id&&(e.status=1),e}),t.emitReaded(),null==e.tag_ids&&$.post(window.location.origin+"/backend/chat/get-tag-ids",{creatorid:e.creator_id},function(e){t.currentSender.tag_ids=e;var r=t.currentSender;if(r.tags=[],null!=r.tag_ids){var n=r.tag_ids.split(",");n=n.map(Number);for(var o=0;o<n.length;o++){var a=_.find(t.tags,["id",n[o]]);a&&r.tags.push(a)}}t.$forceUpdate()}),$.post(window.location.origin+"/backend/chat/get-user-courses",{id:e.creator_id},function(e){t.currentCourses=e});case 22:case"end":return r.stop()}},r,this)}));return e}(),sendMessage:function(){var e=this;$("#screen_box")[0].scrollHeight<=$("#screen_box").height()&&e.emitReaded();var t=$("#input_textarea").val().trim();if(t){var r={content:t,conversationId:e.currentSender.id,receiverId:e.currentSender.creator_id,senderId:adminId,senderType:"admin",senderName:"Admin",sentId:Date.now()+"_"+adminId+"_"+Math.floor(100*Math.random())+(Math.floor(100*Math.random())+100),type:"text"};if(e.rootMessage&&(r.rootMessage=e.rootMessage),e.currentSender.creator_id>0){var n=e.encrypt(JSON.stringify(r));e.socket.emit("send_enc",n)}else e.socket.emit("send",r);var o={content:t,conversation_id:e.currentSender.id,created_at:new Date,id:-1,sender_id:adminId,sender_type:"admin",status:0,type:"text",sentId:r.sentId,isSent:!1,admin_name:adminName,rootMessage:e.rootMessage,message_id:e.rootMessage?e.rootMessage.id:null};e.messages.push(o),e.message="",e.rootMessage=null,$("#input_textarea").css("height","35px"),e.goToBottom(),e.senders=e.senders.map(function(t){return t.id==e.currentSender.id&&(t.content=o.content,t.created_at=o.created_at,t.type=o.type,t.last_id=-1,t.status=1),t})}},sendImage:function(){var e=this;if($("#screen_box")[0].scrollHeight<=$("#screen_box").height()&&e.emitReaded(),e.currentSender){var t=new FormData($("#send_image_form")[0]);t.append("conversation_id",app.currentSender.id),t.append("user_id",app.currentSender.creator_id),t.append("message_id",e.rootMessage?e.rootMessage.id:null),$.ajax({url:window.location.origin+"/backend/send-image",type:"POST",data:t,processData:!1,contentType:!1,async:!0,error:function(e){console.log("Error get messages",e)},success:function(t){console.log("Success send image",t);try{t.content=JSON.parse(t.content)}catch(r){console.log("error JSON.parse new message content",r)}var n=_objectSpread(_objectSpread({},t),{},{rootMessage:e.rootMessage,message_id:e.rootMessage?e.rootMessage.id:null});e.messages.push(n),$("#input_file").val(""),e.goToBottom();var o={content:t.content,conversationId:t.conversation_id,receiverId:e.currentSender.creator_id,senderId:adminId,senderType:"admin",senderName:"Admin",sentId:Date.now()+"_"+adminId+"_"+Math.floor(100*Math.random())+(Math.floor(100*Math.random())+100),type:"image"};if(e.rootMessage&&(o.rootMessage=e.rootMessage),e.currentSender.creator_id>0){var a=e.encrypt(JSON.stringify(o));e.socket.emit("send_enc",a)}else e.socket.emit("send",o);e.rootMessage=null,e.senders=e.senders.map(function(r){return r.id==e.currentSender.id&&(r.content=t.content,r.created_at=t.created_at,r.type=t.type,r.last_id=-1,r.status=1),r})}})}},encrypt:function(e){return CryptoJS.AES.encrypt(JSON.stringify(e),aesKey).toString()},decrypt:function(e){var t=CryptoJS.AES.decrypt(e,aesKey);return t=t.toString(CryptoJS.enc.Utf8),t=JSON.parse(t)},listenReceive:function(e){var t=this,r={content:e.content,conversation_id:e.conversationId,created_at:new Date,id:e.id,sender_id:e.senderId,sender_type:"user",status:0,type:e.type,rootMessage:e.rootMessage};t.messages.push(r),$("#screen_box").scrollTop()+$("#screen_box").innerHeight()>=$("#screen_box")[0].scrollHeight&&t.goToBottom(),$("#screen_box")[0].scrollHeight<=$("#screen_box").height()&&t.emitReaded()},listen:function(){var e=this;e.socket.on("received_"+e.currentSender.id+"_0",function(t){e.listenReceive(t)}),e.socket.on("received_enc_"+e.currentSender.id+"_0",function(t){t=JSON.parse(e.decrypt(t)),e.listenReceive(t)}),e.currentSender.creator_id>0?e.socket.on("sent_enc_"+e.currentSender.id+"_"+adminId,function(t){t=JSON.parse(e.decrypt(t)),e.messages=e.messages.map(function(e){return e.sentId&&e.sentId===t.sentId&&(e=_objectSpread({},e),e.isSent=!0),e})}):e.socket.on("sent_"+e.currentSender.id+"_"+adminId,function(t){e.messages=e.messages.map(function(e){return e.sentId&&e.sentId===t.sentId&&(e=_objectSpread({},e),e.isSent=!0),e})}),e.socket.on("typing_"+e.currentSender.id+"_"+e.currentSender.creator_id,function(t){e.userIsTyping=!0}),e.socket.on("stopTyping_"+e.currentSender.id+"_"+e.currentSender.creator_id,function(t){e.userIsTyping=!1}),e.socket.on("readed_"+e.currentSender.id+"_0",function(){e.messages=e.messages.map(function(e){return"admin"==e.sender_type&&(e.status=1),e})})},emitTyping:function(){var e=this;e.socket.emit("typing",{conversationId:e.currentSender.id,senderId:0})},emitStopTyping:function(){var e=this;e.socket.emit("stopTyping",{conversationId:e.currentSender.id,senderId:0})},emitReaded:function(){chatTab&&chatTab.updateCountNotRead();var e=this;e.getLastUserMessage()&&0!=e.getLastUserMessage().status||(e.socket.emit("readed",{conversationId:e.currentSender.id,receiverId:e.currentSender.creator_id}),e.setRead(e.currentSender.id))},setRead:function(e){$.post(window.location.origin+"/backend/chat/mark-as-read",{conversationId:e},function(e){})},goToBottom:function(){setTimeout(function(){var e=document.getElementById("screen_box");e.scrollTop=e.scrollHeight},50)},loadByPage:function(){function e(){return t.apply(this,arguments)}var t=_asyncToGenerator(_regeneratorRuntime().mark(function r(){var e;return _regeneratorRuntime().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:if(e=this,!(e.mesageLastPage&&e.messagePage+1>e.mesageLastPage)){t.next=3;break}return t.abrupt("return");case 3:return e.messagePage++,t.next=6,$.ajax({url:window.location.origin+"/backend/messages",type:"GET",data:{conversation_id:e.currentSender.id,page:e.messagePage},async:!0,error:function(e){console.log("Error get messages",e)},success:function(t){var r;e.mesageLastPage=t.last_page;var n=t.data;n.reverse(),n.map(function(e){try{e.content=JSON.parse(e.content)}catch(t){console.log("error JSON.parse message from load",t)}return e}),(r=e.messages).unshift.apply(r,_toConsumableArray(n))}});case 6:case"end":return t.stop()}},r,this)}));return e}(),renderContent:function(e){var t=e.content;try{t=JSON.parse(e.content)}catch(r){console.log("Parse content error",r)}var n=t.toString(),t=t.toString();n=n.replace("<","&#60;"),n=n.replace(">","&#62;"),n=t.replace(new RegExp("\r?\n","g"),"<br />");var o=/(\(.*?)?\b((?:https?|ftp|file):\/\/[-a-z0-9+&@#\/%?=~_()|!:,.;]*[-a-z0-9+&@#\/%=~_()|])/gi;return n.replace(o,function(e,t,r){var n="";t=t||"";for(var o=/\(/g;o.exec(t);){var a;(a=/(.*)(\.\).*)/.exec(r)||/(.*)(\).*)/.exec(r))&&(r=a[1],n=a[2]+n)}return t+"<a class='msg-link' href='"+r+"' target='_blank'>"+r+"</a>"+n})},getLastUserMessage:function(){var e=this;if(!e.currentSender||!e.messages)return null;for(var t=e.messages.length-1;t>=0;t--)if("user"==e.messages[t].sender_type)return e.messages[t];return null},loadSenderByPage:function(){var e=this;if(!e.loadSenderFinish){this.page=this.page+1;var t=_objectSpread(_objectSpread({},this.filter),{},{page:this.page});$.ajax({url:window.location.origin+"/backend/load-more-sender",type:"GET",data:t,async:!0,error:function(e){console.log("Error get more sender",e)},success:function(t){var r,n=t;n.map(function(e){try{e.content=JSON.parse(e.content)}catch(t){console.log("error JSON.parse message from load",t)}return e}),(r=e.senders).push.apply(r,_toConsumableArray(n)),e.mergeTag(e.senders),n.length<20&&(e.loadSenderFinish=!0)}})}},loadMore:function(){this.loadByPage()},loadMoreSender:function(){this.loadSenderByPage()},getTime:function(e){var t=new Date(e),r=new Date;return t.getFullYear()==r.getFullYear()?t.getMonth()==r.getMonth()&&t.getDate()==r.getDate()?moment(e).format("HH:mm"):moment(e).format("DD/MM HH:mm"):moment(e).format("DD/MM/YYYY HH:mm")},reviewImage:function(e){var t=this;t.imageUrl=e,$("#show_full_image").modal("toggle")},showModalRemoveMessage:function(e){var t=this;t.messageId=e,$("#remove_message").modal("toggle")},removeMessage:function(){var e=this;$.ajax({url:window.location.origin+"/backend/remove-message",type:"POST",data:{message_id:e.messageId},async:!0,error:function(e){console.log("Error remove message",e),$("#remove_message").modal("hide"),alert("Có lỗi xảy ra, Vui lòng thử lại")},success:function(t){console.log("Success remove message",t),$("#remove_message").modal("hide"),e.messages=e.messages.filter(function(t){return t.id!=e.messageId})}})},getRandomColor:function(e){return"#"+hashids.encode(e)},filterChatByKeyword:function(e){var t=this;$.post(window.location.origin+"/backend/chat/filter",{type:"keywords",key:e},function(e){t.filter=e.filter;var r=e.senders;r.map(function(e){try{e.content=JSON.parse(e.content)}catch(t){console.log("error JSON.parse message from load",t)}return e}),t.senders=r,t.mergeTag(t.senders)})},clearSearch:function(){var e=this;e.keywords="",e.senders=senders,e.senders=e.senders.map(function(e){try{e.content=JSON.parse(e.content)}catch(t){console.log("error JSON.parse message from load",t)}return e}),e.mergeTag(e.senders)},filterChatByAdmin:function(e){var t=this;this.page=1,$.post(window.location.origin+"/backend/chat/filter",{page:this.page,type:"admin",id:e},function(e){t.filter=e.filter;var r=e.senders;r.map(function(e){try{e.content=JSON.parse(e.content)}catch(t){console.log("error JSON.parse message from load",t)}return e}),t.senders=r,t.mergeTag(t.senders)})},filterChatByUnread:function(){var e=this;$.post(window.location.origin+"/backend/chat/filter",{type:"unread"},function(t){e.filter=t.filter;var r=t.senders;r.map(function(e){try{e.content=JSON.parse(e.content)}catch(t){console.log("error JSON.parse message from load",t)}return e}),e.senders=r,e.mergeTag(e.senders)})},filterChatByLemonQuestionDog:function(){var e=this;$.post(window.location.origin+"/backend/chat/filter",{type:"lemon_question_dog"},function(t){e.filter=t.filter;var r=t.senders;r.map(function(e){try{e.content=JSON.parse(e.content)}catch(t){console.log("error JSON.parse message from load",t)}return e}),e.senders=r,e.mergeTag(e.senders)})},filterChatByKnowledge:function(){var e=this;$.post(window.location.origin+"/backend/chat/filter",{type:"knowledge"},function(t){e.filter=t.filter;var r=t.senders;r.map(function(e){try{e.content=JSON.parse(e.content)}catch(t){console.log("error JSON.parse message from load",t)}return e}),e.senders=r,e.mergeTag(e.senders)})},filterChatByBugs:function(){var e=this;$.post(window.location.origin+"/backend/chat/filter",{type:"bug"},function(t){e.filter=t.filter;var r=t.senders;r.map(function(e){try{e.content=JSON.parse(e.content)}catch(t){console.log("error JSON.parse message from load",t)}return e}),e.senders=r,e.mergeTag(e.senders)})},chooseTag:function(e){var t=this;t.filterTag=_.find(t.tags,["id",e]),t.filterChatByTags()},filterChatByTags:function(){var e=this;e.filterFrom&&e.filterTo&&e.filterTag.name&&$.post(window.location.origin+"/backend/chat/filter",{type:"tag",filterFrom:e.filterFrom+" 00:00:00",filterTo:e.filterTo+" 23:59:59",id:e.filterTag.id},function(t){e.filter=t.filter;var r=t.senders;r.map(function(e){try{e.content=JSON.parse(e.content)}catch(t){console.log("error JSON.parse message from load",t)}return e}),e.senders=r,e.mergeTag(e.senders)})},filterChatByUrl:function(e){var t=this;$.post(window.location.origin+"/backend/chat/filter",{type:"url",id:e},function(e){t.filter=e.filter;var r=e.senders;r.map(function(e){try{e.content=JSON.parse(e.content)}catch(t){console.log("error JSON.parse message from load",t)}return e}),t.senders=r,t.mergeTag(t.senders),t.openChat(r[0])})},filterChatOnline:function(e){var t=this;$.post(window.location.origin+"/backend/chat/filter",{online:!0},function(e){t.filter=e.filter;var r=e.senders;r.map(function(e){try{e.content=JSON.parse(e.content)}catch(t){console.log("error JSON.parse message from load",t)}return e}),t.senders=r,t.mergeTag(t.senders),t.openChat(r[0])})},filterChatIncognito:function(){var e=this;$.post(window.location.origin+"/backend/chat/filter",{incognito:!0},function(t){e.filter=t.filter;var r=t.senders;r.map(function(e){try{e.content=JSON.parse(e.content)}catch(t){console.log("error JSON.parse message from load",t)}return e}),e.senders=r,e.mergeTag(e.senders),e.openChat(r[0])})},filterChatIncognitoOnline:function(){var e=this;$.post(window.location.origin+"/backend/chat/filter",{incognito:!0,online:!0},function(t){e.filter=t.filter;var r=t.senders;r.map(function(e){try{e.content=JSON.parse(e.content)}catch(t){console.log("error JSON.parse message from load",t)}return e}),e.senders=r,e.mergeTag(e.senders),e.openChat(r[0])})},markAsUnread:function(e){$.post(window.location.origin+"/backend/chat/mark-as-unread",{id:e},function(t){console.log("mark-as-unread ",t),"success"==t&&($("#focus-"+e).removeClass("senderItem"),$("#focus-"+e).addClass("senderItemNotRead"))})},markAsKnowledge:function(e){var t=this;$.post(window.location.origin+"/backend/chat/mark-as-knowledge",{id:e},function(r){if("success"==r){var n,o=_createForOfIteratorHelper(t.senders);try{for(o.s();!(n=o.n()).done;){var a=n.value;if(a.creator_id==e){0==a.is_knowledge?a.is_knowledge=!0:a.is_knowledge=!1;break}}}catch(i){o.e(i)}finally{o.f()}}})},markAsBug:function(e){var t=this;$.post(window.location.origin+"/backend/chat/mark-as-bug",{id:e},function(r){if("success"==r){var n,o=_createForOfIteratorHelper(t.senders);try{for(o.s();!(n=o.n()).done;){var a=n.value;if(a.creator_id==e){0==a.is_bug?a.is_bug=!0:a.is_bug=!1;break}}}catch(i){o.e(i)}finally{o.f()}}})},create_tmpID:function(){var e=(new Date).getTime(),t="xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,function(t){var r=(e+16*Math.random())%16|0;return e=Math.floor(e/16),("x"==t?r:3&r|8).toString(16)});return t},getQuickReplies:function(){var e=this;$.get(window.location.origin+"/backend/quick-reply",function(t){e.quickReplies=t.data.map(function(t){return t.tmpId=e.create_tmpID(),t.editing=!1,t.showPicker=!1,t.hover=!1,t})})},addNewQuickReply:function(){var e=this,t=e.create_tmpID();e.quickReplies.unshift({id:void 0,tmpId:t,content:"",color:"red",editing:!0,showPicker:!1,hover:!1}),setTimeout(function(){$("#note-"+t).focus()},50)},confirmRemove:function(e,t){var r=this,n=window.confirm("Xác nhận xoá ghi chú này?");n&&r.removeQuickReply(e,t)},removeQuickReply:function(e,t){var r=this;if(e){var n=window.location.origin+"/backend/quick-reply/delete",o={id:e};$.post(n,o,function(e){200==e.code?r.quickReplies=r.quickReplies.filter(function(e){return e.tmpId!=t}):404==e.code?alert(e.data.error):alert("Đã xảy ra lỗi")})}else r.quickReplies=r.quickReplies.filter(function(e){return e.tmpId!=t})},onSaveReply:function(e){var t=this,r="";r=e.id?"/backend/quick-reply/update":"/backend/quick-reply/store";var n={id:e.id,content:_.trim(e.content),color:e.color};$.post(window.location.origin+r,n,function(r){200==r.code?t.quickReplies=t.quickReplies.map(function(t){return t.tmpId==e.tmpId&&(t=r.data,t.editing=!1,t.showPicker=!1,t.hover=!0),t}):alert("Đã xảy ra lỗi")})},changeColor:function(e,t){var r=this;r.quickReplies.forEach(function(r){return r.tmpId==e&&(r.color=t,r.editing=!0,r.showPicker=!1),r})},renderColor:function(e){switch(e){case"yellow":return{primary:"#f1b808",secondary:"#fbf3b5",text:"#000"};case"green":return{primary:"#0F8904",secondary:"#87C481",text:"#000"};case"pink":return{primary:"#D407A8",secondary:"#ED9BDC",text:"#000"};case"purple":return{primary:"#5c239b",secondary:"#BEA7D7",text:"#000"};case"blue":return{primary:"#0078d7",secondary:"#99C8EF",text:"#000"};case"gray":return{primary:"#767676",secondary:"#D5D5D5",text:"#000"};default:return{primary:"#f1b808",secondary:"#fbf3b5",text:"#000"}}},pasteQuickReply:function(e){this.message=e.content},searchQuickReply:function(e){var t=this;if("Enter"==e.key){var r={keyword:e.target.value};$.post(window.location.origin+"/backend/quick-reply/filter",r,function(e){t.quickReplies=e.data.map(function(e){return e.tmpId=t.create_tmpID(),e.editing=!1,e.showPicker=!1,e})})}},getStudentNotes:function(){var e=this;$.get(window.location.origin+"/backend/user-chat-note?conversation_id="+e.currentSender.id,function(t){e.studentNotes=t.data.map(function(t){return t.tmpId=e.create_tmpID(),t.editing=!1,t.showPicker=!1,t.hover=!1,t})})},addStudentNote:function(){var e=this,t=e.create_tmpID();e.studentNotes.unshift({id:void 0,tmpId:t,content:"",color:"red",editing:!0,showPicker:!1,hover:!1}),setTimeout(function(){$("#note-user-"+t).focus()},50)},confirmRemoveStudentNote:function(e,t){var r=this,n=window.confirm("Xác nhận xoá ghi chú này?");n&&r.removeStudentNote(e,t)},removeStudentNote:function(e,t){var r=this;if(e){var n=window.location.origin+"/backend/user-chat-note/delete",o={id:e};$.post(n,o,function(e){200==e.code?r.studentNotes=r.studentNotes.filter(function(e){return e.tmpId!=t}):404==e.code?alert(e.data.error):alert("Đã xảy ra lỗi")})}else r.studentNotes=r.studentNotes.filter(function(e){return e.tmpId!=t})},onSaveStudentNote:function(e){var t=this,r=e.id?"/backend/user-chat-note/update":"/backend/user-chat-note/create",n={id:e.id,content:_.trim(e.content),color:e.color,conversation_id:t.currentSender.id};$.post(window.location.origin+r,n,function(r){200==r.code?t.studentNotes=t.studentNotes.map(function(t){return t.tmpId==e.tmpId&&(t=r.data,t.editing=!1,t.showPicker=!1,t.hover=!0),t}):alert("Đã xảy ra lỗi")})},changeNoteColor:function(e,t){var r=this;r.studentNotes.forEach(function(r){return r.tmpId==e&&(r.color=t,r.editing=!0,r.showPicker=!1),r})},focusInput:function(e){setTimeout(function(){$("#note-"+e).focus()},50)},pickTag:function(e,t,r){var n=this,o={id:e,tagId:t,action:r};$.post(window.location.origin+"/backend/chat/update-tag",o,function(o){if("success"==o){var a=_.find(n.senders,["id",e]);if("add"==r){var i=_.find(n.tags,["id",t]);_.find(a.tags,["id",t])||(a.tags.push(i),n.$forceUpdate())}"remove"==r&&(_.remove(a.tags,{id:t}),n.$forceUpdate())}})},listenNewMessageEvent:function(e){
var t=this,r=t.senders.map(function(e){return e.id});if(r.includes(parseInt(e.conversationId)))t.senders=t.senders.map(function(r){return r.creator_id==e.senderId&&(r.content=e.content,r.created_at=new Date,r.type=e.type,r.last_id=-1,r.status=0,e.senderId==t.currentSender.creator_id&&$("#screen_box").scrollTop()+$("#screen_box").innerHeight()>=$("#screen_box")[0].scrollHeight&&(r.status=1)),r});else{var n={avatar:e.senderAvatar,content:e.content,created_at:new Date,creator_id:e.senderId,id:e.conversationId,last_id:-1,name:e.senderName,sender_id:e.senderId,sender_type:"user",status:0,type:e.type};t.senders.unshift(n)}},getContentImage:function(e){return"string"==typeof e?JSON.parse(e):e}},mounted:function(){var e=this,t=this;t.senders=t.senders.map(function(e){try{e.content=JSON.parse(e.content)}catch(t){console.log("error JSON.parse message from load",t)}return e}),socket?t.socket=socket:socketServer&&(t.socket=io.connect(socketServer)),t.socket.on("send_new_message_enc",function(e){e=JSON.parse(t.decrypt(e)),0==e.receiverId&&t.listenNewMessageEvent(e)}),t.socket.on("send_new_message",function(e){t.listenNewMessageEvent(e)}),window.location.href.split("#")[1]?""!=window.location.href.split("#")[1]&&t.filterChatByUrl(window.location.href.split("#")[1]):senders.length>0&&setTimeout(function(){t.initChatAtFirstTime(senders[0]),t.currentSender=senders[0],t.senderPage=1,t.senderLastPage=senders.last_page,t.getStudentNotes()},200),t.getQuickReplies();var r=document.getElementById("input_file");window.addEventListener("paste",function(t){t.clipboardData.files.length>0&&("image/png"==t.clipboardData.files[0].type&&(r.files=t.clipboardData.files,e.sendImage()),t.preventDefault())})},updated:function(){var e=this;e.firstUpdate&&setTimeout(function(){var t=document.getElementById("screen_box");t.scrollTop=t.scrollHeight,e.firstUpdate=!1},1500)}});$(document).ready(function(){setTimeout(function(){var e=$("#wrapper").height()+$("#navbar-sidebar").height();$("#page-wrapper").css("height","calc(100vh - "+e+"px)"),$("#page-wrapper").css("min-height","calc(100vh - "+e+"px)")},1e3),$.ajaxSetup({headers:{"X-CSRF-TOKEN":$('meta[name="csrf-token"]').attr("content")}}),$("#sender_box").on("scroll",function(){$(this).scrollTop()+$(this).innerHeight()>=$(this)[0].scrollHeight&&app.senders.length>0&&app.loadMoreSender()}),$("#screen_box").on("scroll",function(){$(this).scrollTop()+$(this).innerHeight()>=$(this)[0].scrollHeight&&app.emitReaded()}),$("#screen_box").on("scroll",function(){var e=$(this).scrollTop();e<=0&&app.messages.length>0&&app.loadMore()}),$("#input_textarea").focus(function(){app.emitTyping()}),$("#input_textarea").blur(function(){app.message||app.emitStopTyping()}),$("#input_file").change(function(){$(this).val()&&app&&app.sendImage()})});var delayTimer;$("#search-input").keyup(function(){clearTimeout(delayTimer),delayTimer=setTimeout(function(){var e=$("#search-input").val();e.length>0&&app.filterChatByKeyword(e),0==e.length&&app.clearSearch()},500)}),$("html").on("dragover",function(e){e.preventDefault(),e.stopPropagation()}),$("html").on("drop",function(e){e.preventDefault(),e.stopPropagation()}),$(".form-message").on("dragenter",function(e){e.stopPropagation(),e.preventDefault()}),$(".form-message").on("dragover",function(e){e.stopPropagation(),e.preventDefault()}),$(".form-message").on("drop",function(e){e.stopPropagation(),e.preventDefault();var t=e.originalEvent.dataTransfer.files,r=document.getElementById("input_file");t.length>0&&"image/png"==t[0].type&&(r.files=t,app&&app.sendImage())});