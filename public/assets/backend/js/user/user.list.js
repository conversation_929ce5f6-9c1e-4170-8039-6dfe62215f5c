"use strict";function _typeof(t){"@babel/helpers - typeof";return(_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function _regeneratorRuntime(){function t(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}function e(t,e,r,o){var i=e&&e.prototype instanceof n?e:n,a=Object.create(i.prototype),c=new d(o||[]);return g(a,"_invoke",{value:s(t,r,c)}),a}function r(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(n){return{type:"throw",arg:n}}}function n(){}function o(){}function i(){}function a(e){["next","throw","return"].forEach(function(r){t(e,r,function(t){return this._invoke(r,t)})})}function c(t,e){function n(o,i,a,c){var s=r(t[o],t,i);if("throw"!==s.type){var u=s.arg,l=u.value;return l&&"object"==_typeof(l)&&m.call(l,"__await")?e.resolve(l.__await).then(function(t){n("next",t,a,c)},function(t){n("throw",t,a,c)}):e.resolve(l).then(function(t){u.value=t,a(u)},function(t){return n("throw",t,a,c)})}c(s.arg)}var o;g(this,"_invoke",{value:function(t,r){function i(){return new e(function(e,o){n(t,r,e,o)})}return o=o?o.then(i,i):i()}})}function s(t,e,n){var o="suspendedStart";return function(i,a){if("executing"===o)throw new Error("Generator is already running");if("completed"===o){if("throw"===i)throw a;return p()}for(n.method=i,n.arg=a;;){var c=n.delegate;if(c){var s=u(c,n);if(s){if(s===L)continue;return s}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if("suspendedStart"===o)throw o="completed",n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);o="executing";var l=r(t,e,n);if("normal"===l.type){if(o=n.done?"completed":"suspendedYield",l.arg===L)continue;return{value:l.arg,done:n.done}}"throw"===l.type&&(o="completed",n.method="throw",n.arg=l.arg)}}}function u(t,e){var n=e.method,o=t.iterator[n];if(void 0===o)return e.delegate=null,"throw"===n&&t.iterator["return"]&&(e.method="return",e.arg=void 0,u(t,e),"throw"===e.method)||"return"!==n&&(e.method="throw",e.arg=new TypeError("The iterator does not provide a '"+n+"' method")),L;var i=r(o,t.iterator,e.arg);if("throw"===i.type)return e.method="throw",e.arg=i.arg,e.delegate=null,L;var a=i.arg;return a?a.done?(e[t.resultName]=a.value,e.next=t.nextLoc,"return"!==e.method&&(e.method="next",e.arg=void 0),e.delegate=null,L):a:(e.method="throw",e.arg=new TypeError("iterator result is not an object"),e.delegate=null,L)}function l(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function f(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function d(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(l,this),this.reset(!0)}function h(t){if(t){var e=t[_];if(e)return e.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var r=-1,n=function o(){for(;++r<t.length;)if(m.call(t,r))return o.value=t[r],o.done=!1,o;return o.value=void 0,o.done=!0,o};return n.next=n}}return{next:p}}function p(){return{value:void 0,done:!0}}_regeneratorRuntime=function(){return v};var v={},y=Object.prototype,m=y.hasOwnProperty,g=Object.defineProperty||function(t,e,r){t[e]=r.value},w="function"==typeof Symbol?Symbol:{},_=w.iterator||"@@iterator",b=w.asyncIterator||"@@asyncIterator",k=w.toStringTag||"@@toStringTag";try{t({},"")}catch(x){t=function(t,e,r){return t[e]=r}}v.wrap=e;var L={},E={};t(E,_,function(){return this});var S=Object.getPrototypeOf,I=S&&S(S(h([])));I&&I!==y&&m.call(I,_)&&(E=I);var M=i.prototype=n.prototype=Object.create(E);return o.prototype=i,g(M,"constructor",{value:i,configurable:!0}),g(i,"constructor",{value:o,configurable:!0}),o.displayName=t(i,k,"GeneratorFunction"),v.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===o||"GeneratorFunction"===(e.displayName||e.name))},v.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,i):(e.__proto__=i,t(e,k,"GeneratorFunction")),e.prototype=Object.create(M),e},v.awrap=function(t){return{__await:t}},a(c.prototype),t(c.prototype,b,function(){return this}),v.AsyncIterator=c,v.async=function(t,r,n,o,i){void 0===i&&(i=Promise);var a=new c(e(t,r,n,o),i);return v.isGeneratorFunction(r)?a:a.next().then(function(t){return t.done?t.value:a.next()})},a(M),t(M,k,"Generator"),t(M,_,function(){return this}),t(M,"toString",function(){return"[object Generator]"}),v.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function o(){for(;r.length;){var t=r.pop();if(t in e)return o.value=t,o.done=!1,o}return o.done=!0,o}},v.values=h,d.prototype={constructor:d,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(f),!t)for(var e in this)"t"===e.charAt(0)&&m.call(this,e)&&!isNaN(+e.slice(1))&&(this[e]=void 0)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(t){function e(e,n){return i.type="throw",i.arg=t,r.next=e,n&&(r.method="next",r.arg=void 0),!!n}if(this.done)throw t;for(var r=this,n=this.tryEntries.length-1;n>=0;--n){var o=this.tryEntries[n],i=o.completion;if("root"===o.tryLoc)return e("end");if(o.tryLoc<=this.prev){var a=m.call(o,"catchLoc"),c=m.call(o,"finallyLoc");if(a&&c){if(this.prev<o.catchLoc)return e(o.catchLoc,!0);if(this.prev<o.finallyLoc)return e(o.finallyLoc)}else if(a){if(this.prev<o.catchLoc)return e(o.catchLoc,!0)}else{if(!c)throw new Error("try statement without catch or finally");if(this.prev<o.finallyLoc)return e(o.finallyLoc)}}}},abrupt:function(t,e){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.tryLoc<=this.prev&&m.call(n,"finallyLoc")&&this.prev<n.finallyLoc){var o=n;break}}o&&("break"===t||"continue"===t)&&o.tryLoc<=e&&e<=o.finallyLoc&&(o=null);var i=o?o.completion:{};return i.type=t,i.arg=e,o?(this.method="next",this.next=o.finallyLoc,L):this.complete(i)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),L},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),f(r),L}},"catch":function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var n=r.completion;if("throw"===n.type){var o=n.arg;f(r)}return o}}throw new Error("illegal catch attempt")},delegateYield:function(t,e,r){return this.delegate={iterator:h(t),resultName:e,nextLoc:r},"next"===this.method&&(this.arg=void 0),L}},v}function asyncGeneratorStep(t,e,r,n,o,i,a){try{var c=t[i](a),s=c.value}catch(u){return void r(u)}c.done?e(s):Promise.resolve(s).then(n,o)}function _asyncToGenerator(t){return function(){var e=this,r=arguments;return new Promise(function(n,o){function i(t){asyncGeneratorStep(c,n,o,i,a,"next",t)}function a(t){asyncGeneratorStep(c,n,o,i,a,"throw",t)}var c=t.apply(e,r);i(void 0)})}}$.ajaxSetup({headers:{"X-CSRF-TOKEN":$('meta[name="csrf-token"]').attr("content")}});var router=new VueRouter({mode:"history"});Vue.filter("dateTimeToMinute",function(t){return moment(t).format("HH:mm DD/MM/YYYY")});var user__list=new Vue({el:"#user__screen",components:{paginate:VuejsPaginate},data:function(){return{url:window.location.origin,loading:!1,socket:null,results:[],editModal:!1,careers:[],provinces:[],courses:[],syncSuccess:0,totalSync:0,filter:{id:void 0,course:"",is_tester:"",watch_expired_day:"",completed:"",skype:"",time_from:void 0,time_to:void 0,sort:"",page:1,per_page:20,total_page:10},formData:{id:void 0,email:void 0,username:void 0,activation:void 0,blocked:void 0,password:void 0},messageContent:"",messageContentError:void 0,total_result:0}},watch:{filter:{handler:function(t,e){},deep:!0}},methods:{setTester:function(t){var e=this,r={id:t.id};$.post(e.url+"/backend/user/set-tester",r,function(e){200===e.code&&(t.is_tester=!t.is_tester)})},updateUser:function(){var t=this,e=_.pick(t.formData,["id","activation","email","blocked","password"]);$.post(t.url+"/backend/user/edit",e,function(e){200===e.code&&(t.results=t.results.map(function(t){return t.id===e.data.id&&(t.email=e.data.email,t.activation=e.data.activation,t.blocked=e.data.blocked),t})),t.closeEditModal()})},openEditModal:function(t){var e=this;e.formData={id:t.id,email:t.email,username:t.username,activation:t.activation,blocked:t.blocked,password:""},setTimeout(function(){e.editModal=!0},100)},closeEditModal:function(){var t=this;t.editModal=!1,t.formData={id:void 0,email:void 0,username:void 0,activation:void 0,blocked:void 0,password:void 0}},sortTotalScore:function(){var t=this;switch(t.filter.orderBy){case"":t.filter.orderBy="total_score";break;case"total_score":"desc"===t.filter.sort&&(t.filter.orderBy="");break;default:t.filter.orderBy=""}switch(t.filter.sort){case"":t.filter.sort="asc";break;case"asc":t.filter.sort="desc";break;case"desc":t.filter.sort="";break;default:t.filter.sort=""}t.applyFilter()},changePage:function(t){var e=this;e.filter.page=t;var r=_.omit(_.pickBy(e.filter,function(t,e){return void 0!==t&&null!==t&&""!==t}),["total_page"]);e.getList(r),router.replace(window.location.pathname+"?"+$.param(r))},applyFilter:function(){var t=this;t.filter.page=1;var e=_.omit(_.pickBy(t.filter,function(t,e){return void 0!==t&&null!==t&&""!==t}),["total_page","page"]);t.getList(e),router.replace(window.location.pathname+"?"+$.param(e))},onChangeCheckbox:function(t,e){vm=this,vm.filter[t]=e.target.checked?1:0,vm.applyFilter()},onChangeCourse:function(){var t=this;""==t.filter.course&&(t.filter.skype="",t.filter.watch_expired_day="",t.filter.completed=""),t.applyFilter()},getList:function(t){var e=this;e.loading=!0,setTimeout(function(){$.get(window.location.origin+"/backend/user/get-list",t,function(t){if(200===t.code){e.results=t.data.results.map(function(t){return t.certificate_info=null,t}),e.filter.total_page=t.data.total_page,e.total_result=t.data.total_result;var r=e.results.map(function(t){return t.id});e.getCertInfoByUserIds(r),e.getRemainingSkypeByUserIds(r)}else alert("Có lỗi! Liên hệ dev!!!");e.loading=!1})},200)},getRemainingSkypeByUserIds:function(t){var e=this,r={ids:t};$.post(window.location.origin+"/backend/user/get-remaining-skype-by-user-ids",r,function(t){200===t.code?(e.careers=t.careers,e.results=e.results.map(function(e){return t.data.user_with_skype.map(function(t){if(e.id===t.id){var r;e.kaiwa_total_booking=null===(r=t.course_owner[0])||void 0===r?void 0:r.kaiwa_total_booking,e.booking_count=t.booking_count}}),e})):alert("Có lỗi! Liên hệ dev!!!")})},getCertInfoByUserIds:function(t){var e=this,r={ids:t};$.post(window.location.origin+"/backend/user/get-info-by-user-ids",r,function(t){200===t.code?(e.careers=t.careers,e.results=e.results.map(function(e){return t.data.map(function(t){e.id===t.user_id&&(e.certificate_info=JSON.parse(t.certificate_info))}),e})):alert("Có lỗi! Liên hệ dev!!!")})},setFilterByUrl:function(){var t=this,e=$.deparam.querystring();_.forEach(e,function(e,r){t.filter[r]=e==parseInt(e)?parseInt(e):e})},resetFilter:function(){var t=this;t.filter={id:void 0,course:"",is_tester:"",watch_expired_day:"",completed:"",skype:"",time_from:void 0,time_to:void 0,sort:"",page:1,per_page:20,total_page:10},this.applyFilter()},onChangeDatetime:function(t){var e=this;e.filter[t.target.name]=moment(t.date).format("YYYY-MM-DD HH:mm")},sendMessage:function(t,e,r,n){vm=this;var o={content:t,conversationId:e,receiverId:r,senderId:n,senderType:"admin",senderName:"Dũng Mori",sentId:Date.now()+"_"+n+"_"+Math.floor(100*Math.random())+(Math.floor(100*Math.random())+100),type:"text"};vm.socket.emit("send",o)},initConversation:function(t){$.ajax({type:"post",url:"/backend/user/create-conversation",data:{id:t},success:function(e){$(".fa-comments-"+t).css("color","#00ab2e"),window.open(window.location.origin+"/backend/chat#"+e,"_blank")}})},getUserToSync:function(){var t=this,e={courseId:this.filter.course};t.syncSuccess=0,$.post(t.url+"/backend/user/get-user-to-sync",e,function(e){t.totalSync=e.data.users.length,200===e.code&&t.syncProgress(e.data)})},syncProgress:function(t){var e=this;return _asyncToGenerator(_regeneratorRuntime().mark(function r(){var n,o,i;return _regeneratorRuntime().wrap(function(r){for(;;)switch(r.prev=r.next){case 0:n=e,o=0;case 2:if(!(o<t.users.length)){r.next=9;break}return i={courseId:n.filter.course,userId:t.users[o],lessonIds:t.lessonIds},r.next=6,$.post(n.url+"/backend/user/sync-progress",i,function(t){200==t.code&&n.syncSuccess++});case 6:o++,r.next=2;break;case 9:case"end":return r.stop()}},r)}))()}},mounted:function(){var t=this;t.socket=io.connect("https://chat.dungmori.com"),t.courses=courses,t.setFilterByUrl();var e=_.omit(_.pickBy(t.filter,function(t,e){return void 0!==t&&null!==t&&""!==t}),["total_page"]);this.getList(e)}});