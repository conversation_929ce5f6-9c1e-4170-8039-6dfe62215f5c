/******/ (function() { // webpackBootstrap
/******/ 	// The require scope
/******/ 	var __webpack_require__ = {};
/******/ 	
/************************************************************************/
/******/ 	/* webpack/runtime/global */
/******/ 	!function() {
/******/ 		__webpack_require__.g = (function() {
/******/ 			if (typeof globalThis === 'object') return globalThis;
/******/ 			try {
/******/ 				return this || new Function('return this')();
/******/ 			} catch (e) {
/******/ 				if (typeof window === 'object') return window;
/******/ 			}
/******/ 		})();
/******/ 	}();
/******/ 	
/************************************************************************/
/*!*********************************************************!*\
  !*** ./resources/assets/js/backend/lesson-component.js ***!
  \*********************************************************/
function _typeof(o) { "@babel/helpers - typeof"; return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && "function" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? "symbol" : typeof o; }, _typeof(o); }
function _regeneratorRuntime() { "use strict"; /*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */ _regeneratorRuntime = function _regeneratorRuntime() { return e; }; var t, e = {}, r = Object.prototype, n = r.hasOwnProperty, o = Object.defineProperty || function (t, e, r) { t[e] = r.value; }, i = "function" == typeof Symbol ? Symbol : {}, a = i.iterator || "@@iterator", c = i.asyncIterator || "@@asyncIterator", u = i.toStringTag || "@@toStringTag"; function define(t, e, r) { return Object.defineProperty(t, e, { value: r, enumerable: !0, configurable: !0, writable: !0 }), t[e]; } try { define({}, ""); } catch (t) { define = function define(t, e, r) { return t[e] = r; }; } function wrap(t, e, r, n) { var i = e && e.prototype instanceof Generator ? e : Generator, a = Object.create(i.prototype), c = new Context(n || []); return o(a, "_invoke", { value: makeInvokeMethod(t, r, c) }), a; } function tryCatch(t, e, r) { try { return { type: "normal", arg: t.call(e, r) }; } catch (t) { return { type: "throw", arg: t }; } } e.wrap = wrap; var h = "suspendedStart", l = "suspendedYield", f = "executing", s = "completed", y = {}; function Generator() {} function GeneratorFunction() {} function GeneratorFunctionPrototype() {} var p = {}; define(p, a, function () { return this; }); var d = Object.getPrototypeOf, v = d && d(d(values([]))); v && v !== r && n.call(v, a) && (p = v); var g = GeneratorFunctionPrototype.prototype = Generator.prototype = Object.create(p); function defineIteratorMethods(t) { ["next", "throw", "return"].forEach(function (e) { define(t, e, function (t) { return this._invoke(e, t); }); }); } function AsyncIterator(t, e) { function invoke(r, o, i, a) { var c = tryCatch(t[r], t, o); if ("throw" !== c.type) { var u = c.arg, h = u.value; return h && "object" == _typeof(h) && n.call(h, "__await") ? e.resolve(h.__await).then(function (t) { invoke("next", t, i, a); }, function (t) { invoke("throw", t, i, a); }) : e.resolve(h).then(function (t) { u.value = t, i(u); }, function (t) { return invoke("throw", t, i, a); }); } a(c.arg); } var r; o(this, "_invoke", { value: function value(t, n) { function callInvokeWithMethodAndArg() { return new e(function (e, r) { invoke(t, n, e, r); }); } return r = r ? r.then(callInvokeWithMethodAndArg, callInvokeWithMethodAndArg) : callInvokeWithMethodAndArg(); } }); } function makeInvokeMethod(e, r, n) { var o = h; return function (i, a) { if (o === f) throw Error("Generator is already running"); if (o === s) { if ("throw" === i) throw a; return { value: t, done: !0 }; } for (n.method = i, n.arg = a;;) { var c = n.delegate; if (c) { var u = maybeInvokeDelegate(c, n); if (u) { if (u === y) continue; return u; } } if ("next" === n.method) n.sent = n._sent = n.arg;else if ("throw" === n.method) { if (o === h) throw o = s, n.arg; n.dispatchException(n.arg); } else "return" === n.method && n.abrupt("return", n.arg); o = f; var p = tryCatch(e, r, n); if ("normal" === p.type) { if (o = n.done ? s : l, p.arg === y) continue; return { value: p.arg, done: n.done }; } "throw" === p.type && (o = s, n.method = "throw", n.arg = p.arg); } }; } function maybeInvokeDelegate(e, r) { var n = r.method, o = e.iterator[n]; if (o === t) return r.delegate = null, "throw" === n && e.iterator["return"] && (r.method = "return", r.arg = t, maybeInvokeDelegate(e, r), "throw" === r.method) || "return" !== n && (r.method = "throw", r.arg = new TypeError("The iterator does not provide a '" + n + "' method")), y; var i = tryCatch(o, e.iterator, r.arg); if ("throw" === i.type) return r.method = "throw", r.arg = i.arg, r.delegate = null, y; var a = i.arg; return a ? a.done ? (r[e.resultName] = a.value, r.next = e.nextLoc, "return" !== r.method && (r.method = "next", r.arg = t), r.delegate = null, y) : a : (r.method = "throw", r.arg = new TypeError("iterator result is not an object"), r.delegate = null, y); } function pushTryEntry(t) { var e = { tryLoc: t[0] }; 1 in t && (e.catchLoc = t[1]), 2 in t && (e.finallyLoc = t[2], e.afterLoc = t[3]), this.tryEntries.push(e); } function resetTryEntry(t) { var e = t.completion || {}; e.type = "normal", delete e.arg, t.completion = e; } function Context(t) { this.tryEntries = [{ tryLoc: "root" }], t.forEach(pushTryEntry, this), this.reset(!0); } function values(e) { if (e || "" === e) { var r = e[a]; if (r) return r.call(e); if ("function" == typeof e.next) return e; if (!isNaN(e.length)) { var o = -1, i = function next() { for (; ++o < e.length;) if (n.call(e, o)) return next.value = e[o], next.done = !1, next; return next.value = t, next.done = !0, next; }; return i.next = i; } } throw new TypeError(_typeof(e) + " is not iterable"); } return GeneratorFunction.prototype = GeneratorFunctionPrototype, o(g, "constructor", { value: GeneratorFunctionPrototype, configurable: !0 }), o(GeneratorFunctionPrototype, "constructor", { value: GeneratorFunction, configurable: !0 }), GeneratorFunction.displayName = define(GeneratorFunctionPrototype, u, "GeneratorFunction"), e.isGeneratorFunction = function (t) { var e = "function" == typeof t && t.constructor; return !!e && (e === GeneratorFunction || "GeneratorFunction" === (e.displayName || e.name)); }, e.mark = function (t) { return Object.setPrototypeOf ? Object.setPrototypeOf(t, GeneratorFunctionPrototype) : (t.__proto__ = GeneratorFunctionPrototype, define(t, u, "GeneratorFunction")), t.prototype = Object.create(g), t; }, e.awrap = function (t) { return { __await: t }; }, defineIteratorMethods(AsyncIterator.prototype), define(AsyncIterator.prototype, c, function () { return this; }), e.AsyncIterator = AsyncIterator, e.async = function (t, r, n, o, i) { void 0 === i && (i = Promise); var a = new AsyncIterator(wrap(t, r, n, o), i); return e.isGeneratorFunction(r) ? a : a.next().then(function (t) { return t.done ? t.value : a.next(); }); }, defineIteratorMethods(g), define(g, u, "Generator"), define(g, a, function () { return this; }), define(g, "toString", function () { return "[object Generator]"; }), e.keys = function (t) { var e = Object(t), r = []; for (var n in e) r.push(n); return r.reverse(), function next() { for (; r.length;) { var t = r.pop(); if (t in e) return next.value = t, next.done = !1, next; } return next.done = !0, next; }; }, e.values = values, Context.prototype = { constructor: Context, reset: function reset(e) { if (this.prev = 0, this.next = 0, this.sent = this._sent = t, this.done = !1, this.delegate = null, this.method = "next", this.arg = t, this.tryEntries.forEach(resetTryEntry), !e) for (var r in this) "t" === r.charAt(0) && n.call(this, r) && !isNaN(+r.slice(1)) && (this[r] = t); }, stop: function stop() { this.done = !0; var t = this.tryEntries[0].completion; if ("throw" === t.type) throw t.arg; return this.rval; }, dispatchException: function dispatchException(e) { if (this.done) throw e; var r = this; function handle(n, o) { return a.type = "throw", a.arg = e, r.next = n, o && (r.method = "next", r.arg = t), !!o; } for (var o = this.tryEntries.length - 1; o >= 0; --o) { var i = this.tryEntries[o], a = i.completion; if ("root" === i.tryLoc) return handle("end"); if (i.tryLoc <= this.prev) { var c = n.call(i, "catchLoc"), u = n.call(i, "finallyLoc"); if (c && u) { if (this.prev < i.catchLoc) return handle(i.catchLoc, !0); if (this.prev < i.finallyLoc) return handle(i.finallyLoc); } else if (c) { if (this.prev < i.catchLoc) return handle(i.catchLoc, !0); } else { if (!u) throw Error("try statement without catch or finally"); if (this.prev < i.finallyLoc) return handle(i.finallyLoc); } } } }, abrupt: function abrupt(t, e) { for (var r = this.tryEntries.length - 1; r >= 0; --r) { var o = this.tryEntries[r]; if (o.tryLoc <= this.prev && n.call(o, "finallyLoc") && this.prev < o.finallyLoc) { var i = o; break; } } i && ("break" === t || "continue" === t) && i.tryLoc <= e && e <= i.finallyLoc && (i = null); var a = i ? i.completion : {}; return a.type = t, a.arg = e, i ? (this.method = "next", this.next = i.finallyLoc, y) : this.complete(a); }, complete: function complete(t, e) { if ("throw" === t.type) throw t.arg; return "break" === t.type || "continue" === t.type ? this.next = t.arg : "return" === t.type ? (this.rval = this.arg = t.arg, this.method = "return", this.next = "end") : "normal" === t.type && e && (this.next = e), y; }, finish: function finish(t) { for (var e = this.tryEntries.length - 1; e >= 0; --e) { var r = this.tryEntries[e]; if (r.finallyLoc === t) return this.complete(r.completion, r.afterLoc), resetTryEntry(r), y; } }, "catch": function _catch(t) { for (var e = this.tryEntries.length - 1; e >= 0; --e) { var r = this.tryEntries[e]; if (r.tryLoc === t) { var n = r.completion; if ("throw" === n.type) { var o = n.arg; resetTryEntry(r); } return o; } } throw Error("illegal catch attempt"); }, delegateYield: function delegateYield(e, r, n) { return this.delegate = { iterator: values(e), resultName: r, nextLoc: n }, "next" === this.method && (this.arg = t), y; } }, e; }
function asyncGeneratorStep(n, t, e, r, o, a, c) { try { var i = n[a](c), u = i.value; } catch (n) { return void e(n); } i.done ? t(u) : Promise.resolve(u).then(r, o); }
function _asyncToGenerator(n) { return function () { var t = this, e = arguments; return new Promise(function (r, o) { var a = n.apply(t, e); function _next(n) { asyncGeneratorStep(a, r, o, _next, _throw, "next", n); } function _throw(n) { asyncGeneratorStep(a, r, o, _next, _throw, "throw", n); } _next(void 0); }); }; }
function _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }
function _toPropertyKey(t) { var i = _toPrimitive(t, "string"); return "symbol" == _typeof(i) ? i : i + ""; }
function _toPrimitive(t, r) { if ("object" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || "default"); if ("object" != _typeof(i)) return i; throw new TypeError("@@toPrimitive must return a primitive value."); } return ("string" === r ? String : Number)(t); }
var configCkEditor = {
  filebrowserBrowseUrl: "/backend/ckfinder/browser",
  filebrowserUploadUrl: "/backend/ckfinder/connector?command=QuickUpload&type=Files",
  extraPlugins: "stylescombo,maximize,sourcearea,button,panelbutton,fakeobjects,justify,colorbutton,dialogui,dialog,flash,filetools,popup,filebrowser,font,table,image,furigana,panel,listblock,floatpanel,richcombo,format,resize,lineheight",
  fontSize_sizes: "8/8px;9/9px;10/10px;11/11px;12/12px;14/14px;16/16px;18/18px;20/20px;22/22px;24/24px;26/26px;28/28px;36/36px;48/48px;64/64px;72/72px;",
  allowedContent: {
    $1: {
      elements: CKEDITOR.dtd,
      attributes: true,
      styles: true,
      classes: true
    }
  },
  disallowedContent: "script;style; *[on*]",
  stylesSet: [{
    name: "Overline Text",
    element: "span",
    attributes: {
      style: "text-decoration: overline;"
    }
  }]
};
$(document).ready(function () {
  $.ajaxSetup({
    headers: {
      "X-CSRF-TOKEN": $('meta[name="csrf-token"]').attr("content")
    }
  });
  // $("#lesson_info").validate({
  //   ignore: [],
  //   rules: {
  //     course: {
  //       required: true,
  //     },
  //   },
  //   messages: {
  //     course: "Chưa chọn",
  //   },
  // });
  if ($("#lesson_id").val() > 0) {
    $("#content_tab").prop("disabled", false);
  } else {
    $("#content_tab").prop("disabled", true);
  }

  // Thêm thành phần vào câu hỏi
  $(document).on("click", ".question_item_header_add_content", function () {
    // var questionItem = $(this).closest(".question_item");
    // var contentItems = questionItem.find(".question_content_item");
    add_conjunction("new");
  });
  $(document).on("click", ".btn_sentence_jumble_item_add_content", function () {
    add_sentence_jumble("new");
  });
  $(document).on("click", ".btn_sentence_jumble_item_add_content_false", function () {
    add_item_sentence_jumble_false("new");
  });
  $(document).on("click", ".btn_sentence_jumble_item_del_content", function () {
    if (number_sentence_jumble > 2) {
      $(".item_component_sentence_jumble[data-number=".concat(number_sentence_jumble, "]")).remove();
      ckeditorSentenceJumble = ckeditorSentenceJumble.filter(function (item) {
        return item !== "lesson[value][".concat(number_sentence_jumble, "]");
      });
      number_sentence_jumble--;
    }
  });
  $(document).on("click", ".btn_sentence_jumble_item_del_content_false", function () {
    $(".item_component_sentence_jumble_false[data-number=".concat(number_item_sentence_jumble_false, "]")).remove();
    ckeditorSentenceJumble = ckeditorSentenceJumble.filter(function (item) {
      return item !== "lesson[item_false][".concat(number_item_sentence_jumble_false, "]");
    });
    number_item_sentence_jumble_false--;
  });
  $(document).on("click", ".btn_word_pair_matching_item_add_content", function () {
    add_word_pair_matching("new");
  });
  $(document).on("click", ".btn_word_pair_matching_item_del_content", function () {
    if (number_word_pair_matching > 2) {
      $(".word_pair_matching_item[data-number=".concat($(this).data("number"), "]")).remove();
      number_word_pair_matching--;
      var itemLeft = "lesson[value][".concat($(this).data("number"), "][left]");
      var itemRight = "lesson[value][".concat($(this).data("number"), "][right]");
      CKEDITOR.instances[itemLeft].destroy(true);
      CKEDITOR.instances[itemRight].destroy(true);
      ckeditorWordPairMatching = ckeditorWordPairMatching.filter(function (item) {
        return item !== itemLeft && item !== itemRight;
      });
    }
  });

  // Xóa thành phần trong câu hỏi
  $(document).on("click", ".question_item_header_del_content", function () {
    if (number_conjunction > 0) {
      console.log("CKEDITOR.instances[lesson[item][".concat(number_conjunction, "][value]: "), CKEDITOR.instances["lesson[item][".concat(number_conjunction, "][value]")]);
      CKEDITOR.instances["lesson[item][".concat(number_conjunction, "][value]")].destroy(true);
      $("#result" + number_conjunction).remove();
      number_conjunction--;
      ckeditorConjunction.splice(number_conjunction, 1);
    } // Xóa thành phần
  });
});

//select nhóm bài học theo khóa học
var task_choice = CKEDITOR.replace("task_choice", configCkEditor);
var suggest = CKEDITOR.replace("suggest", configCkEditor);
var explain = CKEDITOR.replace("explain", configCkEditor);
var task_content = CKEDITOR.replace("task_content", configCkEditor);
var conjunction_suggest = CKEDITOR.replace("lesson_suggest_conjunction", configCkEditor);
var conjunction_explain = CKEDITOR.replace("lesson_explain_conjunction", configCkEditor);
var sentence_jumble_suggest = CKEDITOR.replace("lesson_suggest_sentence_jumble", configCkEditor);
var sentence_jumble_explain = CKEDITOR.replace("lesson_explain_sentence_jumble", configCkEditor);
var sentence_jumble_question = CKEDITOR.replace("lesson_question_sentence_jumble", configCkEditor);

// var word_pair_matching_suggest = CKEDITOR.replace("lesson_suggest_word_pair_matching", configCkEditor);
var word_pair_matching_explain = CKEDITOR.replace("lesson_explain_word_pair_matching", configCkEditor);
// var word_pair_matching_question = CKEDITOR.replace("lesson_question_word_pair_matching", configCkEditor);

var lesson_question_speaking = CKEDITOR.replace("lesson_question_speaking", configCkEditor);
var name_html = CKEDITOR.replace("name_html", configCkEditor);
if (lesson && lesson.name_html) {
  name_html.setData(lesson.name_html);
}
CKFinder.setupCKEditor(task_choice);
CKFinder.setupCKEditor(suggest);
CKFinder.setupCKEditor(explain);
CKFinder.setupCKEditor(task_content);
CKFinder.setupCKEditor(conjunction_suggest);
CKFinder.setupCKEditor(conjunction_explain);
CKFinder.setupCKEditor(sentence_jumble_suggest);
CKFinder.setupCKEditor(sentence_jumble_explain);
CKFinder.setupCKEditor(sentence_jumble_question);
// CKFinder.setupCKEditor(word_pair_matching_suggest);
CKFinder.setupCKEditor(word_pair_matching_explain);
CKFinder.setupCKEditor(lesson_question_speaking);
// CKFinder.setupCKEditor(word_pair_matching_question);

var number_faq = 1;
var num_answer = 1;
var num_feedback = 1;
var number_result = 1;
var number_conjunction = 0;
var ckeditorResult = [];
var ckeditorConjunction = [];
var listCkeditor = [];
var ckeditorSentenceJumble = [];
var number_sentence_jumble = 2;
var number_item_sentence_jumble_false = 0;
var ckeditorWordPairMatching = [];
var number_word_pair_matching = 0;

//sap xep keo tha
sortTable("body_task", 2, 1, "/backend/lesson/task/sort", "sort", "task_table");
$("#task-type").val(lesson_task.length > 0 ? lesson_task[lesson_task.length - 1].type : 1);
//tim kiem theo khoa hoc va nhom bai hoc
__webpack_require__.g.selectGroupByCourse = function (groupId) {
  var course_id = $("#course").val();
  $("#group").empty();
  $.ajax({
    type: "post",
    url: "/backend/group-by-course",
    data: {
      _token: $("input[name=_token]").val(),
      course_id: course_id
    },
    success: function success(data) {
      if (data.errors) {
        //chua xui li
      } else {
        var option;
        $.each(data.group, function (item, value) {
          if (groupId == value.id) {
            option += '<option value="' + value.id + '" selected>' + value.name + "</option>";
          } else {
            option += '<option value="' + value.id + '">' + value.name + "</option>";
          }
        });
        $("#group").append(option);
      }
    }
  });
};

//upload anh
__webpack_require__.g.previewLesson = function () {
  var preview = document.querySelector("#image_lesson");
  var file = document.querySelector("#img_lesson").files[0];
  var reader = new FileReader();
  reader.onloadend = function () {
    preview.src = reader.result;
  };
  if (file) {
    var nameFile = file.name;
    var extension = nameFile.substring(nameFile.lastIndexOf(".") + 1).toLowerCase();
    if (file.size / 1000000 > 3) {
      alert("Ảnh vượt quá dung lượng");
      preview.src = noImageUrl;
      $("#img_lesson").val("");
    } else if (extension != "gif" && extension != "jpg" && extension != "jpeg" && extension != "png" && extension != "svg") {
      alert("Ảnh không đúng định dạng");
      preview.src = noImageUrl;
      $("#img_lesson").val("");
    } else {
      reader.readAsDataURL(file);
    }
  } else {
    preview.src = noImageUrl;
  }
};

//layout khi lựa chọn kiểm tra
if ($("input[name=is_examination]:checked").length) {
  $(".refer_total_marks").slideDown("250");
} else {
  $(".refer_total_marks").slideUp("250");
}
$("input[name=is_examination]").on("change", function () {
  if ($("input[name=is_examination]:checked").length) {
    $(".refer_total_marks").slideDown("250");
  } else {
    $(".refer_total_marks").slideUp("250");
  }
});

//Khi lua chon tinh diem ca bai tu dong
if ($("input[name=total_marks_calculation]:checked").length) {
  $("#total_marks").attr("disabled", true);
} else {
  $("#total_marks").attr("disabled", false);
}
$("input[name=total_marks_calculation]").on("change", function () {
  if ($("input[name=total_marks_calculation]:checked").length) {
    $("#total_marks").attr("disabled", true);
  } else {
    $("#total_marks").attr("disabled", false);
  }
});

//Khi lua chon tac vu cho tung noi dung
__webpack_require__.g.displayTask = function (type) {
  var _lesson_task$mondai, _lesson_task$mondai2, _lesson_task$mondai3, _lesson_task$mondai4;
  $(".global_error").addClass("hidden");
  console.log("displayTask: ", type);
  switch (type) {
    case 2:
      showFormById("#body_video");
      $("#link_video").val("");
      $("#title_video").val("");
      $("#video_sort").val("");
      $("#video_show_on").prop("checked", true);
      manipulation("video_add");
      break;
    case 5:
      showFormById("#body_mp3");
      $("#mp3_descr").val("");
      $("#mp3_link").val("");
      $("#mp3_sort").val("");
      $("#mp3_show_on").prop("checked", true);
      $("#mp3MondaiName").val(lesson_task.length ? (_lesson_task$mondai = lesson_task[lesson_task.length - 1].mondai) === null || _lesson_task$mondai === void 0 ? void 0 : _lesson_task$mondai.title : "");
      manipulation("mp3_add");
      break;
    case 1:
      showFormById("#body_content");
      $("#content_sort").val("");
      $("#content_show_on").prop("checked", true);
      $("#contentMondaiName").val(lesson_task.length ? (_lesson_task$mondai2 = lesson_task[lesson_task.length - 1].mondai) === null || _lesson_task$mondai2 === void 0 ? void 0 : _lesson_task$mondai2.title : "");
      manipulation("content_add");
      break;
    case 3:
      showFormById("#body_multi_choice");
      $("#choice_sort").val("");
      $("#choice_grade").val("");
      $(".answer_content").empty().html();
      $(".feedback_add").empty().html();
      $("#choice_show_on").prop("checked", true);
      $("#choiceMondaiName").val(lesson_task.length ? (_lesson_task$mondai3 = lesson_task[lesson_task.length - 1].mondai) === null || _lesson_task$mondai3 === void 0 ? void 0 : _lesson_task$mondai3.title : "");
      manipulation("choice_add");
      num_answer = 1;
      // TODO: Update later KAN-14
      if (listCkeditorAnswerOption && listCkeditorAnswerOption.length >= 1) {
        for (var i = 0; i < listCkeditorAnswerOption.length; i++) {
          CKEDITOR.instances["answer_content_" + (i + 1)].destroy(true);
        }
      }
      listCkeditorAnswerOption = [];
      num_feedback = 1;
      break;
    case 6:
      showFormById("#body_answer");
      $(".faq_content_wraper").empty().html();
      $("#faq_sort").val("");
      $("#faq_grade").val("");
      $("#faq_show_on").prop("checked", true);
      manipulation("faq_add");
      number_faq = 1;
      if (listCkeditor.length >= 1) {
        for (var i = 0; i < listCkeditor.length; i++) {
          CKEDITOR.instances["faq_question_" + (i + 1)].destroy(true);
        }
      }
      listCkeditor = [];
      break;
    case 8:
      showFormById("#body_pdf");
      $("#body_pdf").data("type", "add");
      $("#task_submit").data("type", "pdf_upload");
      $("#progress-pdf-upload").hide();
      manipulation("pdf_add");
      $("#pdfFilename").val("");
      break;
    case 7:
      showFormById("#body_audio");
      $("#body_audio").data("type", "add");
      $("#task_submit").data("type", "audio");
      manipulation("audio_add");
      $("#audio_filename").val("");
      $("#audio_text").val("");
      $("#is_kaiwa2").prop("checked", false);
      break;
    case 9:
      showFormById("#body_flashcard");
      $("#body_flashcard").data("type", "add");
      $("#task_submit").data("type", "audio_upload");
      $("#progress-pdf-upload").hide();
      manipulation("flashcard_add");
      $("#pdfFilename").val("");
      break;
    case 13:
      // điền vào chỗ trống
      showFormById("#body_conjunction");
      number_conjunction = 0;
      ckeditorConjunction = [];
      resetCkeditorConjunction();
      // conjunction_suggest.setData("");
      // conjunction_explain.setData("");
      // $("#body_flashcard").data("type", "add");
      // $("#task_submit").data("type", "audio_upload");
      // $("#progress-pdf-upload").hide();
      $("#title_question_conjunction").val("");
      $(".flc_img_preview").empty();
      $("#audio_preview_conjunction").empty();
      $("#audio_preview_explain_mp3_conjunction").empty();
      $("#text_vi_conjunction").val("");
      $(".question_item_content").empty();
      $("#conjunctionMondaiName").val(lesson_task.length ? (_lesson_task$mondai4 = lesson_task[lesson_task.length - 1].mondai) === null || _lesson_task$mondai4 === void 0 ? void 0 : _lesson_task$mondai4.title : "");
      manipulation("conjunction_add");
      // $("#pdfFilename").val("");
      break;
    case 14:
      // sắp xếp câu
      resetFormSentenceJumble("add");
      showFormById("#body_sentence_jumble");
      manipulation("sentence_jumble_add");
      break;
    case 15:
      resetFormWordPairMatching("add");
      showFormById("#body_word_pair_matching");
      manipulation("word_pair_matching_add");
      break;
    case 16:
      resetFormSpeaking("add");
      showFormById("#body_speaking");
      manipulation("speaking_add");
      break;
    default:
      showFormById("#body_result");
      $(".result_content_wraper").empty().html();
      $("#result_sort").val("");
      $("#result_show_on").prop("checked", true);
      manipulation("result_add");
      number_result = 1;
      if (ckeditorResult.length >= 1) {
        for (var j = 0; j < ckeditorResult.length; j++) {
          CKEDITOR.instances["result_content_" + (j + 1)].destroy(true);
        }
      }
      ckeditorResult = [];
      break;
  }
};
__webpack_require__.g.showFormById = function (id) {
  $(".form-lesson-component").hide();
  $(id).show();
};
__webpack_require__.g.hideAllForm = function () {
  $(".form-lesson-component").hide();
};
__webpack_require__.g.manipulation = function (thenAddThisOne) {
  var removeTheseAll = ["video_edit", "video_add", "mp3_add", "mp3_edit", "content_add", "content_edit", "choice_add", "choice_edit", "faq_add", "faq_edit", "result_add", "result_edit", "pdf_add", "pdf_edit", "quiz_add", "quiz_edit", "gap_fill_add", "gap_fill_edit", "conjunction_add", "conjunction_edit", "sentence_jumble_add", "sentence_jumble_edit", "word_pair_matching_add", "word_pair_matching_edit", "speaking_add", "speaking_edit"].join(" ");
  $(".actionBtn").removeClass(removeTheseAll);
  $(".actionBtn").addClass(thenAddThisOne);
};
$(document).on("click", "#task-add", function () {
  Object.assign(vueInstance.$data, originalData);
  vueInstance.$data.filters.createdAt = "desc";
  vueInstance.$data.filters.name = "";
  vueInstance.$data.isAddVideo = true;
  $("#task_submit").data("type", "");
  setTimeout(function () {
    $("#get-videos-vn").click();
  }, 0);
  var type = $("#task-type").val();
  if (type == 17) {
    window.open("/backend/flashcard/create?lesson_id=" + lesson.id, "_blank");
    return;
  }
  displayTask(parseInt(type));
  task_content.setData("");
  task_choice.setData("");
  suggest.setData("");
  explain.setData("");
  $("#audio_preview").empty();
  conjunction_explain.setData("");
  conjunction_suggest.setData("");
  sentence_jumble_question.setData("");
  sentence_jumble_suggest.setData("");
  sentence_jumble_explain.setData("");
  // word_pair_matching_question.setData("");
  word_pair_matching_explain.setData("");
  lesson_question_speaking.setData("");
  // word_pair_matching_suggest.setData("");
  $("#lesson_component_id").val("");
  $("#pageModal").modal("show");
});

//function them dap an cho phan trac nghiem
//var num_answer = 1 ;
var listCkeditorAnswerOption = [];
__webpack_require__.g.add_answer_option = function (mode) {
  var answer = $(document.createElement("div")).attr("id", "answer" + num_answer);
  // TODO: Update later KAN-14 convert input to textarea
  answer.after().html("<div class=\"col-sm-12\" style=\"margin-bottom: 10px;\">\n      <textarea class=\"form-control\" id=\"answer_content_".concat(num_answer, "\" name=\"answer_content_").concat(num_answer, "\" placeholder=\"\u0110\xE1p \xE1n ").concat(num_answer, "\"></textarea>\n      <span class=\"btn btn-xs btn-info btn-add-mp3-answer-content\" data-id=\"").concat(num_answer, "\"><i class=\"fa fa-volume-down\"></i> Th\xEAm mp3</span>\n      <label class=\"mt5\">\n        <span>\u0110i\u1EC3m: </span>\n      </label>\n      <input type=\"number\" id=\"answer_grade_").concat(num_answer, "\" name=\"answer_grade_").concat(num_answer, "\" value=\"0\" style=\"width: 60px;\">\n      </div>"));
  answer.appendTo(".answer_content");

  // TODO: Update later KAN-14
  if (mode == "new") {
    var editor = CKEDITOR.replace("answer_content_" + num_answer, configCkEditor);
    CKFinder.setupCKEditor(editor);
  }
  listCkeditorAnswerOption.push("answer_content_" + num_answer);
  num_answer++;
};
$(document).on("click", ".btn-add-mp3-answer-content", function () {
  var id = $(this).data("id");
  var input = document.createElement("input");
  input.type = "file";
  input.accept = "audio/mp3";
  input.style.display = "none";
  input.onchange = function (event) {
    var file = event.target.files[0];
    if (file) {
      var reader = new FileReader();
      reader.onload = function (e) {
        uploadAudio(file, function (res) {
          // TODO: Update later KAN-14
          CKEDITOR.instances["answer_content_" + id].insertHtml("{! ".concat(res.data.file_path, " !}"));
          //   $("#answer_content_" + id).val(res.data.file_path);
        });
      };
      reader.readAsDataURL(file);
    }
  };
  document.body.appendChild(input);
  input.click();
});
__webpack_require__.g.del_answer_option = function () {
  if (num_answer > 1) {
    num_answer--;
    // TODO: Update later KAN-14
    CKEDITOR.instances["answer_content_" + num_answer].destroy(true);
    listCkeditorAnswerOption.splice(num_answer - 1, 1);
    $("#answer" + num_answer).remove();
  }
};

//function them phan hoi cho phan trac nghiem
//var num_feedback = 1;
__webpack_require__.g.add_feedback_option = function () {
  var feedback = $(document.createElement("div")).attr("id", "feedback" + num_feedback);
  feedback.after().html('<div class="col-sm-12">' + "<span>Điểm yêu cầu: </span>" + '<input type="number" id="feedback_grade_' + num_feedback + '" name="feedback_grade_' + num_feedback + '" style="margin-bottom: 4px; width: 60px;">' + '<textarea class="form-control" placeholder="Phản hồi' + num_feedback + '" id="feedback_content_' + num_feedback + '" name="feedback_content_' + num_feedback + '" style="margin-bottom: 10px;"></textarea>' + "<hr>" + "</div>");
  feedback.appendTo(".feedback_add");
  num_feedback++;
};
__webpack_require__.g.del_feedback_option = function () {
  if (num_feedback > 1) {
    num_feedback--;
    $("#feedback" + num_feedback).remove();
  }
};

//function them dap an cho phan dien tu vao cho trong
//var number_faq = 1;
__webpack_require__.g.add_faq = function (mode) {
  var faq = $(document.createElement("div")).attr("id", "faq" + number_faq);
  faq.after().html('<div class="row" style="margin: 10px 0px;">' + '<textarea id="faq_question_' + number_faq + '" name="faq_question_' + number_faq + '" class="form-control" style="height: 80px; "></textarea>' + '<input type="text" id="faq_content_' + number_faq + '" name="faq_content_' + number_faq + '" class="form-control" placeholder="Đáp án ' + number_faq + '">' + '<input type="number" id="faq_grade_' + number_faq + '" name="faq_grade_' + number_faq + '"class="form-control" placeholder="Điểm ">' + "</div>");
  faq.appendTo(".faq_content_wraper");
  if (mode == "new") {
    var editor = CKEDITOR.replace("faq_question_" + number_faq, configCkEditor);
    CKFinder.setupCKEditor(editor);
  }
  listCkeditor.push("faq_question_" + number_faq);
  number_faq++;
};
__webpack_require__.g.del_faq = function () {
  if (number_faq > 1) {
    number_faq--;
    CKEDITOR.instances["faq_question_" + number_faq].destroy(true);
    listCkeditor.splice(number_faq - 1, 1);
    $("#faq" + number_faq).remove();
  }
};

//var number_result = 1 ;
__webpack_require__.g.add_result = function (mode) {
  var result = $(document.createElement("div")).attr("id", "result" + number_result);
  result.after().html('<input type="text" id="result_title_' + number_result + '" name="result_title_' + number_result + '" class="form-control">' + '<textarea id="result_content_' + number_result + '" name="result_content_' + number_result + '" class="form-control" style="height: 120px;"></textarea>');
  result.appendTo(".result_content_wraper");
  if (mode == "new") {
    var editor = CKEDITOR.replace("result_content_" + number_result, configCkEditor);
    CKFinder.setupCKEditor(editor);
  }
  ckeditorResult.push("result_content_" + number_result);
  number_result++;
};
__webpack_require__.g.del_result = function () {
  if (number_result > 1) {
    number_result--;
    CKEDITOR.instances["result_content_" + number_result].destroy(true);
    ckeditorResult.splice(number_result - 1, 1);
    $("#result" + number_result).remove();
  }
};

//Khi click edit va delete tung tac vu da co
var originalVideoName = "";
$(document).on("click", ".edit-task", function () {
  vueInstance.$data.isAddVideo = false;
  setTimeout(function () {
    $("#get-videos-vn").click();
  }, 0);
  var id = $(this).data("info");
  $.ajax({
    type: "get",
    url: "/backend/task/" + id,
    success: function success(data) {
      displayTask(data[0].type);
      fillmodalData(data[0]);
      originalVideoName = data[0].video_name;
    }
  });
  //fillmodalData(task)
  $("#pageModal").modal("show");
});
__webpack_require__.g.fillmodalData = function (task) {
  var _task$part, _task$mondai, _task$part2, _task$mondai2, _task$part3, _task$mondai3, _task$part4, _task$mondai4;
  switch (task.type) {
    case 5:
      //mp3
      manipulation("mp3_edit");
      $("#mp3_id").val(task.id);
      $("#mp3MondaiPart").val((_task$part = task.part) === null || _task$part === void 0 ? void 0 : _task$part.type);
      $("#mp3MondaiName").val((_task$mondai = task.mondai) === null || _task$mondai === void 0 ? void 0 : _task$mondai.title);
      $("#mp3Skill").val(task.skill);
      var mp3 = JSON.parse(task.value);
      $("#mp3_descr").val(mp3.name);
      $("#mp3_link").val(mp3.link);
      task.show == 1 ? $("#mp3_show_on").prop("checked", true) : $("#mp3_show_off").prop("checked", true);
      break;
    case 3:
      //trac nghiem
      manipulation("choice_edit");
      $("#choice_id").val(task.id);
      $("#part").val((_task$part2 = task.part) === null || _task$part2 === void 0 ? void 0 : _task$part2.type);
      $("#mondaiName").val((_task$mondai2 = task.mondai) === null || _task$mondai2 === void 0 ? void 0 : _task$mondai2.title);
      $("#skill").val(task.skill);
      task_choice.setData(task.value);
      suggest.setData(task.suggest);
      explain.setData(task.explain);
      $("#audio_preview").empty();
      if (task.explain_mp3 && task.explain_mp3.length > 0) {
        var contentAudio = "<audio controls controlslist=\"nodownload\">\n          <source id=\"source_audio\" src=\"/cdn/audio/".concat(task.explain_mp3, "\" type=\"audio/mp3\">\n        </audio>");
        $("#audio_preview").append(contentAudio);
      }
      $("#choice_grade").val(task.grade);
      task.type_ld == null ? $("#typeld").val("") : $("#typeld").val(task.type_ld);
      task.show == 1 ? $("#choice_show_on").prop("checked", true) : $("#choice_show_off").prop("checked", true);
      $.ajax({
        type: "post",
        url: "/backend/lesson/task/answer",
        data: {
          id: task.id
        },
        success: function success(data) {
          if (data.errors) {
            return;
          }
          $.each(data, function (index, item) {
            add_answer_option("edit");
            // TODO: Update later KAN-14
            var editor = CKEDITOR.replace("answer_content_" + (num_answer - 1), configCkEditor).setData(item.value);
            CKFinder.setupCKEditor(editor);
            $("#answer_grade_" + (num_answer - 1)).val(item.grade);
            // $("#answer_content_" + (num_answer - 1)).val(item.value);
          });
        }
      });
      break;
    case 1:
      //noi dung
      manipulation("content_edit");
      $("#content_id").val(task.id);
      $("#contentMondaiPart").val((_task$part3 = task.part) === null || _task$part3 === void 0 ? void 0 : _task$part3.type);
      $("#contentMondaiName").val((_task$mondai3 = task.mondai) === null || _task$mondai3 === void 0 ? void 0 : _task$mondai3.title);
      $("#contentSkill").val(task.skill);
      $("#skill").val(task.skill);
      task_content.setData(task.value);
      task.show == 1 ? $("#content_show_on").prop("checked", true) : $("#content_show_off").prop("checked", true);
      task.type_ld == null ? $("#typeld").val("") : $("#typeld").val(task.type_ld);
      break;
    case 2:
      //video
      manipulation("video_edit");
      $("#video_id").val(task.id);
      $("#link_video").val(task.video_name);
      $("#title_video").val(task.video_title);
      task.show == 1 ? $("#video_show_on").prop("checked", true) : $("#video_show_off").prop("checked", true);
      vueInstance.$data.videoFull = task.video_full;
      break;
    case 4:
      // kết quả
      manipulation("result_edit");
      $("#result_id").val(task.id);
      $.each(JSON.parse(task.value), function (index, item) {
        number_result = item.pos;
        add_result("edit");
        var editor = CKEDITOR.replace("result_content_" + item.pos, configCkEditor).setData(item.content);
        CKFinder.setupCKEditor(editor);
        $("#result_title_" + item.pos).val(item.name);
      });
      task.show == 1 ? $("#result_show_on").prop("checked", true) : $("#result_show_off").prop("checked", true);
      break;
    case 7:
      $("#body_audio").data("type", "edit");
      $("#task_submit").data("type", "audio");
      manipulation("audio_edit");
      var tmp = JSON.parse(task.value);
      $("#audio_filename").val(tmp.link);
      $("#audio_text").val(tmp.name);
      $("#audio_filename").data("id", task.id);
      tmp.type == 1 ? $("#is_kaiwa2").prop("checked", true) : $("#is_kaiwa2").prop("checked", false);
      task.show == 1 ? $("#audio_show_on").prop("checked", true) : $("#audio_show_off").prop("checked", true);
      break;
    case 8:
      $("#body_pdf").data("type", "edit");
      $("#task_submit").data("type", "pdf_upload");
      $("#progress-pdf-upload").hide();
      manipulation("pdf_edit");
      $("#pdfFilename").val(task.value);
      $("#pdfFilename").data("id", task.id);
      $("#pdfFilename").data("old-file", task.value);
      task.show == 1 ? $("#pdf_show_on").prop("checked", true) : $("#pdf_show_off").prop("checked", true);
      break;
    case 9:
      break;
    case 13:
      var value = JSON.parse(task.value);
      console.log("value: ", value);
      ckeditorConjunction = [];
      resetCkeditorConjunction();
      $("#title_question_conjunction").val(value.title_question);
      $("#type_question_conjunction").val(value.type_question);
      $("#grade_question_conjunction").val(task.grade);
      var $radios = $("input:radio[name=conjunction_show]");
      $radios.filter("[value=".concat(task.show, "]")).prop("checked", true);
      // $("#lesson_explain_conjunction").val(value.explain);
      // $("#lesson_suggest_conjunction").val(value.suggest);

      $(".question_item_content").empty();
      Object.keys(value.question).forEach(function (key) {
        console.log(key, value.question[key]);
        number_conjunction = key;
        add_conjunction("edit");
        CKEDITOR.instances["lesson[item][".concat(number_conjunction, "][value]")].setData(value.question[key].value);
        $("input[name='lesson[item][".concat(number_conjunction, "][type]'][value=").concat(value.question[key].type, "]")).prop("checked", true);
      });
      $(".flc_img_preview").empty();
      if (value.img !== null && value.img !== "") {
        var contentImg = "<img src=\"/cdn/lesson/default/".concat(value.img, "\" width=\"64px\" class=\"img-rounded\">");
        $(".flc_img_preview").append(contentImg);
      }
      $("#lesson_component_id").val(task.id);
      $("#audio_preview_conjunction").empty();
      console.log("value______________: ", value, value.audio != "", value.audio != null, value.audio.length > 0);
      if (value.audio != "" && value.audio != null && value.audio.length > 0) {
        var _contentAudio = "<audio controls controlslist=\"nodownload\">\n                                    <source id=\"source_audio\" src=\"/cdn/audio/".concat(value.audio, "\"\n                                            type=\"audio/mp3\">\n                                </audio>");
        console.log("contentAudio: ", _contentAudio);
        $("#audio_preview_conjunction").append(_contentAudio);
      }
      $("#audio_preview_explain_mp3_conjunction").empty();
      console.log("value______________: ", value, value.audio != "", value.audio != null, value.audio.length > 0);
      if (task.explain_mp3 != "" && task.explain_mp3 != null && task.explain_mp3.length > 0) {
        var _contentAudio2 = "<audio controls controlslist=\"nodownload\">\n                                    <source id=\"source_audio\" src=\"/cdn/audio/".concat(task.explain_mp3, "\"\n                                            type=\"audio/mp3\">\n                                </audio>");
        console.log("contentAudio: ", _contentAudio2);
        $("#audio_preview_explain_mp3_conjunction").append(_contentAudio2);
      }
      $("#grade_question_conjunction").val(task.grade);
      $("#text_vi_conjunction").empty();
      console.log(11231231231231231231231231, value);
      // if (value.text_vi !== null && value.text_vi !== "")  {
      $("#text_vi_conjunction").val(value.text_vi);
      // }

      $("#text_vi_conjunction").empty();
      console.log(11231231231231231231231231, value);
      // if (value.text_vi !== null && value.text_vi !== "")  {
      $("#text_vi_conjunction").val(value.text_vi);
      $("#conjunctionPart").val((_task$part4 = task.part) === null || _task$part4 === void 0 ? void 0 : _task$part4.type);
      $("#conjunctionMondaiName").val((_task$mondai4 = task.mondai) === null || _task$mondai4 === void 0 ? void 0 : _task$mondai4.title);
      $("#conjunctionSkill").val(task.skill);
      // }

      conjunction_suggest.setData(value.suggest);
      conjunction_explain.setData(value.explain);
      console.log("value.suggest: ", value.suggest);
      // CKEDITOR.instances[`lesson_suggest_conjunction`].setData(value.suggest);
      manipulation("conjunction_edit");
      break;
    case 14:
      resetFormSentenceJumble("edit");
      manipulation("sentence_jumble_edit");
      fillDataSentenceJumble(task);
      break;
    case 15:
      resetFormWordPairMatching("edit");
      fillDataWordPairMatching(task);
      manipulation("word_pair_matching_edit");
      break;
    case 16:
      console.log("task: ", task);
      resetFormSpeaking("edit");
      fillDataSpeaking(task);
      manipulation("speaking_edit");
      break;
    default:
      //tra loi cau hoi
      manipulation("faq_edit");
      $("#faq_id").val(task.id);
      $.each(JSON.parse(task.value), function (index, item) {
        add_faq("edit");
        var editor = CKEDITOR.replace("faq_question_" + (number_faq - 1), configCkEditor).setData(item.question);
        CKFinder.setupCKEditor(editor);
        $("#faq_grade_" + (number_faq - 1)).val(item.grade);
        $("#faq_content_" + (number_faq - 1)).val(item.answer);
      });
      $("#faq_grade").val(task.grade);
      task.show == 1 ? $("#faq_show_on").prop("checked", true) : $("#faq_show_off").prop("checked", true);
      break;
  }
};
$(document).on("click", "#btn-choose-video", function () {
  window.open("/backend/video?type=choose", "Chọn video");
});
__webpack_require__.g.setUrlVideo = function (url) {
  $("#link_video").val(url);
};

//Thêm dữ liệu video khi nhấn thêm mới ở dialog
$("#new-footer").on("click", ".video_add", function () {
  var url = "/backend/lesson/task/video/add";
  var data = _defineProperty(_defineProperty({
    _token: $("input[name=_token]").val(),
    link: $("#link_video").val(),
    title: $("#title_video").val(),
    sort: $("#video_sort").val(),
    type: 2,
    lesson_id: $("#lesson_id").val(),
    show: $("input[name=video_show]:checked").val()
  }, "sort", $("#task_table tbody tr").length + 1), "video_full", vueInstance.$data.videoFull);
  axios.post(url, data).then(function (res) {
    if (res.data.errors) {
      if (Object.keys(res.data.errors).length > 0) {
        $("#pageModal").modal("show");
        $(".global_error").removeClass("hidden");
        $(".global_error").text("Dữ liệu còn trống !");
      }
    } else {
      $("#pageModal").modal("hide");
      $("#task_area").html(res.data);
      sortTable("body_task", 2, 1, "/backend/lesson/task/sort", "sort", "task_table");
    }
  });
});
//Sửa dữ liệu video khi nhấn edit
$("#new-footer").on("click", ".video_edit", function () {
  var url = "/backend/lesson/task/video/edit";
  var data = {
    _token: $("input[name=_token]").val(),
    id: $("#video_id").val(),
    link: $("#link_video").val(),
    title: $("#title_video").val(),
    sort: $("#video_sort").val(),
    lesson_id: $("#lesson_id").val(),
    show: $("input[name=video_show]:checked").val(),
    old_video_name: originalVideoName,
    video_full: vueInstance.$data.videoFull
  };
  axios.post(url, data).then(function (res) {
    if (res.data.errors) {
      if (Object.keys(res.data.errors).length > 0) {
        $("#pageModal").modal("show");
        $(".global_error").removeClass("hidden");
        $(".global_error").text("Dữ liệu còn trống !");
      }
    } else {
      $("#pageModal").modal("hide");
      $("#task_area").html(res.data);
      sortTable("body_task", 2, 1, "/backend/lesson/task/sort", "sort", "task_table");
    }
  });

  // console.log(data);
  // $.ajax({
  //     type: 'post',
  //     url: '/backend/lesson/task/video/edit',
  //     beforeSend: function() {
  //         $("#task_submit").button('loading');
  //     },
  //     complete: function() {
  //         $("#task_submit").button('reset');
  //     },
  //     success: function(data) {
  //         if (data.errors){
  //             if(Object.keys(data.errors).length > 0){
  //                 $('#pageModal').modal('show');
  //                 $('.global_error').removeClass('hidden');
  //                 $('.global_error').text("Dữ liệu còn trống !");
  //             }
  //         } else {
  //             $('#pageModal').modal('hide');
  //             $('#task_area').html(data);
  //             sortTable('body_task', 2, 1 , '/backend/lesson/task/sort', 'sort', 'task_table');
  //         }
  //     }
  // });
});
$(document).on("click", ".delete-task", function () {
  var id = $(this).data("info");
  $(".deleteBtn").addClass("task_delete");
  $(".task_id").text(id);
  $("#lessModal").modal("show");
});
$("#delete-footer").on("click", ".task_delete", function () {
  $.ajax({
    type: "post",
    url: "/backend/lesson/task/video/delete",
    data: {
      _token: $("input[name=_token]").val(),
      id: $(".task_id").text()
    },
    beforeSend: function beforeSend() {
      $(".preloader").show();
    },
    complete: function complete() {
      $(".preloader").hide();
    },
    success: function success(data) {
      $(".item" + $(".task_id").text()).remove();
    }
  });
});
$(document).on("click", "#btn-add-mp3-link", function () {
  $("#audio_file_mp3_type").trigger("click");
});
$("#audio_file_mp3_type").on("change", function () {
  var file = this.files[0];
  var reader = new FileReader();
  $("#mp3_link").val("");
  reader.onload = function (e) {
    uploadAudio($("#audio_file_mp3_type")[0].files[0], function (res) {
      $("#mp3_link").val(res.data.file_path);
    });
  };
  if (!file) return;
  reader.readAsDataURL(file);
});
__webpack_require__.g.uploadAudio = function (file, callback) {
  $.ajax({
    type: "get",
    url: "/backend/video/api/get-token",
    success: function success(token) {
      var formData = new FormData();
      formData.append("token", token);
      formData.append("file_upload", file);
      $.ajax({
        type: "post",
        url: videoBaseURL + "/api/admin/upload-audio",
        processData: false,
        contentType: false,
        data: formData,
        success: function success(data) {
          if (callback) {
            callback(data);
          }
        },
        error: function error(data) {
          alert("Lỗi upload file");
        }
      });
    }
  });
};
$("#new-footer").on("click", ".mp3_add", /*#__PURE__*/_asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee() {
  return _regeneratorRuntime().wrap(function _callee$(_context) {
    while (1) switch (_context.prev = _context.next) {
      case 0:
        $.ajax({
          type: "post",
          url: "/backend/lesson/task/mp3/add",
          data: {
            _token: $("input[name=_token]").val(),
            description: $("#mp3_descr").val(),
            type: 5,
            link: $("#mp3_link").val(),
            lesson_id: $("#lesson_id").val(),
            show: $("input[name=mp3_show]:checked").val(),
            sort: $("#task_table tbody tr").length + 1,
            partId: $("#partId").val(),
            part: $("#mp3MondaiPart").val(),
            mondaiId: $("#mp3MondaiId").val(),
            mondaiName: $("#mp3MondaiName").val(),
            skill: $("#mp3Skill").val()
          },
          success: function success(data) {
            if (data.errors) {
              if (Object.keys(data.errors).length > 0) {
                $("#pageModal").modal("show");
                $(".global_error").removeClass("hidden");
                $(".global_error").text("Dữ liệu còn trống !");
              }
            } else {
              $("#pageModal").modal("hide");
              $("#task_area").html(data);
              sortTable("body_task", 2, 1, "/backend/lesson/task/sort", "sort", "task_table");
            }
          }
        });
      case 1:
      case "end":
        return _context.stop();
    }
  }, _callee);
})));
$("#new-footer").on("click", ".mp3_edit", function () {
  $.ajax({
    type: "post",
    url: "/backend/lesson/task/mp3/edit",
    data: {
      _token: $("input[name=_token]").val(),
      id: $("#mp3_id").val(),
      description: $("#mp3_descr").val(),
      sort: $("#mp3_sort").val(),
      link: $("#mp3_link").val(),
      lesson_id: $("#lesson_id").val(),
      show: $("input[name=mp3_show]:checked").val(),
      partId: $("#partId").val(),
      part: $("#mp3MondaiPart").val(),
      mondaiId: $("#mp3MondaiId").val(),
      mondaiName: $("#mp3MondaiName").val(),
      skill: $("#mp3Skill").val()
    },
    success: function success(data) {
      if (data.errors) {
        if (Object.keys(data.errors).length > 0) {
          $("#pageModal").modal("show");
          $(".global_error").removeClass("hidden");
          $(".global_error").text("Dữ liệu còn trống !");
        }
      } else {
        $("#pageModal").modal("hide");
        $("#task_area").html(data);
        sortTable("body_task", 2, 1, "/backend/lesson/task/sort", "sort", "task_table");
      }
    }
  });
});

//noi dung
$("#new-footer").on("click", ".content_add", function () {
  $.ajax({
    type: "post",
    url: "/backend/lesson/task/content/add",
    data: _defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty({
      _token: $("input[name=_token]").val(),
      content: task_content.getData(),
      sort: $("#content_sort").val(),
      type: 1,
      type_ld: $("#typeld").val(),
      lesson_id: $("#lesson_id").val(),
      show: $("input[name=content_show]:checked").val()
    }, "sort", $("#task_table tbody tr").length + 1), "partId", $("#partId").val()), "part", $("#contentMondaiPart").val()), "mondaiId", $("#mondaiId").val()), "mondaiName", $("#contentMondaiName").val()), "skill", $("#contentSkill").val()),
    success: function success(data) {
      if (data.errors) {
        if (Object.keys(data.errors).length > 0) {
          $("#pageModal").modal("show");
          $(".global_error").removeClass("hidden");
          $(".global_error").text("Dữ liệu còn trống !");
        }
      } else {
        $("#pageModal").modal("hide");
        $("#task_area").html(data);
        sortTable("body_task", 2, 1, "/backend/lesson/task/sort", "sort", "task_table");
      }
    }
  });
});
$("#new-footer").on("click", ".content_edit", function () {
  $.ajax({
    type: "post",
    url: "/backend/lesson/task/content/edit",
    data: {
      _token: $("input[name=_token]").val(),
      id: $("#content_id").val(),
      content: task_content.getData(),
      type_ld: $("#typeld").val(),
      sort: $("#content_sort").val(),
      lesson_id: $("#lesson_id").val(),
      show: $("input[name=content_show]:checked").val(),
      partId: $("#partId").val(),
      part: $("#contentMondaiPart").val(),
      mondaiId: $("#contentMondaiId").val(),
      mondaiName: $("#contentMondaiName").val(),
      skill: $("#contentSkill").val()
    },
    success: function success(data) {
      if (data.errors) {
        if (Object.keys(data.errors).length > 0) {
          $("#pageModal").modal("show");
          $(".global_error").removeClass("hidden");
          $(".global_error").text("Dữ liệu còn trống !");
        }
      } else {
        $("#pageModal").modal("hide");
        $("#task_area").html(data);
        sortTable("body_task", 2, 1, "/backend/lesson/task/sort", "sort", "task_table");
      }
    }
  });
});
$(document).on("click", "#btn-add-mp3", function () {
  $("#audio_file_multi_choice").trigger("click");
});
$("#audio_file_multi_choice").on("change", function () {
  var file = this.files[0];
  var reader = new FileReader();
  reader.onload = function (e) {
    var content = CKEDITOR.instances["task_choice"].getData();
    uploadAudio(file, function (res) {
      var mp3Prefix = "{! ".concat(res.data.file_path, " !}");
      task_choice.setData(content + mp3Prefix);
    });
  };
  if (!file) return;
  reader.readAsDataURL(file);
});

//dữ liệu bài kiểm tra
$("#new-footer").on("click", ".choice_add", function () {
  var content = new FormData($("#form_task")[0]);
  content.append("task_choice", CKEDITOR.instances["task_choice"].getData());
  content.append("suggest", CKEDITOR.instances["suggest"].getData());
  content.append("explain", CKEDITOR.instances["explain"].getData());
  $.each(listCkeditorAnswerOption, function (item, value) {
    content["delete"](value);
    content.append(value, CKEDITOR.instances[value].getData());
  });
  content.append("sort", $("#task_table tbody tr").length + 1);
  $.ajax({
    type: "post",
    url: "/backend/lesson/task/choice/add",
    processData: false,
    contentType: false,
    data: content,
    success: function success(data) {
      if (data.errors) {
        if (Object.keys(data.errors).length > 0) {
          $("#pageModal").modal("show");
          $(".global_error").removeClass("hidden");
          $(".global_error").text("Dữ liệu còn trống !");
        }
      } else {
        $("#audio_preview").empty();
        $("#audio_file_explain").val("");
        $("#pageModal").modal("hide");
        $("#task_area").html(data);
        sortTable("body_task", 2, 1, "/backend/lesson/task/sort", "sort", "task_table");
      }
    }
  });
});
$("#new-footer").on("click", ".choice_edit", function () {
  var content = new FormData($("#form_task")[0]);
  $.each(listCkeditorAnswerOption, function (item, value) {
    content["delete"](value);
    content.append(value, CKEDITOR.instances[value].getData());
  });
  content.append("task_choice", CKEDITOR.instances["task_choice"].getData());
  content.append("suggest", CKEDITOR.instances["suggest"].getData());
  content.append("explain", CKEDITOR.instances["explain"].getData());
  $.ajax({
    type: "post",
    url: "/backend/lesson/task/choice/edit",
    processData: false,
    contentType: false,
    data: content,
    success: function success(data) {
      // console.log("check data", data);
      if (data.errors) {
        if (Object.keys(data.errors).length > 0) {
          $("#pageModal").modal("show");
          $(".global_error").removeClass("hidden");
          $(".global_error").text("Dữ liệu còn trống !");
        }
      } else {
        $("#audio_preview").empty();
        $("#audio_file_explain").val("");
        $("#pageModal").modal("hide");
        $("#task_area").html(data);
        sortTable("body_task", 2, 1, "/backend/lesson/task/sort", "sort", "task_table");
      }
    }
  });
});
$("#new-footer").on("click", ".faq_add", function () {
  var content = new FormData($("#form_task")[0]);
  $.each(listCkeditor, function (item, value) {
    content.append(value, CKEDITOR.instances[value].getData());
  });
  content.append("sort", $("#task_table tbody tr").length + 1);
  $.ajax({
    type: "post",
    url: "/backend/lesson/task/faq/add",
    processData: false,
    contentType: false,
    data: content,
    success: function success(data) {
      if (data == "error") {
        $("#pageModal").modal("show");
        $(".global_error").removeClass("hidden");
        $(".global_error").text("Dữ liệu còn trống !");
      } else {
        $("#pageModal").modal("hide");
        $("#task_area").html(data);
        sortTable("body_task", 2, 1, "/backend/lesson/task/sort", "sort", "task_table");
      }
    }
  });
});
$("#new-footer").on("click", ".faq_edit", function () {
  var content = new FormData($("#form_task")[0]);
  $.each(listCkeditor, function (item, value) {
    content.append(value, CKEDITOR.instances[value].getData());
  });
  $.ajax({
    type: "post",
    url: "/backend/lesson/task/faq/edit",
    processData: false,
    contentType: false,
    data: content,
    success: function success(data) {
      if (data == "error") {
        $("#pageModal").modal("show");
        $(".global_error").removeClass("hidden");
        $(".global_error").text("Dữ liệu còn trống !");
      } else {
        $("#pageModal").modal("hide");
        $("#task_area").html(data);
        sortTable("body_task", 2, 1, "/backend/lesson/task/sort", "sort", "task_table");
      }
    }
  });
});
$("#new-footer").on("click", ".result_add", function () {
  var content = new FormData($("#form_task")[0]);
  $.each(ckeditorResult, function (item, value) {
    content.append(value, CKEDITOR.instances[value].getData());
  });
  content.append("sort", $("#task_table tbody tr").length + 1);
  $.ajax({
    type: "post",
    url: "/backend/lesson/task/result/add",
    processData: false,
    contentType: false,
    data: content,
    success: function success(data) {
      if (data == "error") {
        $("#pageModal").modal("show");
        $(".global_error").removeClass("hidden");
        $(".global_error").text("Dữ liệu còn trống !");
      } else {
        $("#pageModal").modal("hide");
        $("#task_area").html(data);
        sortTable("body_task", 2, 1, "/backend/lesson/task/sort", "sort", "task_table");
      }
    }
  });
});
$("#new-footer").on("click", ".result_edit", function () {
  var content = new FormData($("#form_task")[0]);
  $.each(ckeditorResult, function (item, value) {
    content.append(value, CKEDITOR.instances[value].getData());
  });
  content.append("number_result", number_result);
  $.ajax({
    type: "post",
    url: "/backend/lesson/task/result/edit",
    processData: false,
    contentType: false,
    data: content,
    success: function success(data) {
      if (data == "error") {
        $("#pageModal").modal("show");
        $(".global_error").removeClass("hidden");
        $(".global_error").text("Dữ liệu còn trống !");
      } else {
        $("#pageModal").modal("hide");
        $("#task_area").html(data);
        sortTable("body_task", 2, 1, "/backend/lesson/task/sort", "sort", "task_table");
      }
    }
  });
});
$("#new-footer").on("click", ".conjunction_add", function () {
  var content = new FormData($("#form_task")[0]);
  console.log("content: ", content);
  console.log(ckeditorConjunction);
  $.each(ckeditorConjunction, function (item, value) {
    console.log("item: ", item);
    console.log("value: ", value);
    console.log(CKEDITOR.instances[value].getData());
    content.append(value, CKEDITOR.instances[value].getData());
  });
  content.append("number_conjunction", number_conjunction);
  content.append("lesson[explain]", conjunction_explain.getData());
  content.append("lesson[suggest]", conjunction_suggest.getData());
  console.log("content: ", content);
  $.ajax({
    type: "post",
    url: "/backend/lesson/add-conjunction",
    processData: false,
    contentType: false,
    data: content,
    // params: content,
    success: function success(data) {
      console.log("data: ", data);
      if (data.status === "success") {
        $("#pageModal").modal("hide");
        location.reload();
      } else {
        $("#pageModal").modal("show");
        $(".global_error").removeClass("hidden");
        $(".global_error").text("Dữ liệu còn trống !");
      }
    }
  });
});
$("#new-footer").on("click", ".conjunction_edit", function () {
  console.log("vao dy.........");
  var content = new FormData($("#form_task")[0]);
  $.each(ckeditorConjunction, function (item, value) {
    console.log("item: ", item);
    console.log("value: ", value);
    content.append(value, CKEDITOR.instances[value].getData());
  });
  content.append("number_conjunction", number_conjunction);
  content.append("lesson[explain]", conjunction_explain.getData());
  content.append("lesson[suggest]", conjunction_suggest.getData());
  $.ajax({
    type: "post",
    url: "/backend/lesson/edit-conjunction",
    processData: false,
    contentType: false,
    data: content,
    // params: content,
    success: function success(data) {
      console.log("data: ", data);
      if (data.status === "success") {
        $("#pageModal").modal("hide");
        location.reload();
        // $("#task_area").html(data.detail);
        // sortTable(
        //     "body_task",
        //     2,
        //     1,
        //     "/backend/lesson/task/sort",
        //     "sort",
        //     "task_table"
        // );
      } else {
        $("#pageModal").modal("show");
        $(".global_error").removeClass("hidden");
        $(".global_error").text("Dữ liệu còn trống !");
      }
    }
  });
});
__webpack_require__.g.add_conjunction = function (mode) {
  number_conjunction++;
  var result = $(document.createElement("div")).attr("id", "result" + number_conjunction).attr("class", "p-2 w-full flex justify-around items-center border-solid border-[1px] border-[#e4e3e3]");
  result.after().html("<div class=\"max-w-[70%] form-control reloadCKeditor\" style=\"max-width: 70%\" contenteditable=\"true\"\n               id=\"lesson[item][".concat(number_conjunction, "][value]\" data-text=\"dap an\"></div>\n          <label class=\"tcb-inline\">\n              <input type=\"radio\" name=\"lesson[item][").concat(number_conjunction, "][type]\" value=\"default\" checked>\n              <span class=\"labels\"> Th\xE0nh ph\u1EA7n</span>\n          </label>\n          <label class=\"tcb-inline\">\n              <input type=\"radio\" name=\"lesson[item][").concat(number_conjunction, "][type]\" value=\"question\">\n              <span class=\"labels\"> \u0110\xE1p \xE1n </span>\n          </label>"));
  result.appendTo(".question_item_content");
  CKEDITOR.inline("lesson[item][".concat(number_conjunction, "][value]"), {
    extraPlugins: "furigana,colorbutton",
    allowedContent: {
      $1: {
        // Use the ability to specify elements as an object.
        elements: CKEDITOR.dtd,
        attributes: true,
        styles: true,
        classes: true
      }
    },
    disallowedContent: "script;style; *[on*]"
  });
  ckeditorConjunction.push("lesson[item][".concat(number_conjunction, "][value]"));
};
$("#new-footer").on("click", ".sentence_jumble_add", function () {
  var content = new FormData($("#form_task")[0]);
  console.log("content: ", content);
  console.log(ckeditorSentenceJumble);
  $.each(ckeditorSentenceJumble, function (item, value) {
    console.log("item: ", item);
    console.log("value: ", value);
    console.log(CKEDITOR.instances[value].getData());
    content.append(value, CKEDITOR.instances[value].getData());
  });
  content.append("number_sentence_jumble", number_sentence_jumble);
  content.append("lesson[explain]", sentence_jumble_explain.getData());
  content.append("lesson[suggest]", sentence_jumble_suggest.getData());
  content.append("lesson[title_question]", sentence_jumble_question.getData());
  console.log("content: ", content);
  $.ajax({
    type: "post",
    url: "/backend/lesson/add-sentence-jumble",
    processData: false,
    contentType: false,
    data: content,
    // params: content,
    success: function success(data) {
      console.log("data: ", data);
      if (data.status === "success") {
        $("#pageModal").modal("hide");
        location.reload();
      } else {
        $("#pageModal").modal("show");
        $(".global_error").removeClass("hidden");
        $(".global_error").text("Dữ liệu còn trống !");
      }
    }
  });
});
$("#new-footer").on("click", ".sentence_jumble_edit", function () {
  var content = new FormData($("#form_task")[0]);
  console.log("content: ", content);
  console.log(ckeditorSentenceJumble);
  $.each(ckeditorSentenceJumble, function (item, value) {
    console.log("item: ", item);
    console.log("value: ", value);
    console.log(CKEDITOR.instances[value].getData());
    content.append(value, CKEDITOR.instances[value].getData());
  });
  content.append("number_sentence_jumble", number_sentence_jumble);
  content.append("lesson[explain]", sentence_jumble_explain.getData());
  content.append("lesson[suggest]", sentence_jumble_suggest.getData());
  content.append("lesson[title_question]", sentence_jumble_question.getData());
  console.log("content: ", content);
  $.ajax({
    type: "post",
    url: "/backend/lesson/edit-sentence-jumble",
    processData: false,
    contentType: false,
    data: content,
    // params: content,
    success: function success(data) {
      console.log("data: ", data);
      if (data.status === "success") {
        $("#pageModal").modal("hide");
        location.reload();
      } else {
        $("#pageModal").modal("show");
        $(".global_error").removeClass("hidden");
        $(".global_error").text("Dữ liệu còn trống !");
      }
    }
  });
});
__webpack_require__.g.add_sentence_jumble = function (mode) {
  number_sentence_jumble++;
  var result = "";
  if (number_sentence_jumble === 1) {
    result = "<div class=\"item_component_sentence_jumble flex items-center\" data-number=\"".concat(number_sentence_jumble, "\" id=\"item_component_sentence_jumble_").concat(number_sentence_jumble, "\">\n                        <div  id=\"lesson[value][").concat(number_sentence_jumble, "]\" contenteditable=\"true\"  class=\"form-control reloadCKeditor min-w-[100px]\" name=\"lesson[value][").concat(number_sentence_jumble, "]\"  placeholder=\"Th\xE0nh ph\u1EA7n ").concat(number_sentence_jumble, "\"></div>\n                    </div>");
  } else {
    result = "<div class=\"item_component_sentence_jumble flex items-center\" data-number=\"".concat(number_sentence_jumble, "\" id=\"item_component_sentence_jumble_").concat(number_sentence_jumble, "\">\n                        <p class=\"mx-2\"> / </p>\n                        <div id=\"lesson[value][").concat(number_sentence_jumble, "]\" contenteditable=\"true\"  class=\"form-control reloadCKeditor min-w-[100px]\" name=\"lesson[value][").concat(number_sentence_jumble, "]\"  placeholder=\"Th\xE0nh ph\u1EA7n ").concat(number_sentence_jumble, "\"></div>\n                    </div>");
  }
  $(".sentence_jumble_item_content").append(result);
  CKEDITOR.inline("lesson[value][".concat(number_sentence_jumble, "]"), {
    extraPlugins: "furigana,colorbutton"
    // allowedContent: {
    //   $1: {
    //     // Use the ability to specify elements as an object.
    //     elements: CKEDITOR.dtd,
    //     attributes: true,
    //     styles: true,
    //     classes: true,
    //   },
    // },
    // disallowedContent: 'script;style; *[on*]'
  });
  ckeditorSentenceJumble.push("lesson[value][".concat(number_sentence_jumble, "]"));
};
__webpack_require__.g.add_item_sentence_jumble_false = function () {
  number_item_sentence_jumble_false++;
  var result = "";
  if (number_item_sentence_jumble_false === 1) {
    result = "<div class=\"item_component_sentence_jumble_false flex items-center\" data-number=\"".concat(number_item_sentence_jumble_false, "\">\n                            <div class=\"form-control reloadCKeditor min-w-[100px]\" id=\"lesson[item_false][").concat(number_item_sentence_jumble_false, "]\" contenteditable=\"true\" name=\"lesson[item_false][").concat(number_item_sentence_jumble_false, "]\" placeholder=\"Th\xE0nh ph\u1EA7n sai ").concat(number_item_sentence_jumble_false, "\"></div>\n                        </div>");
  } else {
    result = "<div class=\"item_component_sentence_jumble_false flex items-center\" data-number=\"".concat(number_item_sentence_jumble_false, "\">\n                            <p class=\"mx-2\"> , </p>\n                            <div class=\"form-control reloadCKeditor min-w-[100px]\" id=\"lesson[item_false][").concat(number_item_sentence_jumble_false, "]\" contenteditable=\"true\" name=\"lesson[item_false][").concat(number_item_sentence_jumble_false, "]\" placeholder=\"Th\xE0nh ph\u1EA7n sai ").concat(number_item_sentence_jumble_false, "\"></div>\n                        </div>");
  }
  $(".sentence_jumble_item_content_false").append(result);
  CKEDITOR.inline("lesson[item_false][".concat(number_item_sentence_jumble_false, "]"), {
    extraPlugins: "furigana,colorbutton"
    // allowedContent: {
    //   $1: {
    //     // Use the ability to specify elements as an object.
    //     elements: CKEDITOR.dtd,
    //     attributes: true,
    //     styles: true,
    //     classes: true,
    //   },
    // },
    // disallowedContent: 'script;style; *[on*]'
  });
  ckeditorSentenceJumble.push("lesson[item_false][".concat(number_item_sentence_jumble_false, "]"));
};
__webpack_require__.g.resetCkeditorConjunction = function () {
  for (var instanceName in CKEDITOR.instances) {
    console.log("instanceName: ", instanceName);
    if (CKEDITOR.instances.hasOwnProperty(instanceName) && instanceName.includes("lesson[item]")) {
      console.log("instanceName destroy: ", instanceName);
      CKEDITOR.instances[instanceName].destroy(true);
    } else {
      if (!ckeditorConjunction.includes(instanceName)) {
        ckeditorConjunction.push(instanceName);
      }
    }
  }
};
__webpack_require__.g.fillDataSentenceJumble = function (task) {
  var _task$part5, _task$mondai5;
  // reset form default
  // resetCkeditorSentenceJumble();
  var value = JSON.parse(task.value);
  sentence_jumble_question.setData(value.title_question);
  $("#type_question_sentence_jumble").val(value.type_question);
  var $radios = $("input:radio[name=sentence_jumble_show]");
  $radios.filter("[value=".concat(task.show, "]")).prop("checked", true);
  $("#grade_question_sentence_jumble").val(task.grade);
  $(".flc_img_preview").empty();
  var contentImg = "<img src=\"/cdn/lesson/default/".concat(value.img, "\" width=\"64px\" class=\"img-rounded\">");
  $(".flc_img_preview").append(contentImg);
  $("#audio_preview_sentence_jumble").empty();
  if (value.audio != "" && value.audio != null && value.audio.length > 0) {
    var contentAudio = "<audio controls controlslist=\"nodownload\">\n                                    <source id=\"source_audio\" src=\"/cdn/audio/".concat(value.audio, "\"\n                                            type=\"audio/mp3\">\n                                </audio>");
    $("#audio_preview_sentence_jumble").append(contentAudio);
  }
  $("#audio_preview_explain_mp3_sentence_jumble").empty();
  if (task.explain_mp3 != "" && task.explain_mp3 != null && task.explain_mp3.length > 0) {
    var _contentAudio3 = "<audio controls controlslist=\"nodownload\">\n                                    <source id=\"source_audio\" src=\"/cdn/audio/".concat(task.explain_mp3, "\"\n                                            type=\"audio/mp3\">\n                                </audio>");
    $("#audio_preview_explain_mp3_sentence_jumble").append(_contentAudio3);
  }
  sentence_jumble_suggest.setData(value.suggest);
  sentence_jumble_explain.setData(value.explain);
  ckeditorSentenceJumble = [];
  $("#lesson_component_id").val(task.id);
  number_sentence_jumble = 0;
  Object.keys(value.question).forEach(function (key) {
    add_sentence_jumble("edit");
    CKEDITOR.instances["lesson[value][".concat(number_sentence_jumble, "]")].setData(value.question[key].value);
  });
  $(".sentence_jumble_item_content_false").empty();
  number_item_sentence_jumble_false = 0;
  Object.keys(value.item_false).forEach(function (key) {
    add_item_sentence_jumble_false("edit");
    CKEDITOR.instances["lesson[item_false][".concat(number_item_sentence_jumble_false, "]")].setData(value.item_false[key].value);
  });
  $("#sentencePart").val((_task$part5 = task.part) === null || _task$part5 === void 0 ? void 0 : _task$part5.type);
  $("#sentenceMondaiName").val((_task$mondai5 = task.mondai) === null || _task$mondai5 === void 0 ? void 0 : _task$mondai5.title);
};
__webpack_require__.g.resetFormSentenceJumble = function (mode) {
  var _lesson_task$mondai5;
  number_sentence_jumble = 0;
  number_item_sentence_jumble_false = 0;
  $(".sentence_jumble_item_content").empty();
  $(".sentence_jumble_item_content_false").empty();
  $("#audio_preview_sentence_jumble").empty();
  $(".flc_img_preview").empty();
  $("#audio_preview_explain_mp3_sentence_jumble").empty();
  $("#grade_question_sentence_jumble").val("");
  $("#sentenceMondaiName").val(lesson_task.length ? (_lesson_task$mondai5 = lesson_task[lesson_task.length - 1].mondai) === null || _lesson_task$mondai5 === void 0 ? void 0 : _lesson_task$mondai5.title : "");
  if (mode == "add") {
    ckeditorSentenceJumble = ["lesson[value][1]", "lesson[value][2]"];
    ckeditorSentenceJumble.forEach(function (item) {
      add_sentence_jumble();
    });
  }
  if (mode == "edit") {
    ckeditorSentenceJumble = [];
  }
};
__webpack_require__.g.resetFormWordPairMatching = function (mode) {
  var _lesson_task$mondai6;
  number_word_pair_matching = 0;
  ckeditorWordPairMatching = [];
  $(".word_pair_matching_item_content").empty();
  $("#grade_question_word_pair_matching").val("");
  $("#lesson_component_id").val("");
  $("#wordPairMondaiName").val(lesson_task.length ? (_lesson_task$mondai6 = lesson_task[lesson_task.length - 1].mondai) === null || _lesson_task$mondai6 === void 0 ? void 0 : _lesson_task$mondai6.title : "");
  if (mode == "add") {
    for (var i = 1; i < 3; i++) {
      add_word_pair_matching();
    }
  }
  console.log("reset f\u1ECFm word pair matching");
};
__webpack_require__.g.add_word_pair_matching = function () {
  number_word_pair_matching++;
  var html = "<div class=\"word_pair_matching_item w-full mb-2\" data-number=\"".concat(number_word_pair_matching, "\">\n                        <div class=\"flex items-center justify-between\">\n                            <div class=\"w-3/12 form-control reloadCKeditor min-w-[100px]\" id=\"lesson[value][").concat(number_word_pair_matching, "][left]\" contenteditable=\"true\" name=\"lesson[value][").concat(number_word_pair_matching, "][left]\" placeholder=\"Th\xE0nh ph\u1EA7n 1\"></div>\n                            <div class=\"w-3/12 form-control reloadCKeditor min-w-[100px]\" id=\"lesson[value][").concat(number_word_pair_matching, "][right]\" contenteditable=\"true\" name=\"lesson[value][").concat(number_word_pair_matching, "][right]\" placeholder=\"Th\xE0nh ph\u1EA7n 1\"></div>\n                            <div class=\"btn_word_pair_matching_item_del_content cursor-pointer text-red underline italic font-bold\" data-number=\"").concat(number_word_pair_matching, "\">\n                                X\xF3a\n                            </div>\n                        </div>\n                    </div>");
  $(".word_pair_matching_item_content").append(html);
  console.log("add_word_pair_matching");
  CKEDITOR.inline("lesson[value][".concat(number_word_pair_matching, "][left]"), {
    extraPlugins: "furigana,colorbutton"
    // allowedContent: {
    //   $1: {
    //     // Use the ability to specify elements as an object.
    //     elements: CKEDITOR.dtd,
    //     attributes: true,
    //     styles: true,
    //     classes: true,
    //   },
    // },
    // disallowedContent: 'script;style; *[on*]'
  });
  ckeditorWordPairMatching.push("lesson[value][".concat(number_word_pair_matching, "][left]"));
  CKEDITOR.inline("lesson[value][".concat(number_word_pair_matching, "][right]"), {
    extraPlugins: "furigana,colorbutton"
    // allowedContent: {
    //   $1: {
    //     // Use the ability to specify elements as an object.
    //     elements: CKEDITOR.dtd,
    //     attributes: true,
    //     styles: true,
    //     classes: true,
    //   },
    // },
    // disallowedContent: 'script;style; *[on*]'
  });
  ckeditorWordPairMatching.push("lesson[value][".concat(number_word_pair_matching, "][right]"));
};
__webpack_require__.g.fillDataWordPairMatching = function (task) {
  var _task$part6, _task$mondai6;
  console.log("task: ", task);
  var value = JSON.parse(task.value);
  console.log("value: ", value);
  $("#type_question_word_pair_matching").val(value.type_question);
  var $radios = $("input:radio[name=word_pair_matching_show]");
  $radios.filter("[value=".concat(task.show, "]")).prop("checked", true);
  $("#grade_question_word_pair_matching").val(task.grade);
  $("#audio_preview_explain_mp3_word_pair_matching").empty();
  if (task.explain_mp3 != "" && task.explain_mp3 != null && task.explain_mp3.length > 0) {
    var contentAudio = "<audio controls controlslist=\"nodownload\">\n                                    <source id=\"source_audio\" src=\"/cdn/audio/".concat(task.explain_mp3, "\"\n                                            type=\"audio/mp3\">\n                                </audio>");
    $("#audio_preview_explain_mp3_word_pair_matching").append(contentAudio);
  }
  word_pair_matching_explain.setData(value.explain);
  ckeditorWordPairMatching = [];
  $("#lesson_component_id").val(task.id);
  number_word_pair_matching = 0;
  $(".word_pair_matching_item_content").empty();
  $("#wordPairPart").val((_task$part6 = task.part) === null || _task$part6 === void 0 ? void 0 : _task$part6.type);
  $("#wordPairMondaiName").val((_task$mondai6 = task.mondai) === null || _task$mondai6 === void 0 ? void 0 : _task$mondai6.title);
  if (value.question.left) {
    for (var i = 0; i < value.question.left.length; i++) {
      add_word_pair_matching("edit");
      console.log("i: ", i);
      console.log(value.question.left[i]);
      console.log("number_item_sentence_jumble_false: ", number_word_pair_matching);
      CKEDITOR.instances["lesson[value][".concat(number_word_pair_matching, "][left]")].setData(value.question.left[i].value);
      CKEDITOR.instances["lesson[value][".concat(number_word_pair_matching, "][right]")].setData(value.question.right[i].value);
    }
  }
};
__webpack_require__.g.resetFormSpeaking = function (mode) {
  $("#grade_question_speaking").val("");
  lesson_question_speaking.setData("");
  $(".flc_img_preview").empty();
  $("#audio_preview_speaking_speed_default").empty();
  $("#audio_preview_speaking_speed_slow").empty();
  $("#input_image_speaking").val("");
  $("#input_audio_speaking_speed_default").val("");
  $("#input_audio_speaking_speed_slow").val("");
  console.log("mode: ", mode);
};
__webpack_require__.g.fillDataSpeaking = function (task) {
  console.log("task: ", task);
  var value = JSON.parse(task.value);
  console.log("value: ", value);
  $("#grade_question_speaking").val(task.grade);
  $("#lesson_component_id").val(task.id);
  setTimeout(function () {
    lesson_question_speaking.setData(value.title_question);
  }, 500);
  var contentImg = "<img src=\"/cdn/lesson/default/".concat(value.img, "\" width=\"64px\" class=\"img-rounded\">");
  $(".flc_img_preview").append(contentImg);
  var contentAudioDefault = "<audio controls controlslist=\"nodownload\">\n                                    <source id=\"source_audio\" src=\"/cdn/audio/".concat(value.audio_speaking_speed_default, "\"\n                                            type=\"audio/mp3\">\n                                </audio>");
  $("#audio_preview_speaking_speed_default").append(contentAudioDefault);
  var contentAudioSlow = "<audio controls controlslist=\"nodownload\">\n                                    <source id=\"source_audio\" src=\"/cdn/audio/".concat(value.audio_speaking_speed_slow, "\"\n                                            type=\"audio/mp3\">\n                                </audio>");
  $("#audio_preview_speaking_speed_slow").append(contentAudioSlow);
};
$("#new-footer").on("click", ".word_pair_matching_add", function () {
  var content = new FormData($("#form_task")[0]);
  console.log("content: ", content);
  console.log(ckeditorWordPairMatching);
  $.each(ckeditorWordPairMatching, function (item, value) {
    console.log("item: ", item);
    console.log("value: ", value);
    console.log(CKEDITOR.instances[value].getData());
    content.append(value, CKEDITOR.instances[value].getData());
  });
  content.append("number_word_pair_matching", number_word_pair_matching);
  content.append("lesson[explain]", word_pair_matching_explain.getData());
  // content.append("lesson[suggest]", word_pair_matching_suggest.getData());
  // content.append("lesson[title_question]", word_pair_matching_question.getData());
  console.log("content: ", content);
  $.ajax({
    type: "post",
    url: "/backend/lesson/add-word-pair-matching",
    processData: false,
    contentType: false,
    data: content,
    // params: content,
    success: function success(data) {
      console.log("data: ", data);
      if (data.status === "success") {
        $("#pageModal").modal("hide");
        location.reload();
      } else {
        $("#pageModal").modal("show");
        $(".global_error").removeClass("hidden");
        $(".global_error").text("Dữ liệu còn trống !");
      }
    }
  });
});
$("#new-footer").on("click", ".word_pair_matching_edit", function () {
  var content = new FormData($("#form_task")[0]);
  console.log("content: ", content);
  console.log(ckeditorWordPairMatching);
  $.each(ckeditorWordPairMatching, function (item, value) {
    console.log("item: ", item);
    console.log("value: ", value);
    console.log(CKEDITOR.instances[value].getData());
    content.append(value, CKEDITOR.instances[value].getData());
  });
  content.append("number_sentence_jumble", number_word_pair_matching);
  content.append("lesson[explain]", word_pair_matching_explain.getData());
  console.log("content: ", content);
  $.ajax({
    type: "post",
    url: "/backend/lesson/edit-word-pair-matching",
    processData: false,
    contentType: false,
    data: content,
    // params: content,
    success: function success(data) {
      console.log("data: ", data);
      if (data.status === "success") {
        $("#pageModal").modal("hide");
        location.reload();
      } else {
        $("#pageModal").modal("show");
        $(".global_error").removeClass("hidden");
        $(".global_error").text("Dữ liệu còn trống !");
      }
    }
  });
});
$("#new-footer").on("click", ".speaking_add", function () {
  var content = new FormData($("#form_task")[0]);
  console.log("content: ", content);
  content.append("lesson[title_question]", lesson_question_speaking.getData());
  console.log("content: ", content);
  $.ajax({
    type: "post",
    url: "/backend/lesson/add-speaking",
    processData: false,
    contentType: false,
    data: content,
    // params: content,
    success: function success(data) {
      console.log("data: ", data);
      if (data.status === "success") {
        $("#pageModal").modal("hide");
        location.reload();
      } else {
        $("#pageModal").modal("show");
        $(".global_error").removeClass("hidden");
        $(".global_error").text("Dữ liệu còn trống !");
      }
    }
  });
});
$("#new-footer").on("click", ".speaking_edit", function () {
  var content = new FormData($("#form_task")[0]);
  content.append("lesson[title_question]", lesson_question_speaking.getData());
  console.log("content: ", content);
  $.ajax({
    type: "post",
    url: "/backend/lesson/edit-speaking",
    processData: false,
    contentType: false,
    data: content,
    // params: content,
    success: function success(data) {
      console.log("data: ", data);
      if (data.status === "success") {
        $("#pageModal").modal("hide");
        location.reload();
      } else {
        $("#pageModal").modal("show");
        $(".global_error").removeClass("hidden");
        $(".global_error").text("Dữ liệu còn trống !");
      }
    }
  });
});
$.fn.modal.Constructor.prototype.enforceFocus = function () {
  modal_this = this;
  $(document).on("focusin.modal", function (e) {
    if (modal_this.$element[0] !== e.target && !modal_this.$element.has(e.target).length && !$(e.target.parentNode).hasClass("cke_dialog_ui_input_select") && !$(e.target.parentNode).hasClass("cke_dialog_ui_input_textarea") && !$(e.target.parentNode).hasClass("cke_dialog_ui_input_text")) {
      modal_this.$element.focus();
    }
  });
};

// Form Pdf
!function ($) {
  "use strict";

  var FormPdf = function FormPdf() {
    this.btnUpload = $("#btn-upload-pdf");
    this.pdfFilename = $("#pdfFilename");
    this.pdfFile = $("#pdfFile");
    this.taskSubmit = $("#task_submit");
    this.bodyElement = $("#body_pdf");
  };
  FormPdf.prototype.readFileName = function (input, inputName) {
    if (input.files && input.files[0]) {
      $(inputName).val(input.files[0].name);
    }
  }, FormPdf.prototype.resetValueUpload = function () {
    var _this = this;
    $(_this.pdfFile).val("");
    $(_this.pdfFilename).val("");
  }, FormPdf.prototype.setEvents = function () {
    var _this = this;
    _this.pdfFilename.on("click", function () {
      _this.resetValueUpload();
      _this.pdfFile.trigger("click");
    });
    _this.pdfFile.on("change", function () {
      _this.readFileName(this, _this.pdfFilename);
    });
    _this.taskSubmit.on("click", function () {
      if ($(this).data("type") == "pdf_upload" && _this.bodyElement.data("type") == "add") {
        _this.addPdfDocument();
      }
      if ($(this).data("type") == "pdf_upload" && _this.bodyElement.data("type") == "edit") {
        _this.editPdfDocument();
      }
    });
    _this.btnUpload.on("click", function () {
      uploadFile("pdfFile", "/backend/lesson/upload-pdf");
    });
  }, FormPdf.prototype.addPdfDocument = function () {
    var _this = this;
    if (_this.pdfFilename.val() == "") {
      $(".global_error").text("Dữ liệu còn trống !");
      $(".global_error").removeClass("hidden");
      return;
    }
    $(".global_error").addClass("hidden");
    var formData = new FormData();
    formData.append("_token", window.Laravel.csrfToken);
    formData.append("filename", _this.pdfFilename.val());
    formData.append("lesson_id", $("#lesson_id").val());
    formData.append("show", $("input[name='pdf_show']:checked").val());
    $.ajax({
      type: "post",
      url: "/backend/lesson/save-pdf-entity",
      async: true,
      dataType: "JSON",
      data: formData,
      cache: false,
      contentType: false,
      processData: false,
      success: function success(response) {
        $("#pageModal").modal("hide");
        $("#task_area").html(response.html);
        sortTable("body_task", 2, 1, "/backend/lesson/task/sort", "sort", "task_table");
      },
      error: function error(_error) {
        $(".global_error").removeClass("hidden");
        $(".global_error").text("Tệp tin không tồn tại!");
      }
    });
  }, FormPdf.prototype.editPdfDocument = function () {
    var _this = this;
    if (_this.pdfFilename.val() == "") {
      $(".global_error").text("Dữ liệu còn trống !");
      $(".global_error").removeClass("hidden");
      return;
    }
    $(".global_error").addClass("hidden");
    var formData = new FormData();
    formData.append("_token", window.Laravel.csrfToken);
    formData.append("id", _this.pdfFilename.data("id"));
    formData.append("filename", _this.pdfFilename.val());
    formData.append("filename_old", _this.pdfFilename.data("old-file"));
    formData.append("lesson_id", $("#lesson_id").val());
    formData.append("show", $("input[name='pdf_show']:checked").val());
    $.ajax({
      type: "post",
      url: "/backend/lesson/save-pdf-entity",
      async: true,
      dataType: "JSON",
      data: formData,
      cache: false,
      contentType: false,
      processData: false,
      success: function success(response) {
        $("#pageModal").modal("hide");
        $("#task_area").html(response.html);
        sortTable("body_task", 2, 1, "/backend/lesson/task/sort", "sort", "task_table");
      },
      error: function error(_error2) {
        $(".global_error").removeClass("hidden");
        $(".global_error").text("Tệp tin không tồn tại!");
      }
    });
  }, FormPdf.prototype.init = function () {
    var $this = this;
    $this.setEvents();
  },
  //init FormPdf
  $.FormPdf = new FormPdf(), $.FormPdf.Constructor = FormPdf;
  $.FormPdf.init();
}(window.jQuery);

// Form Audio
!function ($) {
  "use strict";

  var FormAudio = function FormAudio() {
    this.audio_filename = $("#audio_filename");
    this.audio_text = $("#audio_text");
    this.taskSubmit = $("#task_submit");
    this.bodyElement = $("#body_audio");
    this.iskaiwa2 = $("#is_kaiwa2");
  };
  FormAudio.prototype.setEvents = function () {
    var _this = this;
    _this.taskSubmit.on("click", function () {
      if ($(this).data("type") == "audio" && _this.bodyElement.data("type") == "add") {
        _this.add();
      }
      if ($(this).data("type") == "audio" && _this.bodyElement.data("type") == "edit") {
        _this.edit();
      }
    });
  }, FormAudio.prototype.add = function () {
    var _this = this;
    if (_this.audio_filename.val() == "" || _this.audio_text.val() == "") {
      $(".global_error").text("Dữ liệu còn trống !");
      $(".global_error").removeClass("hidden");
      return;
    }
    $(".global_error").addClass("hidden");
    var formData = new FormData();
    formData.append("_token", window.Laravel.csrfToken);
    formData.append("audio_filename", _this.audio_filename.val());
    formData.append("audio_text", _this.audio_text.val());
    formData.append("lesson_id", $("#lesson_id").val());
    formData.append("show", $("input[name='pdf_show']:checked").val());
    formData.append("is_kaiwa2", _this.iskaiwa2[0].checked);
    $.ajax({
      type: "post",
      url: "/backend/lesson/save-audio-entity",
      async: true,
      dataType: "JSON",
      data: formData,
      cache: false,
      contentType: false,
      processData: false,
      success: function success(response) {
        $("#pageModal").modal("hide");
        $("#task_area").html(response.html);
        sortTable("body_task", 2, 1, "/backend/lesson/task/sort", "sort", "task_table");
      },
      error: function error(_error3) {
        $(".global_error").removeClass("hidden");
        $(".global_error").text("Tệp tin không tồn tại!");
      }
    });
  }, FormAudio.prototype.edit = function () {
    var _this = this;
    if (_this.audio_filename.val() == "") {
      $(".global_error").text("Dữ liệu còn trống !");
      $(".global_error").removeClass("hidden");
      return;
    }
    $(".global_error").addClass("hidden");
    var formData = new FormData();
    formData.append("_token", window.Laravel.csrfToken);
    formData.append("id", _this.audio_filename.data("id"));
    formData.append("audio_filename", _this.audio_filename.val());
    formData.append("audio_text", _this.audio_text.val());
    formData.append("lesson_id", $("#lesson_id").val());
    formData.append("show", $("input[name='pdf_show']:checked").val());
    formData.append("is_kaiwa2", _this.iskaiwa2[0].checked);
    $.ajax({
      type: "post",
      url: "/backend/lesson/save-audio-entity",
      async: true,
      dataType: "JSON",
      data: formData,
      cache: false,
      contentType: false,
      processData: false,
      success: function success(response) {
        $("#pageModal").modal("hide");
        $("#task_area").html(response.html);
        sortTable("body_task", 2, 1, "/backend/lesson/task/sort", "sort", "task_table");
      },
      error: function error(_error4) {
        $(".global_error").removeClass("hidden");
        $(".global_error").text("Tệp tin không tồn tại!");
      }
    });
  }, FormAudio.prototype.init = function () {
    var $this = this;
    $this.setEvents();
  },
  //init FormAudio
  $.FormAudio = new FormAudio(), $.FormAudio.Constructor = FormAudio;
  $.FormAudio.init();
}(window.jQuery);
/******/ })()
;