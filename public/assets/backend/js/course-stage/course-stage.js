/******/ (function() { // webpackBootstrap
// This entry needs to be wrapped in an IIFE because it needs to be isolated against other entry modules.
!function() {
/*!*****************************************************************!*\
  !*** ./resources/assets/js/backend/course-stage/stage-panel.js ***!
  \*****************************************************************/
Vue.component('course-part-panel', {
  template: '#course-parts-panel-template',
  props: ['courses', 'stages'],
  data: function data() {
    return {
      url: window.location.origin
    };
  },
  mounted: function mounted() {
    var vm = this;
    console.log('courses --> ', vm.courses);
    console.log('parts --> ', vm.parts);
  },
  methods: {}
});
}();
// This entry needs to be wrapped in an IIFE because it needs to be isolated against other entry modules.
!function() {
/*!********************************************************************!*\
  !*** ./resources/assets/js/backend/course-stage/category-panel.js ***!
  \********************************************************************/
function _typeof(o) { "@babel/helpers - typeof"; return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && "function" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? "symbol" : typeof o; }, _typeof(o); }
function _toConsumableArray(r) { return _arrayWithoutHoles(r) || _iterableToArray(r) || _unsupportedIterableToArray(r) || _nonIterableSpread(); }
function _nonIterableSpread() { throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method."); }
function _unsupportedIterableToArray(r, a) { if (r) { if ("string" == typeof r) return _arrayLikeToArray(r, a); var t = {}.toString.call(r).slice(8, -1); return "Object" === t && r.constructor && (t = r.constructor.name), "Map" === t || "Set" === t ? Array.from(r) : "Arguments" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0; } }
function _iterableToArray(r) { if ("undefined" != typeof Symbol && null != r[Symbol.iterator] || null != r["@@iterator"]) return Array.from(r); }
function _arrayWithoutHoles(r) { if (Array.isArray(r)) return _arrayLikeToArray(r); }
function _arrayLikeToArray(r, a) { (null == a || a > r.length) && (a = r.length); for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e]; return n; }
function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }
function _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }
function _toPropertyKey(t) { var i = _toPrimitive(t, "string"); return "symbol" == _typeof(i) ? i : i + ""; }
function _toPrimitive(t, r) { if ("object" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || "default"); if ("object" != _typeof(i)) return i; throw new TypeError("@@toPrimitive must return a primitive value."); } return ("string" === r ? String : Number)(t); }
Vue.component("lesson-category-panel", {
  template: "#lesson-categories-panel-template",
  props: ["course", "stage", "stages", "permission"],
  watch: {
    stage: function stage(val) {
      var vm = this;
      vm.getCategoriesByStage(this.course.id, val);
      this.formData.stage = val;
    },
    course: function course(val) {
      var vm = this;
      vm.formData.course_id = val.id;
      vm.getCategoriesByStage(val, this.stage);
    }
  },
  data: function data() {
    return {
      url: window.location.origin,
      categories: [],
      selectedCategory: {},
      loading: false,
      showModal: false,
      draggable: false,
      stages: this.stages,
      formData: {
        id: undefined,
        title: "",
        type: 1,
        icon: "",
        course_id: 0,
        stage: this.stage,
        status: 1,
        iconImg: null
      }
    };
  },
  mounted: function mounted() {
    var vm = this;
  },
  methods: {
    onDragEnd: function onDragEnd() {
      var vm = this;
      var ids = vm.categories.map(function (category) {
        return category.id;
      });
      var data = {
        course_id: vm.course.id,
        stage: vm.stage,
        ids: ids
      };
      $.post(vm.url + "/backend/lesson-categories/apply-sorting", data, function (res) {
        if (res.code === 200) {
          vm.categories = res.data.map(function (category) {
            return category;
          });
        }
      });
    },
    changeStatus: function changeStatus(category, status) {
      var vm = this;
      var data = {
        id: category.id,
        status: status
      };
      $.post(vm.url + "/backend/lesson-categories/change-status", data, function (res) {
        if (res.code === 200) {
          vm.categories = vm.categories.map(function (item) {
            if (item.id === res.data.id) {
              item.status = res.data.status;
            }
            return item;
          });
        }
      });
    },
    changeFormIcon: function changeFormIcon(event) {
      var vm = this;
      vm.formData.iconImg = event.target.files[0];
    },
    saveForm: function saveForm() {
      var vm = this;
      var data = new FormData();
      data.append("id", vm.formData.id);
      data.append("title", vm.formData.title);
      data.append("type", vm.formData.type);
      data.append("icon", vm.formData.icon);
      data.append("course_id", vm.formData.course_id);
      data.append("stage", vm.formData.stage);
      data.append("status", vm.formData.status);
      data.append("iconImg", vm.formData.iconImg);
      if (vm.formData.id) {
        vm.updateCategory(data);
      } else {
        vm.addCategory(data);
      }
    },
    editCategory: function editCategory(category) {
      var vm = this;
      vm.formData = _objectSpread(_objectSpread({}, vm.formData), {}, {
        id: category.id,
        title: category.title,
        type: category.type,
        icon: category.icon,
        course_id: category.course_id,
        stage: category.stage,
        status: category.status
      });
      vm.showModal = true;
    },
    addCategory: function addCategory(data) {
      var vm = this;
      $.ajax({
        url: vm.url + "/backend/lesson-categories/add-category",
        type: "POST",
        data: data,
        processData: false,
        contentType: false,
        success: function success(res) {
          if (res.code === 200) {
            if (res.data.stage === vm.stage) {
              vm.categories.push(res.data);
            }
            vm.closeModal();
          } else {
            alert(res.msg);
          }
        }
      });
    },
    updateCategory: function updateCategory(data) {
      var vm = this;
      $.ajax({
        url: vm.url + "/backend/lesson-categories/update-category",
        type: "POST",
        data: data,
        processData: false,
        contentType: false,
        success: function success(res) {
          if (res.code === 200) {
            vm.categories = vm.categories.map(function (category) {
              if (category.id === res.data.id) {
                category = res.data;
              }
              return category;
            });
            vm.closeModal();
          } else {
            alert(res.msg);
          }
        }
      });
    },
    deleteCategory: function deleteCategory(category) {
      var vm = this;
      var confirm = window.confirm("Xác nhận xoá danh mục cùng với toàn bộ bài học trong danh mục?");
      if (confirm) {
        var data = {
          course_id: category.course_id,
          id: category.id
        };
        $.post(vm.url + "/backend/lesson-categories/delete-category", data, function (res) {
          if (res.code === 200) {
            vm.categories = vm.categories.filter(function (item) {
              return item.id !== category.id;
            });
            if (vm.selectedCategory.id === category.id) {
              if (vm.categories.length > 0) {
                vm.selectCategory(vm.categories[0]);
              } else {
                vm.selectUncategorized();
              }
            }
          }
        });
      }
    },
    setFormStatus: function setFormStatus(event) {
      var vm = this;
      vm.formData.status = parseInt(event.target.value);
    },
    closeModal: function closeModal() {
      var vm = this;
      vm.formData = _objectSpread(_objectSpread({}, vm.formData), {}, {
        id: undefined,
        title: "",
        type: 1,
        icon: "",
        course_id: vm.course.id,
        status: 1,
        iconImg: null
      });
      vm.showModal = false;
    },
    getCategoriesByStage: function getCategoriesByStage(courseId, stage) {
      var vm = this;
      var data = {
        courseId: courseId,
        stage: stage
      };
      vm.loading = true;
      $.post(vm.url + "/backend/lesson-categories", data, function (res) {
        if (res.code === 200) {
          vm.categories = _toConsumableArray(res.data);
          vm.selectedCategory = vm.categories[0] || {};
          vm.$emit("update:category", vm.categories[0] || {});
          vm.loading = false;
        }
      });
    },
    selectCategory: function selectCategory(category) {
      var vm = this;
      vm.selectedCategory = category;
      vm.$emit("update:category", category);
    },
    selectUncategorized: function selectUncategorized() {
      var vm = this;
      vm.selectedCategory = {
        course_id: vm.course.id,
        icon: "",
        id: 0,
        stage: vm.stage.id,
        status: 1,
        title: "Chưa được phân loại",
        type: 1
      };
      vm.$emit("update:category", vm.selectedCategory);
    }
  }
});
}();
// This entry needs to be wrapped in an IIFE because it needs to be isolated against other entry modules.
!function() {
/*!*****************************************************************!*\
  !*** ./resources/assets/js/backend/course-stage/group-panel.js ***!
  \*****************************************************************/
function _typeof(o) { "@babel/helpers - typeof"; return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && "function" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? "symbol" : typeof o; }, _typeof(o); }
function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }
function _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }
function _toPropertyKey(t) { var i = _toPrimitive(t, "string"); return "symbol" == _typeof(i) ? i : i + ""; }
function _toPrimitive(t, r) { if ("object" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || "default"); if ("object" != _typeof(i)) return i; throw new TypeError("@@toPrimitive must return a primitive value."); } return ("string" === r ? String : Number)(t); }
function _toConsumableArray(r) { return _arrayWithoutHoles(r) || _iterableToArray(r) || _unsupportedIterableToArray(r) || _nonIterableSpread(); }
function _nonIterableSpread() { throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method."); }
function _unsupportedIterableToArray(r, a) { if (r) { if ("string" == typeof r) return _arrayLikeToArray(r, a); var t = {}.toString.call(r).slice(8, -1); return "Object" === t && r.constructor && (t = r.constructor.name), "Map" === t || "Set" === t ? Array.from(r) : "Arguments" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0; } }
function _iterableToArray(r) { if ("undefined" != typeof Symbol && null != r[Symbol.iterator] || null != r["@@iterator"]) return Array.from(r); }
function _arrayWithoutHoles(r) { if (Array.isArray(r)) return _arrayLikeToArray(r); }
function _arrayLikeToArray(r, a) { (null == a || a > r.length) && (a = r.length); for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e]; return n; }
Vue.component("lesson-group-panel", {
  template: "#lesson-groups-panel-template",
  props: ["category", "permission"],
  data: function data() {
    return {
      url: window.location.origin,
      groups: [],
      lessons: [],
      choseLessons: [],
      choseType: undefined,
      choseRule: undefined,
      groupIdToChange: undefined,
      categories: [],
      showingGroup: {},
      loading: false,
      showModal: false,
      showLessonsModal: false,
      draggable: false,
      playbackRate: [0.75, 1, 1.25, 1.5, 2],
      formData: {
        id: undefined,
        course_id: 0,
        name: "",
        description: "",
        component_count: "",
        skill: "",
        lesson_category_id: 0,
        show: 0,
        // is_secret: 0,
        is_specialezed: 0
      }
    };
  },
  watch: {
    category: function category(val) {
      var vm = this;
      vm.getGroupByCategory(val);
      vm.formData.lesson_category_id = val.id;
    }
  },
  mounted: function mounted() {
    var vm = this;
  },
  methods: {
    printLessonIcon: function printLessonIcon(type) {
      var vm = this;
      var imgName = "";
      switch (type) {
        case "docs":
          imgName = "docb.png";
          break;
        case "video":
          imgName = "videob.png";
          break;
        case "test":
          imgName = "quizb.png";
          break;
        case "flashcard":
          imgName = "fcb.png";
          break;
        default:
          imgName = "docb.png";
      }
      return imgName;
    },
    updateLesson: function updateLesson(event, lesson, field) {
      var vm = this;
      var data = {
        id: lesson.id,
        field: field,
        value: event.target.value
      };
      $.post(vm.url + "/backend/lesson-groups/update-lesson", data, function (res) {
        if (res.code === 200) {
          vm.lessons = vm.lessons.map(function (item) {
            if (item.id === res.data.id) {
              item[field] = res.data[field];
            }
            return item;
          });
          alert("Thành công");
        } else {
          alert("Thất bại");
        }
      });
    },
    changeLessonSpeed: function changeLessonSpeed(event, lesson) {
      var vm = this;
      var data = {
        id: lesson.id,
        speed: event.target.value
      };
      $.post(vm.url + "/backend/lesson-groups/change-lesson-speed", data, function (res) {
        if (res.code === 200) {
          vm.lessons = vm.lessons.map(function (item) {
            if (item.id === res.data.id) {
              item.default_speed = res.data.default_speed;
            }
            return item;
          });
        } else {
          alert("Thất bại");
        }
      });
    },
    changeLessonType: function changeLessonType() {
      var vm = this;
      if (vm.choseType && vm.choseLessons.length > 0) {
        var data = {
          ids: _toConsumableArray(vm.choseLessons),
          choseType: vm.choseType
        };
        $.post(vm.url + "/backend/lesson/change-lesson-type", data, function (res) {
          if (res.code === 200) {
            vm.choseLessons = [];
            vm.choseType = undefined;
            vm.lessons = vm.lessons.map(function (lesson, index) {
              lesson[index].type = res.data[index].type;
              return lesson;
            });
            alert("Chuyển thành công");
          } else {
            alert("Chuyển thất bại");
          }
        });
      }
    },
    addLessonRule: function addLessonRule() {
      var vm = this;
      if (vm.choseRule && vm.choseLessons.length > 0) {
        var data = {
          ids: _toConsumableArray(vm.choseLessons),
          choseRule: vm.choseRule
        };
        $.post(vm.url + "/backend/lesson-groups/lesson-rule/attach", data, function (res) {
          if (res.code === 200) {
            var lessons = res.data;
            res.data.forEach(function (o) {
              var idx = vm.lessons.findIndex(function (lesson) {
                return lesson.id === o.id;
              });
              if (idx === -1) {
                vm.lessons.push(o);
              } else {
                Vue.set(vm.lessons, idx, o);
              }
            });
          } else {
            alert("Chuyển thất bại");
          }
        });
      }
    },
    changeGroupId: function changeGroupId() {
      var vm = this;
      if (vm.groupIdToChange && vm.choseLessons.length > 0) {
        var data = {
          ids: _toConsumableArray(vm.choseLessons),
          groupIdToChange: vm.groupIdToChange
        };
        $.post(vm.url + "/backend/lesson/change-group", data, function (res) {
          if (res.code === 200) {
            vm.lessons = vm.lessons.filter(function (lesson) {
              return !vm.choseLessons.includes(lesson.id);
            });
            vm.choseLessons = [];
            alert("Chuyển thành công");
          } else {
            alert("Chuyển thất bại");
          }
        });
      }
    },
    checkAllLesson: function checkAllLesson(event) {
      var vm = this;
      var checked = event.target.checked;
      if (checked) {
        vm.choseLessons = vm.lessons.map(function (lesson) {
          return lesson.id;
        });
      } else {
        vm.choseLessons = [];
      }
      console.log("array --> ", vm.choseLessons);
    },
    checkOneLesson: function checkOneLesson(event, lessonId) {
      var vm = this;
      var checked = event.target.checked;
      if (checked) {
        vm.choseLessons.push(lessonId);
      } else {
        vm.choseLessons = vm.choseLessons.filter(function (lesson) {
          return lesson !== lessonId;
        });
      }
      console.log("array --> ", vm.choseLessons);
    },
    changeLessonRulePoint: function changeLessonRulePoint(rule) {
      var vm = this;
      var point = window.prompt("Nhập số điểm");
      $.post(vm.url + "/backend/lesson-groups/rule/".concat(rule.pivot.id, "/point"), {
        point: point
      }, function (res) {
        if (res.code === 200) {
          rule.pivot.point = point;
        } else {
          alert(res.msg);
        }
      });
    },
    detachRule: function detachRule(lesson, ruleId, idx) {
      var vm = this;
      var data = {
        lesson_id: lesson.id,
        rule_id: ruleId
      };
      $.post(vm.url + "/backend/lesson-groups/lesson-rule/detach", data, function (res) {
        if (res.code === 200) {
          lesson.experience_rules.splice(idx, 1);
        } else {
          alert(res.msg);
        }
      });
    },
    toggleHideLessonTitle: function toggleHideLessonTitle(lesson) {
      var vm = this;
      var data = {
        id: lesson.id
      };
      $.post(vm.url + "/backend/lesson/toggle-secret", data, function (res) {
        if (res.code === 200) {
          vm.lessons = vm.lessons.map(function (item) {
            if (item.id === res.data.id) {
              item.is_secret = res.data.is_secret;
            }
            return item;
          });
        } else {
          alert(res.msg);
        }
      });
    },
    onChangeCheckbox: function onChangeCheckbox(event, param) {
      var vm = this;
      vm.formData[param] = event.target.checked ? 1 : 0;
    },
    onDragLessonEnd: function onDragLessonEnd() {
      var vm = this;
      var ids = vm.lessons.map(function (lesson) {
        return lesson.id;
      });
      var data = {
        group_id: vm.showingGroup.id,
        ids: ids
      };
      $.post(vm.url + "/backend/lesson-groups/apply-sorting-lesson", data, function (res) {
        if (res.code === 200) {
          vm.lessons = res.data.map(function (lesson) {
            return lesson;
          });
        }
      });
    },
    closeLessonsModal: function closeLessonsModal() {
      var vm = this;
      vm.lessons = [];
      vm.showLessonsModal = false;
      vm.groupIdToChange = undefined;
      vm.choseLessons = [];
    },
    showLessons: function showLessons(group) {
      var vm = this;
      var data = {
        id: group.id
      };
      vm.showingGroup = group;
      $.post(vm.url + "/backend/lesson-groups/get-lessons", data, function (res) {
        if (res.code === 200) {
          vm.lessons = _toConsumableArray(res.data);
          vm.showLessonsModal = true;
        }
      });
    },
    onDragEnd: function onDragEnd() {
      var vm = this;
      var ids = vm.groups.map(function (group) {
        return group.id;
      });
      var data = {
        course_id: vm.category.course_id,
        lesson_category_id: vm.category.id,
        ids: ids
      };
      $.post(vm.url + "/backend/lesson-groups/apply-sorting", data, function (res) {
        if (res.code == 200) {
          vm.groups = res.data.map(function (group) {
            return group;
          });
        }
      });
    },
    changeLessonStatus: function changeLessonStatus(lesson, show) {
      var vm = this;
      var data = {
        id: lesson.id,
        show: show
      };
      $.post(vm.url + "/backend/lesson-groups/change-lesson-status", data, function (res) {
        if (res.code === 200) {
          vm.lessons = vm.lessons.map(function (item) {
            if (item.id === res.data.id) {
              item.show = res.data.show;
            }
            return item;
          });
        }
      });
    },
    changeStatus: function changeStatus(group, show) {
      var vm = this;
      var data = {
        id: group.id,
        show: show
      };
      $.post(vm.url + "/backend/lesson-groups/change-status", data, function (res) {
        if (res.code === 200) {
          vm.groups = vm.groups.map(function (item) {
            if (item.id === res.data.id) {
              item.show = res.data.show;
            }
            return item;
          });
        }
      });
    },
    saveForm: function saveForm() {
      var vm = this;
      var data = new FormData();
      data.append("id", vm.formData.id);
      data.append("name", vm.formData.name);
      data.append("description", vm.formData.description);
      data.append("skill", vm.formData.skill);
      data.append("component_count", vm.formData.component_count);
      data.append("course_id", vm.category.course_id);
      data.append("lesson_category_id", vm.formData.lesson_category_id);
      data.append("show", vm.formData.show);
      // data.append('is_secret', vm.formData.is_secret);
      if (vm.formData.id) {
        vm.updateGroup(data);
      } else {
        vm.addGroup(data);
      }
    },
    editGroup: function editGroup(group) {
      console.log("group", group);
      var vm = this;
      vm.formData = _objectSpread(_objectSpread({}, vm.formData), {}, {
        id: group.id,
        name: group.name,
        description: group.description,
        skill: group.skill,
        component_count: group.component_count,
        lesson_category_id: group.lesson_category_id,
        show: group.show
        // is_secret: group.is_secret,
      });
      vm.showModal = true;
    },
    addGroup: function addGroup(data) {
      var vm = this;
      $.ajax({
        url: vm.url + "/backend/lesson-groups/add-group",
        type: "POST",
        data: data,
        processData: false,
        contentType: false,
        success: function success(res) {
          if (res.code === 200) {
            if (res.data.lesson_category_id === vm.category.id) {
              vm.groups.push(res.data);
            }
            vm.closeModal();
          } else {
            alert(res.msg);
          }
        }
      });
    },
    updateGroup: function updateGroup(data) {
      var vm = this;
      $.ajax({
        url: vm.url + "/backend/lesson-groups/update-group",
        type: "POST",
        data: data,
        processData: false,
        contentType: false,
        success: function success(res) {
          if (res.code === 200) {
            if (res.data.lesson_category_id !== vm.category.id) {
              vm.groups = _.remove(vm.groups, function (group) {
                return group.id !== res.data.id;
              });
            } else {
              vm.groups = vm.groups.map(function (group) {
                if (group.id === res.data.id) {
                  group = _objectSpread({}, res.data);
                }
                return group;
              });
            }
            vm.closeModal();
          } else {
            alert(res.msg);
          }
        }
      });
    },
    deleteGroup: function deleteGroup(group) {
      var vm = this;
      var confirm = window.confirm("Xác nhận xoá nhóm bài học cùng với toàn bộ bài học trong nhóm?");
      if (confirm) {
        var data = {
          id: group.id
        };
        $.post(vm.url + "/backend/lesson-groups/delete-group", data, function (res) {
          if (res.code === 200) {
            vm.groups = vm.groups.filter(function (item) {
              return item.id !== group.id;
            });
          }
        });
      }
    },
    setFormStatus: function setFormStatus(event) {
      var vm = this;
      vm.formData.show = parseInt(event.target.value);
    },
    closeModal: function closeModal() {
      var vm = this;
      vm.formData = _objectSpread(_objectSpread({}, vm.formData), {}, {
        id: undefined,
        course_id: 0,
        name: "",
        description: "",
        skill: "",
        component_count: "",
        show: 0
        // is_secret: 0,
      });
      vm.showModal = false;
    },
    getGroupByCategory: function getGroupByCategory(category) {
      var vm = this;
      vm.loading = true;
      var data = {
        course_id: category.course_id,
        id: category.id
      };
      $.post(vm.url + "/backend/lesson-groups", data, function (res) {
        if (res.code === 200) {
          vm.groups = _toConsumableArray(res.data.groups);
          vm.categories = _toConsumableArray(res.data.categories);
          vm.loading = false;
        }
      });
    }
  }
});
}();
// This entry needs to be wrapped in an IIFE because it needs to be isolated against other entry modules.
!function() {
/*!******************************************************************!*\
  !*** ./resources/assets/js/backend/course-stage/course-stage.js ***!
  \******************************************************************/
function _typeof(o) { "@babel/helpers - typeof"; return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && "function" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? "symbol" : typeof o; }, _typeof(o); }
function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }
function _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }
function _toPropertyKey(t) { var i = _toPrimitive(t, "string"); return "symbol" == _typeof(i) ? i : i + ""; }
function _toPrimitive(t, r) { if ("object" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || "default"); if ("object" != _typeof(i)) return i; throw new TypeError("@@toPrimitive must return a primitive value."); } return ("string" === r ? String : Number)(t); }
var courseStageScreen = new Vue({
  el: "#course-stages__screen",
  data: function data() {
    return {
      url: window.location.origin,
      admin: {},
      permission: 0,
      courses: [],
      stages: [],
      stage_names: [],
      selectedCourse: {},
      selectedStage: 1,
      selectedCategory: {},
      loading: false,
      formData: {
        id: undefined,
        title: "",
        key: "",
        course_id: "",
        status: 1
      }
    };
  },
  methods: {
    getStageName: function getStageName(stage) {
      if (this.stage_names.length === 0) {
        return;
      }
      return this.stage_names.filter(function (entity) {
        return entity.stage === stage;
      }).length > 0 ? this.stage_names.filter(function (entity) {
        return entity.stage === stage;
      })[0].stage_name : null;
    },
    categorySelected: function categorySelected(event) {
      var vm = this;
      vm.selectedCategory = event;
    },
    selectCourse: function selectCourse(event, course) {
      var vm = this;
      if (event.target.checked) {
        vm.selectedCourse = _objectSpread({}, course);
      }
    },
    checkAdmin: function checkAdmin(admin) {
      return vm.admin.permission === 1 || vm.admin.id === 36 || vm.admin.id === 4 || vm.admin.id === 69;
    },
    addStage: function addStage() {
      this.stages.push(this.stages.length + 1);
    },
    setMaxStage: function setMaxStage(maxStage) {
      this.stages = [];
      for (var i = 1; i <= maxStage; i++) {
        this.stages.push(i);
      }
    },
    removeLastStage: function removeLastStage() {
      var vm = this;
      var isConfirmed = confirm("Bạn có chắc chắn muốn xóa chặng cuối chứ? Dữ liệu không thể khôi phục sau khi xóa!");
      if (!isConfirmed) {
        return;
      }
      vm.stages.pop();
      $.post(vm.url + "/backend/delete-last-stage", {
        course_id: vm.selectedCourse.id
      }, function (res) {
        console.log(res);
      });
    },
    getMaxStage: function getMaxStage() {
      var vm = this;
      if (vm.selectedCourse) {
        var courseId = vm.selectedCourse.id;
        $.get(vm.url + "/backend/get-max-stage?courseId=" + courseId, function (res) {
          vm.setMaxStage(res.max_stage);
          vm.stage_names = res.stage_list;
        });
      }
    },
    changeStageName: function changeStageName(courseId, stage) {
      var vm = this;
      var stageName = $("#stage_".concat(stage)).val();
      if (this.stage_names.filter(function (entity) {
        return entity.stage === stage;
      }).length == 0) {
        alert("Bạn phải tạo chặng trước khi cập nhật tên chặng!");
        return;
      }
      $.post(vm.url + "/backend/update-stage-name", {
        course_id: courseId,
        stage: stage,
        stage_name: stageName
      }, function (res) {
        alert("Cập nhật tên chặng thành công!");
      });
    }
  },
  watch: {
    selectedCourse: function selectedCourse() {
      this.getMaxStage();
    }
  },
  mounted: function mounted() {
    var vm = this;
    vm.admin = admin;
    vm.permission = vm.admin.permission === 1 || vm.admin.id === 36 || vm.admin.id === 4 || vm.admin.id === 69;
    vm.courses = _.orderBy(courses, "name").filter(function (course) {
      if (["N1", "N2", "N3", "N4", "N5", "EJU - Toán", "Sơ cấp N4", "Sơ cấp N5", "Luyện đề N4", "Luyện đề N5", "N1 (mới)", "N2 (mới)", "N3 (mới)", "Chữ Hán N5"].includes(course.name)) {
        return course;
      }
    });
    vm.selectedCourse = vm.courses[0];
    vm.getMaxStage();
  }
});
}();
/******/ })()
;