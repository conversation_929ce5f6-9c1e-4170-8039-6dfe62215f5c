{"version": 3, "sources": ["stage-panel.js", "category-panel.js", "group-panel.js", "course-stage.js"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;ACpBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AChPA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AC1YA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "course-stage.js", "sourcesContent": ["Vue.component('course-part-panel', {\n    template: '#course-parts-panel-template',\n\n    props: ['courses', 'stages'],\n\n    data: function () {\n        return {\n            url: window.location.origin\n        };\n    },\n\n    mounted: function () {\n        var vm = this;\n        console.log('courses --> ', vm.courses)\n        console.log('parts --> ', vm.parts)\n    },\n\n    methods: {\n\n    },\n});", "Vue.component('lesson-category-panel', {\n    template: '#lesson-categories-panel-template',\n\n    props: ['course', 'stage', 'permission'],\n\n    watch: {\n        stage: function (val) {\n            var vm = this;\n            vm.getCategoriesByStage(this.course.id, val);\n        },\n        course: function (val) {\n            var vm = this;\n            vm.formData.course_id = val.id;\n            vm.getCategoriesByStage(val, this.stage);\n        }\n    },\n    data: function () {\n        return {\n            url: window.location.origin,\n            categories: [],\n            selectedCategory: {},\n            loading: false,\n            showModal: false,\n            draggable: false,\n            formData: {\n                id: undefined,\n                title: '',\n                type: 1,\n                icon: '',\n                course_id: 0,\n                stage: 1,\n                status: 1,\n                iconImg: null\n            }\n        };\n    },\n\n    mounted: function () {\n        var vm = this;\n    },\n\n    methods: {\n        onDragEnd: function () {\n            var vm = this;\n            var ids = vm.categories.map(function (category) {\n                return category.id;\n            });\n            var data = {\n                course_id: vm.course.id,\n                stage: vm.stage,\n                ids: ids\n            };\n            $.post(vm.url + '/backend/lesson-categories/apply-sorting', data, function (res) {\n                if (res.code === 200) {\n                    vm.categories = res.data.map(function (category) {\n                        return category;\n                    })\n                }\n            })\n        },\n        changeStatus: function (category, status) {\n            var vm = this;\n            var data = {\n                id: category.id,\n                status: status\n            };\n            $.post(vm.url + '/backend/lesson-categories/change-status', data, function (res) {\n                if (res.code === 200) {\n                    vm.categories = vm.categories.map(function (item) {\n                        if (item.id === res.data.id) {\n                            item.status = res.data.status;\n                        }\n                        return item;\n                    })\n                }\n            })\n        },\n        changeFormIcon: function (event) {\n            var vm = this;\n            vm.formData.iconImg = event.target.files[0];\n        },\n        saveForm: function () {\n            var vm = this;\n\n            var data = new FormData();\n\n            data.append('id', vm.formData.id);\n            data.append('title', vm.formData.title);\n            data.append('type', vm.formData.type);\n            data.append('icon', vm.formData.icon);\n            data.append('course_id', vm.formData.course_id);\n            data.append('stage', vm.formData.stage);\n            data.append('status', vm.formData.status);\n            data.append('iconImg', vm.formData.iconImg);\n            if (vm.formData.id) {\n                vm.updateCategory(data);\n            } else {\n                vm.addCategory(data);\n            }\n\n        },\n        editCategory: function (category) {\n            var vm = this;\n\n            vm.formData = {\n                ...vm.formData,\n                id: category.id,\n                title: category.title,\n                type: category.type,\n                icon: category.icon,\n                course_id: category.course_id,\n                stage: category.stage,\n                status: category.status\n            };\n            vm.showModal = true;\n        },\n        addCategory: function (data) {\n            var vm = this;\n\n            $.ajax({\n                url: vm.url + '/backend/lesson-categories/add-category',\n                type: \"POST\",\n                data: data,\n                processData: false,\n                contentType: false,\n                success: function (res) {\n                    if (res.code === 200) {\n                        if (res.data.stage === vm.stage) {\n                            vm.categories.push(res.data);\n                        }\n                        vm.closeModal();\n                    } else {\n                        alert(res.msg)\n                    }\n                }\n            });\n        },\n        updateCategory: function (data) {\n            var vm = this;\n\n            $.ajax({\n                url: vm.url + '/backend/lesson-categories/update-category',\n                type: \"POST\",\n                data: data,\n                processData: false,\n                contentType: false,\n                success: function (res) {\n                    if (res.code === 200) {\n                        vm.categories = vm.categories.map(function (category) {\n                            if (category.id === res.data.id) {\n                                category = res.data\n                            }\n                            return category;\n                        });\n                        vm.closeModal();\n                    } else {\n                        alert(res.msg)\n                    }\n                }\n            });\n        },\n        deleteCategory: function (category) {\n            var vm = this;\n\n            var confirm = window.confirm('Xác nhận xoá danh mục cùng với toàn bộ bài học trong danh mục?');\n            if (confirm) {\n                var data = {\n                    course_id: category.course_id,\n                    id: category.id\n                };\n                $.post(vm.url + '/backend/lesson-categories/delete-category', data, function (res) {\n                    if (res.code === 200) {\n                        vm.categories = vm.categories.filter(function (item) {\n                            return item.id !== category.id;\n                        });\n                        if (vm.selectedCategory.id === category.id) {\n                            if (vm.categories.length > 0) {\n                                vm.selectCategory(vm.categories[0])\n                            } else {\n                                vm.selectUncategorized();\n                            }\n                        }\n                    }\n                })\n            }\n        },\n        setFormStatus: function (event) {\n            var vm = this;\n            vm.formData.status = parseInt(event.target.value);\n        },\n        closeModal: function () {\n            var vm = this;\n            vm.formData = {\n                ...vm.formData,\n                id: undefined,\n                title: '',\n                type: 1,\n                icon: '',\n                course_id: vm.course.id,\n                stage: 1,\n                status: 1,\n                iconImg: null\n            };\n            vm.showModal = false;\n        },\n        getCategoriesByStage: function (courseId, stage) {\n            var vm = this;\n            var data = {\n                courseId: courseId,\n                stage: stage\n            };\n            vm.loading = true;\n            $.post(vm.url + '/backend/lesson-categories', data, function (res) {\n                if (res.code === 200) {\n                    vm.categories = [...res.data];\n                    vm.selectedCategory = vm.categories[0] || {};\n                    vm.$emit('update:category', vm.categories[0] || {});\n                    vm.loading = false;\n                }\n            })\n        },\n        selectCategory: function (category) {\n            var vm = this;\n            vm.selectedCategory = category;\n            vm.$emit('update:category', category)\n        },\n        selectUncategorized: function () {\n            var vm = this;\n            vm.selectedCategory = {\n                course_id: vm.course.id,\n                icon: \"\",\n                id: 0,\n                stage: vm.stage.id,\n                status: 1,\n                title: \"Chưa được phân loại\",\n                type: 1\n            };\n            vm.$emit('update:category', vm.selectedCategory)\n        }\n    },\n});", "Vue.component('lesson-group-panel', {\n    template: '#lesson-groups-panel-template',\n\n    props: ['category', 'permission'],\n\n    data: function () {\n        return {\n            url: window.location.origin,\n            groups: [],\n            lessons: [],\n            choseLessons: [],\n            choseType: undefined,\n            groupIdToChange: undefined,\n            categories: [],\n            showingGroup: {},\n            loading: false,\n            showModal: false,\n            showLessonsModal: false,\n            draggable: false,\n            playbackRate: [0.75, 1, 1.25, 1.5, 2],\n            formData: {\n                id: undefined,\n                course_id: 0,\n                name: '',\n                lesson_category_id: 0,\n                show: 0,\n                // is_secret: 0,\n                is_specialezed: 0\n            }\n        };\n    },\n\n    watch: {\n        category: function (val) {\n            var vm = this;\n            vm.getGroupByCategory(val);\n        }\n    },\n    mounted: function () {\n        var vm = this;\n    },\n\n    methods: {\n        printLessonIcon: function (type) {\n            var vm = this;\n            var imgName = '';\n            switch (type) {\n                case 'docs':\n                    imgName = 'docb.png';\n                    break;\n                case 'video':\n                    imgName = 'videob.png';\n                    break;\n                case 'test':\n                    imgName = 'quizb.png';\n                    break;\n                case 'flashcard':\n                    imgName = 'fcb.png';\n                    break;\n                default:\n                    imgName = 'docb.png';\n            }\n            return imgName;\n        },\n        changeLessonSpeed: function(event, lesson) {\n            var vm = this;\n            var data = {\n                id: lesson.id,\n                speed: event.target.value,\n            };\n            $.post(vm.url + '/backend/lesson-groups/change-lesson-speed', data, function (res) {\n                if (res.code === 200) {\n                    vm.lessons = vm.lessons.map(function (item) {\n                        if (item.id === res.data.id) {\n                            item.default_speed = res.data.default_speed;\n                        }\n                        return item;\n                    })\n                } else {\n                    alert('Thất bại')\n                }\n            })\n        },\n        changeLessonType: function () {\n            var vm = this;\n            if (vm.choseType && vm.choseLessons.length > 0) {\n                var data = {\n                    ids: [...vm.choseLessons],\n                    choseType: vm.choseType\n                };\n                $.post(vm.url + '/backend/lesson/change-lesson-type', data, function (res) {\n                    if (res.code === 200) {\n                        vm.choseLessons = [];\n                        vm.choseType = undefined;\n                        vm.lessons = vm.lessons.map(function (lesson, index) {\n                            lesson[index].type = res.data[index].type;\n                            return lesson;\n                        });\n                        alert('Chuyển thành công');\n                    } else {\n                        alert('Chuyển thất bại')\n                    }\n                })\n            }\n        },\n        changeGroupId: function () {\n            var vm = this;\n            if (vm.groupIdToChange && vm.choseLessons.length > 0) {\n                var data = {\n                    ids: [...vm.choseLessons],\n                    groupIdToChange: vm.groupIdToChange\n                };\n                $.post(vm.url + '/backend/lesson/change-group', data, function (res) {\n                    if (res.code === 200) {\n                        vm.lessons = vm.lessons.filter(function (lesson) {\n                            return !vm.choseLessons.includes(lesson.id)\n                        });\n                        vm.choseLessons = [];\n                        alert('Chuyển thành công')\n                    } else {\n                        alert('Chuyển thất bại')\n                    }\n                })\n            }\n        },\n        checkAllLesson: function (event) {\n            var vm = this;\n\n            var checked = event.target.checked;\n            if (checked) {\n                vm.choseLessons = vm.lessons.map(function (lesson) {\n                    return lesson.id;\n                })\n            } else {\n                vm.choseLessons = [];\n            }\n            console.log(\"array --> \", vm.choseLessons)\n        },\n        checkOneLesson: function (event, lessonId) {\n            var vm = this;\n            var checked = event.target.checked;\n            if (checked) {\n                vm.choseLessons.push(lessonId);\n            } else {\n                vm.choseLessons = vm.choseLessons.filter(function (lesson) {\n                    return lesson !== lessonId;\n                })\n            }\n            console.log(\"array --> \", vm.choseLessons)\n        },\n        toggleHideLessonTitle: function (lesson) {\n            var vm = this;\n            var data = {\n                id: lesson.id\n            };\n            $.post(vm.url + '/backend/lesson/toggle-secret', data, function (res) {\n                if (res.code === 200) {\n                    vm.lessons = vm.lessons.map(function (item) {\n                        if (item.id === res.data.id) {\n                            item.is_secret = res.data.is_secret;\n                        }\n                        return item;\n                    })\n                } else {\n                    alert(res.msg);\n                }\n            })\n        },\n        onChangeCheckbox: function (event, param) {\n            var vm = this;\n            vm.formData[param] = event.target.checked ? 1 : 0;\n        },\n        onDragLessonEnd: function () {\n            var vm = this;\n            var ids = vm.lessons.map(function (lesson) {\n                return lesson.id;\n            });\n            var data = {\n                group_id: vm.showingGroup.id,\n                ids: ids\n            };\n            $.post(vm.url + '/backend/lesson-groups/apply-sorting-lesson', data, function (res) {\n                if (res.code === 200) {\n                    vm.lessons = res.data.map(function (lesson) {\n                        return lesson;\n                    })\n                }\n            })\n        },\n        closeLessonsModal: function () {\n            var vm = this;\n            vm.lessons = [];\n            vm.showLessonsModal = false;\n            vm.groupIdToChange = undefined;\n            vm.choseLessons = [];\n        },\n        showLessons: function (group) {\n            var vm = this;\n            var data = {\n                id: group.id\n            };\n            vm.showingGroup = group;\n            $.post(vm.url + '/backend/lesson-groups/get-lessons', data, function (res) {\n                if (res.code === 200) {\n                    vm.lessons = [...res.data];\n                    vm.showLessonsModal = true;\n                }\n            })\n        },\n        onDragEnd: function () {\n            var vm = this;\n            var ids = vm.groups.map(function (group) {\n                return group.id;\n            });\n            var data = {\n                course_id: vm.category.course_id,\n                lesson_category_id: vm.category.id,\n                ids: ids\n            };\n            $.post(vm.url + '/backend/lesson-groups/apply-sorting', data, function (res) {\n                if (res.code == 200) {\n                    vm.groups = res.data.map(function (group) {\n                        return group;\n                    })\n                }\n            })\n        },\n        changeLessonStatus: function (lesson, show) {\n            var vm = this;\n            var data = {\n                id: lesson.id,\n                show: show\n            };\n            $.post(vm.url + '/backend/lesson-groups/change-lesson-status', data, function (res) {\n                if (res.code === 200) {\n                    vm.lessons = vm.lessons.map(function (item) {\n                        if (item.id === res.data.id) {\n                            item.show = res.data.show;\n                        }\n                        return item;\n                    })\n                }\n            })\n        },\n        changeStatus: function (group, show) {\n            var vm = this;\n            var data = {\n                id: group.id,\n                show: show\n            };\n            $.post(vm.url + '/backend/lesson-groups/change-status', data, function (res) {\n                if (res.code === 200) {\n                    vm.groups = vm.groups.map(function (item) {\n                        if (item.id === res.data.id) {\n                            item.show = res.data.show;\n                        }\n                        return item;\n                    })\n                }\n            })\n        },\n        saveForm: function () {\n            var vm = this;\n\n            var data = new FormData();\n\n            data.append('id', vm.formData.id);\n            data.append('name', vm.formData.name);\n            data.append('course_id', vm.category.course_id);\n            data.append('lesson_category_id', vm.formData.lesson_category_id);\n            data.append('show', vm.formData.show);\n            // data.append('is_secret', vm.formData.is_secret);\n            if (vm.formData.id) {\n                vm.updateGroup(data);\n            } else {\n                vm.addGroup(data);\n            }\n\n        },\n        editGroup: function (group) {\n            var vm = this;\n\n            vm.formData = {\n                ...vm.formData,\n                id: group.id,\n                name: group.name,\n                lesson_category_id: group.lesson_category_id,\n                show: group.show,\n                // is_secret: group.is_secret,\n            };\n            vm.showModal = true;\n        },\n        addGroup: function (data) {\n            var vm = this;\n\n            $.ajax({\n                url: vm.url + '/backend/lesson-groups/add-group',\n                type: \"POST\",\n                data: data,\n                processData: false,\n                contentType: false,\n                success: function (res) {\n                    if (res.code === 200) {\n                        if (res.data.lesson_category_id === vm.category.id) {\n                            vm.groups.push(res.data);\n                        }\n                        vm.closeModal();\n                    } else {\n                        alert(res.msg)\n                    }\n                }\n            });\n        },\n        updateGroup: function (data) {\n            var vm = this;\n\n            $.ajax({\n                url: vm.url + '/backend/lesson-groups/update-group',\n                type: \"POST\",\n                data: data,\n                processData: false,\n                contentType: false,\n                success: function (res) {\n                    if (res.code === 200) {\n                        if (res.data.lesson_category_id !== vm.category.id) {\n                            vm.groups = _.remove(vm.groups, function (group) {\n                                return group.id !== res.data.id;\n                            });\n                        } else {\n                            vm.groups = vm.groups.map(function (group) {\n                                if (group.id === res.data.id) {\n                                    group = {...res.data}\n                                }\n                                return group;\n                            })\n                        }\n                        vm.closeModal();\n                    } else {\n                        alert(res.msg)\n                    }\n                }\n            });\n        },\n        deleteGroup: function (group) {\n            var vm = this;\n\n            var confirm = window.confirm('Xác nhận xoá nhóm bài học cùng với toàn bộ bài học trong nhóm?');\n            if (confirm) {\n                var data = {\n                    id: group.id\n                };\n                $.post(vm.url + '/backend/lesson-groups/delete-group', data, function (res) {\n                    if (res.code === 200) {\n                        vm.groups = vm.groups.filter(function (item) {\n                            return item.id !== group.id;\n                        })\n                    }\n                })\n            }\n        },\n        setFormStatus: function (event) {\n            var vm = this;\n            vm.formData.show = parseInt(event.target.value);\n        },\n        closeModal: function () {\n            var vm = this;\n            vm.formData = {\n                ...vm.formData,\n                id: undefined,\n                course_id: 0,\n                name: '',\n                lesson_category_id: 0,\n                show: 0,\n                // is_secret: 0,\n            };\n            vm.showModal = false;\n        },\n        getGroupByCategory: function (category) {\n            var vm = this;\n            vm.loading = true;\n            var data = {\n                course_id: category.course_id,\n                id: category.id\n            };\n            $.post(vm.url + '/backend/lesson-groups', data, function (res) {\n                if (res.code === 200) {\n                    vm.groups = [...res.data.groups];\n                    vm.categories = [...res.data.categories];\n                    vm.loading = false;\n                }\n            })\n        }\n    },\n});\n", "var courseStageScreen = new Vue({\n    el: '#course-stages__screen',\n    data: function () {\n        return {\n            url: window.location.origin,\n            admin: {},\n            permission: 0,\n            courses: [],\n            stages: [],\n            // selectedCourses: [],\n            selectedCourse: {},\n            selectedStage: 1,\n            selectedCategory: {},\n            loading: false,\n            showModal: false,\n            formData: {\n                id: undefined,\n                title: '',\n                key: '',\n                course_id: '',\n                status: 1\n            }\n        }\n    },\n    methods: {\n        categorySelected: function (event) {\n            var vm = this;\n            vm.selectedCategory = event;\n        },\n        selectCourse: function (event, course) {\n            var vm = this;\n\n            if (event.target.checked) {\n                vm.selectedCourse = {...course}\n            }\n        },\n        checkAdmin: function (admin) {\n            return vm.admin.permission === 1 || vm.admin.id === 36 || vm.admin.id === 4\n        }\n    },\n    mounted: function () {\n        var vm = this;\n        vm.admin = admin;\n        vm.permission = vm.admin.permission === 1 || vm.admin.id === 36 || vm.admin.id === 4\n\n        vm.courses = _.orderBy(courses, 'name').filter(function (course) {\n            if (['N1','N2','N3','N4','N5','EJU - Toán'].includes(course.name)) {\n                return course;\n            }\n        });\n        console.log(vm.courses)\n        vm.selectedCourse = vm.courses[0]\n    }\n});"]}