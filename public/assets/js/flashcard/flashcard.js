/******/ (function() { // webpackBootstrap
/*!************************************************************!*\
  !*** ./resources/assets/js/backend/flashcard/flashcard.js ***!
  \************************************************************/
function _toConsumableArray(r) { return _arrayWithoutHoles(r) || _iterableToArray(r) || _unsupportedIterableToArray(r) || _nonIterableSpread(); }
function _nonIterableSpread() { throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method."); }
function _unsupportedIterableToArray(r, a) { if (r) { if ("string" == typeof r) return _arrayLikeToArray(r, a); var t = {}.toString.call(r).slice(8, -1); return "Object" === t && r.constructor && (t = r.constructor.name), "Map" === t || "Set" === t ? Array.from(r) : "Arguments" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0; } }
function _iterableToArray(r) { if ("undefined" != typeof Symbol && null != r[Symbol.iterator] || null != r["@@iterator"]) return Array.from(r); }
function _arrayWithoutHoles(r) { if (Array.isArray(r)) return _arrayLikeToArray(r); }
function _arrayLikeToArray(r, a) { (null == a || a > r.length) && (a = r.length); for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e]; return n; }
Vue.component("v-select", VueSelect.VueSelect);
new Vue({
  el: "#flashcardEdit",
  data: function data() {
    return {
      component: component,
      formData: {
        word: "",
        word_stress: "",
        meaning: "",
        word_type: [],
        audio: "",
        front_image: "",
        back_image: "",
        kanji_meaning: "",
        show: "1",
        examples: [],
        meaning_examples: [],
        quiz_questions: ["", "", "", ""]
      },
      wordTypes: [{
        id: 'Danh từ',
        text: 'Danh từ'
      }, {
        id: 'Động từ',
        text: 'Động từ'
      }, {
        id: 'Tính từ đuôi な',
        text: 'Tính từ đuôi な'
      }, {
        id: 'Tính từ đuôi い',
        text: 'Tính từ đuôi い'
      }, {
        id: 'Phó từ',
        text: 'Phó từ'
      }, {
        id: 'Liên từ',
        text: 'Liên từ'
      }, {
        id: 'Katakana',
        text: 'Katakana'
      }, {
        id: 'Số lượng từ',
        text: 'Số lượng từ'
      }, {
        id: 'Câu thông dụng',
        text: 'Câu thông dụng'
      }, {
        id: 'Chữ Hán',
        text: 'Chữ Hán'
      }, {
        id: 'Danh động từ',
        text: 'Danh động từ'
      }, {
        id: 'Trạng từ',
        text: 'Trạng từ'
      }],
      configCkEditor: {
        extraPlugins: "stylescombo,maximize,sourcearea,button,panelbutton,fakeobjects,justify,colorbutton,dialogui,dialog,flash,filetools,popup,filebrowser,font,table,image,furigana,panel,listblock,floatpanel,richcombo,format,resize,lineheight",
        fontSize_sizes: "8/8px;9/9px;10/10px;11/11px;12/12px;14/14px;16/16px;18/18px;20/20px;22/22px;24/24px;26/26px;28/28px;36/36px;48/48px;64/64px;72/72px;",
        allowedContent: {
          $1: {
            elements: CKEDITOR.dtd,
            attributes: true,
            styles: true,
            classes: true
          }
        },
        disallowedContent: "script;style; *[on*]",
        colorButton_colors: "EF6D13,FF0000,FF9900,FFFF00,008000,0000FF,800080,000000,FFFFFF,FFC0CB,A52A2A,808080,00FFFF,FFD700,FF1493,87CEEB,FF6347,40E0D0,EE82EE,F5DEB3,C0C0C0",
        stylesSet: [{
          name: "Overline Text",
          element: "span",
          attributes: {
            style: "text-decoration: overline;"
          }
        }]
      }
    };
  },
  mounted: function mounted() {
    this.initData();
    this.initCkEditor();
    this.initEventHandlers();
  },
  methods: {
    initData: function initData() {
      // Lấy dữ liệu từ component nếu có
      if (this.component) {
        if (this.component.value) {
          var flashcardData = JSON.parse(this.component.value);
          this.formData.word = flashcardData.word || "";
          this.formData.word_stress = flashcardData.word_stress || "";
          this.formData.meaning = flashcardData.meaning || "";
          this.formData.word_type = flashcardData.word_type ? flashcardData.word_type.split(', ') || [] : [];
          this.formData.audio = flashcardData.audio || "";
          this.formData.front_image = flashcardData.front_image || "";
          this.formData.back_image = flashcardData.back_image || "";
          this.formData.kanji_meaning = flashcardData.kanji_meaning || "";
          this.formData.show = flashcardData.show || "1";

          // Xử lý examples
          if (flashcardData.example && Array.isArray(flashcardData.example)) {
            this.formData.examples = flashcardData.example.map(function (item, index) {
              return {
                id: index + 1,
                example: item.example || "",
                audio: item.audio || ""
              };
            });
          }

          // Xử lý meaning_examples
          if (flashcardData.meaning_example && Array.isArray(flashcardData.meaning_example)) {
            this.formData.meaning_examples = flashcardData.meaning_example.map(function (item, index) {
              return {
                id: index + 1,
                content: item
              };
            });
          }

          // Xử lý quiz_questions
          if (flashcardData.quiz_question && Array.isArray(flashcardData.quiz_question)) {
            this.formData.quiz_questions = _toConsumableArray(flashcardData.quiz_question);
            // Đảm bảo luôn có 4 phần tử
            while (this.formData.quiz_questions.length < 4) {
              this.formData.quiz_questions.push("");
            }
          }
        }
      }
    },
    initCkEditor: function initCkEditor() {
      var _this = this;
      // Khởi tạo CKEditor cho các trường chính
      // Khởi tạo CKEditor cho tất cả các phần tử có class 'editor'
      $(".editor").each(function (index, element) {
        if (!CKEDITOR.instances[element.id]) {
          CKEDITOR.replace(element.id, _this.configCkEditor);
        }
      });
      this.$nextTick(function () {
        if (CKEDITOR.instances["word"]) {
          CKEDITOR.instances["word"].setData(_this.formData.word);
        }
        if (CKEDITOR.instances["word_stress"]) {
          CKEDITOR.instances["word_stress"].setData(_this.formData.word_stress);
        }
        if (CKEDITOR.instances["meaning"]) {
          CKEDITOR.instances["meaning"].setData(_this.formData.meaning);
        }

        // Khởi tạo CKEditor cho các ví dụ
        _this.formData.examples.forEach(function (item, index) {
          var editorId = "example-".concat(item.id);
          if (CKEDITOR.instances[editorId]) {
            CKEDITOR.instances[editorId].setData(item.example);
            CKEDITOR.instances[editorId].on("change", function () {
              var index = _this.formData.examples.findIndex(function (example) {
                return example.id === item.id;
              });
              Vue.set(_this.formData.examples, index, {
                id: item.id,
                example: CKEDITOR.instances[editorId].getData(),
                audio: item.audio
              });
            });
          }
        });

        // Khởi tạo CKEditor cho các nghĩa ví dụ
        _this.formData.meaning_examples.forEach(function (meaning, index) {
          var editorId = "meaning-example-".concat(meaning.id);
          if (CKEDITOR.instances[editorId]) {
            CKEDITOR.instances[editorId].setData(meaning.content);
            CKEDITOR.instances[editorId].on("change", function () {
              var index = _this.formData.meaning_examples.findIndex(function (item) {
                return item.id === meaning.id;
              });
              Vue.set(_this.formData.meaning_examples, index, {
                id: meaning.id,
                content: CKEDITOR.instances[editorId].getData()
              });
            });
          }
        });
      });
    },
    initEventHandlers: function initEventHandlers() {
      var _this2 = this;
      // Xử lý sự kiện cho CKEditor
      this.$nextTick(function () {
        if (CKEDITOR.instances["word"]) {
          CKEDITOR.instances["word"].on("change", function () {
            _this2.formData.word = CKEDITOR.instances["word"].getData();
          });
        }
        if (CKEDITOR.instances["word_stress"]) {
          CKEDITOR.instances["word_stress"].on("change", function () {
            _this2.formData.word_stress = CKEDITOR.instances["word_stress"].getData();
          });
        }
        if (CKEDITOR.instances["meaning"]) {
          CKEDITOR.instances["meaning"].on("change", function () {
            _this2.formData.meaning = CKEDITOR.instances["meaning"].getData();
          });
        }
      });
      $(document).on("click", ".btn-remove-row", function (e) {
        var id = $(e.target).closest(".example-group").data("id");
        _this2.formData.examples = _this2.formData.examples.filter(function (item) {
          return item.id !== id;
        });
        _this2.$emit("change", _this2.formData.examples);
        $(e.target).closest(".example-group").remove();
        _this2.$nextTick(function () {
          $(".example-group").map(function (index, element) {
            $(element).find(".example-label").text("Ví dụ " + (index + 1) + ":");
          });
        });
      });
      $(document).on("click", ".btn-remove-meaning", function (e) {
        var id = $(e.target).closest(".meaning-example-group").data("id");
        _this2.formData.meaning_examples = _this2.formData.meaning_examples.filter(function (item) {
          return item.id !== id;
        });
        $(e.target).closest(".meaning-example-group").remove();
        _this2.$nextTick(function () {
          $(".meaning-example-group").map(function (index, element) {
            $(element).find(".meaning-example-label").text("Ví dụ " + (index + 1) + ":");
          });
        });
      });
      $(document).on("click", ".btn-upload-file", function (e) {
        var file = $(e.target).data("id");
        $("#" + file).trigger("click");
      });
      $(document).on("change", ".upload-file", function (e) {
        var file = $(e.target).prop("files")[0];
        var type = $(e.target).data("type");
        var id = $(e.target).data("id");
        _this2.uploadFile(file, type, function (res) {
          $("#" + id).val(res.data.file_path);
          $("#" + id + "_file").val("");

          // Cập nhật dữ liệu vào formData
          if (id === "audio") {
            _this2.formData.audio = res.data.file_path;
          } else if (id === "front_image") {
            _this2.formData.front_image = res.data.file_path;
          } else if (id === "back_image") {
            _this2.formData.back_image = res.data.file_path;
          } else if (id.startsWith("audio_example_")) {
            var dataId = $(e.target).closest(".example-group").data("id");
            _this2.formData.examples.find(function (item) {
              return item.id === dataId;
            }).audio = res.data.file_path;
          }
        });
      });
      $("#add-example").click(function () {
        var id = Math.random().toString(36).substring(2, 15);
        _this2.formData.examples.push({
          id: id,
          example: "",
          audio: ""
        });
        var html = "\n          <div class=\"example-group mb-3\" data-id=\"".concat(id, "\">\n            <label class=\"example-label\">V\xED d\u1EE5 :</label>\n            <textarea class=\"form-control mb-2 editor\" id=\"example-").concat(id, "\" name=\"examples[]\"></textarea>\n            <div class=\"input-group my-2 flex\">\n              <div class=\"form-group\">\n                <label class=\"col-sm-2 control-label\">Audio</label>\n                <div class=\"col-sm-8\">\n                  <input type=\"text\" name=\"audio_examples[]\" placeholder=\"Nh\u1EA5p \u0111\u1EC3 ch\u1ECDn file\" readonly id=\"audio_example_").concat(id, "\" class=\"form-control\">\n                  <input type=\"file\" style=\"display: none\" accept=\"audio/*\" class=\"upload-file\" id=\"audio_example_").concat(id, "_file\" data-id=\"audio_example_").concat(id, "\" data-type=\"audio\">\n                </div>\n                <div class=\"col-sm-2\">\n                  <div class=\"flex\">\n                    <button type=\"button\" class=\"btn btn-primary btn-upload-file\" data-id=\"audio_example_").concat(id, "_file\">T\u1EA3i l\xEAn</button>\n                    <button type=\"button\" class=\"ml-1 btn btn-danger btn-remove-row\">X\xF3a</button>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        ");
        $("#examples-container").append(html);
        _this2.$nextTick(function () {
          $(".example-group").map(function (index, element) {
            $(element).find(".example-label").text("Ví dụ " + (index + 1) + ":");
          });
          CKEDITOR.replace("example-" + id, _this2.configCkEditor);
          CKEDITOR.instances["example-" + id].on("change", function () {
            var index = _this2.formData.examples.findIndex(function (item) {
              return item.id === id;
            });
            Vue.set(_this2.formData.examples, index, {
              id: id,
              example: CKEDITOR.instances["example-" + id].getData(),
              audio: ""
            });
            console.log(_this2.formData.examples);
          });
        });
      });
      $("#add-meaning-example").click(function () {
        var id = Math.random().toString(36).substring(2, 15);
        _this2.formData.meaning_examples.push({
          id: id,
          content: ""
        });
        var html = "\n          <div class=\"meaning-example-group mb-3\" data-id=\"".concat(id, "\">\n            <label class=\"meaning-example-label\">V\xED d\u1EE5 ").concat(id, ":</label>\n            <textarea class=\"form-control editor\" id=\"meaning-example-").concat(id, "\" name=\"meaning_examples[]\"></textarea>\n            <button type=\"button\" class=\"btn btn-danger btn-remove-meaning\">X\xF3a</button>\n          </div>\n        ");
        $("#meaning-examples-container").append(html);
        _this2.$nextTick(function () {
          $(".meaning-example-group").map(function (index, element) {
            $(element).find(".meaning-example-label").text("Ví dụ " + (index + 1) + ":");
          });
          CKEDITOR.replace("meaning-example-" + id, _this2.configCkEditor);
          CKEDITOR.instances["meaning-example-" + id].on("change", function () {
            var index = _this2.formData.meaning_examples.findIndex(function (item) {
              return item.id === id;
            });
            Vue.set(_this2.formData.meaning_examples, index, {
              id: id,
              content: CKEDITOR.instances["meaning-example-" + id].getData()
            });
          });
        });
      });
      $("#flashcardForm").submit(function (e) {
        e.preventDefault();
        var formData = new FormData();

        // Thêm dữ liệu vào FormData
        formData.append("lesson_id", $("#lesson_id").val());
        formData.append("word", _this2.formData.word);
        formData.append("word_stress", _this2.formData.word_stress);
        formData.append("meaning", _this2.formData.meaning);
        console.log(_this2.formData.word_type);
        formData.append("word_type", _this2.formData.word_type.join(', '));
        formData.append("audio", _this2.formData.audio);
        formData.append("front_image", _this2.formData.front_image);
        formData.append("back_image", _this2.formData.back_image);
        formData.append("kanji_meaning", _this2.formData.kanji_meaning);
        formData.append("show", _this2.formData.show);

        // Thêm quiz_questions
        _this2.formData.quiz_questions.forEach(function (question, index) {
          formData.append("quiz_questions[".concat(index, "]"), question);
        });

        // Thêm examples
        _this2.formData.examples.forEach(function (item, index) {
          formData.append("examples[".concat(index, "]"), item.example);
          formData.append("audio_examples[".concat(index, "]"), item.audio);
        });

        // Thêm meaning_examples
        _this2.formData.meaning_examples.forEach(function (meaning, index) {
          formData.append("meaning_examples[".concat(index, "]"), meaning.content);
        });
        $.ajax({
          url: "/backend/flashcard/" + (_this2.component ? _this2.component.id + "/update" : "create"),
          type: "POST",
          headers: {
            "X-CSRF-TOKEN": $('meta[name="csrf-token"]').attr("content")
          },
          data: formData,
          processData: false,
          contentType: false,
          success: function success(response) {
            if (response.success) {
              window.location.href = "/backend/flashcard";
            } else {
              alert(response.error);
            }
          },
          error: function error(res) {
            alert(res.responseJSON.error);
          }
        });
      });
    },
    uploadFile: function uploadFile(file, type, callback) {
      $.ajax({
        type: "get",
        url: "/backend/video/api/get-token",
        success: function success(token) {
          var formData = new FormData();
          formData.append("token", token);
          formData.append("file_upload", file);
          formData.append("type", type);
          $.ajax({
            type: "post",
            url: videoBaseURL + "/api/admin/upload-file",
            processData: false,
            contentType: false,
            data: formData,
            success: function success(data) {
              if (callback) {
                callback(data);
              }
            },
            error: function error() {
              alert("Lỗi upload file");
            }
          });
        }
      });
    }
  }
});
/******/ })()
;