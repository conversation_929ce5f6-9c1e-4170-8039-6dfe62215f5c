"use strict";var exam=new Vue({el:".main-exam-right",data:function(){return{onlineUsers:0,exams:enableExams,joinBtnState:{},currentTimes:{},showCurrentTimes:{},showCurrentDate:"--/--",vnTime:"00:00:00",course:{}}},created:function(){this.countDownVNTime(),this.refreshBtn()},methods:{joinRoom:function(e){var t="vi";"en"==getCookie("_lang")&&(t="en");var n=!1;if(null!=enableExams&&enableExams.forEach(function(r,i){if(r.course==e){if(null==r.access_code)return me.exam_id=r.id,me.course=e,me.lang=t,void(n=!0);var o=prompt("Bài thi yêu cầu mã truy cập","");if(null!=o&&o==r.access_code)return me.exam_id=r.id,me.course=e,me.lang=t,void(n=!0);alert("Mã truy cập không đúng")}}),1==n){var r=CryptoJS.AES.encrypt(JSON.stringify(me),"DUNGMORIJLPT");location.href=jlptUrl+"?"+r}},countDownVNTime:function(){var e,t=this;$.get(jlptUrl+"/get-current-time",function(n){e=parseInt(n),t.showCurrentDate=new Date(e).toLocaleDateString("en-GB",{timeZone:"Asia/Ho_Chi_Minh",day:"2-digit",month:"2-digit",year:"numeric"});setInterval(function(){t.vnTime=new Date(e).toLocaleTimeString("en-GB",{timeZone:"Asia/Ho_Chi_Minh"}),e+=1e3},1e3)})},refreshBtn:function(){var e=this;$.get(jlptUrl+"/get-current-time",function(t){console.log("giờ server: ",t);var n=parseInt(t),r=new Date(n),i=("0"+r.getDate(),"0"+(r.getMonth()+1),{}),o={},a=0,s={},u={},c={};for(var m in timeStarts)i[m]=1e3*timeStarts[m],o[m]=1e3*timeEnds[m],a++,u[m]=n,s[m]=1,c[m]="00:00:00";setInterval(function(){for(var t in i){if(u[t]<i[t]){s[t]=1;var n=new Date(u[t]),r=n.getHours(),a="0"+n.getMinutes(),m="0"+n.getSeconds();c[t]=r+":"+a.substr(-2)+":"+m.substr(-2)}else u[t]>=i[t]&&u[t]<o[t]?s[t]=2:s[t]=3;u[t]+=1e3}e.showCurrentTimes=c,e.currentTimes=u,e.joinBtnState=s},1e3)})},totalOnline:function(e){var t=this;if("N0"!=e)return t.onlineUsers[e];var n=Object.values(t.onlineUsers),r=0;return n.forEach(function(e){r+=e}),r},isEnableExam:function(e){var t=!1;return null!=enableExams&&enableExams.forEach(function(n,r){if(n.course==e)return void(t=!0)}),t}},mounted:function(){var e=this;$(".main-exam-right").css("opacity",1);var t={rejectUnauthorized:!1},n=io.connect(jlptSocket,{transports:["websocket"],query:"type=N0"},t);n.on("count",function(t){e.onlineUsers=t})}});