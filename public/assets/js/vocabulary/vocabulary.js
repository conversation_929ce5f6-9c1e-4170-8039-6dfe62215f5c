/*! For license information please see vocabulary.js.LICENSE.txt */
!function(){var t={2543:function(t,e,n){var r;t=n.nmd(t),function(){var a,i="Expected a function",o="__lodash_hash_undefined__",s="__lodash_placeholder__",c=16,u=32,l=64,f=128,d=256,h=1/0,p=9007199254740991,v=NaN,m=**********,g=[["ary",f],["bind",1],["bindKey",2],["curry",8],["curryRight",c],["flip",512],["partial",u],["partialRight",l],["rearg",d]],_="[object Arguments]",y="[object Array]",x="[object Boolean]",b="[object Date]",w="[object Error]",C="[object Function]",k="[object GeneratorFunction]",F="[object Map]",L="[object Number]",E="[object Object]",S="[object Promise]",j="[object RegExp]",D="[object Set]",O="[object String]",A="[object Symbol]",T="[object WeakMap]",I="[object ArrayBuffer]",P="[object DataView]",B="[object Float32Array]",R="[object Float64Array]",V="[object Int8Array]",z="[object Int16Array]",M="[object Int32Array]",N="[object Uint8Array]",U="[object Uint8ClampedArray]",$="[object Uint16Array]",H="[object Uint32Array]",q=/\b__p \+= '';/g,W=/\b(__p \+=) '' \+/g,Z=/(__e\(.*?\)|\b__t\)) \+\n'';/g,G=/&(?:amp|lt|gt|quot|#39);/g,J=/[&<>"']/g,Q=RegExp(G.source),Y=RegExp(J.source),K=/<%-([\s\S]+?)%>/g,X=/<%([\s\S]+?)%>/g,tt=/<%=([\s\S]+?)%>/g,et=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,nt=/^\w*$/,rt=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,at=/[\\^$.*+?()[\]{}|]/g,it=RegExp(at.source),ot=/^\s+/,st=/\s/,ct=/\{(?:\n\/\* \[wrapped with .+\] \*\/)?\n?/,ut=/\{\n\/\* \[wrapped with (.+)\] \*/,lt=/,? & /,ft=/[^\x00-\x2f\x3a-\x40\x5b-\x60\x7b-\x7f]+/g,dt=/[()=,{}\[\]\/\s]/,ht=/\\(\\)?/g,pt=/\$\{([^\\}]*(?:\\.[^\\}]*)*)\}/g,vt=/\w*$/,mt=/^[-+]0x[0-9a-f]+$/i,gt=/^0b[01]+$/i,_t=/^\[object .+?Constructor\]$/,yt=/^0o[0-7]+$/i,xt=/^(?:0|[1-9]\d*)$/,bt=/[\xc0-\xd6\xd8-\xf6\xf8-\xff\u0100-\u017f]/g,wt=/($^)/,Ct=/['\n\r\u2028\u2029\\]/g,kt="\\ud800-\\udfff",Ft="\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff",Lt="\\u2700-\\u27bf",Et="a-z\\xdf-\\xf6\\xf8-\\xff",St="A-Z\\xc0-\\xd6\\xd8-\\xde",jt="\\ufe0e\\ufe0f",Dt="\\xac\\xb1\\xd7\\xf7\\x00-\\x2f\\x3a-\\x40\\x5b-\\x60\\x7b-\\xbf\\u2000-\\u206f \\t\\x0b\\f\\xa0\\ufeff\\n\\r\\u2028\\u2029\\u1680\\u180e\\u2000\\u2001\\u2002\\u2003\\u2004\\u2005\\u2006\\u2007\\u2008\\u2009\\u200a\\u202f\\u205f\\u3000",Ot="['’]",At="["+kt+"]",Tt="["+Dt+"]",It="["+Ft+"]",Pt="\\d+",Bt="["+Lt+"]",Rt="["+Et+"]",Vt="[^"+kt+Dt+Pt+Lt+Et+St+"]",zt="\\ud83c[\\udffb-\\udfff]",Mt="[^"+kt+"]",Nt="(?:\\ud83c[\\udde6-\\uddff]){2}",Ut="[\\ud800-\\udbff][\\udc00-\\udfff]",$t="["+St+"]",Ht="\\u200d",qt="(?:"+Rt+"|"+Vt+")",Wt="(?:"+$t+"|"+Vt+")",Zt="(?:['’](?:d|ll|m|re|s|t|ve))?",Gt="(?:['’](?:D|LL|M|RE|S|T|VE))?",Jt="(?:"+It+"|"+zt+")"+"?",Qt="["+jt+"]?",Yt=Qt+Jt+("(?:"+Ht+"(?:"+[Mt,Nt,Ut].join("|")+")"+Qt+Jt+")*"),Kt="(?:"+[Bt,Nt,Ut].join("|")+")"+Yt,Xt="(?:"+[Mt+It+"?",It,Nt,Ut,At].join("|")+")",te=RegExp(Ot,"g"),ee=RegExp(It,"g"),ne=RegExp(zt+"(?="+zt+")|"+Xt+Yt,"g"),re=RegExp([$t+"?"+Rt+"+"+Zt+"(?="+[Tt,$t,"$"].join("|")+")",Wt+"+"+Gt+"(?="+[Tt,$t+qt,"$"].join("|")+")",$t+"?"+qt+"+"+Zt,$t+"+"+Gt,"\\d*(?:1ST|2ND|3RD|(?![123])\\dTH)(?=\\b|[a-z_])","\\d*(?:1st|2nd|3rd|(?![123])\\dth)(?=\\b|[A-Z_])",Pt,Kt].join("|"),"g"),ae=RegExp("["+Ht+kt+Ft+jt+"]"),ie=/[a-z][A-Z]|[A-Z]{2}[a-z]|[0-9][a-zA-Z]|[a-zA-Z][0-9]|[^a-zA-Z0-9 ]/,oe=["Array","Buffer","DataView","Date","Error","Float32Array","Float64Array","Function","Int8Array","Int16Array","Int32Array","Map","Math","Object","Promise","RegExp","Set","String","Symbol","TypeError","Uint8Array","Uint8ClampedArray","Uint16Array","Uint32Array","WeakMap","_","clearTimeout","isFinite","parseInt","setTimeout"],se=-1,ce={};ce[B]=ce[R]=ce[V]=ce[z]=ce[M]=ce[N]=ce[U]=ce[$]=ce[H]=!0,ce[_]=ce[y]=ce[I]=ce[x]=ce[P]=ce[b]=ce[w]=ce[C]=ce[F]=ce[L]=ce[E]=ce[j]=ce[D]=ce[O]=ce[T]=!1;var ue={};ue[_]=ue[y]=ue[I]=ue[P]=ue[x]=ue[b]=ue[B]=ue[R]=ue[V]=ue[z]=ue[M]=ue[F]=ue[L]=ue[E]=ue[j]=ue[D]=ue[O]=ue[A]=ue[N]=ue[U]=ue[$]=ue[H]=!0,ue[w]=ue[C]=ue[T]=!1;var le={"\\":"\\","'":"'","\n":"n","\r":"r","\u2028":"u2028","\u2029":"u2029"},fe=parseFloat,de=parseInt,he="object"==typeof n.g&&n.g&&n.g.Object===Object&&n.g,pe="object"==typeof self&&self&&self.Object===Object&&self,ve=he||pe||Function("return this")(),me=e&&!e.nodeType&&e,ge=me&&t&&!t.nodeType&&t,_e=ge&&ge.exports===me,ye=_e&&he.process,xe=function(){try{var t=ge&&ge.require&&ge.require("util").types;return t||ye&&ye.binding&&ye.binding("util")}catch(t){}}(),be=xe&&xe.isArrayBuffer,we=xe&&xe.isDate,Ce=xe&&xe.isMap,ke=xe&&xe.isRegExp,Fe=xe&&xe.isSet,Le=xe&&xe.isTypedArray;function Ee(t,e,n){switch(n.length){case 0:return t.call(e);case 1:return t.call(e,n[0]);case 2:return t.call(e,n[0],n[1]);case 3:return t.call(e,n[0],n[1],n[2])}return t.apply(e,n)}function Se(t,e,n,r){for(var a=-1,i=null==t?0:t.length;++a<i;){var o=t[a];e(r,o,n(o),t)}return r}function je(t,e){for(var n=-1,r=null==t?0:t.length;++n<r&&!1!==e(t[n],n,t););return t}function De(t,e){for(var n=null==t?0:t.length;n--&&!1!==e(t[n],n,t););return t}function Oe(t,e){for(var n=-1,r=null==t?0:t.length;++n<r;)if(!e(t[n],n,t))return!1;return!0}function Ae(t,e){for(var n=-1,r=null==t?0:t.length,a=0,i=[];++n<r;){var o=t[n];e(o,n,t)&&(i[a++]=o)}return i}function Te(t,e){return!!(null==t?0:t.length)&&$e(t,e,0)>-1}function Ie(t,e,n){for(var r=-1,a=null==t?0:t.length;++r<a;)if(n(e,t[r]))return!0;return!1}function Pe(t,e){for(var n=-1,r=null==t?0:t.length,a=Array(r);++n<r;)a[n]=e(t[n],n,t);return a}function Be(t,e){for(var n=-1,r=e.length,a=t.length;++n<r;)t[a+n]=e[n];return t}function Re(t,e,n,r){var a=-1,i=null==t?0:t.length;for(r&&i&&(n=t[++a]);++a<i;)n=e(n,t[a],a,t);return n}function Ve(t,e,n,r){var a=null==t?0:t.length;for(r&&a&&(n=t[--a]);a--;)n=e(n,t[a],a,t);return n}function ze(t,e){for(var n=-1,r=null==t?0:t.length;++n<r;)if(e(t[n],n,t))return!0;return!1}var Me=Ze("length");function Ne(t,e,n){var r;return n(t,(function(t,n,a){if(e(t,n,a))return r=n,!1})),r}function Ue(t,e,n,r){for(var a=t.length,i=n+(r?1:-1);r?i--:++i<a;)if(e(t[i],i,t))return i;return-1}function $e(t,e,n){return e==e?function(t,e,n){var r=n-1,a=t.length;for(;++r<a;)if(t[r]===e)return r;return-1}(t,e,n):Ue(t,qe,n)}function He(t,e,n,r){for(var a=n-1,i=t.length;++a<i;)if(r(t[a],e))return a;return-1}function qe(t){return t!=t}function We(t,e){var n=null==t?0:t.length;return n?Qe(t,e)/n:v}function Ze(t){return function(e){return null==e?a:e[t]}}function Ge(t){return function(e){return null==t?a:t[e]}}function Je(t,e,n,r,a){return a(t,(function(t,a,i){n=r?(r=!1,t):e(n,t,a,i)})),n}function Qe(t,e){for(var n,r=-1,i=t.length;++r<i;){var o=e(t[r]);o!==a&&(n=n===a?o:n+o)}return n}function Ye(t,e){for(var n=-1,r=Array(t);++n<t;)r[n]=e(n);return r}function Ke(t){return t?t.slice(0,mn(t)+1).replace(ot,""):t}function Xe(t){return function(e){return t(e)}}function tn(t,e){return Pe(e,(function(e){return t[e]}))}function en(t,e){return t.has(e)}function nn(t,e){for(var n=-1,r=t.length;++n<r&&$e(e,t[n],0)>-1;);return n}function rn(t,e){for(var n=t.length;n--&&$e(e,t[n],0)>-1;);return n}var an=Ge({"À":"A","Á":"A","Â":"A","Ã":"A","Ä":"A","Å":"A","à":"a","á":"a","â":"a","ã":"a","ä":"a","å":"a","Ç":"C","ç":"c","Ð":"D","ð":"d","È":"E","É":"E","Ê":"E","Ë":"E","è":"e","é":"e","ê":"e","ë":"e","Ì":"I","Í":"I","Î":"I","Ï":"I","ì":"i","í":"i","î":"i","ï":"i","Ñ":"N","ñ":"n","Ò":"O","Ó":"O","Ô":"O","Õ":"O","Ö":"O","Ø":"O","ò":"o","ó":"o","ô":"o","õ":"o","ö":"o","ø":"o","Ù":"U","Ú":"U","Û":"U","Ü":"U","ù":"u","ú":"u","û":"u","ü":"u","Ý":"Y","ý":"y","ÿ":"y","Æ":"Ae","æ":"ae","Þ":"Th","þ":"th","ß":"ss","Ā":"A","Ă":"A","Ą":"A","ā":"a","ă":"a","ą":"a","Ć":"C","Ĉ":"C","Ċ":"C","Č":"C","ć":"c","ĉ":"c","ċ":"c","č":"c","Ď":"D","Đ":"D","ď":"d","đ":"d","Ē":"E","Ĕ":"E","Ė":"E","Ę":"E","Ě":"E","ē":"e","ĕ":"e","ė":"e","ę":"e","ě":"e","Ĝ":"G","Ğ":"G","Ġ":"G","Ģ":"G","ĝ":"g","ğ":"g","ġ":"g","ģ":"g","Ĥ":"H","Ħ":"H","ĥ":"h","ħ":"h","Ĩ":"I","Ī":"I","Ĭ":"I","Į":"I","İ":"I","ĩ":"i","ī":"i","ĭ":"i","į":"i","ı":"i","Ĵ":"J","ĵ":"j","Ķ":"K","ķ":"k","ĸ":"k","Ĺ":"L","Ļ":"L","Ľ":"L","Ŀ":"L","Ł":"L","ĺ":"l","ļ":"l","ľ":"l","ŀ":"l","ł":"l","Ń":"N","Ņ":"N","Ň":"N","Ŋ":"N","ń":"n","ņ":"n","ň":"n","ŋ":"n","Ō":"O","Ŏ":"O","Ő":"O","ō":"o","ŏ":"o","ő":"o","Ŕ":"R","Ŗ":"R","Ř":"R","ŕ":"r","ŗ":"r","ř":"r","Ś":"S","Ŝ":"S","Ş":"S","Š":"S","ś":"s","ŝ":"s","ş":"s","š":"s","Ţ":"T","Ť":"T","Ŧ":"T","ţ":"t","ť":"t","ŧ":"t","Ũ":"U","Ū":"U","Ŭ":"U","Ů":"U","Ű":"U","Ų":"U","ũ":"u","ū":"u","ŭ":"u","ů":"u","ű":"u","ų":"u","Ŵ":"W","ŵ":"w","Ŷ":"Y","ŷ":"y","Ÿ":"Y","Ź":"Z","Ż":"Z","Ž":"Z","ź":"z","ż":"z","ž":"z","Ĳ":"IJ","ĳ":"ij","Œ":"Oe","œ":"oe","ŉ":"'n","ſ":"s"}),on=Ge({"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"});function sn(t){return"\\"+le[t]}function cn(t){return ae.test(t)}function un(t){var e=-1,n=Array(t.size);return t.forEach((function(t,r){n[++e]=[r,t]})),n}function ln(t,e){return function(n){return t(e(n))}}function fn(t,e){for(var n=-1,r=t.length,a=0,i=[];++n<r;){var o=t[n];o!==e&&o!==s||(t[n]=s,i[a++]=n)}return i}function dn(t){var e=-1,n=Array(t.size);return t.forEach((function(t){n[++e]=t})),n}function hn(t){var e=-1,n=Array(t.size);return t.forEach((function(t){n[++e]=[t,t]})),n}function pn(t){return cn(t)?function(t){var e=ne.lastIndex=0;for(;ne.test(t);)++e;return e}(t):Me(t)}function vn(t){return cn(t)?function(t){return t.match(ne)||[]}(t):function(t){return t.split("")}(t)}function mn(t){for(var e=t.length;e--&&st.test(t.charAt(e)););return e}var gn=Ge({"&amp;":"&","&lt;":"<","&gt;":">","&quot;":'"',"&#39;":"'"});var _n=function t(e){var n,r=(e=null==e?ve:_n.defaults(ve.Object(),e,_n.pick(ve,oe))).Array,st=e.Date,kt=e.Error,Ft=e.Function,Lt=e.Math,Et=e.Object,St=e.RegExp,jt=e.String,Dt=e.TypeError,Ot=r.prototype,At=Ft.prototype,Tt=Et.prototype,It=e["__core-js_shared__"],Pt=At.toString,Bt=Tt.hasOwnProperty,Rt=0,Vt=(n=/[^.]+$/.exec(It&&It.keys&&It.keys.IE_PROTO||""))?"Symbol(src)_1."+n:"",zt=Tt.toString,Mt=Pt.call(Et),Nt=ve._,Ut=St("^"+Pt.call(Bt).replace(at,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$"),$t=_e?e.Buffer:a,Ht=e.Symbol,qt=e.Uint8Array,Wt=$t?$t.allocUnsafe:a,Zt=ln(Et.getPrototypeOf,Et),Gt=Et.create,Jt=Tt.propertyIsEnumerable,Qt=Ot.splice,Yt=Ht?Ht.isConcatSpreadable:a,Kt=Ht?Ht.iterator:a,Xt=Ht?Ht.toStringTag:a,ne=function(){try{var t=di(Et,"defineProperty");return t({},"",{}),t}catch(t){}}(),ae=e.clearTimeout!==ve.clearTimeout&&e.clearTimeout,le=st&&st.now!==ve.Date.now&&st.now,he=e.setTimeout!==ve.setTimeout&&e.setTimeout,pe=Lt.ceil,me=Lt.floor,ge=Et.getOwnPropertySymbols,ye=$t?$t.isBuffer:a,xe=e.isFinite,Me=Ot.join,Ge=ln(Et.keys,Et),yn=Lt.max,xn=Lt.min,bn=st.now,wn=e.parseInt,Cn=Lt.random,kn=Ot.reverse,Fn=di(e,"DataView"),Ln=di(e,"Map"),En=di(e,"Promise"),Sn=di(e,"Set"),jn=di(e,"WeakMap"),Dn=di(Et,"create"),On=jn&&new jn,An={},Tn=Vi(Fn),In=Vi(Ln),Pn=Vi(En),Bn=Vi(Sn),Rn=Vi(jn),Vn=Ht?Ht.prototype:a,zn=Vn?Vn.valueOf:a,Mn=Vn?Vn.toString:a;function Nn(t){if(ns(t)&&!qo(t)&&!(t instanceof qn)){if(t instanceof Hn)return t;if(Bt.call(t,"__wrapped__"))return zi(t)}return new Hn(t)}var Un=function(){function t(){}return function(e){if(!es(e))return{};if(Gt)return Gt(e);t.prototype=e;var n=new t;return t.prototype=a,n}}();function $n(){}function Hn(t,e){this.__wrapped__=t,this.__actions__=[],this.__chain__=!!e,this.__index__=0,this.__values__=a}function qn(t){this.__wrapped__=t,this.__actions__=[],this.__dir__=1,this.__filtered__=!1,this.__iteratees__=[],this.__takeCount__=m,this.__views__=[]}function Wn(t){var e=-1,n=null==t?0:t.length;for(this.clear();++e<n;){var r=t[e];this.set(r[0],r[1])}}function Zn(t){var e=-1,n=null==t?0:t.length;for(this.clear();++e<n;){var r=t[e];this.set(r[0],r[1])}}function Gn(t){var e=-1,n=null==t?0:t.length;for(this.clear();++e<n;){var r=t[e];this.set(r[0],r[1])}}function Jn(t){var e=-1,n=null==t?0:t.length;for(this.__data__=new Gn;++e<n;)this.add(t[e])}function Qn(t){var e=this.__data__=new Zn(t);this.size=e.size}function Yn(t,e){var n=qo(t),r=!n&&Ho(t),a=!n&&!r&&Jo(t),i=!n&&!r&&!a&&ls(t),o=n||r||a||i,s=o?Ye(t.length,jt):[],c=s.length;for(var u in t)!e&&!Bt.call(t,u)||o&&("length"==u||a&&("offset"==u||"parent"==u)||i&&("buffer"==u||"byteLength"==u||"byteOffset"==u)||yi(u,c))||s.push(u);return s}function Kn(t){var e=t.length;return e?t[Jr(0,e-1)]:a}function Xn(t,e){return Pi(Da(t),cr(e,0,t.length))}function tr(t){return Pi(Da(t))}function er(t,e,n){(n!==a&&!No(t[e],n)||n===a&&!(e in t))&&or(t,e,n)}function nr(t,e,n){var r=t[e];Bt.call(t,e)&&No(r,n)&&(n!==a||e in t)||or(t,e,n)}function rr(t,e){for(var n=t.length;n--;)if(No(t[n][0],e))return n;return-1}function ar(t,e,n,r){return hr(t,(function(t,a,i){e(r,t,n(t),i)})),r}function ir(t,e){return t&&Oa(e,As(e),t)}function or(t,e,n){"__proto__"==e&&ne?ne(t,e,{configurable:!0,enumerable:!0,value:n,writable:!0}):t[e]=n}function sr(t,e){for(var n=-1,i=e.length,o=r(i),s=null==t;++n<i;)o[n]=s?a:Es(t,e[n]);return o}function cr(t,e,n){return t==t&&(n!==a&&(t=t<=n?t:n),e!==a&&(t=t>=e?t:e)),t}function ur(t,e,n,r,i,o){var s,c=1&e,u=2&e,l=4&e;if(n&&(s=i?n(t,r,i,o):n(t)),s!==a)return s;if(!es(t))return t;var f=qo(t);if(f){if(s=function(t){var e=t.length,n=new t.constructor(e);e&&"string"==typeof t[0]&&Bt.call(t,"index")&&(n.index=t.index,n.input=t.input);return n}(t),!c)return Da(t,s)}else{var d=vi(t),h=d==C||d==k;if(Jo(t))return ka(t,c);if(d==E||d==_||h&&!i){if(s=u||h?{}:gi(t),!c)return u?function(t,e){return Oa(t,pi(t),e)}(t,function(t,e){return t&&Oa(e,Ts(e),t)}(s,t)):function(t,e){return Oa(t,hi(t),e)}(t,ir(s,t))}else{if(!ue[d])return i?t:{};s=function(t,e,n){var r=t.constructor;switch(e){case I:return Fa(t);case x:case b:return new r(+t);case P:return function(t,e){var n=e?Fa(t.buffer):t.buffer;return new t.constructor(n,t.byteOffset,t.byteLength)}(t,n);case B:case R:case V:case z:case M:case N:case U:case $:case H:return La(t,n);case F:return new r;case L:case O:return new r(t);case j:return function(t){var e=new t.constructor(t.source,vt.exec(t));return e.lastIndex=t.lastIndex,e}(t);case D:return new r;case A:return a=t,zn?Et(zn.call(a)):{}}var a}(t,d,c)}}o||(o=new Qn);var p=o.get(t);if(p)return p;o.set(t,s),ss(t)?t.forEach((function(r){s.add(ur(r,e,n,r,t,o))})):rs(t)&&t.forEach((function(r,a){s.set(a,ur(r,e,n,a,t,o))}));var v=f?a:(l?u?ii:ai:u?Ts:As)(t);return je(v||t,(function(r,a){v&&(r=t[a=r]),nr(s,a,ur(r,e,n,a,t,o))})),s}function lr(t,e,n){var r=n.length;if(null==t)return!r;for(t=Et(t);r--;){var i=n[r],o=e[i],s=t[i];if(s===a&&!(i in t)||!o(s))return!1}return!0}function fr(t,e,n){if("function"!=typeof t)throw new Dt(i);return Oi((function(){t.apply(a,n)}),e)}function dr(t,e,n,r){var a=-1,i=Te,o=!0,s=t.length,c=[],u=e.length;if(!s)return c;n&&(e=Pe(e,Xe(n))),r?(i=Ie,o=!1):e.length>=200&&(i=en,o=!1,e=new Jn(e));t:for(;++a<s;){var l=t[a],f=null==n?l:n(l);if(l=r||0!==l?l:0,o&&f==f){for(var d=u;d--;)if(e[d]===f)continue t;c.push(l)}else i(e,f,r)||c.push(l)}return c}Nn.templateSettings={escape:K,evaluate:X,interpolate:tt,variable:"",imports:{_:Nn}},Nn.prototype=$n.prototype,Nn.prototype.constructor=Nn,Hn.prototype=Un($n.prototype),Hn.prototype.constructor=Hn,qn.prototype=Un($n.prototype),qn.prototype.constructor=qn,Wn.prototype.clear=function(){this.__data__=Dn?Dn(null):{},this.size=0},Wn.prototype.delete=function(t){var e=this.has(t)&&delete this.__data__[t];return this.size-=e?1:0,e},Wn.prototype.get=function(t){var e=this.__data__;if(Dn){var n=e[t];return n===o?a:n}return Bt.call(e,t)?e[t]:a},Wn.prototype.has=function(t){var e=this.__data__;return Dn?e[t]!==a:Bt.call(e,t)},Wn.prototype.set=function(t,e){var n=this.__data__;return this.size+=this.has(t)?0:1,n[t]=Dn&&e===a?o:e,this},Zn.prototype.clear=function(){this.__data__=[],this.size=0},Zn.prototype.delete=function(t){var e=this.__data__,n=rr(e,t);return!(n<0)&&(n==e.length-1?e.pop():Qt.call(e,n,1),--this.size,!0)},Zn.prototype.get=function(t){var e=this.__data__,n=rr(e,t);return n<0?a:e[n][1]},Zn.prototype.has=function(t){return rr(this.__data__,t)>-1},Zn.prototype.set=function(t,e){var n=this.__data__,r=rr(n,t);return r<0?(++this.size,n.push([t,e])):n[r][1]=e,this},Gn.prototype.clear=function(){this.size=0,this.__data__={hash:new Wn,map:new(Ln||Zn),string:new Wn}},Gn.prototype.delete=function(t){var e=li(this,t).delete(t);return this.size-=e?1:0,e},Gn.prototype.get=function(t){return li(this,t).get(t)},Gn.prototype.has=function(t){return li(this,t).has(t)},Gn.prototype.set=function(t,e){var n=li(this,t),r=n.size;return n.set(t,e),this.size+=n.size==r?0:1,this},Jn.prototype.add=Jn.prototype.push=function(t){return this.__data__.set(t,o),this},Jn.prototype.has=function(t){return this.__data__.has(t)},Qn.prototype.clear=function(){this.__data__=new Zn,this.size=0},Qn.prototype.delete=function(t){var e=this.__data__,n=e.delete(t);return this.size=e.size,n},Qn.prototype.get=function(t){return this.__data__.get(t)},Qn.prototype.has=function(t){return this.__data__.has(t)},Qn.prototype.set=function(t,e){var n=this.__data__;if(n instanceof Zn){var r=n.__data__;if(!Ln||r.length<199)return r.push([t,e]),this.size=++n.size,this;n=this.__data__=new Gn(r)}return n.set(t,e),this.size=n.size,this};var hr=Ia(br),pr=Ia(wr,!0);function vr(t,e){var n=!0;return hr(t,(function(t,r,a){return n=!!e(t,r,a)})),n}function mr(t,e,n){for(var r=-1,i=t.length;++r<i;){var o=t[r],s=e(o);if(null!=s&&(c===a?s==s&&!us(s):n(s,c)))var c=s,u=o}return u}function gr(t,e){var n=[];return hr(t,(function(t,r,a){e(t,r,a)&&n.push(t)})),n}function _r(t,e,n,r,a){var i=-1,o=t.length;for(n||(n=_i),a||(a=[]);++i<o;){var s=t[i];e>0&&n(s)?e>1?_r(s,e-1,n,r,a):Be(a,s):r||(a[a.length]=s)}return a}var yr=Pa(),xr=Pa(!0);function br(t,e){return t&&yr(t,e,As)}function wr(t,e){return t&&xr(t,e,As)}function Cr(t,e){return Ae(e,(function(e){return Ko(t[e])}))}function kr(t,e){for(var n=0,r=(e=xa(e,t)).length;null!=t&&n<r;)t=t[Ri(e[n++])];return n&&n==r?t:a}function Fr(t,e,n){var r=e(t);return qo(t)?r:Be(r,n(t))}function Lr(t){return null==t?t===a?"[object Undefined]":"[object Null]":Xt&&Xt in Et(t)?function(t){var e=Bt.call(t,Xt),n=t[Xt];try{t[Xt]=a;var r=!0}catch(t){}var i=zt.call(t);r&&(e?t[Xt]=n:delete t[Xt]);return i}(t):function(t){return zt.call(t)}(t)}function Er(t,e){return t>e}function Sr(t,e){return null!=t&&Bt.call(t,e)}function jr(t,e){return null!=t&&e in Et(t)}function Dr(t,e,n){for(var i=n?Ie:Te,o=t[0].length,s=t.length,c=s,u=r(s),l=1/0,f=[];c--;){var d=t[c];c&&e&&(d=Pe(d,Xe(e))),l=xn(d.length,l),u[c]=!n&&(e||o>=120&&d.length>=120)?new Jn(c&&d):a}d=t[0];var h=-1,p=u[0];t:for(;++h<o&&f.length<l;){var v=d[h],m=e?e(v):v;if(v=n||0!==v?v:0,!(p?en(p,m):i(f,m,n))){for(c=s;--c;){var g=u[c];if(!(g?en(g,m):i(t[c],m,n)))continue t}p&&p.push(m),f.push(v)}}return f}function Or(t,e,n){var r=null==(t=Si(t,e=xa(e,t)))?t:t[Ri(Qi(e))];return null==r?a:Ee(r,t,n)}function Ar(t){return ns(t)&&Lr(t)==_}function Tr(t,e,n,r,i){return t===e||(null==t||null==e||!ns(t)&&!ns(e)?t!=t&&e!=e:function(t,e,n,r,i,o){var s=qo(t),c=qo(e),u=s?y:vi(t),l=c?y:vi(e),f=(u=u==_?E:u)==E,d=(l=l==_?E:l)==E,h=u==l;if(h&&Jo(t)){if(!Jo(e))return!1;s=!0,f=!1}if(h&&!f)return o||(o=new Qn),s||ls(t)?ni(t,e,n,r,i,o):function(t,e,n,r,a,i,o){switch(n){case P:if(t.byteLength!=e.byteLength||t.byteOffset!=e.byteOffset)return!1;t=t.buffer,e=e.buffer;case I:return!(t.byteLength!=e.byteLength||!i(new qt(t),new qt(e)));case x:case b:case L:return No(+t,+e);case w:return t.name==e.name&&t.message==e.message;case j:case O:return t==e+"";case F:var s=un;case D:var c=1&r;if(s||(s=dn),t.size!=e.size&&!c)return!1;var u=o.get(t);if(u)return u==e;r|=2,o.set(t,e);var l=ni(s(t),s(e),r,a,i,o);return o.delete(t),l;case A:if(zn)return zn.call(t)==zn.call(e)}return!1}(t,e,u,n,r,i,o);if(!(1&n)){var p=f&&Bt.call(t,"__wrapped__"),v=d&&Bt.call(e,"__wrapped__");if(p||v){var m=p?t.value():t,g=v?e.value():e;return o||(o=new Qn),i(m,g,n,r,o)}}if(!h)return!1;return o||(o=new Qn),function(t,e,n,r,i,o){var s=1&n,c=ai(t),u=c.length,l=ai(e),f=l.length;if(u!=f&&!s)return!1;var d=u;for(;d--;){var h=c[d];if(!(s?h in e:Bt.call(e,h)))return!1}var p=o.get(t),v=o.get(e);if(p&&v)return p==e&&v==t;var m=!0;o.set(t,e),o.set(e,t);var g=s;for(;++d<u;){var _=t[h=c[d]],y=e[h];if(r)var x=s?r(y,_,h,e,t,o):r(_,y,h,t,e,o);if(!(x===a?_===y||i(_,y,n,r,o):x)){m=!1;break}g||(g="constructor"==h)}if(m&&!g){var b=t.constructor,w=e.constructor;b==w||!("constructor"in t)||!("constructor"in e)||"function"==typeof b&&b instanceof b&&"function"==typeof w&&w instanceof w||(m=!1)}return o.delete(t),o.delete(e),m}(t,e,n,r,i,o)}(t,e,n,r,Tr,i))}function Ir(t,e,n,r){var i=n.length,o=i,s=!r;if(null==t)return!o;for(t=Et(t);i--;){var c=n[i];if(s&&c[2]?c[1]!==t[c[0]]:!(c[0]in t))return!1}for(;++i<o;){var u=(c=n[i])[0],l=t[u],f=c[1];if(s&&c[2]){if(l===a&&!(u in t))return!1}else{var d=new Qn;if(r)var h=r(l,f,u,t,e,d);if(!(h===a?Tr(f,l,3,r,d):h))return!1}}return!0}function Pr(t){return!(!es(t)||(e=t,Vt&&Vt in e))&&(Ko(t)?Ut:_t).test(Vi(t));var e}function Br(t){return"function"==typeof t?t:null==t?ac:"object"==typeof t?qo(t)?Ur(t[0],t[1]):Nr(t):hc(t)}function Rr(t){if(!ki(t))return Ge(t);var e=[];for(var n in Et(t))Bt.call(t,n)&&"constructor"!=n&&e.push(n);return e}function Vr(t){if(!es(t))return function(t){var e=[];if(null!=t)for(var n in Et(t))e.push(n);return e}(t);var e=ki(t),n=[];for(var r in t)("constructor"!=r||!e&&Bt.call(t,r))&&n.push(r);return n}function zr(t,e){return t<e}function Mr(t,e){var n=-1,a=Zo(t)?r(t.length):[];return hr(t,(function(t,r,i){a[++n]=e(t,r,i)})),a}function Nr(t){var e=fi(t);return 1==e.length&&e[0][2]?Li(e[0][0],e[0][1]):function(n){return n===t||Ir(n,t,e)}}function Ur(t,e){return bi(t)&&Fi(e)?Li(Ri(t),e):function(n){var r=Es(n,t);return r===a&&r===e?Ss(n,t):Tr(e,r,3)}}function $r(t,e,n,r,i){t!==e&&yr(e,(function(o,s){if(i||(i=new Qn),es(o))!function(t,e,n,r,i,o,s){var c=ji(t,n),u=ji(e,n),l=s.get(u);if(l)return void er(t,n,l);var f=o?o(c,u,n+"",t,e,s):a,d=f===a;if(d){var h=qo(u),p=!h&&Jo(u),v=!h&&!p&&ls(u);f=u,h||p||v?qo(c)?f=c:Go(c)?f=Da(c):p?(d=!1,f=ka(u,!0)):v?(d=!1,f=La(u,!0)):f=[]:is(u)||Ho(u)?(f=c,Ho(c)?f=_s(c):es(c)&&!Ko(c)||(f=gi(u))):d=!1}d&&(s.set(u,f),i(f,u,r,o,s),s.delete(u));er(t,n,f)}(t,e,s,n,$r,r,i);else{var c=r?r(ji(t,s),o,s+"",t,e,i):a;c===a&&(c=o),er(t,s,c)}}),Ts)}function Hr(t,e){var n=t.length;if(n)return yi(e+=e<0?n:0,n)?t[e]:a}function qr(t,e,n){e=e.length?Pe(e,(function(t){return qo(t)?function(e){return kr(e,1===t.length?t[0]:t)}:t})):[ac];var r=-1;e=Pe(e,Xe(ui()));var a=Mr(t,(function(t,n,a){var i=Pe(e,(function(e){return e(t)}));return{criteria:i,index:++r,value:t}}));return function(t,e){var n=t.length;for(t.sort(e);n--;)t[n]=t[n].value;return t}(a,(function(t,e){return function(t,e,n){var r=-1,a=t.criteria,i=e.criteria,o=a.length,s=n.length;for(;++r<o;){var c=Ea(a[r],i[r]);if(c)return r>=s?c:c*("desc"==n[r]?-1:1)}return t.index-e.index}(t,e,n)}))}function Wr(t,e,n){for(var r=-1,a=e.length,i={};++r<a;){var o=e[r],s=kr(t,o);n(s,o)&&ta(i,xa(o,t),s)}return i}function Zr(t,e,n,r){var a=r?He:$e,i=-1,o=e.length,s=t;for(t===e&&(e=Da(e)),n&&(s=Pe(t,Xe(n)));++i<o;)for(var c=0,u=e[i],l=n?n(u):u;(c=a(s,l,c,r))>-1;)s!==t&&Qt.call(s,c,1),Qt.call(t,c,1);return t}function Gr(t,e){for(var n=t?e.length:0,r=n-1;n--;){var a=e[n];if(n==r||a!==i){var i=a;yi(a)?Qt.call(t,a,1):da(t,a)}}return t}function Jr(t,e){return t+me(Cn()*(e-t+1))}function Qr(t,e){var n="";if(!t||e<1||e>p)return n;do{e%2&&(n+=t),(e=me(e/2))&&(t+=t)}while(e);return n}function Yr(t,e){return Ai(Ei(t,e,ac),t+"")}function Kr(t){return Kn(Ns(t))}function Xr(t,e){var n=Ns(t);return Pi(n,cr(e,0,n.length))}function ta(t,e,n,r){if(!es(t))return t;for(var i=-1,o=(e=xa(e,t)).length,s=o-1,c=t;null!=c&&++i<o;){var u=Ri(e[i]),l=n;if("__proto__"===u||"constructor"===u||"prototype"===u)return t;if(i!=s){var f=c[u];(l=r?r(f,u,c):a)===a&&(l=es(f)?f:yi(e[i+1])?[]:{})}nr(c,u,l),c=c[u]}return t}var ea=On?function(t,e){return On.set(t,e),t}:ac,na=ne?function(t,e){return ne(t,"toString",{configurable:!0,enumerable:!1,value:ec(e),writable:!0})}:ac;function ra(t){return Pi(Ns(t))}function aa(t,e,n){var a=-1,i=t.length;e<0&&(e=-e>i?0:i+e),(n=n>i?i:n)<0&&(n+=i),i=e>n?0:n-e>>>0,e>>>=0;for(var o=r(i);++a<i;)o[a]=t[a+e];return o}function ia(t,e){var n;return hr(t,(function(t,r,a){return!(n=e(t,r,a))})),!!n}function oa(t,e,n){var r=0,a=null==t?r:t.length;if("number"==typeof e&&e==e&&a<=2147483647){for(;r<a;){var i=r+a>>>1,o=t[i];null!==o&&!us(o)&&(n?o<=e:o<e)?r=i+1:a=i}return a}return sa(t,e,ac,n)}function sa(t,e,n,r){var i=0,o=null==t?0:t.length;if(0===o)return 0;for(var s=(e=n(e))!=e,c=null===e,u=us(e),l=e===a;i<o;){var f=me((i+o)/2),d=n(t[f]),h=d!==a,p=null===d,v=d==d,m=us(d);if(s)var g=r||v;else g=l?v&&(r||h):c?v&&h&&(r||!p):u?v&&h&&!p&&(r||!m):!p&&!m&&(r?d<=e:d<e);g?i=f+1:o=f}return xn(o,4294967294)}function ca(t,e){for(var n=-1,r=t.length,a=0,i=[];++n<r;){var o=t[n],s=e?e(o):o;if(!n||!No(s,c)){var c=s;i[a++]=0===o?0:o}}return i}function ua(t){return"number"==typeof t?t:us(t)?v:+t}function la(t){if("string"==typeof t)return t;if(qo(t))return Pe(t,la)+"";if(us(t))return Mn?Mn.call(t):"";var e=t+"";return"0"==e&&1/t==-1/0?"-0":e}function fa(t,e,n){var r=-1,a=Te,i=t.length,o=!0,s=[],c=s;if(n)o=!1,a=Ie;else if(i>=200){var u=e?null:Qa(t);if(u)return dn(u);o=!1,a=en,c=new Jn}else c=e?[]:s;t:for(;++r<i;){var l=t[r],f=e?e(l):l;if(l=n||0!==l?l:0,o&&f==f){for(var d=c.length;d--;)if(c[d]===f)continue t;e&&c.push(f),s.push(l)}else a(c,f,n)||(c!==s&&c.push(f),s.push(l))}return s}function da(t,e){return null==(t=Si(t,e=xa(e,t)))||delete t[Ri(Qi(e))]}function ha(t,e,n,r){return ta(t,e,n(kr(t,e)),r)}function pa(t,e,n,r){for(var a=t.length,i=r?a:-1;(r?i--:++i<a)&&e(t[i],i,t););return n?aa(t,r?0:i,r?i+1:a):aa(t,r?i+1:0,r?a:i)}function va(t,e){var n=t;return n instanceof qn&&(n=n.value()),Re(e,(function(t,e){return e.func.apply(e.thisArg,Be([t],e.args))}),n)}function ma(t,e,n){var a=t.length;if(a<2)return a?fa(t[0]):[];for(var i=-1,o=r(a);++i<a;)for(var s=t[i],c=-1;++c<a;)c!=i&&(o[i]=dr(o[i]||s,t[c],e,n));return fa(_r(o,1),e,n)}function ga(t,e,n){for(var r=-1,i=t.length,o=e.length,s={};++r<i;){var c=r<o?e[r]:a;n(s,t[r],c)}return s}function _a(t){return Go(t)?t:[]}function ya(t){return"function"==typeof t?t:ac}function xa(t,e){return qo(t)?t:bi(t,e)?[t]:Bi(ys(t))}var ba=Yr;function wa(t,e,n){var r=t.length;return n=n===a?r:n,!e&&n>=r?t:aa(t,e,n)}var Ca=ae||function(t){return ve.clearTimeout(t)};function ka(t,e){if(e)return t.slice();var n=t.length,r=Wt?Wt(n):new t.constructor(n);return t.copy(r),r}function Fa(t){var e=new t.constructor(t.byteLength);return new qt(e).set(new qt(t)),e}function La(t,e){var n=e?Fa(t.buffer):t.buffer;return new t.constructor(n,t.byteOffset,t.length)}function Ea(t,e){if(t!==e){var n=t!==a,r=null===t,i=t==t,o=us(t),s=e!==a,c=null===e,u=e==e,l=us(e);if(!c&&!l&&!o&&t>e||o&&s&&u&&!c&&!l||r&&s&&u||!n&&u||!i)return 1;if(!r&&!o&&!l&&t<e||l&&n&&i&&!r&&!o||c&&n&&i||!s&&i||!u)return-1}return 0}function Sa(t,e,n,a){for(var i=-1,o=t.length,s=n.length,c=-1,u=e.length,l=yn(o-s,0),f=r(u+l),d=!a;++c<u;)f[c]=e[c];for(;++i<s;)(d||i<o)&&(f[n[i]]=t[i]);for(;l--;)f[c++]=t[i++];return f}function ja(t,e,n,a){for(var i=-1,o=t.length,s=-1,c=n.length,u=-1,l=e.length,f=yn(o-c,0),d=r(f+l),h=!a;++i<f;)d[i]=t[i];for(var p=i;++u<l;)d[p+u]=e[u];for(;++s<c;)(h||i<o)&&(d[p+n[s]]=t[i++]);return d}function Da(t,e){var n=-1,a=t.length;for(e||(e=r(a));++n<a;)e[n]=t[n];return e}function Oa(t,e,n,r){var i=!n;n||(n={});for(var o=-1,s=e.length;++o<s;){var c=e[o],u=r?r(n[c],t[c],c,n,t):a;u===a&&(u=t[c]),i?or(n,c,u):nr(n,c,u)}return n}function Aa(t,e){return function(n,r){var a=qo(n)?Se:ar,i=e?e():{};return a(n,t,ui(r,2),i)}}function Ta(t){return Yr((function(e,n){var r=-1,i=n.length,o=i>1?n[i-1]:a,s=i>2?n[2]:a;for(o=t.length>3&&"function"==typeof o?(i--,o):a,s&&xi(n[0],n[1],s)&&(o=i<3?a:o,i=1),e=Et(e);++r<i;){var c=n[r];c&&t(e,c,r,o)}return e}))}function Ia(t,e){return function(n,r){if(null==n)return n;if(!Zo(n))return t(n,r);for(var a=n.length,i=e?a:-1,o=Et(n);(e?i--:++i<a)&&!1!==r(o[i],i,o););return n}}function Pa(t){return function(e,n,r){for(var a=-1,i=Et(e),o=r(e),s=o.length;s--;){var c=o[t?s:++a];if(!1===n(i[c],c,i))break}return e}}function Ba(t){return function(e){var n=cn(e=ys(e))?vn(e):a,r=n?n[0]:e.charAt(0),i=n?wa(n,1).join(""):e.slice(1);return r[t]()+i}}function Ra(t){return function(e){return Re(Ks(Hs(e).replace(te,"")),t,"")}}function Va(t){return function(){var e=arguments;switch(e.length){case 0:return new t;case 1:return new t(e[0]);case 2:return new t(e[0],e[1]);case 3:return new t(e[0],e[1],e[2]);case 4:return new t(e[0],e[1],e[2],e[3]);case 5:return new t(e[0],e[1],e[2],e[3],e[4]);case 6:return new t(e[0],e[1],e[2],e[3],e[4],e[5]);case 7:return new t(e[0],e[1],e[2],e[3],e[4],e[5],e[6])}var n=Un(t.prototype),r=t.apply(n,e);return es(r)?r:n}}function za(t){return function(e,n,r){var i=Et(e);if(!Zo(e)){var o=ui(n,3);e=As(e),n=function(t){return o(i[t],t,i)}}var s=t(e,n,r);return s>-1?i[o?e[s]:s]:a}}function Ma(t){return ri((function(e){var n=e.length,r=n,o=Hn.prototype.thru;for(t&&e.reverse();r--;){var s=e[r];if("function"!=typeof s)throw new Dt(i);if(o&&!c&&"wrapper"==si(s))var c=new Hn([],!0)}for(r=c?r:n;++r<n;){var u=si(s=e[r]),l="wrapper"==u?oi(s):a;c=l&&wi(l[0])&&424==l[1]&&!l[4].length&&1==l[9]?c[si(l[0])].apply(c,l[3]):1==s.length&&wi(s)?c[u]():c.thru(s)}return function(){var t=arguments,r=t[0];if(c&&1==t.length&&qo(r))return c.plant(r).value();for(var a=0,i=n?e[a].apply(this,t):r;++a<n;)i=e[a].call(this,i);return i}}))}function Na(t,e,n,i,o,s,c,u,l,d){var h=e&f,p=1&e,v=2&e,m=24&e,g=512&e,_=v?a:Va(t);return function f(){for(var y=arguments.length,x=r(y),b=y;b--;)x[b]=arguments[b];if(m)var w=ci(f),C=function(t,e){for(var n=t.length,r=0;n--;)t[n]===e&&++r;return r}(x,w);if(i&&(x=Sa(x,i,o,m)),s&&(x=ja(x,s,c,m)),y-=C,m&&y<d){var k=fn(x,w);return Ga(t,e,Na,f.placeholder,n,x,k,u,l,d-y)}var F=p?n:this,L=v?F[t]:t;return y=x.length,u?x=function(t,e){var n=t.length,r=xn(e.length,n),i=Da(t);for(;r--;){var o=e[r];t[r]=yi(o,n)?i[o]:a}return t}(x,u):g&&y>1&&x.reverse(),h&&l<y&&(x.length=l),this&&this!==ve&&this instanceof f&&(L=_||Va(L)),L.apply(F,x)}}function Ua(t,e){return function(n,r){return function(t,e,n,r){return br(t,(function(t,a,i){e(r,n(t),a,i)})),r}(n,t,e(r),{})}}function $a(t,e){return function(n,r){var i;if(n===a&&r===a)return e;if(n!==a&&(i=n),r!==a){if(i===a)return r;"string"==typeof n||"string"==typeof r?(n=la(n),r=la(r)):(n=ua(n),r=ua(r)),i=t(n,r)}return i}}function Ha(t){return ri((function(e){return e=Pe(e,Xe(ui())),Yr((function(n){var r=this;return t(e,(function(t){return Ee(t,r,n)}))}))}))}function qa(t,e){var n=(e=e===a?" ":la(e)).length;if(n<2)return n?Qr(e,t):e;var r=Qr(e,pe(t/pn(e)));return cn(e)?wa(vn(r),0,t).join(""):r.slice(0,t)}function Wa(t){return function(e,n,i){return i&&"number"!=typeof i&&xi(e,n,i)&&(n=i=a),e=ps(e),n===a?(n=e,e=0):n=ps(n),function(t,e,n,a){for(var i=-1,o=yn(pe((e-t)/(n||1)),0),s=r(o);o--;)s[a?o:++i]=t,t+=n;return s}(e,n,i=i===a?e<n?1:-1:ps(i),t)}}function Za(t){return function(e,n){return"string"==typeof e&&"string"==typeof n||(e=gs(e),n=gs(n)),t(e,n)}}function Ga(t,e,n,r,i,o,s,c,f,d){var h=8&e;e|=h?u:l,4&(e&=~(h?l:u))||(e&=-4);var p=[t,e,i,h?o:a,h?s:a,h?a:o,h?a:s,c,f,d],v=n.apply(a,p);return wi(t)&&Di(v,p),v.placeholder=r,Ti(v,t,e)}function Ja(t){var e=Lt[t];return function(t,n){if(t=gs(t),(n=null==n?0:xn(vs(n),292))&&xe(t)){var r=(ys(t)+"e").split("e");return+((r=(ys(e(r[0]+"e"+(+r[1]+n)))+"e").split("e"))[0]+"e"+(+r[1]-n))}return e(t)}}var Qa=Sn&&1/dn(new Sn([,-0]))[1]==h?function(t){return new Sn(t)}:uc;function Ya(t){return function(e){var n=vi(e);return n==F?un(e):n==D?hn(e):function(t,e){return Pe(e,(function(e){return[e,t[e]]}))}(e,t(e))}}function Ka(t,e,n,o,h,p,v,m){var g=2&e;if(!g&&"function"!=typeof t)throw new Dt(i);var _=o?o.length:0;if(_||(e&=-97,o=h=a),v=v===a?v:yn(vs(v),0),m=m===a?m:vs(m),_-=h?h.length:0,e&l){var y=o,x=h;o=h=a}var b=g?a:oi(t),w=[t,e,n,o,h,y,x,p,v,m];if(b&&function(t,e){var n=t[1],r=e[1],a=n|r,i=a<131,o=r==f&&8==n||r==f&&n==d&&t[7].length<=e[8]||384==r&&e[7].length<=e[8]&&8==n;if(!i&&!o)return t;1&r&&(t[2]=e[2],a|=1&n?0:4);var c=e[3];if(c){var u=t[3];t[3]=u?Sa(u,c,e[4]):c,t[4]=u?fn(t[3],s):e[4]}(c=e[5])&&(u=t[5],t[5]=u?ja(u,c,e[6]):c,t[6]=u?fn(t[5],s):e[6]);(c=e[7])&&(t[7]=c);r&f&&(t[8]=null==t[8]?e[8]:xn(t[8],e[8]));null==t[9]&&(t[9]=e[9]);t[0]=e[0],t[1]=a}(w,b),t=w[0],e=w[1],n=w[2],o=w[3],h=w[4],!(m=w[9]=w[9]===a?g?0:t.length:yn(w[9]-_,0))&&24&e&&(e&=-25),e&&1!=e)C=8==e||e==c?function(t,e,n){var i=Va(t);return function o(){for(var s=arguments.length,c=r(s),u=s,l=ci(o);u--;)c[u]=arguments[u];var f=s<3&&c[0]!==l&&c[s-1]!==l?[]:fn(c,l);return(s-=f.length)<n?Ga(t,e,Na,o.placeholder,a,c,f,a,a,n-s):Ee(this&&this!==ve&&this instanceof o?i:t,this,c)}}(t,e,m):e!=u&&33!=e||h.length?Na.apply(a,w):function(t,e,n,a){var i=1&e,o=Va(t);return function e(){for(var s=-1,c=arguments.length,u=-1,l=a.length,f=r(l+c),d=this&&this!==ve&&this instanceof e?o:t;++u<l;)f[u]=a[u];for(;c--;)f[u++]=arguments[++s];return Ee(d,i?n:this,f)}}(t,e,n,o);else var C=function(t,e,n){var r=1&e,a=Va(t);return function e(){return(this&&this!==ve&&this instanceof e?a:t).apply(r?n:this,arguments)}}(t,e,n);return Ti((b?ea:Di)(C,w),t,e)}function Xa(t,e,n,r){return t===a||No(t,Tt[n])&&!Bt.call(r,n)?e:t}function ti(t,e,n,r,i,o){return es(t)&&es(e)&&(o.set(e,t),$r(t,e,a,ti,o),o.delete(e)),t}function ei(t){return is(t)?a:t}function ni(t,e,n,r,i,o){var s=1&n,c=t.length,u=e.length;if(c!=u&&!(s&&u>c))return!1;var l=o.get(t),f=o.get(e);if(l&&f)return l==e&&f==t;var d=-1,h=!0,p=2&n?new Jn:a;for(o.set(t,e),o.set(e,t);++d<c;){var v=t[d],m=e[d];if(r)var g=s?r(m,v,d,e,t,o):r(v,m,d,t,e,o);if(g!==a){if(g)continue;h=!1;break}if(p){if(!ze(e,(function(t,e){if(!en(p,e)&&(v===t||i(v,t,n,r,o)))return p.push(e)}))){h=!1;break}}else if(v!==m&&!i(v,m,n,r,o)){h=!1;break}}return o.delete(t),o.delete(e),h}function ri(t){return Ai(Ei(t,a,qi),t+"")}function ai(t){return Fr(t,As,hi)}function ii(t){return Fr(t,Ts,pi)}var oi=On?function(t){return On.get(t)}:uc;function si(t){for(var e=t.name+"",n=An[e],r=Bt.call(An,e)?n.length:0;r--;){var a=n[r],i=a.func;if(null==i||i==t)return a.name}return e}function ci(t){return(Bt.call(Nn,"placeholder")?Nn:t).placeholder}function ui(){var t=Nn.iteratee||ic;return t=t===ic?Br:t,arguments.length?t(arguments[0],arguments[1]):t}function li(t,e){var n,r,a=t.__data__;return("string"==(r=typeof(n=e))||"number"==r||"symbol"==r||"boolean"==r?"__proto__"!==n:null===n)?a["string"==typeof e?"string":"hash"]:a.map}function fi(t){for(var e=As(t),n=e.length;n--;){var r=e[n],a=t[r];e[n]=[r,a,Fi(a)]}return e}function di(t,e){var n=function(t,e){return null==t?a:t[e]}(t,e);return Pr(n)?n:a}var hi=ge?function(t){return null==t?[]:(t=Et(t),Ae(ge(t),(function(e){return Jt.call(t,e)})))}:mc,pi=ge?function(t){for(var e=[];t;)Be(e,hi(t)),t=Zt(t);return e}:mc,vi=Lr;function mi(t,e,n){for(var r=-1,a=(e=xa(e,t)).length,i=!1;++r<a;){var o=Ri(e[r]);if(!(i=null!=t&&n(t,o)))break;t=t[o]}return i||++r!=a?i:!!(a=null==t?0:t.length)&&ts(a)&&yi(o,a)&&(qo(t)||Ho(t))}function gi(t){return"function"!=typeof t.constructor||ki(t)?{}:Un(Zt(t))}function _i(t){return qo(t)||Ho(t)||!!(Yt&&t&&t[Yt])}function yi(t,e){var n=typeof t;return!!(e=null==e?p:e)&&("number"==n||"symbol"!=n&&xt.test(t))&&t>-1&&t%1==0&&t<e}function xi(t,e,n){if(!es(n))return!1;var r=typeof e;return!!("number"==r?Zo(n)&&yi(e,n.length):"string"==r&&e in n)&&No(n[e],t)}function bi(t,e){if(qo(t))return!1;var n=typeof t;return!("number"!=n&&"symbol"!=n&&"boolean"!=n&&null!=t&&!us(t))||(nt.test(t)||!et.test(t)||null!=e&&t in Et(e))}function wi(t){var e=si(t),n=Nn[e];if("function"!=typeof n||!(e in qn.prototype))return!1;if(t===n)return!0;var r=oi(n);return!!r&&t===r[0]}(Fn&&vi(new Fn(new ArrayBuffer(1)))!=P||Ln&&vi(new Ln)!=F||En&&vi(En.resolve())!=S||Sn&&vi(new Sn)!=D||jn&&vi(new jn)!=T)&&(vi=function(t){var e=Lr(t),n=e==E?t.constructor:a,r=n?Vi(n):"";if(r)switch(r){case Tn:return P;case In:return F;case Pn:return S;case Bn:return D;case Rn:return T}return e});var Ci=It?Ko:gc;function ki(t){var e=t&&t.constructor;return t===("function"==typeof e&&e.prototype||Tt)}function Fi(t){return t==t&&!es(t)}function Li(t,e){return function(n){return null!=n&&(n[t]===e&&(e!==a||t in Et(n)))}}function Ei(t,e,n){return e=yn(e===a?t.length-1:e,0),function(){for(var a=arguments,i=-1,o=yn(a.length-e,0),s=r(o);++i<o;)s[i]=a[e+i];i=-1;for(var c=r(e+1);++i<e;)c[i]=a[i];return c[e]=n(s),Ee(t,this,c)}}function Si(t,e){return e.length<2?t:kr(t,aa(e,0,-1))}function ji(t,e){if(("constructor"!==e||"function"!=typeof t[e])&&"__proto__"!=e)return t[e]}var Di=Ii(ea),Oi=he||function(t,e){return ve.setTimeout(t,e)},Ai=Ii(na);function Ti(t,e,n){var r=e+"";return Ai(t,function(t,e){var n=e.length;if(!n)return t;var r=n-1;return e[r]=(n>1?"& ":"")+e[r],e=e.join(n>2?", ":" "),t.replace(ct,"{\n/* [wrapped with "+e+"] */\n")}(r,function(t,e){return je(g,(function(n){var r="_."+n[0];e&n[1]&&!Te(t,r)&&t.push(r)})),t.sort()}(function(t){var e=t.match(ut);return e?e[1].split(lt):[]}(r),n)))}function Ii(t){var e=0,n=0;return function(){var r=bn(),i=16-(r-n);if(n=r,i>0){if(++e>=800)return arguments[0]}else e=0;return t.apply(a,arguments)}}function Pi(t,e){var n=-1,r=t.length,i=r-1;for(e=e===a?r:e;++n<e;){var o=Jr(n,i),s=t[o];t[o]=t[n],t[n]=s}return t.length=e,t}var Bi=function(t){var e=Po(t,(function(t){return 500===n.size&&n.clear(),t})),n=e.cache;return e}((function(t){var e=[];return 46===t.charCodeAt(0)&&e.push(""),t.replace(rt,(function(t,n,r,a){e.push(r?a.replace(ht,"$1"):n||t)})),e}));function Ri(t){if("string"==typeof t||us(t))return t;var e=t+"";return"0"==e&&1/t==-1/0?"-0":e}function Vi(t){if(null!=t){try{return Pt.call(t)}catch(t){}try{return t+""}catch(t){}}return""}function zi(t){if(t instanceof qn)return t.clone();var e=new Hn(t.__wrapped__,t.__chain__);return e.__actions__=Da(t.__actions__),e.__index__=t.__index__,e.__values__=t.__values__,e}var Mi=Yr((function(t,e){return Go(t)?dr(t,_r(e,1,Go,!0)):[]})),Ni=Yr((function(t,e){var n=Qi(e);return Go(n)&&(n=a),Go(t)?dr(t,_r(e,1,Go,!0),ui(n,2)):[]})),Ui=Yr((function(t,e){var n=Qi(e);return Go(n)&&(n=a),Go(t)?dr(t,_r(e,1,Go,!0),a,n):[]}));function $i(t,e,n){var r=null==t?0:t.length;if(!r)return-1;var a=null==n?0:vs(n);return a<0&&(a=yn(r+a,0)),Ue(t,ui(e,3),a)}function Hi(t,e,n){var r=null==t?0:t.length;if(!r)return-1;var i=r-1;return n!==a&&(i=vs(n),i=n<0?yn(r+i,0):xn(i,r-1)),Ue(t,ui(e,3),i,!0)}function qi(t){return(null==t?0:t.length)?_r(t,1):[]}function Wi(t){return t&&t.length?t[0]:a}var Zi=Yr((function(t){var e=Pe(t,_a);return e.length&&e[0]===t[0]?Dr(e):[]})),Gi=Yr((function(t){var e=Qi(t),n=Pe(t,_a);return e===Qi(n)?e=a:n.pop(),n.length&&n[0]===t[0]?Dr(n,ui(e,2)):[]})),Ji=Yr((function(t){var e=Qi(t),n=Pe(t,_a);return(e="function"==typeof e?e:a)&&n.pop(),n.length&&n[0]===t[0]?Dr(n,a,e):[]}));function Qi(t){var e=null==t?0:t.length;return e?t[e-1]:a}var Yi=Yr(Ki);function Ki(t,e){return t&&t.length&&e&&e.length?Zr(t,e):t}var Xi=ri((function(t,e){var n=null==t?0:t.length,r=sr(t,e);return Gr(t,Pe(e,(function(t){return yi(t,n)?+t:t})).sort(Ea)),r}));function to(t){return null==t?t:kn.call(t)}var eo=Yr((function(t){return fa(_r(t,1,Go,!0))})),no=Yr((function(t){var e=Qi(t);return Go(e)&&(e=a),fa(_r(t,1,Go,!0),ui(e,2))})),ro=Yr((function(t){var e=Qi(t);return e="function"==typeof e?e:a,fa(_r(t,1,Go,!0),a,e)}));function ao(t){if(!t||!t.length)return[];var e=0;return t=Ae(t,(function(t){if(Go(t))return e=yn(t.length,e),!0})),Ye(e,(function(e){return Pe(t,Ze(e))}))}function io(t,e){if(!t||!t.length)return[];var n=ao(t);return null==e?n:Pe(n,(function(t){return Ee(e,a,t)}))}var oo=Yr((function(t,e){return Go(t)?dr(t,e):[]})),so=Yr((function(t){return ma(Ae(t,Go))})),co=Yr((function(t){var e=Qi(t);return Go(e)&&(e=a),ma(Ae(t,Go),ui(e,2))})),uo=Yr((function(t){var e=Qi(t);return e="function"==typeof e?e:a,ma(Ae(t,Go),a,e)})),lo=Yr(ao);var fo=Yr((function(t){var e=t.length,n=e>1?t[e-1]:a;return n="function"==typeof n?(t.pop(),n):a,io(t,n)}));function ho(t){var e=Nn(t);return e.__chain__=!0,e}function po(t,e){return e(t)}var vo=ri((function(t){var e=t.length,n=e?t[0]:0,r=this.__wrapped__,i=function(e){return sr(e,t)};return!(e>1||this.__actions__.length)&&r instanceof qn&&yi(n)?((r=r.slice(n,+n+(e?1:0))).__actions__.push({func:po,args:[i],thisArg:a}),new Hn(r,this.__chain__).thru((function(t){return e&&!t.length&&t.push(a),t}))):this.thru(i)}));var mo=Aa((function(t,e,n){Bt.call(t,n)?++t[n]:or(t,n,1)}));var go=za($i),_o=za(Hi);function yo(t,e){return(qo(t)?je:hr)(t,ui(e,3))}function xo(t,e){return(qo(t)?De:pr)(t,ui(e,3))}var bo=Aa((function(t,e,n){Bt.call(t,n)?t[n].push(e):or(t,n,[e])}));var wo=Yr((function(t,e,n){var a=-1,i="function"==typeof e,o=Zo(t)?r(t.length):[];return hr(t,(function(t){o[++a]=i?Ee(e,t,n):Or(t,e,n)})),o})),Co=Aa((function(t,e,n){or(t,n,e)}));function ko(t,e){return(qo(t)?Pe:Mr)(t,ui(e,3))}var Fo=Aa((function(t,e,n){t[n?0:1].push(e)}),(function(){return[[],[]]}));var Lo=Yr((function(t,e){if(null==t)return[];var n=e.length;return n>1&&xi(t,e[0],e[1])?e=[]:n>2&&xi(e[0],e[1],e[2])&&(e=[e[0]]),qr(t,_r(e,1),[])})),Eo=le||function(){return ve.Date.now()};function So(t,e,n){return e=n?a:e,e=t&&null==e?t.length:e,Ka(t,f,a,a,a,a,e)}function jo(t,e){var n;if("function"!=typeof e)throw new Dt(i);return t=vs(t),function(){return--t>0&&(n=e.apply(this,arguments)),t<=1&&(e=a),n}}var Do=Yr((function(t,e,n){var r=1;if(n.length){var a=fn(n,ci(Do));r|=u}return Ka(t,r,e,n,a)})),Oo=Yr((function(t,e,n){var r=3;if(n.length){var a=fn(n,ci(Oo));r|=u}return Ka(e,r,t,n,a)}));function Ao(t,e,n){var r,o,s,c,u,l,f=0,d=!1,h=!1,p=!0;if("function"!=typeof t)throw new Dt(i);function v(e){var n=r,i=o;return r=o=a,f=e,c=t.apply(i,n)}function m(t){var n=t-l;return l===a||n>=e||n<0||h&&t-f>=s}function g(){var t=Eo();if(m(t))return _(t);u=Oi(g,function(t){var n=e-(t-l);return h?xn(n,s-(t-f)):n}(t))}function _(t){return u=a,p&&r?v(t):(r=o=a,c)}function y(){var t=Eo(),n=m(t);if(r=arguments,o=this,l=t,n){if(u===a)return function(t){return f=t,u=Oi(g,e),d?v(t):c}(l);if(h)return Ca(u),u=Oi(g,e),v(l)}return u===a&&(u=Oi(g,e)),c}return e=gs(e)||0,es(n)&&(d=!!n.leading,s=(h="maxWait"in n)?yn(gs(n.maxWait)||0,e):s,p="trailing"in n?!!n.trailing:p),y.cancel=function(){u!==a&&Ca(u),f=0,r=l=o=u=a},y.flush=function(){return u===a?c:_(Eo())},y}var To=Yr((function(t,e){return fr(t,1,e)})),Io=Yr((function(t,e,n){return fr(t,gs(e)||0,n)}));function Po(t,e){if("function"!=typeof t||null!=e&&"function"!=typeof e)throw new Dt(i);var n=function(){var r=arguments,a=e?e.apply(this,r):r[0],i=n.cache;if(i.has(a))return i.get(a);var o=t.apply(this,r);return n.cache=i.set(a,o)||i,o};return n.cache=new(Po.Cache||Gn),n}function Bo(t){if("function"!=typeof t)throw new Dt(i);return function(){var e=arguments;switch(e.length){case 0:return!t.call(this);case 1:return!t.call(this,e[0]);case 2:return!t.call(this,e[0],e[1]);case 3:return!t.call(this,e[0],e[1],e[2])}return!t.apply(this,e)}}Po.Cache=Gn;var Ro=ba((function(t,e){var n=(e=1==e.length&&qo(e[0])?Pe(e[0],Xe(ui())):Pe(_r(e,1),Xe(ui()))).length;return Yr((function(r){for(var a=-1,i=xn(r.length,n);++a<i;)r[a]=e[a].call(this,r[a]);return Ee(t,this,r)}))})),Vo=Yr((function(t,e){var n=fn(e,ci(Vo));return Ka(t,u,a,e,n)})),zo=Yr((function(t,e){var n=fn(e,ci(zo));return Ka(t,l,a,e,n)})),Mo=ri((function(t,e){return Ka(t,d,a,a,a,e)}));function No(t,e){return t===e||t!=t&&e!=e}var Uo=Za(Er),$o=Za((function(t,e){return t>=e})),Ho=Ar(function(){return arguments}())?Ar:function(t){return ns(t)&&Bt.call(t,"callee")&&!Jt.call(t,"callee")},qo=r.isArray,Wo=be?Xe(be):function(t){return ns(t)&&Lr(t)==I};function Zo(t){return null!=t&&ts(t.length)&&!Ko(t)}function Go(t){return ns(t)&&Zo(t)}var Jo=ye||gc,Qo=we?Xe(we):function(t){return ns(t)&&Lr(t)==b};function Yo(t){if(!ns(t))return!1;var e=Lr(t);return e==w||"[object DOMException]"==e||"string"==typeof t.message&&"string"==typeof t.name&&!is(t)}function Ko(t){if(!es(t))return!1;var e=Lr(t);return e==C||e==k||"[object AsyncFunction]"==e||"[object Proxy]"==e}function Xo(t){return"number"==typeof t&&t==vs(t)}function ts(t){return"number"==typeof t&&t>-1&&t%1==0&&t<=p}function es(t){var e=typeof t;return null!=t&&("object"==e||"function"==e)}function ns(t){return null!=t&&"object"==typeof t}var rs=Ce?Xe(Ce):function(t){return ns(t)&&vi(t)==F};function as(t){return"number"==typeof t||ns(t)&&Lr(t)==L}function is(t){if(!ns(t)||Lr(t)!=E)return!1;var e=Zt(t);if(null===e)return!0;var n=Bt.call(e,"constructor")&&e.constructor;return"function"==typeof n&&n instanceof n&&Pt.call(n)==Mt}var os=ke?Xe(ke):function(t){return ns(t)&&Lr(t)==j};var ss=Fe?Xe(Fe):function(t){return ns(t)&&vi(t)==D};function cs(t){return"string"==typeof t||!qo(t)&&ns(t)&&Lr(t)==O}function us(t){return"symbol"==typeof t||ns(t)&&Lr(t)==A}var ls=Le?Xe(Le):function(t){return ns(t)&&ts(t.length)&&!!ce[Lr(t)]};var fs=Za(zr),ds=Za((function(t,e){return t<=e}));function hs(t){if(!t)return[];if(Zo(t))return cs(t)?vn(t):Da(t);if(Kt&&t[Kt])return function(t){for(var e,n=[];!(e=t.next()).done;)n.push(e.value);return n}(t[Kt]());var e=vi(t);return(e==F?un:e==D?dn:Ns)(t)}function ps(t){return t?(t=gs(t))===h||t===-1/0?17976931348623157e292*(t<0?-1:1):t==t?t:0:0===t?t:0}function vs(t){var e=ps(t),n=e%1;return e==e?n?e-n:e:0}function ms(t){return t?cr(vs(t),0,m):0}function gs(t){if("number"==typeof t)return t;if(us(t))return v;if(es(t)){var e="function"==typeof t.valueOf?t.valueOf():t;t=es(e)?e+"":e}if("string"!=typeof t)return 0===t?t:+t;t=Ke(t);var n=gt.test(t);return n||yt.test(t)?de(t.slice(2),n?2:8):mt.test(t)?v:+t}function _s(t){return Oa(t,Ts(t))}function ys(t){return null==t?"":la(t)}var xs=Ta((function(t,e){if(ki(e)||Zo(e))Oa(e,As(e),t);else for(var n in e)Bt.call(e,n)&&nr(t,n,e[n])})),bs=Ta((function(t,e){Oa(e,Ts(e),t)})),ws=Ta((function(t,e,n,r){Oa(e,Ts(e),t,r)})),Cs=Ta((function(t,e,n,r){Oa(e,As(e),t,r)})),ks=ri(sr);var Fs=Yr((function(t,e){t=Et(t);var n=-1,r=e.length,i=r>2?e[2]:a;for(i&&xi(e[0],e[1],i)&&(r=1);++n<r;)for(var o=e[n],s=Ts(o),c=-1,u=s.length;++c<u;){var l=s[c],f=t[l];(f===a||No(f,Tt[l])&&!Bt.call(t,l))&&(t[l]=o[l])}return t})),Ls=Yr((function(t){return t.push(a,ti),Ee(Ps,a,t)}));function Es(t,e,n){var r=null==t?a:kr(t,e);return r===a?n:r}function Ss(t,e){return null!=t&&mi(t,e,jr)}var js=Ua((function(t,e,n){null!=e&&"function"!=typeof e.toString&&(e=zt.call(e)),t[e]=n}),ec(ac)),Ds=Ua((function(t,e,n){null!=e&&"function"!=typeof e.toString&&(e=zt.call(e)),Bt.call(t,e)?t[e].push(n):t[e]=[n]}),ui),Os=Yr(Or);function As(t){return Zo(t)?Yn(t):Rr(t)}function Ts(t){return Zo(t)?Yn(t,!0):Vr(t)}var Is=Ta((function(t,e,n){$r(t,e,n)})),Ps=Ta((function(t,e,n,r){$r(t,e,n,r)})),Bs=ri((function(t,e){var n={};if(null==t)return n;var r=!1;e=Pe(e,(function(e){return e=xa(e,t),r||(r=e.length>1),e})),Oa(t,ii(t),n),r&&(n=ur(n,7,ei));for(var a=e.length;a--;)da(n,e[a]);return n}));var Rs=ri((function(t,e){return null==t?{}:function(t,e){return Wr(t,e,(function(e,n){return Ss(t,n)}))}(t,e)}));function Vs(t,e){if(null==t)return{};var n=Pe(ii(t),(function(t){return[t]}));return e=ui(e),Wr(t,n,(function(t,n){return e(t,n[0])}))}var zs=Ya(As),Ms=Ya(Ts);function Ns(t){return null==t?[]:tn(t,As(t))}var Us=Ra((function(t,e,n){return e=e.toLowerCase(),t+(n?$s(e):e)}));function $s(t){return Ys(ys(t).toLowerCase())}function Hs(t){return(t=ys(t))&&t.replace(bt,an).replace(ee,"")}var qs=Ra((function(t,e,n){return t+(n?"-":"")+e.toLowerCase()})),Ws=Ra((function(t,e,n){return t+(n?" ":"")+e.toLowerCase()})),Zs=Ba("toLowerCase");var Gs=Ra((function(t,e,n){return t+(n?"_":"")+e.toLowerCase()}));var Js=Ra((function(t,e,n){return t+(n?" ":"")+Ys(e)}));var Qs=Ra((function(t,e,n){return t+(n?" ":"")+e.toUpperCase()})),Ys=Ba("toUpperCase");function Ks(t,e,n){return t=ys(t),(e=n?a:e)===a?function(t){return ie.test(t)}(t)?function(t){return t.match(re)||[]}(t):function(t){return t.match(ft)||[]}(t):t.match(e)||[]}var Xs=Yr((function(t,e){try{return Ee(t,a,e)}catch(t){return Yo(t)?t:new kt(t)}})),tc=ri((function(t,e){return je(e,(function(e){e=Ri(e),or(t,e,Do(t[e],t))})),t}));function ec(t){return function(){return t}}var nc=Ma(),rc=Ma(!0);function ac(t){return t}function ic(t){return Br("function"==typeof t?t:ur(t,1))}var oc=Yr((function(t,e){return function(n){return Or(n,t,e)}})),sc=Yr((function(t,e){return function(n){return Or(t,n,e)}}));function cc(t,e,n){var r=As(e),a=Cr(e,r);null!=n||es(e)&&(a.length||!r.length)||(n=e,e=t,t=this,a=Cr(e,As(e)));var i=!(es(n)&&"chain"in n&&!n.chain),o=Ko(t);return je(a,(function(n){var r=e[n];t[n]=r,o&&(t.prototype[n]=function(){var e=this.__chain__;if(i||e){var n=t(this.__wrapped__);return(n.__actions__=Da(this.__actions__)).push({func:r,args:arguments,thisArg:t}),n.__chain__=e,n}return r.apply(t,Be([this.value()],arguments))})})),t}function uc(){}var lc=Ha(Pe),fc=Ha(Oe),dc=Ha(ze);function hc(t){return bi(t)?Ze(Ri(t)):function(t){return function(e){return kr(e,t)}}(t)}var pc=Wa(),vc=Wa(!0);function mc(){return[]}function gc(){return!1}var _c=$a((function(t,e){return t+e}),0),yc=Ja("ceil"),xc=$a((function(t,e){return t/e}),1),bc=Ja("floor");var wc,Cc=$a((function(t,e){return t*e}),1),kc=Ja("round"),Fc=$a((function(t,e){return t-e}),0);return Nn.after=function(t,e){if("function"!=typeof e)throw new Dt(i);return t=vs(t),function(){if(--t<1)return e.apply(this,arguments)}},Nn.ary=So,Nn.assign=xs,Nn.assignIn=bs,Nn.assignInWith=ws,Nn.assignWith=Cs,Nn.at=ks,Nn.before=jo,Nn.bind=Do,Nn.bindAll=tc,Nn.bindKey=Oo,Nn.castArray=function(){if(!arguments.length)return[];var t=arguments[0];return qo(t)?t:[t]},Nn.chain=ho,Nn.chunk=function(t,e,n){e=(n?xi(t,e,n):e===a)?1:yn(vs(e),0);var i=null==t?0:t.length;if(!i||e<1)return[];for(var o=0,s=0,c=r(pe(i/e));o<i;)c[s++]=aa(t,o,o+=e);return c},Nn.compact=function(t){for(var e=-1,n=null==t?0:t.length,r=0,a=[];++e<n;){var i=t[e];i&&(a[r++]=i)}return a},Nn.concat=function(){var t=arguments.length;if(!t)return[];for(var e=r(t-1),n=arguments[0],a=t;a--;)e[a-1]=arguments[a];return Be(qo(n)?Da(n):[n],_r(e,1))},Nn.cond=function(t){var e=null==t?0:t.length,n=ui();return t=e?Pe(t,(function(t){if("function"!=typeof t[1])throw new Dt(i);return[n(t[0]),t[1]]})):[],Yr((function(n){for(var r=-1;++r<e;){var a=t[r];if(Ee(a[0],this,n))return Ee(a[1],this,n)}}))},Nn.conforms=function(t){return function(t){var e=As(t);return function(n){return lr(n,t,e)}}(ur(t,1))},Nn.constant=ec,Nn.countBy=mo,Nn.create=function(t,e){var n=Un(t);return null==e?n:ir(n,e)},Nn.curry=function t(e,n,r){var i=Ka(e,8,a,a,a,a,a,n=r?a:n);return i.placeholder=t.placeholder,i},Nn.curryRight=function t(e,n,r){var i=Ka(e,c,a,a,a,a,a,n=r?a:n);return i.placeholder=t.placeholder,i},Nn.debounce=Ao,Nn.defaults=Fs,Nn.defaultsDeep=Ls,Nn.defer=To,Nn.delay=Io,Nn.difference=Mi,Nn.differenceBy=Ni,Nn.differenceWith=Ui,Nn.drop=function(t,e,n){var r=null==t?0:t.length;return r?aa(t,(e=n||e===a?1:vs(e))<0?0:e,r):[]},Nn.dropRight=function(t,e,n){var r=null==t?0:t.length;return r?aa(t,0,(e=r-(e=n||e===a?1:vs(e)))<0?0:e):[]},Nn.dropRightWhile=function(t,e){return t&&t.length?pa(t,ui(e,3),!0,!0):[]},Nn.dropWhile=function(t,e){return t&&t.length?pa(t,ui(e,3),!0):[]},Nn.fill=function(t,e,n,r){var i=null==t?0:t.length;return i?(n&&"number"!=typeof n&&xi(t,e,n)&&(n=0,r=i),function(t,e,n,r){var i=t.length;for((n=vs(n))<0&&(n=-n>i?0:i+n),(r=r===a||r>i?i:vs(r))<0&&(r+=i),r=n>r?0:ms(r);n<r;)t[n++]=e;return t}(t,e,n,r)):[]},Nn.filter=function(t,e){return(qo(t)?Ae:gr)(t,ui(e,3))},Nn.flatMap=function(t,e){return _r(ko(t,e),1)},Nn.flatMapDeep=function(t,e){return _r(ko(t,e),h)},Nn.flatMapDepth=function(t,e,n){return n=n===a?1:vs(n),_r(ko(t,e),n)},Nn.flatten=qi,Nn.flattenDeep=function(t){return(null==t?0:t.length)?_r(t,h):[]},Nn.flattenDepth=function(t,e){return(null==t?0:t.length)?_r(t,e=e===a?1:vs(e)):[]},Nn.flip=function(t){return Ka(t,512)},Nn.flow=nc,Nn.flowRight=rc,Nn.fromPairs=function(t){for(var e=-1,n=null==t?0:t.length,r={};++e<n;){var a=t[e];r[a[0]]=a[1]}return r},Nn.functions=function(t){return null==t?[]:Cr(t,As(t))},Nn.functionsIn=function(t){return null==t?[]:Cr(t,Ts(t))},Nn.groupBy=bo,Nn.initial=function(t){return(null==t?0:t.length)?aa(t,0,-1):[]},Nn.intersection=Zi,Nn.intersectionBy=Gi,Nn.intersectionWith=Ji,Nn.invert=js,Nn.invertBy=Ds,Nn.invokeMap=wo,Nn.iteratee=ic,Nn.keyBy=Co,Nn.keys=As,Nn.keysIn=Ts,Nn.map=ko,Nn.mapKeys=function(t,e){var n={};return e=ui(e,3),br(t,(function(t,r,a){or(n,e(t,r,a),t)})),n},Nn.mapValues=function(t,e){var n={};return e=ui(e,3),br(t,(function(t,r,a){or(n,r,e(t,r,a))})),n},Nn.matches=function(t){return Nr(ur(t,1))},Nn.matchesProperty=function(t,e){return Ur(t,ur(e,1))},Nn.memoize=Po,Nn.merge=Is,Nn.mergeWith=Ps,Nn.method=oc,Nn.methodOf=sc,Nn.mixin=cc,Nn.negate=Bo,Nn.nthArg=function(t){return t=vs(t),Yr((function(e){return Hr(e,t)}))},Nn.omit=Bs,Nn.omitBy=function(t,e){return Vs(t,Bo(ui(e)))},Nn.once=function(t){return jo(2,t)},Nn.orderBy=function(t,e,n,r){return null==t?[]:(qo(e)||(e=null==e?[]:[e]),qo(n=r?a:n)||(n=null==n?[]:[n]),qr(t,e,n))},Nn.over=lc,Nn.overArgs=Ro,Nn.overEvery=fc,Nn.overSome=dc,Nn.partial=Vo,Nn.partialRight=zo,Nn.partition=Fo,Nn.pick=Rs,Nn.pickBy=Vs,Nn.property=hc,Nn.propertyOf=function(t){return function(e){return null==t?a:kr(t,e)}},Nn.pull=Yi,Nn.pullAll=Ki,Nn.pullAllBy=function(t,e,n){return t&&t.length&&e&&e.length?Zr(t,e,ui(n,2)):t},Nn.pullAllWith=function(t,e,n){return t&&t.length&&e&&e.length?Zr(t,e,a,n):t},Nn.pullAt=Xi,Nn.range=pc,Nn.rangeRight=vc,Nn.rearg=Mo,Nn.reject=function(t,e){return(qo(t)?Ae:gr)(t,Bo(ui(e,3)))},Nn.remove=function(t,e){var n=[];if(!t||!t.length)return n;var r=-1,a=[],i=t.length;for(e=ui(e,3);++r<i;){var o=t[r];e(o,r,t)&&(n.push(o),a.push(r))}return Gr(t,a),n},Nn.rest=function(t,e){if("function"!=typeof t)throw new Dt(i);return Yr(t,e=e===a?e:vs(e))},Nn.reverse=to,Nn.sampleSize=function(t,e,n){return e=(n?xi(t,e,n):e===a)?1:vs(e),(qo(t)?Xn:Xr)(t,e)},Nn.set=function(t,e,n){return null==t?t:ta(t,e,n)},Nn.setWith=function(t,e,n,r){return r="function"==typeof r?r:a,null==t?t:ta(t,e,n,r)},Nn.shuffle=function(t){return(qo(t)?tr:ra)(t)},Nn.slice=function(t,e,n){var r=null==t?0:t.length;return r?(n&&"number"!=typeof n&&xi(t,e,n)?(e=0,n=r):(e=null==e?0:vs(e),n=n===a?r:vs(n)),aa(t,e,n)):[]},Nn.sortBy=Lo,Nn.sortedUniq=function(t){return t&&t.length?ca(t):[]},Nn.sortedUniqBy=function(t,e){return t&&t.length?ca(t,ui(e,2)):[]},Nn.split=function(t,e,n){return n&&"number"!=typeof n&&xi(t,e,n)&&(e=n=a),(n=n===a?m:n>>>0)?(t=ys(t))&&("string"==typeof e||null!=e&&!os(e))&&!(e=la(e))&&cn(t)?wa(vn(t),0,n):t.split(e,n):[]},Nn.spread=function(t,e){if("function"!=typeof t)throw new Dt(i);return e=null==e?0:yn(vs(e),0),Yr((function(n){var r=n[e],a=wa(n,0,e);return r&&Be(a,r),Ee(t,this,a)}))},Nn.tail=function(t){var e=null==t?0:t.length;return e?aa(t,1,e):[]},Nn.take=function(t,e,n){return t&&t.length?aa(t,0,(e=n||e===a?1:vs(e))<0?0:e):[]},Nn.takeRight=function(t,e,n){var r=null==t?0:t.length;return r?aa(t,(e=r-(e=n||e===a?1:vs(e)))<0?0:e,r):[]},Nn.takeRightWhile=function(t,e){return t&&t.length?pa(t,ui(e,3),!1,!0):[]},Nn.takeWhile=function(t,e){return t&&t.length?pa(t,ui(e,3)):[]},Nn.tap=function(t,e){return e(t),t},Nn.throttle=function(t,e,n){var r=!0,a=!0;if("function"!=typeof t)throw new Dt(i);return es(n)&&(r="leading"in n?!!n.leading:r,a="trailing"in n?!!n.trailing:a),Ao(t,e,{leading:r,maxWait:e,trailing:a})},Nn.thru=po,Nn.toArray=hs,Nn.toPairs=zs,Nn.toPairsIn=Ms,Nn.toPath=function(t){return qo(t)?Pe(t,Ri):us(t)?[t]:Da(Bi(ys(t)))},Nn.toPlainObject=_s,Nn.transform=function(t,e,n){var r=qo(t),a=r||Jo(t)||ls(t);if(e=ui(e,4),null==n){var i=t&&t.constructor;n=a?r?new i:[]:es(t)&&Ko(i)?Un(Zt(t)):{}}return(a?je:br)(t,(function(t,r,a){return e(n,t,r,a)})),n},Nn.unary=function(t){return So(t,1)},Nn.union=eo,Nn.unionBy=no,Nn.unionWith=ro,Nn.uniq=function(t){return t&&t.length?fa(t):[]},Nn.uniqBy=function(t,e){return t&&t.length?fa(t,ui(e,2)):[]},Nn.uniqWith=function(t,e){return e="function"==typeof e?e:a,t&&t.length?fa(t,a,e):[]},Nn.unset=function(t,e){return null==t||da(t,e)},Nn.unzip=ao,Nn.unzipWith=io,Nn.update=function(t,e,n){return null==t?t:ha(t,e,ya(n))},Nn.updateWith=function(t,e,n,r){return r="function"==typeof r?r:a,null==t?t:ha(t,e,ya(n),r)},Nn.values=Ns,Nn.valuesIn=function(t){return null==t?[]:tn(t,Ts(t))},Nn.without=oo,Nn.words=Ks,Nn.wrap=function(t,e){return Vo(ya(e),t)},Nn.xor=so,Nn.xorBy=co,Nn.xorWith=uo,Nn.zip=lo,Nn.zipObject=function(t,e){return ga(t||[],e||[],nr)},Nn.zipObjectDeep=function(t,e){return ga(t||[],e||[],ta)},Nn.zipWith=fo,Nn.entries=zs,Nn.entriesIn=Ms,Nn.extend=bs,Nn.extendWith=ws,cc(Nn,Nn),Nn.add=_c,Nn.attempt=Xs,Nn.camelCase=Us,Nn.capitalize=$s,Nn.ceil=yc,Nn.clamp=function(t,e,n){return n===a&&(n=e,e=a),n!==a&&(n=(n=gs(n))==n?n:0),e!==a&&(e=(e=gs(e))==e?e:0),cr(gs(t),e,n)},Nn.clone=function(t){return ur(t,4)},Nn.cloneDeep=function(t){return ur(t,5)},Nn.cloneDeepWith=function(t,e){return ur(t,5,e="function"==typeof e?e:a)},Nn.cloneWith=function(t,e){return ur(t,4,e="function"==typeof e?e:a)},Nn.conformsTo=function(t,e){return null==e||lr(t,e,As(e))},Nn.deburr=Hs,Nn.defaultTo=function(t,e){return null==t||t!=t?e:t},Nn.divide=xc,Nn.endsWith=function(t,e,n){t=ys(t),e=la(e);var r=t.length,i=n=n===a?r:cr(vs(n),0,r);return(n-=e.length)>=0&&t.slice(n,i)==e},Nn.eq=No,Nn.escape=function(t){return(t=ys(t))&&Y.test(t)?t.replace(J,on):t},Nn.escapeRegExp=function(t){return(t=ys(t))&&it.test(t)?t.replace(at,"\\$&"):t},Nn.every=function(t,e,n){var r=qo(t)?Oe:vr;return n&&xi(t,e,n)&&(e=a),r(t,ui(e,3))},Nn.find=go,Nn.findIndex=$i,Nn.findKey=function(t,e){return Ne(t,ui(e,3),br)},Nn.findLast=_o,Nn.findLastIndex=Hi,Nn.findLastKey=function(t,e){return Ne(t,ui(e,3),wr)},Nn.floor=bc,Nn.forEach=yo,Nn.forEachRight=xo,Nn.forIn=function(t,e){return null==t?t:yr(t,ui(e,3),Ts)},Nn.forInRight=function(t,e){return null==t?t:xr(t,ui(e,3),Ts)},Nn.forOwn=function(t,e){return t&&br(t,ui(e,3))},Nn.forOwnRight=function(t,e){return t&&wr(t,ui(e,3))},Nn.get=Es,Nn.gt=Uo,Nn.gte=$o,Nn.has=function(t,e){return null!=t&&mi(t,e,Sr)},Nn.hasIn=Ss,Nn.head=Wi,Nn.identity=ac,Nn.includes=function(t,e,n,r){t=Zo(t)?t:Ns(t),n=n&&!r?vs(n):0;var a=t.length;return n<0&&(n=yn(a+n,0)),cs(t)?n<=a&&t.indexOf(e,n)>-1:!!a&&$e(t,e,n)>-1},Nn.indexOf=function(t,e,n){var r=null==t?0:t.length;if(!r)return-1;var a=null==n?0:vs(n);return a<0&&(a=yn(r+a,0)),$e(t,e,a)},Nn.inRange=function(t,e,n){return e=ps(e),n===a?(n=e,e=0):n=ps(n),function(t,e,n){return t>=xn(e,n)&&t<yn(e,n)}(t=gs(t),e,n)},Nn.invoke=Os,Nn.isArguments=Ho,Nn.isArray=qo,Nn.isArrayBuffer=Wo,Nn.isArrayLike=Zo,Nn.isArrayLikeObject=Go,Nn.isBoolean=function(t){return!0===t||!1===t||ns(t)&&Lr(t)==x},Nn.isBuffer=Jo,Nn.isDate=Qo,Nn.isElement=function(t){return ns(t)&&1===t.nodeType&&!is(t)},Nn.isEmpty=function(t){if(null==t)return!0;if(Zo(t)&&(qo(t)||"string"==typeof t||"function"==typeof t.splice||Jo(t)||ls(t)||Ho(t)))return!t.length;var e=vi(t);if(e==F||e==D)return!t.size;if(ki(t))return!Rr(t).length;for(var n in t)if(Bt.call(t,n))return!1;return!0},Nn.isEqual=function(t,e){return Tr(t,e)},Nn.isEqualWith=function(t,e,n){var r=(n="function"==typeof n?n:a)?n(t,e):a;return r===a?Tr(t,e,a,n):!!r},Nn.isError=Yo,Nn.isFinite=function(t){return"number"==typeof t&&xe(t)},Nn.isFunction=Ko,Nn.isInteger=Xo,Nn.isLength=ts,Nn.isMap=rs,Nn.isMatch=function(t,e){return t===e||Ir(t,e,fi(e))},Nn.isMatchWith=function(t,e,n){return n="function"==typeof n?n:a,Ir(t,e,fi(e),n)},Nn.isNaN=function(t){return as(t)&&t!=+t},Nn.isNative=function(t){if(Ci(t))throw new kt("Unsupported core-js use. Try https://npms.io/search?q=ponyfill.");return Pr(t)},Nn.isNil=function(t){return null==t},Nn.isNull=function(t){return null===t},Nn.isNumber=as,Nn.isObject=es,Nn.isObjectLike=ns,Nn.isPlainObject=is,Nn.isRegExp=os,Nn.isSafeInteger=function(t){return Xo(t)&&t>=-9007199254740991&&t<=p},Nn.isSet=ss,Nn.isString=cs,Nn.isSymbol=us,Nn.isTypedArray=ls,Nn.isUndefined=function(t){return t===a},Nn.isWeakMap=function(t){return ns(t)&&vi(t)==T},Nn.isWeakSet=function(t){return ns(t)&&"[object WeakSet]"==Lr(t)},Nn.join=function(t,e){return null==t?"":Me.call(t,e)},Nn.kebabCase=qs,Nn.last=Qi,Nn.lastIndexOf=function(t,e,n){var r=null==t?0:t.length;if(!r)return-1;var i=r;return n!==a&&(i=(i=vs(n))<0?yn(r+i,0):xn(i,r-1)),e==e?function(t,e,n){for(var r=n+1;r--;)if(t[r]===e)return r;return r}(t,e,i):Ue(t,qe,i,!0)},Nn.lowerCase=Ws,Nn.lowerFirst=Zs,Nn.lt=fs,Nn.lte=ds,Nn.max=function(t){return t&&t.length?mr(t,ac,Er):a},Nn.maxBy=function(t,e){return t&&t.length?mr(t,ui(e,2),Er):a},Nn.mean=function(t){return We(t,ac)},Nn.meanBy=function(t,e){return We(t,ui(e,2))},Nn.min=function(t){return t&&t.length?mr(t,ac,zr):a},Nn.minBy=function(t,e){return t&&t.length?mr(t,ui(e,2),zr):a},Nn.stubArray=mc,Nn.stubFalse=gc,Nn.stubObject=function(){return{}},Nn.stubString=function(){return""},Nn.stubTrue=function(){return!0},Nn.multiply=Cc,Nn.nth=function(t,e){return t&&t.length?Hr(t,vs(e)):a},Nn.noConflict=function(){return ve._===this&&(ve._=Nt),this},Nn.noop=uc,Nn.now=Eo,Nn.pad=function(t,e,n){t=ys(t);var r=(e=vs(e))?pn(t):0;if(!e||r>=e)return t;var a=(e-r)/2;return qa(me(a),n)+t+qa(pe(a),n)},Nn.padEnd=function(t,e,n){t=ys(t);var r=(e=vs(e))?pn(t):0;return e&&r<e?t+qa(e-r,n):t},Nn.padStart=function(t,e,n){t=ys(t);var r=(e=vs(e))?pn(t):0;return e&&r<e?qa(e-r,n)+t:t},Nn.parseInt=function(t,e,n){return n||null==e?e=0:e&&(e=+e),wn(ys(t).replace(ot,""),e||0)},Nn.random=function(t,e,n){if(n&&"boolean"!=typeof n&&xi(t,e,n)&&(e=n=a),n===a&&("boolean"==typeof e?(n=e,e=a):"boolean"==typeof t&&(n=t,t=a)),t===a&&e===a?(t=0,e=1):(t=ps(t),e===a?(e=t,t=0):e=ps(e)),t>e){var r=t;t=e,e=r}if(n||t%1||e%1){var i=Cn();return xn(t+i*(e-t+fe("1e-"+((i+"").length-1))),e)}return Jr(t,e)},Nn.reduce=function(t,e,n){var r=qo(t)?Re:Je,a=arguments.length<3;return r(t,ui(e,4),n,a,hr)},Nn.reduceRight=function(t,e,n){var r=qo(t)?Ve:Je,a=arguments.length<3;return r(t,ui(e,4),n,a,pr)},Nn.repeat=function(t,e,n){return e=(n?xi(t,e,n):e===a)?1:vs(e),Qr(ys(t),e)},Nn.replace=function(){var t=arguments,e=ys(t[0]);return t.length<3?e:e.replace(t[1],t[2])},Nn.result=function(t,e,n){var r=-1,i=(e=xa(e,t)).length;for(i||(i=1,t=a);++r<i;){var o=null==t?a:t[Ri(e[r])];o===a&&(r=i,o=n),t=Ko(o)?o.call(t):o}return t},Nn.round=kc,Nn.runInContext=t,Nn.sample=function(t){return(qo(t)?Kn:Kr)(t)},Nn.size=function(t){if(null==t)return 0;if(Zo(t))return cs(t)?pn(t):t.length;var e=vi(t);return e==F||e==D?t.size:Rr(t).length},Nn.snakeCase=Gs,Nn.some=function(t,e,n){var r=qo(t)?ze:ia;return n&&xi(t,e,n)&&(e=a),r(t,ui(e,3))},Nn.sortedIndex=function(t,e){return oa(t,e)},Nn.sortedIndexBy=function(t,e,n){return sa(t,e,ui(n,2))},Nn.sortedIndexOf=function(t,e){var n=null==t?0:t.length;if(n){var r=oa(t,e);if(r<n&&No(t[r],e))return r}return-1},Nn.sortedLastIndex=function(t,e){return oa(t,e,!0)},Nn.sortedLastIndexBy=function(t,e,n){return sa(t,e,ui(n,2),!0)},Nn.sortedLastIndexOf=function(t,e){if(null==t?0:t.length){var n=oa(t,e,!0)-1;if(No(t[n],e))return n}return-1},Nn.startCase=Js,Nn.startsWith=function(t,e,n){return t=ys(t),n=null==n?0:cr(vs(n),0,t.length),e=la(e),t.slice(n,n+e.length)==e},Nn.subtract=Fc,Nn.sum=function(t){return t&&t.length?Qe(t,ac):0},Nn.sumBy=function(t,e){return t&&t.length?Qe(t,ui(e,2)):0},Nn.template=function(t,e,n){var r=Nn.templateSettings;n&&xi(t,e,n)&&(e=a),t=ys(t),e=ws({},e,r,Xa);var i,o,s=ws({},e.imports,r.imports,Xa),c=As(s),u=tn(s,c),l=0,f=e.interpolate||wt,d="__p += '",h=St((e.escape||wt).source+"|"+f.source+"|"+(f===tt?pt:wt).source+"|"+(e.evaluate||wt).source+"|$","g"),p="//# sourceURL="+(Bt.call(e,"sourceURL")?(e.sourceURL+"").replace(/\s/g," "):"lodash.templateSources["+ ++se+"]")+"\n";t.replace(h,(function(e,n,r,a,s,c){return r||(r=a),d+=t.slice(l,c).replace(Ct,sn),n&&(i=!0,d+="' +\n__e("+n+") +\n'"),s&&(o=!0,d+="';\n"+s+";\n__p += '"),r&&(d+="' +\n((__t = ("+r+")) == null ? '' : __t) +\n'"),l=c+e.length,e})),d+="';\n";var v=Bt.call(e,"variable")&&e.variable;if(v){if(dt.test(v))throw new kt("Invalid `variable` option passed into `_.template`")}else d="with (obj) {\n"+d+"\n}\n";d=(o?d.replace(q,""):d).replace(W,"$1").replace(Z,"$1;"),d="function("+(v||"obj")+") {\n"+(v?"":"obj || (obj = {});\n")+"var __t, __p = ''"+(i?", __e = _.escape":"")+(o?", __j = Array.prototype.join;\nfunction print() { __p += __j.call(arguments, '') }\n":";\n")+d+"return __p\n}";var m=Xs((function(){return Ft(c,p+"return "+d).apply(a,u)}));if(m.source=d,Yo(m))throw m;return m},Nn.times=function(t,e){if((t=vs(t))<1||t>p)return[];var n=m,r=xn(t,m);e=ui(e),t-=m;for(var a=Ye(r,e);++n<t;)e(n);return a},Nn.toFinite=ps,Nn.toInteger=vs,Nn.toLength=ms,Nn.toLower=function(t){return ys(t).toLowerCase()},Nn.toNumber=gs,Nn.toSafeInteger=function(t){return t?cr(vs(t),-9007199254740991,p):0===t?t:0},Nn.toString=ys,Nn.toUpper=function(t){return ys(t).toUpperCase()},Nn.trim=function(t,e,n){if((t=ys(t))&&(n||e===a))return Ke(t);if(!t||!(e=la(e)))return t;var r=vn(t),i=vn(e);return wa(r,nn(r,i),rn(r,i)+1).join("")},Nn.trimEnd=function(t,e,n){if((t=ys(t))&&(n||e===a))return t.slice(0,mn(t)+1);if(!t||!(e=la(e)))return t;var r=vn(t);return wa(r,0,rn(r,vn(e))+1).join("")},Nn.trimStart=function(t,e,n){if((t=ys(t))&&(n||e===a))return t.replace(ot,"");if(!t||!(e=la(e)))return t;var r=vn(t);return wa(r,nn(r,vn(e))).join("")},Nn.truncate=function(t,e){var n=30,r="...";if(es(e)){var i="separator"in e?e.separator:i;n="length"in e?vs(e.length):n,r="omission"in e?la(e.omission):r}var o=(t=ys(t)).length;if(cn(t)){var s=vn(t);o=s.length}if(n>=o)return t;var c=n-pn(r);if(c<1)return r;var u=s?wa(s,0,c).join(""):t.slice(0,c);if(i===a)return u+r;if(s&&(c+=u.length-c),os(i)){if(t.slice(c).search(i)){var l,f=u;for(i.global||(i=St(i.source,ys(vt.exec(i))+"g")),i.lastIndex=0;l=i.exec(f);)var d=l.index;u=u.slice(0,d===a?c:d)}}else if(t.indexOf(la(i),c)!=c){var h=u.lastIndexOf(i);h>-1&&(u=u.slice(0,h))}return u+r},Nn.unescape=function(t){return(t=ys(t))&&Q.test(t)?t.replace(G,gn):t},Nn.uniqueId=function(t){var e=++Rt;return ys(t)+e},Nn.upperCase=Qs,Nn.upperFirst=Ys,Nn.each=yo,Nn.eachRight=xo,Nn.first=Wi,cc(Nn,(wc={},br(Nn,(function(t,e){Bt.call(Nn.prototype,e)||(wc[e]=t)})),wc),{chain:!1}),Nn.VERSION="4.17.21",je(["bind","bindKey","curry","curryRight","partial","partialRight"],(function(t){Nn[t].placeholder=Nn})),je(["drop","take"],(function(t,e){qn.prototype[t]=function(n){n=n===a?1:yn(vs(n),0);var r=this.__filtered__&&!e?new qn(this):this.clone();return r.__filtered__?r.__takeCount__=xn(n,r.__takeCount__):r.__views__.push({size:xn(n,m),type:t+(r.__dir__<0?"Right":"")}),r},qn.prototype[t+"Right"]=function(e){return this.reverse()[t](e).reverse()}})),je(["filter","map","takeWhile"],(function(t,e){var n=e+1,r=1==n||3==n;qn.prototype[t]=function(t){var e=this.clone();return e.__iteratees__.push({iteratee:ui(t,3),type:n}),e.__filtered__=e.__filtered__||r,e}})),je(["head","last"],(function(t,e){var n="take"+(e?"Right":"");qn.prototype[t]=function(){return this[n](1).value()[0]}})),je(["initial","tail"],(function(t,e){var n="drop"+(e?"":"Right");qn.prototype[t]=function(){return this.__filtered__?new qn(this):this[n](1)}})),qn.prototype.compact=function(){return this.filter(ac)},qn.prototype.find=function(t){return this.filter(t).head()},qn.prototype.findLast=function(t){return this.reverse().find(t)},qn.prototype.invokeMap=Yr((function(t,e){return"function"==typeof t?new qn(this):this.map((function(n){return Or(n,t,e)}))})),qn.prototype.reject=function(t){return this.filter(Bo(ui(t)))},qn.prototype.slice=function(t,e){t=vs(t);var n=this;return n.__filtered__&&(t>0||e<0)?new qn(n):(t<0?n=n.takeRight(-t):t&&(n=n.drop(t)),e!==a&&(n=(e=vs(e))<0?n.dropRight(-e):n.take(e-t)),n)},qn.prototype.takeRightWhile=function(t){return this.reverse().takeWhile(t).reverse()},qn.prototype.toArray=function(){return this.take(m)},br(qn.prototype,(function(t,e){var n=/^(?:filter|find|map|reject)|While$/.test(e),r=/^(?:head|last)$/.test(e),i=Nn[r?"take"+("last"==e?"Right":""):e],o=r||/^find/.test(e);i&&(Nn.prototype[e]=function(){var e=this.__wrapped__,s=r?[1]:arguments,c=e instanceof qn,u=s[0],l=c||qo(e),f=function(t){var e=i.apply(Nn,Be([t],s));return r&&d?e[0]:e};l&&n&&"function"==typeof u&&1!=u.length&&(c=l=!1);var d=this.__chain__,h=!!this.__actions__.length,p=o&&!d,v=c&&!h;if(!o&&l){e=v?e:new qn(this);var m=t.apply(e,s);return m.__actions__.push({func:po,args:[f],thisArg:a}),new Hn(m,d)}return p&&v?t.apply(this,s):(m=this.thru(f),p?r?m.value()[0]:m.value():m)})})),je(["pop","push","shift","sort","splice","unshift"],(function(t){var e=Ot[t],n=/^(?:push|sort|unshift)$/.test(t)?"tap":"thru",r=/^(?:pop|shift)$/.test(t);Nn.prototype[t]=function(){var t=arguments;if(r&&!this.__chain__){var a=this.value();return e.apply(qo(a)?a:[],t)}return this[n]((function(n){return e.apply(qo(n)?n:[],t)}))}})),br(qn.prototype,(function(t,e){var n=Nn[e];if(n){var r=n.name+"";Bt.call(An,r)||(An[r]=[]),An[r].push({name:e,func:n})}})),An[Na(a,2).name]=[{name:"wrapper",func:a}],qn.prototype.clone=function(){var t=new qn(this.__wrapped__);return t.__actions__=Da(this.__actions__),t.__dir__=this.__dir__,t.__filtered__=this.__filtered__,t.__iteratees__=Da(this.__iteratees__),t.__takeCount__=this.__takeCount__,t.__views__=Da(this.__views__),t},qn.prototype.reverse=function(){if(this.__filtered__){var t=new qn(this);t.__dir__=-1,t.__filtered__=!0}else(t=this.clone()).__dir__*=-1;return t},qn.prototype.value=function(){var t=this.__wrapped__.value(),e=this.__dir__,n=qo(t),r=e<0,a=n?t.length:0,i=function(t,e,n){var r=-1,a=n.length;for(;++r<a;){var i=n[r],o=i.size;switch(i.type){case"drop":t+=o;break;case"dropRight":e-=o;break;case"take":e=xn(e,t+o);break;case"takeRight":t=yn(t,e-o)}}return{start:t,end:e}}(0,a,this.__views__),o=i.start,s=i.end,c=s-o,u=r?s:o-1,l=this.__iteratees__,f=l.length,d=0,h=xn(c,this.__takeCount__);if(!n||!r&&a==c&&h==c)return va(t,this.__actions__);var p=[];t:for(;c--&&d<h;){for(var v=-1,m=t[u+=e];++v<f;){var g=l[v],_=g.iteratee,y=g.type,x=_(m);if(2==y)m=x;else if(!x){if(1==y)continue t;break t}}p[d++]=m}return p},Nn.prototype.at=vo,Nn.prototype.chain=function(){return ho(this)},Nn.prototype.commit=function(){return new Hn(this.value(),this.__chain__)},Nn.prototype.next=function(){this.__values__===a&&(this.__values__=hs(this.value()));var t=this.__index__>=this.__values__.length;return{done:t,value:t?a:this.__values__[this.__index__++]}},Nn.prototype.plant=function(t){for(var e,n=this;n instanceof $n;){var r=zi(n);r.__index__=0,r.__values__=a,e?i.__wrapped__=r:e=r;var i=r;n=n.__wrapped__}return i.__wrapped__=t,e},Nn.prototype.reverse=function(){var t=this.__wrapped__;if(t instanceof qn){var e=t;return this.__actions__.length&&(e=new qn(this)),(e=e.reverse()).__actions__.push({func:po,args:[to],thisArg:a}),new Hn(e,this.__chain__)}return this.thru(to)},Nn.prototype.toJSON=Nn.prototype.valueOf=Nn.prototype.value=function(){return va(this.__wrapped__,this.__actions__)},Nn.prototype.first=Nn.prototype.head,Kt&&(Nn.prototype[Kt]=function(){return this}),Nn}();ve._=_n,(r=function(){return _n}.call(e,n,e,t))===a||(t.exports=r)}.call(this)},3191:function(t,e,n){"use strict";var r=n(31928);function a(t){if("function"!=typeof t)throw new TypeError("executor must be a function.");var e;this.promise=new Promise((function(t){e=t}));var n=this;t((function(t){n.reason||(n.reason=new r(t),e(n.reason))}))}a.prototype.throwIfRequested=function(){if(this.reason)throw this.reason},a.source=function(){var t;return{token:new a((function(e){t=e})),cancel:t}},t.exports=a},5449:function(t){"use strict";t.exports=function(t,e,n,r,a){return t.config=e,n&&(t.code=n),t.request=r,t.response=a,t}},7018:function(t,e,n){"use strict";var r=n(9516);t.exports=function(t,e){r.forEach(t,(function(n,r){r!==e&&r.toUpperCase()===e.toUpperCase()&&(t[e]=n,delete t[r])}))}},7522:function(t,e,n){"use strict";var r=n(47763);t.exports=function(t,e,n){var a=n.config.validateStatus;n.status&&a&&!a(n.status)?e(r("Request failed with status code "+n.status,n.config,null,n.request,n)):t(n)}},9516:function(t,e,n){"use strict";var r=n(69012),a=n(87206),i=Object.prototype.toString;function o(t){return"[object Array]"===i.call(t)}function s(t){return null!==t&&"object"==typeof t}function c(t){return"[object Function]"===i.call(t)}function u(t,e){if(null!=t)if("object"==typeof t||o(t)||(t=[t]),o(t))for(var n=0,r=t.length;n<r;n++)e.call(null,t[n],n,t);else for(var a in t)Object.prototype.hasOwnProperty.call(t,a)&&e.call(null,t[a],a,t)}t.exports={isArray:o,isArrayBuffer:function(t){return"[object ArrayBuffer]"===i.call(t)},isBuffer:a,isFormData:function(t){return"undefined"!=typeof FormData&&t instanceof FormData},isArrayBufferView:function(t){return"undefined"!=typeof ArrayBuffer&&ArrayBuffer.isView?ArrayBuffer.isView(t):t&&t.buffer&&t.buffer instanceof ArrayBuffer},isString:function(t){return"string"==typeof t},isNumber:function(t){return"number"==typeof t},isObject:s,isUndefined:function(t){return void 0===t},isDate:function(t){return"[object Date]"===i.call(t)},isFile:function(t){return"[object File]"===i.call(t)},isBlob:function(t){return"[object Blob]"===i.call(t)},isFunction:c,isStream:function(t){return s(t)&&c(t.pipe)},isURLSearchParams:function(t){return"undefined"!=typeof URLSearchParams&&t instanceof URLSearchParams},isStandardBrowserEnv:function(){return("undefined"==typeof navigator||"ReactNative"!==navigator.product)&&("undefined"!=typeof window&&"undefined"!=typeof document)},forEach:u,merge:function t(){var e={};function n(n,r){"object"==typeof e[r]&&"object"==typeof n?e[r]=t(e[r],n):e[r]=n}for(var r=0,a=arguments.length;r<a;r++)u(arguments[r],n);return e},extend:function(t,e,n){return u(e,(function(e,a){t[a]=n&&"function"==typeof e?r(e,n):e})),t},trim:function(t){return t.replace(/^\s*/,"").replace(/\s*$/,"")}}},10639:function(t,e,n){"use strict";var r=n(76798),a=n.n(r)()((function(t){return t[1]}));a.push([t.id,".input-radio-test[type=radio][data-v-6a9236dc]{display:none;&:not(:disabled)~label[data-v-6a9236dc]{cursor:pointer}&:disabled~label[data-v-6a9236dc]{border-color:#bcc2bf;-webkit-box-shadow:none;box-shadow:none;color:#bcc2bf;cursor:not-allowed}}.label-radio-test[data-v-6a9236dc]{height:100%;padding:1rem 5px;position:relative;text-align:center}.label-radio-test[data-v-6a9236dc],.number-radio-test[data-v-6a9236dc]{-webkit-box-align:center;-ms-flex-align:center;-webkit-align-items:center;align-items:center;background:#fff;border-radius:9999px;display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex}.number-radio-test[data-v-6a9236dc]{-webkit-box-pack:center;-ms-flex-pack:center;border:3px solid #d9d9d9;-webkit-box-shadow:0 20px 25px -5px rgba(0,0,0,.1),0 8px 10px -6px rgba(0,0,0,.1);box-shadow:0 20px 25px -5px rgba(0,0,0,.1),0 8px 10px -6px rgba(0,0,0,.1);color:#757575;font-family:Beanbag_Dungmori_Rounded_Medium;font-size:16px;height:40px;-webkit-justify-content:center;justify-content:center;margin-right:10px;width:40px}.input-radio-test[type=radio]:checked+.label-radio-test[data-v-6a9236dc]{background:#95ff99;-webkit-box-shadow:0 20px 25px -5px rgba(0,0,0,.1),0 8px 10px -6px rgba(0,0,0,.1);box-shadow:0 20px 25px -5px rgba(0,0,0,.1),0 8px 10px -6px rgba(0,0,0,.1);color:#212121;z-index:1}.input-radio-test[type=radio]:checked+.label-radio-test .number-radio-test[data-v-6a9236dc]{border:unset;-webkit-box-shadow:unset;box-shadow:unset}.input-radio-test.false[type=radio]:checked+.label-radio-test[data-v-6a9236dc]{background:#ff7c79;-webkit-box-shadow:0 20px 25px -5px rgba(0,0,0,.1),0 8px 10px -6px rgba(0,0,0,.1);box-shadow:0 20px 25px -5px rgba(0,0,0,.1),0 8px 10px -6px rgba(0,0,0,.1);color:#fff;z-index:1}",""]),e.A=a},11985:function(t,e,n){"use strict";n.d(e,{Er:function(){return p},UW:function(){return v}});var r=n(2543),a=n.n(r);function i(t){return function(t){if(Array.isArray(t))return o(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(t){if("string"==typeof t)return o(t,e);var n={}.toString.call(t).slice(8,-1);return"Object"===n&&t.constructor&&(n=t.constructor.name),"Map"===n||"Set"===n?Array.from(t):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?o(t,e):void 0}}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function o(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=Array(e);n<e;n++)r[n]=t[n];return r}function s(t){return s="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},s(t)}function c(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,r)}return n}function u(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?c(Object(n),!0).forEach((function(e){l(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):c(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}function l(t,e,n){return(e=d(e))in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function f(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,d(r.key),r)}}function d(t){var e=function(t,e){if("object"!=s(t)||!t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var r=n.call(t,e||"default");if("object"!=s(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==s(e)?e:e+""}var h=function(){return t=function t(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),this.options=u({stackedView:"Top",rotate:!0,visibleItems:3,margin:10,useOverlays:!0},e),this.currentPosition=0,this.isFirstTime=!0,this.elTrans=0,this.container=document.getElementById("stacked-cards-block"),console.log("this.container: ",this.container),this.cardsContainer=this.container.querySelector(".stackedcards-container"),this.cards=Array.from(this.cardsContainer.children),this.rightOverlay=this.container.querySelector(".stackedcards-overlay.right"),this.leftOverlay=this.container.querySelector(".stackedcards-overlay.left"),console.log("this.rightOverlay: ",this.rightOverlay),console.log("this.leftOverlay: ",this.leftOverlay),this.maxElements=this.cards.length,this.options.visibleItems=Math.min(this.options.visibleItems,this.maxElements),this.init()},e=[{key:"init",value:function(){var t=this;this.setupCards(),this.addEventListeners(),this.updateUI(),setTimeout((function(){return t.container.classList.remove("init")}),150)}},{key:"setupCards",value:function(){var t=this,e=this.options,n=e.stackedView,r=e.visibleItems,a=e.margin*(r-1);this.cardsContainer.style.marginBottom="".concat(a,"px"),this.elTrans="Top"===n?a:0,this.cards.slice(r).forEach((function(e){e.classList.add("stackedcards-".concat(n.toLowerCase()),"stackedcards--animatable","stackedcards-origin-".concat(n.toLowerCase())),e.style.cssText="\n        z-index: 0;\n        opacity: 0;\n        transform: scale(".concat(1-.04*r,") translateY(").concat(t.elTrans,"px);\n      ")})),this.updateActiveCard(),this.setupOverlays()}},{key:"setupOverlays",value:function(){var t=this;if(!this.options.useOverlays)return this.leftOverlay.classList.add("stackedcards-overlay-hidden"),void this.rightOverlay.classList.add("stackedcards-overlay-hidden");[this.leftOverlay,this.rightOverlay].forEach((function(e){e.style.transform="translateY(".concat(t.elTrans,"px)")}))}},{key:"updateActiveCard",value:function(){this.cards[this.currentPosition]&&this.cards[this.currentPosition].classList.add("stackedcards-active")}},{key:"addEventListeners",value:function(){var t=this;this.cards.forEach((function(e){e.removeEventListener("click",t.handleCardClick)})),this.handleCardClick=function(t){console.log("e: ",t);for(var e=t.target,n=!0;e&&e!==t.currentTarget;){if(e.classList.contains("noFlip")||e.classList.contains("card_audio")||e.classList.contains("underline")||"BUTTON"===e.tagName||"A"===e.tagName||"SVG"===e.tagName||"path"===e.tagName||null!==e.closest(".card-footer")||null!==e.closest('[class*="cursor-pointer"]')){n=!1;break}e=e.parentElement}if(n){if("undefined"!=typeof course_id&&!t.currentTarget.querySelector(".card-inner").classList.contains("flip")&&[39,40].includes(course_id)&&0===parseInt(authUser.isTester)&&["production","prod"].includes("production")&&"undefined"!=typeof ga){var r=39===parseInt(course_id)?"n5_flc_behind":"n4_flc_behind";ga("send","event","nx_flc_behind",r,r)}t.currentTarget.querySelector(".card-inner").classList.toggle("flip")}},this.cards.forEach((function(e){e.addEventListener("click",t.handleCardClick)}))}},{key:"swipeLeft",value:function(){var t=this;this.currentPosition>=this.maxElements||(this.transformCard(-1e3,0,0),this.options.useOverlays&&this.transformOverlay(this.leftOverlay,-1e3,0,1,(function(){return t.resetOverlay(t.leftOverlay)})),this.nextCard())}},{key:"swipeRight",value:function(){var t=this;this.currentPosition>=this.maxElements||(this.transformCard(1e3,0,0),this.options.useOverlays&&this.transformOverlay(this.rightOverlay,1e3,0,1,(function(){return t.resetOverlay(t.rightOverlay)})),this.nextCard())}},{key:"undo",value:function(){this.currentPosition<=0||(this.currentPosition--,this.updateUI(),this.updateActiveCard())}},{key:"nextCard",value:function(){this.currentPosition++,this.updateUI(),this.updateActiveCard()}},{key:"transformCard",value:function(t,e,n){var r=this.cards[this.currentPosition];r&&(r.classList.remove("no-transition"),r.style.zIndex=6,this.transformElement(r,t,e,n))}},{key:"transformOverlay",value:function(t,e,n,r,a){t.classList.remove("no-transition"),t.style.zIndex=8,this.transformElement(t,e,n,r),setTimeout(a,300)}},{key:"resetOverlay",value:function(t){var e=this;t.classList.add("no-transition"),requestAnimationFrame((function(){t.style.transform="translateY(".concat(e.elTrans,"px)"),t.style.opacity=0})),this.isFirstTime=!1}},{key:"transformElement",value:function(t,e,n,r){var a=this,i=this.options.rotate?Math.min(Math.max(e/10,-15),15):0;requestAnimationFrame((function(){t.style.transform="translateX(".concat(e,"px) translateY(").concat(n+a.elTrans,"px) rotate(").concat(i,"deg)"),t.style.opacity=r}))}},{key:"updateUI",value:function(){var t=this;requestAnimationFrame((function(){var e,n=t.options,r=n.visibleItems,a=(n.margin,5);t.cards.slice(t.currentPosition,t.currentPosition+r).forEach((function(t,n){e=4===a?"scale(0.97)":3===a?"scale(0.95)":"",t.style.cssText="\n                  transform: ".concat(e," translateY(").concat(4===a?23:3===a?42:5===a?0:44,"px);\n                  z-index: ").concat(a--,";\n                  opacity: 1;\n                "),t.classList.add("stackedcards--animatable")}))}))}},{key:"destroy",value:function(){this.cards.forEach((function(t){if(t.querySelector(".card-inner")){var e=t.cloneNode(!0);t.parentNode.replaceChild(e,t)}}))}},{key:"swipeLeftNew",value:function(){var t=this;this.cards[this.currentPosition].setAttribute("data-swiped-left","true"),this.currentPosition>=this.maxElements||(this.transformCard(-1e3,0,0),this.options.useOverlays&&this.transformOverlay(this.leftOverlay,-1e3,0,1,(function(){return t.resetOverlay(t.leftOverlay)})),this.nextCard())}},{key:"destroyEventListenersCards",value:function(){var t=this;console.log("destroyEventListenersCards"),this.cards.forEach((function(e){e.removeEventListener("click",t.handleCardClick)}))}}],e&&f(t.prototype,e),n&&f(t,n),Object.defineProperty(t,"prototype",{writable:!1}),t;var t,e,n}();function p(t){var e={0:{ti:0,li:0},1:{ti:60,li:0},2:{ti:60,li:0},3:{ti:60,li:0},4:{ti:60,li:0},5:{ti:60,li:0}};t=a().sortBy(t,"t"),console.log("history_learn_flc: ",t);var n=[];t.forEach((function(t){n.push({id:t.id,t:t.t,box:t.box,p:t.p})})),console.log("t: ",n);var r=Math.floor(Date.now()/1e3);console.log("currentTime: ",r,new Date(1e3*r).toISOString().slice(0,19).replace("T"," "));var o=[],s=[],c=[],l=[];t.forEach((function(t,n){var a=t.p;t.take_break=0,(t=u(u({},t),{},{is_test:!1})).box=a,!t.i||""===t.i||null===t.i||void 0===t.i||null!==t.id&&void 0!==t.id||(t.id=t.i),t.t&&0!==a?a>=1&&a<=4?(console.log(t),console.log("conditions[box].ti >= currentTime - item.t: ",e[a].ti,r,t.t,r-t.t),console.log("conditions[box].li <= index: ",e[a].li,n),e[a].ti<=r-t.t&&e[a].li<=n?(t.is_test=!0,o.push(t)):s.push(t)):(o=o.filter((function(e){return e.id!==t.id})),s=s.filter((function(e){return e.id!==t.id})),c=c.filter((function(e){return e.id!==t.id})),l.push(t)):c.push(t)})),o=o.sort((function(t,e){return t.is_test===e.is_test?e.box-t.box:t.is_test?-1:1})),s=s.sort((function(t,e){return e.box-t.box})),c=a().sortBy(c,(function(t){return null===t.t?-1/0:t.t}));var f=[];o.forEach((function(t){f.push({id:t.id,box:t.box,t:t.t,p:t.p})}));var d=[];c.forEach((function(t){d.push({id:t.id,box:t.box,t:t.t,p:t.p})}));var h=[];return s.forEach((function(t){h.push({id:t.id,box:t.box,t:t.t,p:t.p})})),console.log("eligibleCardsTemp: ",f),console.log("newCardsTemp: ",d),console.log("notPriorityCardsTemp: ",h),console.log("completedCards: ",l),o.length||c.length||(s=s.sort((function(t,e){return t.box-e.box}))).forEach((function(t){t.is_test=!0})),{sortCards:[].concat(i(o),i(c),i(s)),completedCards:l}}function v(t){return t.replace(/style="([^"]*?)font-size\s*:\s*[^;]+;?\s*([^"]*?)"/g,(function(t,e,n){var r=(e+n).trim();return r?'style="'.concat(r,'"'):""}))}e.Ay=h},17980:function(t){"use strict";t.exports=function(t){return function(e){return t.apply(null,e)}}},18015:function(t,e,n){"use strict";var r=n(9516),a=n(69012),i=n(35155),o=n(96987);function s(t){var e=new i(t),n=a(i.prototype.request,e);return r.extend(n,i.prototype,e),r.extend(n,e),n}var c=s(o);c.Axios=i,c.create=function(t){return s(r.merge(o,t))},c.Cancel=n(31928),c.CancelToken=n(3191),c.isCancel=n(93864),c.all=function(t){return Promise.all(t)},c.spread=n(17980),t.exports=c,t.exports.default=c},29137:function(t){"use strict";t.exports=function(t){return/^([a-z][a-z\d\+\-\.]*:)?\/\//i.test(t)}},31928:function(t){"use strict";function e(t){this.message=t}e.prototype.toString=function(){return"Cancel"+(this.message?": "+this.message:"")},e.prototype.__CANCEL__=!0,t.exports=e},33948:function(t,e,n){"use strict";var r=n(9516);t.exports=r.isStandardBrowserEnv()?{write:function(t,e,n,a,i,o){var s=[];s.push(t+"="+encodeURIComponent(e)),r.isNumber(n)&&s.push("expires="+new Date(n).toGMTString()),r.isString(a)&&s.push("path="+a),r.isString(i)&&s.push("domain="+i),!0===o&&s.push("secure"),document.cookie=s.join("; ")},read:function(t){var e=document.cookie.match(new RegExp("(^|;\\s*)("+t+")=([^;]*)"));return e?decodeURIComponent(e[3]):null},remove:function(t){this.write(t,"",Date.now()-864e5)}}:{write:function(){},read:function(){return null},remove:function(){}}},35155:function(t,e,n){"use strict";var r=n(96987),a=n(9516),i=n(83471),o=n(64490),s=n(29137),c=n(84680);function u(t){this.defaults=t,this.interceptors={request:new i,response:new i}}u.prototype.request=function(t){"string"==typeof t&&(t=a.merge({url:arguments[0]},arguments[1])),(t=a.merge(r,this.defaults,{method:"get"},t)).method=t.method.toLowerCase(),t.baseURL&&!s(t.url)&&(t.url=c(t.baseURL,t.url));var e=[o,void 0],n=Promise.resolve(t);for(this.interceptors.request.forEach((function(t){e.unshift(t.fulfilled,t.rejected)})),this.interceptors.response.forEach((function(t){e.push(t.fulfilled,t.rejected)}));e.length;)n=n.then(e.shift(),e.shift());return n},a.forEach(["delete","get","head","options"],(function(t){u.prototype[t]=function(e,n){return this.request(a.merge(n||{},{method:t,url:e}))}})),a.forEach(["post","put","patch"],(function(t){u.prototype[t]=function(e,n,r){return this.request(a.merge(r||{},{method:t,url:e,data:n}))}})),t.exports=u},35592:function(t,e,n){"use strict";var r=n(9516),a=n(7522),i=n(79106),o=n(62012),s=n(64202),c=n(47763),u="undefined"!=typeof window&&window.btoa&&window.btoa.bind(window)||n(42537);t.exports=function(t){return new Promise((function(e,l){var f=t.data,d=t.headers;r.isFormData(f)&&delete d["Content-Type"];var h=new XMLHttpRequest,p="onreadystatechange",v=!1;if("undefined"==typeof window||!window.XDomainRequest||"withCredentials"in h||s(t.url)||(h=new window.XDomainRequest,p="onload",v=!0,h.onprogress=function(){},h.ontimeout=function(){}),t.auth){var m=t.auth.username||"",g=t.auth.password||"";d.Authorization="Basic "+u(m+":"+g)}if(h.open(t.method.toUpperCase(),i(t.url,t.params,t.paramsSerializer),!0),h.timeout=t.timeout,h[p]=function(){if(h&&(4===h.readyState||v)&&(0!==h.status||h.responseURL&&0===h.responseURL.indexOf("file:"))){var n="getAllResponseHeaders"in h?o(h.getAllResponseHeaders()):null,r={data:t.responseType&&"text"!==t.responseType?h.response:h.responseText,status:1223===h.status?204:h.status,statusText:1223===h.status?"No Content":h.statusText,headers:n,config:t,request:h};a(e,l,r),h=null}},h.onerror=function(){l(c("Network Error",t,null,h)),h=null},h.ontimeout=function(){l(c("timeout of "+t.timeout+"ms exceeded",t,"ECONNABORTED",h)),h=null},r.isStandardBrowserEnv()){var _=n(33948),y=(t.withCredentials||s(t.url))&&t.xsrfCookieName?_.read(t.xsrfCookieName):void 0;y&&(d[t.xsrfHeaderName]=y)}if("setRequestHeader"in h&&r.forEach(d,(function(t,e){void 0===f&&"content-type"===e.toLowerCase()?delete d[e]:h.setRequestHeader(e,t)})),t.withCredentials&&(h.withCredentials=!0),t.responseType)try{h.responseType=t.responseType}catch(e){if("json"!==t.responseType)throw e}"function"==typeof t.onDownloadProgress&&h.addEventListener("progress",t.onDownloadProgress),"function"==typeof t.onUploadProgress&&h.upload&&h.upload.addEventListener("progress",t.onUploadProgress),t.cancelToken&&t.cancelToken.promise.then((function(t){h&&(h.abort(),l(t),h=null)})),void 0===f&&(f=null),h.send(f)}))}},38361:function(t,e,n){"use strict";n.d(e,{A:function(){return xt}});function r(t,e,n,r,a,i,o,s){var c,u="function"==typeof t?t.options:t;if(e&&(u.render=e,u.staticRenderFns=n,u._compiled=!0),r&&(u.functional=!0),i&&(u._scopeId="data-v-"+i),o?(c=function(t){(t=t||this.$vnode&&this.$vnode.ssrContext||this.parent&&this.parent.$vnode&&this.parent.$vnode.ssrContext)||"undefined"==typeof __VUE_SSR_CONTEXT__||(t=__VUE_SSR_CONTEXT__),a&&a.call(this,t),t&&t._registeredComponents&&t._registeredComponents.add(o)},u._ssrRegister=c):a&&(c=s?function(){a.call(this,(u.functional?this.parent:this).$root.$options.shadowRoot)}:a),c)if(u.functional){u._injectStyles=c;var l=u.render;u.render=function(t,e){return c.call(e),l(t,e)}}else{var f=u.beforeCreate;u.beforeCreate=f?[].concat(f,c):[c]}return{exports:t,options:u}}var a=r({props:{currentTab:{type:String,default:"lesson"},hasLesson:{type:Boolean,default:!0}},data:function(){return{searchField:""}},watch:{searchField:function(t){this.$emit("update-search",t)}},methods:{hideMenu:function(){this.$emit("hide-menu")}}},(function(){var t=this,e=t._self._c;return e("div",{staticClass:"flex items-center"},[e("button",{staticClass:"flex-none flex items-center justify-between border-none bg-[#F5F5F5] w-[85px] h-[40px] px-4 rounded-full",on:{click:t.hideMenu}},[e("img",{staticClass:"h-[14px]",attrs:{src:"/images/icons/hide.png",alt:"hide.png"}}),t._v(" "),e("span",{staticClass:"text-sm font-averta-regular"},[t._v("Ẩn")])]),t._v(" "),"comment"!=t.currentTab&&t.hasLesson?e("div",{staticClass:"relative w-full ml-5"},[e("input",{directives:[{name:"model",rawName:"v-model",value:t.searchField,expression:"searchField"}],staticClass:"pl-4 pr-8 text-[#1E1E1E] text-sm font-averta-regular placeholder:text-[#757575] bg-[#F5F5F5] w-full rounded-full h-[40px] w-full sp:hidden",attrs:{type:"text",placeholder:"Tìm kiếm bài học với từ khoá bất kì"},domProps:{value:t.searchField},on:{input:function(e){e.target.composing||(t.searchField=e.target.value)}}}),t._v(" "),e("input",{directives:[{name:"model",rawName:"v-model",value:t.searchField,expression:"searchField"}],staticClass:"pl-4 pr-8 text-[#1E1E1E] text-sm font-averta-regular placeholder:text-[#757575] bg-[#F5F5F5] w-full rounded-full h-[40px] w-full desktop:hidden",attrs:{type:"text",placeholder:"Tìm kiếm bài học"},domProps:{value:t.searchField},on:{input:function(e){e.target.composing||(t.searchField=e.target.value)}}}),t._v(" "),e("div"),t._v(" "),e("img",{staticClass:"absolute top-1/2 right-4 transform -translate-y-1/2 w-[20px] h-[20px]",class:"".concat(t.searchField.length>0?"block":"hidden"),attrs:{src:"/images/icons/clear.png",alt:"clear.png"},on:{click:function(e){t.searchField=""}}}),t._v(" "),e("img",{staticClass:"absolute top-1/2 right-4 transform -translate-y-1/2 w-[20px] h-[20px]",class:"".concat(0===t.searchField.length?"block":"hidden"),attrs:{src:"/images/icons/search.png",alt:"search.png"}})]):t._e()])}),[],!1,null,null,null).exports,i={props:{percent:{type:[Number,String],default:0,validator:function(t){var e=parseFloat(t);return!isNaN(e)&&e>=0&&e<=100}},widthPercent:{type:String,default:"120px"},color:{type:String,default:"#57D061"},height:{type:String,default:"10px"},background:{type:String,default:"white"}},data:function(){return{width:"0%",height:"10px",background:"white",color:"#57D061",widthPercent:"120px"}},computed:{computedWidth:function(){var t=parseFloat(this.percent);return isNaN(t)?"0%":"".concat(t,"%")}}},o=n(85072),s=n.n(o),c=n(93799),u={insert:"head",singleton:!1},l=(s()(c.A,u),c.A.locals,r(i,(function(){var t=this,e=t._self._c;return e("div",[e("div",{staticClass:"progress-bar",style:{height:t.height,background:t.background,width:t.widthPercent}},[e("div",{staticClass:"progress-bar-inner",style:{width:t.computedWidth,background:t.color}})])])}),[],!1,null,"2b96e190",null).exports),f=n(72505),d=n.n(f),h=n(11985),p={name:"ChartVocabulary",props:{chartData:{type:Object,required:!0},options:{type:Object,default:function(){return{showTextMaxLearnWord:!1}},required:!1}},watch:{chartData:{handler:function(t){console.log("chartData changed:",t)},deep:!0}},methods:{getBarHeight:function(t){if(!t)return"0%";var e=this.chartData.totalWords||0;if(e<=0)return"1%";var n=t/e*90;return"".concat(Math.max(n,1),"%")}},created:function(){console.log("chartData: ",this.chartData)}},v=n(75903),m={insert:"head",singleton:!1},g=(s()(v.A,m),v.A.locals,r(p,(function(){var t=this,e=t._self._c;return e("div",{staticClass:"chart-container"},[e("div",{staticClass:"chart-html"},[t.options.showTextMaxLearnWord?e("div",{staticClass:"chart-line-max-text text-center"},[e("div",{staticClass:"text-center font-beanbag-medium text-[15px] text-[##073A3B] px-2 py-1 bg-[#CCF8D1] rounded-full"},[t._v("\n        Rừng ngôn từ\n      ")]),t._v(" "),e("div",{staticClass:"text-center text-[14px] text-[#0C403F]"},[t._v("言葉の森")])]):t._e(),t._v(" "),e("div",{staticClass:"chart-columns relative"},[e("div",{staticClass:"chart-line-max flex items-center w-full justify-stretch pr-[30px] absolute left-[-11px]",style:t.options.showTextMaxLearnWord?"top: 12px;":"top: 15px;"},[e("div",{staticClass:"chart-line-max-value font-beanbag-medium text-[20px] text-white bg-[#176867] p-1 rounded-full w-[77px] text-center flex items-center justify-center"},[e("span",{staticClass:"break-normal",staticStyle:{"font-size":"clamp(12px, calc(5cqi - 2px), 17px)"}},[t._v(t._s(t.chartData.totalWords)+" từ")])]),t._v(" "),e("div",{staticClass:"chart-line-max-bar border-dashed border-[1px] border-[#176867] w-full"})]),t._v(" "),e("div",{staticClass:"chart-column",style:{height:t.getBarHeight(t.chartData.learned+t.chartData.super_mastered)}},[t.chartData.learned+t.chartData.super_mastered<t.chartData.totalWords?e("div",{staticClass:"column-value font-beanbag-medium"},[t._v("\n          "+t._s(t.chartData.learned+t.chartData.super_mastered)+" từ\n        ")]):t._e(),t._v(" "),t._m(0),t._v(" "),e("div",{staticClass:"column-label"},[t._v("Đã học")])]),t._v(" "),e("div",{staticClass:"chart-column",style:{height:t.getBarHeight(t.chartData.temporary+t.chartData.super_mastered)}},[t.chartData.temporary+t.chartData.super_mastered<t.chartData.totalWords?e("div",{staticClass:"column-value font-beanbag-medium"},[t._v("\n          "+t._s(t.chartData.temporary+t.chartData.super_mastered)+" từ\n        ")]):t._e(),t._v(" "),t._m(1),t._v(" "),e("div",{staticClass:"column-label"},[t._v("Tạm nhớ")])]),t._v(" "),e("div",{staticClass:"chart-column",style:{height:t.getBarHeight(t.chartData.memorized+t.chartData.super_mastered)}},[t.chartData.memorized+t.chartData.super_mastered<t.chartData.totalWords?e("div",{staticClass:"column-value font-beanbag-medium"},[t._v("\n          "+t._s(t.chartData.memorized+t.chartData.super_mastered)+" từ\n        ")]):t._e(),t._v(" "),t._m(2),t._v(" "),e("div",{staticClass:"column-label"},[t._v("Ghi nhớ")])]),t._v(" "),e("div",{staticClass:"chart-column",style:{height:t.getBarHeight(t.chartData.mastered+t.chartData.super_mastered)}},[t.chartData.mastered+t.chartData.super_mastered<t.chartData.totalWords?e("div",{staticClass:"column-value font-beanbag-medium"},[t._v("\n          "+t._s(t.chartData.mastered+t.chartData.super_mastered)+" từ\n        ")]):t._e(),t._v(" "),t._m(3),t._v(" "),e("div",{staticClass:"column-label"},[t._v("Thuộc lòng")])])])])])}),[function(){var t=this._self._c;return t("div",{staticClass:"column-bar learned-bar"},[t("div",{staticClass:"column-bar-inner"})])},function(){var t=this._self._c;return t("div",{staticClass:"column-bar temporary-bar"},[t("div",{staticClass:"column-bar-inner"})])},function(){var t=this._self._c;return t("div",{staticClass:"column-bar memorized-bar"},[t("div",{staticClass:"column-bar-inner"})])},function(){var t=this._self._c;return t("div",{staticClass:"column-bar mastered-bar"},[t("div",{staticClass:"column-bar-inner"})])}],!1,null,"3fc31493",null).exports),_=n(2543),y=n.n(_),x=r({props:["search","currentLesson","stageName"],data:function(){return{allLesson:allLesson,searchedList:[]}},watch:{search:function(){var t=this;this.searchedList=[],0!=this.search.length&&(this.searchedList=this.allLesson.filter((function(e){return e.name.normalize("NFD").replace(/[\u0300-\u036f]/g,"").toLowerCase().includes(t.search.normalize("NFD").replace(/[\u0300-\u036f]/g,"").toLowerCase())})))}},methods:{gotoLesson:function(t){var e="/khoa-hoc/".concat(t.course_slug,"/lesson/").concat(t.id,"-").concat(t.slug),n=localStorage.getItem("tab-active-lesson"),r=localStorage.getItem("userResult-lesson-".concat(this.currentLesson.id));if(console.log("tabActive: ",n),r&&(r=JSON.parse(r)),console.log("userResult: ",r),console.log("currentLesson: ",this.currentLesson),["exam","last_exam"].includes(this.currentLesson.type)){if(this.examStage>0&&this.examStage<4)return $("#modal-confirm-leave-exam").find(".confirm-leave__title").text("Xác nhận chuyển bài?"),$("#span_text_exercise").text("Thời gian vẫn sẽ tiếp tục chạy khi chuyển bài"),$("#modal-confirm-leave-exam").find(".btn-confirm-leave").text("Chuyển"),$("#modal-confirm-leave-exam").modal("show"),void $("#modal-confirm-leave-exam").data("url",e);window.location.href=e}else{if(this.currentLesson.require||(window.location.href=e),this.currentLesson.require&&r.ratio_correct_answer<.85&&this.currentLesson.percent<85)return $("#modal-confirm-leave").find(".confirm-leave__title").text("Xác nhận chuyển bài?"),$("#span_text_exercise").text("Bài học có điểm số đạt <85% là bài học chưa đạt định mức hoàn thành"),$("#modal-confirm-leave").find(".btn-confirm-leave").text("Chuyển"),$("#modal-confirm-leave").modal("show"),void $("#modal-confirm-leave").data("url",e);window.location.href=e}}}},(function(){var t=this,e=t._self._c;return e("div",{staticClass:"mt-5 font-averta-regular h-[calc(100vh-245px)] overflow-y-auto"},t._l(t.searchedList,(function(n){return e("div",{key:"search-item-".concat(n.id),staticClass:"py-4 border-b cursor-pointer",on:{click:function(e){return t.gotoLesson(n)}}},[e("div",{staticClass:"flex items-center"},[t._m(0,!0),t._v(" "),e("div",{staticClass:"text-base font-averta-semibold text-[#1E1E1E] ml-5 relative"},[e("span",[t._v(t._s(n.name)+"\n          "),n.require?e("img",{staticClass:"w-[12px]",attrs:{src:"/images/icons/require.png",alt:"require.png"}}):t._e()])])]),t._v(" "),e("div",{staticClass:"text-[#757575] text-xs mt-1"},[t._v("\n      "+t._s(n.stage_name)+" - "+t._s(n.category_name)+" - "+t._s(n.group_name)+"\n    ")])])})),0)}),[function(){var t=this._self._c;return t("div",{staticClass:"flex-none"},[t("img",{staticClass:"w-[16px]",attrs:{src:"/images/icons/play2.png",alt:"play button"}})])}],!1,null,null,null).exports,b=r({props:["percent"]},(function(){var t=this,e=t._self._c;return e("div",{staticClass:"relative ml-auto transform scale-x-[-1]"},[e("svg",{staticClass:"w-[36px] h-[36px] sp:scale-75"},[e("circle",{staticClass:"text-[#F5F5F5]",attrs:{"stroke-width":"3",stroke:"currentColor",fill:"transparent",r:"15",cx:"18",cy:"18"}}),t._v(" "),e("circle",{class:Number(t.percent)<85?"text-[#E8B931]":"text-[#57D061]",attrs:{"stroke-width":"3","stroke-dasharray":"96","stroke-dashoffset":96*(100-Number(t.percent))/100,"stroke-linecap":"round",stroke:"currentColor",fill:"transparent",transform:"rotate(-90 18 18)",r:"15",cx:"18",cy:"18"}})]),t._v(" "),e("div",{staticClass:"absolute inset-0 flex items-center justify-center transform scale-x-[-1]"},[e("span",{staticClass:"text-[#414348] sp:text-xs font-averta-semibold text-[10px]"},[t._v("\n      "+t._s(Number(t.percent))+"%\n    ")])])])}),[],!1,null,null,null).exports;function w(t){return w="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},w(t)}function C(){C=function(){return e};var t,e={},n=Object.prototype,r=n.hasOwnProperty,a=Object.defineProperty||function(t,e,n){t[e]=n.value},i="function"==typeof Symbol?Symbol:{},o=i.iterator||"@@iterator",s=i.asyncIterator||"@@asyncIterator",c=i.toStringTag||"@@toStringTag";function u(t,e,n){return Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{u({},"")}catch(t){u=function(t,e,n){return t[e]=n}}function l(t,e,n,r){var i=e&&e.prototype instanceof g?e:g,o=Object.create(i.prototype),s=new A(r||[]);return a(o,"_invoke",{value:S(t,n,s)}),o}function f(t,e,n){try{return{type:"normal",arg:t.call(e,n)}}catch(t){return{type:"throw",arg:t}}}e.wrap=l;var d="suspendedStart",h="suspendedYield",p="executing",v="completed",m={};function g(){}function _(){}function y(){}var x={};u(x,o,(function(){return this}));var b=Object.getPrototypeOf,k=b&&b(b(T([])));k&&k!==n&&r.call(k,o)&&(x=k);var F=y.prototype=g.prototype=Object.create(x);function L(t){["next","throw","return"].forEach((function(e){u(t,e,(function(t){return this._invoke(e,t)}))}))}function E(t,e){function n(a,i,o,s){var c=f(t[a],t,i);if("throw"!==c.type){var u=c.arg,l=u.value;return l&&"object"==w(l)&&r.call(l,"__await")?e.resolve(l.__await).then((function(t){n("next",t,o,s)}),(function(t){n("throw",t,o,s)})):e.resolve(l).then((function(t){u.value=t,o(u)}),(function(t){return n("throw",t,o,s)}))}s(c.arg)}var i;a(this,"_invoke",{value:function(t,r){function a(){return new e((function(e,a){n(t,r,e,a)}))}return i=i?i.then(a,a):a()}})}function S(e,n,r){var a=d;return function(i,o){if(a===p)throw Error("Generator is already running");if(a===v){if("throw"===i)throw o;return{value:t,done:!0}}for(r.method=i,r.arg=o;;){var s=r.delegate;if(s){var c=j(s,r);if(c){if(c===m)continue;return c}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if(a===d)throw a=v,r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);a=p;var u=f(e,n,r);if("normal"===u.type){if(a=r.done?v:h,u.arg===m)continue;return{value:u.arg,done:r.done}}"throw"===u.type&&(a=v,r.method="throw",r.arg=u.arg)}}}function j(e,n){var r=n.method,a=e.iterator[r];if(a===t)return n.delegate=null,"throw"===r&&e.iterator.return&&(n.method="return",n.arg=t,j(e,n),"throw"===n.method)||"return"!==r&&(n.method="throw",n.arg=new TypeError("The iterator does not provide a '"+r+"' method")),m;var i=f(a,e.iterator,n.arg);if("throw"===i.type)return n.method="throw",n.arg=i.arg,n.delegate=null,m;var o=i.arg;return o?o.done?(n[e.resultName]=o.value,n.next=e.nextLoc,"return"!==n.method&&(n.method="next",n.arg=t),n.delegate=null,m):o:(n.method="throw",n.arg=new TypeError("iterator result is not an object"),n.delegate=null,m)}function D(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function O(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function A(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(D,this),this.reset(!0)}function T(e){if(e||""===e){var n=e[o];if(n)return n.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var a=-1,i=function n(){for(;++a<e.length;)if(r.call(e,a))return n.value=e[a],n.done=!1,n;return n.value=t,n.done=!0,n};return i.next=i}}throw new TypeError(w(e)+" is not iterable")}return _.prototype=y,a(F,"constructor",{value:y,configurable:!0}),a(y,"constructor",{value:_,configurable:!0}),_.displayName=u(y,c,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===_||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,y):(t.__proto__=y,u(t,c,"GeneratorFunction")),t.prototype=Object.create(F),t},e.awrap=function(t){return{__await:t}},L(E.prototype),u(E.prototype,s,(function(){return this})),e.AsyncIterator=E,e.async=function(t,n,r,a,i){void 0===i&&(i=Promise);var o=new E(l(t,n,r,a),i);return e.isGeneratorFunction(n)?o:o.next().then((function(t){return t.done?t.value:o.next()}))},L(F),u(F,c,"Generator"),u(F,o,(function(){return this})),u(F,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),n=[];for(var r in e)n.push(r);return n.reverse(),function t(){for(;n.length;){var r=n.pop();if(r in e)return t.value=r,t.done=!1,t}return t.done=!0,t}},e.values=T,A.prototype={constructor:A,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(O),!e)for(var n in this)"t"===n.charAt(0)&&r.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var n=this;function a(r,a){return s.type="throw",s.arg=e,n.next=r,a&&(n.method="next",n.arg=t),!!a}for(var i=this.tryEntries.length-1;i>=0;--i){var o=this.tryEntries[i],s=o.completion;if("root"===o.tryLoc)return a("end");if(o.tryLoc<=this.prev){var c=r.call(o,"catchLoc"),u=r.call(o,"finallyLoc");if(c&&u){if(this.prev<o.catchLoc)return a(o.catchLoc,!0);if(this.prev<o.finallyLoc)return a(o.finallyLoc)}else if(c){if(this.prev<o.catchLoc)return a(o.catchLoc,!0)}else{if(!u)throw Error("try statement without catch or finally");if(this.prev<o.finallyLoc)return a(o.finallyLoc)}}}},abrupt:function(t,e){for(var n=this.tryEntries.length-1;n>=0;--n){var a=this.tryEntries[n];if(a.tryLoc<=this.prev&&r.call(a,"finallyLoc")&&this.prev<a.finallyLoc){var i=a;break}}i&&("break"===t||"continue"===t)&&i.tryLoc<=e&&e<=i.finallyLoc&&(i=null);var o=i?i.completion:{};return o.type=t,o.arg=e,i?(this.method="next",this.next=i.finallyLoc,m):this.complete(o)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),m},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.finallyLoc===t)return this.complete(n.completion,n.afterLoc),O(n),m}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.tryLoc===t){var r=n.completion;if("throw"===r.type){var a=r.arg;O(n)}return a}}throw Error("illegal catch attempt")},delegateYield:function(e,n,r){return this.delegate={iterator:T(e),resultName:n,nextLoc:r},"next"===this.method&&(this.arg=t),m}},e}function k(t,e,n,r,a,i,o){try{var s=t[i](o),c=s.value}catch(t){return void n(t)}s.done?e(c):Promise.resolve(c).then(r,a)}var F={props:{stageName:{type:String,default:""},isUnlock:{type:Boolean,default:!1},examStage:{type:Object,default:function(){}},hasLesson:{type:Boolean,default:!1}},components:{Percentage:b},data:function(){return{menuData:this.hasLesson?menuData:[],currentLesson:this.hasLesson?currentLesson:{},openingGroupIds:[],openingCategoryIds:[],course:course,isUnlock:!!this.hasLesson&&isUnlock,userLesson:this.hasLesson?userLesson:{}}},mounted:function(){this.toogleOpenGroup(this.currentLesson.group_id),this.toogleOpenCategory(+this.currentLesson.category_id,!0)},methods:{colorGroup:function(t){return!this.isUnlock||t.progress>=100?"text-[#757575]":"text-[#1E1E1E]"},isPassedLesson:function(t){return t.progress>=85},lessonNameClass:function(t){return this.isPassedLesson(t)?"text-[#757575]":t.is_free||this.isUnlock?"text-[#1E1E1E]":"text-[#757575]"},toogleOpenGroup:function(t){t=Number(t),this.openingGroupIds.includes(t)?this.openingGroupIds=this.openingGroupIds.filter((function(e){return e!==t})):(this.openingGroupIds.push(t),setTimeout((function(){fixRequireButton()}),500))},toogleOpenCategory:function(t){arguments.length>1&&void 0!==arguments[1]&&arguments[1]?(this.openingCategoryIds=this.menuData.map((function(t){return+t.id})),this.openingCategoryIds=this.openingCategoryIds.filter((function(e){return+e==+t}))):(this.openingCategoryIds.push(t),this.openingCategoryIds=this.openingCategoryIds.filter((function(e){return+e!=+t}))),console.log("Updated Opening IDs:",this.openingCategoryIds)},goToLesson:function(t){var e,n=this;return(e=C().mark((function e(){var r,a,i,o;return C().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(console.log(1111111),r=n,a="/khoa-hoc/".concat(n.course.SEOurl,"/lesson/").concat(t.id,"-").concat(t.slug),i=localStorage.getItem("tab-active-lesson"),o=localStorage.getItem("userResult-lesson-".concat(n.currentLesson.id)),console.log("tabActive: ",i),o&&(o=JSON.parse(o)),console.log("userResult: ",o),console.log("currentLesson: ",n.currentLesson),"last_exam"!==n.currentLesson.type){e.next=12;break}return window.location.href=a,e.abrupt("return");case 12:return e.next=14,$.get(window.location.origin+"/get-lesson-percent/"+n.currentLesson.id).then((function(e){if("exam"===r.currentLesson.type){if(r.examStage>0&&r.examStage<4)return $("#modal-confirm-leave-exam").find(".confirm-leave__title").text("Bạn muốn chuyển bài?"),$("#span_text_exercise").text("Cố gắng học tiếp để hoàn thành tiến trình bạn iu nha!"),$("#modal-confirm-leave-exam").find(".btn-confirm-leave").text("Chuyển"),$("#modal-confirm-leave-exam").modal("show"),void $("#modal-confirm-leave-exam").data("url",a);if(r.currentLesson.require&&e<85)return $("#modal-confirm-leave").find(".confirm-leave__title").text("Bạn muốn chuyển bài?"),$("#span_text_exercise").text("Bài học có điểm số đạt <85% là bài học chưa đạt định mức hoàn thành"),$("#modal-confirm-leave").find(".btn-confirm-leave").text("Chuyển"),$("#modal-confirm-leave").modal("show"),void $("#modal-confirm-leave").data("url",a);window.location.href=a}else if("tabExercise"===i){if(r.currentLesson.require||(window.location.href=a),r.currentLesson.require&&o.ratio_correct_answer<.85&&e<85)return $("#modal-confirm-leave").find(".confirm-leave__title").text("Bạn muốn chuyển bài?"),$("#span_text_exercise").text("Bài học có điểm số đạt <85% là bài học chưa đạt định mức hoàn thành"),$("#modal-confirm-leave").find(".btn-confirm-leave").text("Chuyển"),$("#modal-confirm-leave").modal("show"),void $("#modal-confirm-leave").data("url",a);window.location.href=a}else r.currentLesson.require&&e<85?($("#modal-confirm-leave").modal("show"),$("#modal-confirm-leave").find(".confirm-leave__title").text("Bạn muốn chuyển bài?"),$("#modal-confirm-leave").find(".btn-confirm-leave").text("Chuyển"),$("#modal-confirm-leave").data("url",a)):(t.is_free||r.isUnlock)&&(window.location.href=a)}));case 14:case"end":return e.stop()}}),e)})),function(){var t=this,n=arguments;return new Promise((function(r,a){var i=e.apply(t,n);function o(t){k(i,r,a,o,s,"next",t)}function s(t){k(i,r,a,o,s,"throw",t)}o(void 0)}))})()},convertMinutesToHourAndMinute:function(t){var e=Math.floor(t/60),n=t%60;return"".concat(e,"h").concat(n,"'")},isLocked:function(t){return!this.isUnlock&&!t.is_free}}},L=r(F,(function(){var t=this,e=t._self._c;return e("div",{staticClass:"mt-5 font-averta-regular h-[calc(100vh-245px)] overflow-y-auto"},[e("div",{staticClass:"text-xl text-[#073A3B] font-averta-semibold"},[t._v("\n    "+t._s(t.stageName)+"\n  ")]),t._v(" "),e("div",t._l(t.menuData,(function(n){return e("div",{key:"category-".concat(n.id),staticClass:"lesson-group-item my-2",class:t.openingCategoryIds.includes(+n.id)?"is-open":""},[e("div",{staticClass:"flex items-center cursor-pointer lesson-group-item__head relative",on:{click:function(e){return t.toogleOpenCategory(+n.id)}}},[e("div",{staticClass:"flex-none max-w-[90%] font-averta-semibold text-xl text-[#073A3B]"},[t._v("\n          "+t._s(n.title)+"\n        ")]),t._v(" "),e("div",{staticClass:"ml-4 w-full h-[2px] bg-[#D0D3DA] mr-10"}),t._v(" "),t._m(0,!0)]),t._v(" "),e("div",t._l(n.groups,(function(n){return e("div",{key:"group-".concat(n.id)},[e("div",{staticClass:"py-2 grid grid-cols-1 gap-4 group-list relative font-averta-regular",class:t.openingGroupIds.includes(n.id)?"is-open":""},[e("div",{staticClass:"flex items-center relative",on:{click:function(e){return t.toogleOpenGroup(n.id)}}},[t._m(1,!0),t._v(" "),e("div",{staticClass:"ml-4",class:t.isUnlock?"text-[#1E1E1E]":"text-[#757575]"},[e("div",{staticClass:"mb-1"},[n.is_trial?e("span",{staticClass:"text-xs text-white bg-[#57D061] py-1 px-2 rounded-full"},[t._v("\n                    Học thử\n                  ")]):t._e()]),t._v(" "),e("div",{staticClass:"text-xl cursor-pointer"},[e("span",{staticClass:"font-averta-semibold"},[t._v(t._s(n.name))])]),t._v(" "),e("div",{class:t.colorGroup(n)},[t._v("\n                  "+t._s(n.total_video)+" videos bài giảng・"+t._s(n.total_time[0]>0?n.total_time[0]+" tiếng ":"")+"\n                  "+t._s(n.total_time[1])+" phút\n                ")])]),t._v(" "),e("div",{staticClass:"absolute right-4",class:t.isUnlock||n.is_trial?"show-arrow":"show-lock"},[0===n.lessons.filter((function(t){return t.require})).length&&n.lessons.filter((function(t){return t.progress>=85})).length===n.lessons.length||n.lessons.filter((function(t){return t.require})).length>0&&n.lessons.filter((function(t){return t.progress>=85&&t.require})).length===n.lessons.filter((function(t){return t.require})).length?e("svg",{attrs:{width:"14",height:"10",viewBox:"0 0 14 10",fill:"none",xmlns:"http://www.w3.org/2000/svg"}},[e("path",{attrs:{d:"M12.4191 1L5.08577 8.33333L1.75244 5",stroke:"#57D061","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"}})]):e("img",{staticClass:"w-2 arrow-group",attrs:{src:"/images/icons/arrow-right.png",alt:"arrow"}}),t._v(" "),t._m(2,!0)])]),t._v(" "),e("div",{staticClass:"lessons pl-5 ml-3 border-l-2 border-[#d0d3da]"},t._l(n.lessons,(function(n){return e("div",{key:"lesson-".concat(n.id),staticClass:"lesson my-2 py-2 cursor-pointer bg-cover bg-center bg-no-repeat",style:n.id===t.currentLesson.id?Number(n.progress)>=85?"background: linear-gradient(90deg, #FFFFFF 0%, #CEFFD8 30%, #CEFFD8 70%, #FFFFFF 100%);":"background: linear-gradient(90deg, #FFFFFF 0%, #FFFBEB 31%, #FFFBEB 70%, #FFFFFF 100%)":"",attrs:{id:n.id===t.currentLesson.id?"current-lesson":""},on:{click:function(e){return t.goToLesson(n)}}},[e("div",{staticClass:"flex items-center"},[e("div",{class:"flex-none rounded-[24px] relative overflow-hidden w-[115px] h-[65px] bg-cover bg-center",style:"background-image: url('".concat(n.image,"')")},[n.component_types.includes(2)?e("img",{staticClass:"absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-[20px]",attrs:{src:"/images/icons/play-button.png",alt:"play-button.png"}}):t._e()]),t._v(" "),e("div",{staticClass:"ml-3 w-full"},[e("div",{staticClass:"relative truncate flex",class:t.lessonNameClass(n)},[n.name_html?e("div",{staticClass:"flex text-base truncate max-w-[170px] sp:max-w-[140px]"},[e("div",{staticClass:"name-lesson w-full flex text-base truncate",domProps:{innerHTML:t._s(n.name_html)}}),t._v(" "),n.require?e("img",{staticClass:"w-[12px] h-[12px] min-w-[12px]",attrs:{src:"/images/icons/".concat(t.isPassedLesson(n)?"locked/":"","require.png"),alt:"require.png"}}):t._e()]):e("div",{staticClass:"text-base truncate max-w-[170px] sp:max-w-[140px]"},[e("div",{staticClass:"relative name-lesson truncate icon-require pr-[15px]"},[t._v(t._s(n.name)+"\n                          "),n.require?e("img",{staticClass:"w-[12px] min-w-[12px] absolute right-0",attrs:{src:"/images/icons/".concat(t.isPassedLesson(n)?"locked/":"","require.png"),alt:"require.png"}}):t._e()])])]),t._v(" "),["last_exam"].includes(n.type)?[n.passed_exam_result?e("div",{staticClass:"mt-1 font-averta-semibold text-[14px] text-[#07403F] bg-[#86F082] min-w-[100px] px-1 rounded-full inline-flex text-center justify-center"},[t._v("Đạt")]):n.last_exam_result&&!n.last_exam_result.submit_at?e("div",{staticClass:"mt-1 font-averta-semibold text-[14px] text-[#414348] bg-[#F5F5F5] min-w-[100px] px-1 rounded-full inline-flex text-center justify-center"},[t._v("Chưa nộp")]):n.exam_result&&!n.passed_exam_result?e("div",{staticClass:"mt-1 font-averta-semibold text-[14px] text-[#682D03] bg-[#FFF1C2] min-w-[100px] px-1 rounded-full inline-flex text-center justify-center"},[t._v("Chưa đạt")]):t._e()]:t._e(),t._v(" "),e("div",{staticClass:"flex flex-wrap gap-y-1 gap-x-2 mt-2",class:t.lessonNameClass(n)},[n.component_types.includes(2)?e("div",{staticClass:"flex items-center"},[e("img",{staticClass:"w-[12px] flex-none",attrs:{src:"/images/icons/".concat(t.isLocked(n)?"locked/":"","play2.png"),alt:"play2.png"}}),t._v(" "),e("span",{staticClass:"ml-1 text-sm"},[t._v(" Video ")])]):t._e(),t._v(" "),n.component_types.includes(3)||n.component_types.includes(13)||n.component_types.includes(14)||n.component_types.includes(15)||n.component_types.includes(16)||n.component_types.includes(17)?e("div",{staticClass:"flex items-center"},["flashcard"===n.lesson_type?e("img",{staticClass:"w-[12px] flex-none",attrs:{src:"/images/icons/tag-flashcard.svg",alt:"file.png"}}):e("img",{staticClass:"w-[12px] flex-none",attrs:{src:"/images/icons/".concat(t.isLocked(n)?"locked/":"","file.png"),alt:"file.png"}}),t._v(" "),e("span",{staticClass:"ml-1 text-sm"},[t._v(" "+t._s("flashcard"===n.lesson_type?n.component_types.filter((function(t){return 17===t})).length+" Từ vựng":"Bài tập")+" ")])]):t._e(),t._v(" "),n.component_types.includes(1)||n.component_types.includes(8)?e("div",{staticClass:"flex items-center"},[e("img",{staticClass:"w-[12px] flex-none",attrs:{src:"/images/icons/".concat(t.isLocked(n)?"locked/":"","docs.png"),alt:"docs.png"}}),t._v(" "),e("span",{staticClass:"ml-1 text-sm"},[t._v(" Tài liệu ")])]):t._e(),t._v(" "),n.component_types.includes(5)?e("div",{staticClass:"flex items-center"},[e("img",{staticClass:"w-[12px] flex-none",attrs:{src:"/images/icons/".concat(t.isLocked(n)?"locked/":"","speaker.png"),alt:"speaker.png"}}),t._v(" "),e("span",{staticClass:"ml-1 text-sm"},[t._v(" File âm thanh ")])]):t._e(),t._v(" "),n.expect_time?e("div",{staticClass:"flex items-center"},[e("img",{staticClass:"w-[12px] flex-none",attrs:{src:"/images/icons/".concat(t.isLocked(n)?"locked/":"","clock.png"),alt:"clock.png"}}),t._v(" "),e("span",{staticClass:"ml-1 text-sm"},[t._v("\n                          "+t._s(t.convertMinutesToHourAndMinute(n.expect_time))+"\n                        ")])]):t._e()])],2),t._v(" "),t.isUnlock||n.is_free?!["exam","last_exam"].includes(n.type)&&n.progress<85?[e("Percentage",{attrs:{percent:n.progress}})]:["exam","last_exam"].includes(n.type)&&n.passed_exam_result&&n.progress>=85||!["exam","last_exam"].includes(n.type)&&n.progress>=85?e("div",{staticClass:"w-[36px] h-[36px] flex-none flex items-center justify-center ml-auto"},[e("img",{staticClass:"w-[24px]",attrs:{src:"/images/icons/done.png",alt:"done.png"}})]):t._e():e("div",{staticClass:"w-[36px] h-[36px] flex-none flex items-center justify-center ml-auto"},[e("img",{staticClass:"w-[20px]",attrs:{src:"/images/icons/lock.png",alt:"lock"}})])],2)])})),0)])])})),0)])})),0)])}),[function(){var t=this._self._c;return t("div",{staticClass:"absolute right-4"},[t("img",{staticClass:"arrow-icon w-2",attrs:{src:"/images/icons/arrow-right.png",alt:"arrow"}})])},function(){var t=this._self._c;return t("div",{staticClass:"flex-none"},[t("img",{staticClass:"w-[16px]",attrs:{src:"/images/icons/play2.png",alt:"play button"}})])},function(){var t=this._self._c;return t("span",{staticClass:"lock-icon"},[t("img",{staticClass:"w-[20px]",attrs:{src:"/images/icons/lock.png",alt:"lock.png"}})])}],!1,null,null,null).exports,E=r({name:"TextareaAutosize",props:{value:{type:[String,Number],default:""},autosize:{type:Boolean,default:!0},minHeight:{type:[Number],default:null},maxHeight:{type:[Number],default:null},hasFile:{type:Boolean,default:!1},important:{type:[Boolean,Array],default:!1}},data:function(){return{val:null,maxHeightScroll:!1,height:"auto"}},computed:{computedStyles:function(){return this.autosize?{resize:this.isResizeImportant?"none !important":"none",height:this.height,overflow:this.maxHeightScroll?"auto":this.isOverflowImportant?"hidden !important":"hidden"}:{}},isResizeImportant:function(){var t=this.important;return!0===t||Array.isArray(t)&&t.includes("resize")},isOverflowImportant:function(){var t=this.important;return!0===t||Array.isArray(t)&&t.includes("overflow")},isHeightImportant:function(){var t=this.important;return!0===t||Array.isArray(t)&&t.includes("height")}},watch:{value:function(t){this.val=t},val:function(t){this.$nextTick(this.resize),this.$emit("input",t)},minHeight:function(){this.$nextTick(this.resize)},maxHeight:function(){this.$nextTick(this.resize)},autosize:function(t){t&&this.resize()},hasFile:function(t){this.resize()}},methods:{resize:function(){var t=this,e=this.isHeightImportant?"important":"";return this.height="auto".concat(e?" !important":""),this.$nextTick((function(){var n=t.$el.scrollHeight+1;t.minHeight&&(n=n<t.minHeight?t.minHeight:n),t.maxHeight&&(n>t.maxHeight?(n=t.maxHeight,t.maxHeightScroll=!0):t.maxHeightScroll=!1);var r=n+(t.hasFile?50:0)+"px";t.height="".concat(r).concat(e?" !important":"")})),this}},created:function(){this.val=this.value},mounted:function(){this.resize()}},(function(){var t=this;return(0,t._self._c)("textarea",{directives:[{name:"model",rawName:"v-model",value:t.val,expression:"val"}],style:t.computedStyles,attrs:{rows:"1",placeholder:"Nhập bình luận của bạn ở đây"},domProps:{value:t.val},on:{focus:t.resize,input:function(e){e.target.composing||(t.val=e.target.value)}}})}),[],!1,null,null,null).exports,S=r({props:{comment:{type:Object,default:function(){}},auth:{type:Object,default:function(){}},currentFlashcard:{type:Object,default:function(){}},hasLesson:{type:Boolean,default:!0}},data:function(){return{currentLesson:this.hasLesson?currentLesson:{},val:"",isLoading:!1}},components:{Reply:E},methods:{sendReply:function(){var t=this;t.isLoading||0==t.val.length||(t.isLoading=!0,d().post("/api/comments/add-new-reply",{tbid:"flashcard"===t.currentLesson.type||17===t.currentFlashcard.type?t.currentFlashcard.id:t.currentLesson.id,tbname:"flashcard"===this.currentLesson.type||17===t.currentFlashcard.type?"flashcard":"lesson",parent_id:t.comment.id,content:t.val}).then((function(e){t.val="",t.$emit("update-reply",e.data)})).catch((function(t){console.log(t)})).finally((function(){t.isLoading=!1})))}}},(function(){var t=this,e=t._self._c;return e("div",{staticClass:"relative"},[e("Reply",{staticClass:"bg-[#F5F5F5] text-[#1E1E1E] placeholder:text-[#757575] p-3 pr-[35px] rounded",staticStyle:{width:"calc(100% - 10px)"},model:{value:t.val,callback:function(e){t.val=e},expression:"val"}}),t._v(" "),e("img",{staticClass:"cursor-pointer w-[20px] h-[20px] absolute right-[15px] -translate-y-1/2",staticStyle:{top:"calc(50% - 3px)"},attrs:{src:t.val.length>0?"/images/icons/send-available.png":"/images/icons/send.png",alt:"send"},on:{click:t.sendReply}})],1)}),[],!1,null,null,null).exports;function j(t){return j="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},j(t)}function D(){D=function(){return e};var t,e={},n=Object.prototype,r=n.hasOwnProperty,a=Object.defineProperty||function(t,e,n){t[e]=n.value},i="function"==typeof Symbol?Symbol:{},o=i.iterator||"@@iterator",s=i.asyncIterator||"@@asyncIterator",c=i.toStringTag||"@@toStringTag";function u(t,e,n){return Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{u({},"")}catch(t){u=function(t,e,n){return t[e]=n}}function l(t,e,n,r){var i=e&&e.prototype instanceof g?e:g,o=Object.create(i.prototype),s=new A(r||[]);return a(o,"_invoke",{value:L(t,n,s)}),o}function f(t,e,n){try{return{type:"normal",arg:t.call(e,n)}}catch(t){return{type:"throw",arg:t}}}e.wrap=l;var d="suspendedStart",h="suspendedYield",p="executing",v="completed",m={};function g(){}function _(){}function y(){}var x={};u(x,o,(function(){return this}));var b=Object.getPrototypeOf,w=b&&b(b(T([])));w&&w!==n&&r.call(w,o)&&(x=w);var C=y.prototype=g.prototype=Object.create(x);function k(t){["next","throw","return"].forEach((function(e){u(t,e,(function(t){return this._invoke(e,t)}))}))}function F(t,e){function n(a,i,o,s){var c=f(t[a],t,i);if("throw"!==c.type){var u=c.arg,l=u.value;return l&&"object"==j(l)&&r.call(l,"__await")?e.resolve(l.__await).then((function(t){n("next",t,o,s)}),(function(t){n("throw",t,o,s)})):e.resolve(l).then((function(t){u.value=t,o(u)}),(function(t){return n("throw",t,o,s)}))}s(c.arg)}var i;a(this,"_invoke",{value:function(t,r){function a(){return new e((function(e,a){n(t,r,e,a)}))}return i=i?i.then(a,a):a()}})}function L(e,n,r){var a=d;return function(i,o){if(a===p)throw Error("Generator is already running");if(a===v){if("throw"===i)throw o;return{value:t,done:!0}}for(r.method=i,r.arg=o;;){var s=r.delegate;if(s){var c=E(s,r);if(c){if(c===m)continue;return c}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if(a===d)throw a=v,r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);a=p;var u=f(e,n,r);if("normal"===u.type){if(a=r.done?v:h,u.arg===m)continue;return{value:u.arg,done:r.done}}"throw"===u.type&&(a=v,r.method="throw",r.arg=u.arg)}}}function E(e,n){var r=n.method,a=e.iterator[r];if(a===t)return n.delegate=null,"throw"===r&&e.iterator.return&&(n.method="return",n.arg=t,E(e,n),"throw"===n.method)||"return"!==r&&(n.method="throw",n.arg=new TypeError("The iterator does not provide a '"+r+"' method")),m;var i=f(a,e.iterator,n.arg);if("throw"===i.type)return n.method="throw",n.arg=i.arg,n.delegate=null,m;var o=i.arg;return o?o.done?(n[e.resultName]=o.value,n.next=e.nextLoc,"return"!==n.method&&(n.method="next",n.arg=t),n.delegate=null,m):o:(n.method="throw",n.arg=new TypeError("iterator result is not an object"),n.delegate=null,m)}function S(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function O(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function A(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(S,this),this.reset(!0)}function T(e){if(e||""===e){var n=e[o];if(n)return n.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var a=-1,i=function n(){for(;++a<e.length;)if(r.call(e,a))return n.value=e[a],n.done=!1,n;return n.value=t,n.done=!0,n};return i.next=i}}throw new TypeError(j(e)+" is not iterable")}return _.prototype=y,a(C,"constructor",{value:y,configurable:!0}),a(y,"constructor",{value:_,configurable:!0}),_.displayName=u(y,c,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===_||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,y):(t.__proto__=y,u(t,c,"GeneratorFunction")),t.prototype=Object.create(C),t},e.awrap=function(t){return{__await:t}},k(F.prototype),u(F.prototype,s,(function(){return this})),e.AsyncIterator=F,e.async=function(t,n,r,a,i){void 0===i&&(i=Promise);var o=new F(l(t,n,r,a),i);return e.isGeneratorFunction(n)?o:o.next().then((function(t){return t.done?t.value:o.next()}))},k(C),u(C,c,"Generator"),u(C,o,(function(){return this})),u(C,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),n=[];for(var r in e)n.push(r);return n.reverse(),function t(){for(;n.length;){var r=n.pop();if(r in e)return t.value=r,t.done=!1,t}return t.done=!0,t}},e.values=T,A.prototype={constructor:A,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(O),!e)for(var n in this)"t"===n.charAt(0)&&r.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var n=this;function a(r,a){return s.type="throw",s.arg=e,n.next=r,a&&(n.method="next",n.arg=t),!!a}for(var i=this.tryEntries.length-1;i>=0;--i){var o=this.tryEntries[i],s=o.completion;if("root"===o.tryLoc)return a("end");if(o.tryLoc<=this.prev){var c=r.call(o,"catchLoc"),u=r.call(o,"finallyLoc");if(c&&u){if(this.prev<o.catchLoc)return a(o.catchLoc,!0);if(this.prev<o.finallyLoc)return a(o.finallyLoc)}else if(c){if(this.prev<o.catchLoc)return a(o.catchLoc,!0)}else{if(!u)throw Error("try statement without catch or finally");if(this.prev<o.finallyLoc)return a(o.finallyLoc)}}}},abrupt:function(t,e){for(var n=this.tryEntries.length-1;n>=0;--n){var a=this.tryEntries[n];if(a.tryLoc<=this.prev&&r.call(a,"finallyLoc")&&this.prev<a.finallyLoc){var i=a;break}}i&&("break"===t||"continue"===t)&&i.tryLoc<=e&&e<=i.finallyLoc&&(i=null);var o=i?i.completion:{};return o.type=t,o.arg=e,i?(this.method="next",this.next=i.finallyLoc,m):this.complete(o)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),m},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.finallyLoc===t)return this.complete(n.completion,n.afterLoc),O(n),m}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.tryLoc===t){var r=n.completion;if("throw"===r.type){var a=r.arg;O(n)}return a}}throw Error("illegal catch attempt")},delegateYield:function(e,n,r){return this.delegate={iterator:T(e),resultName:n,nextLoc:r},"next"===this.method&&(this.arg=t),m}},e}function O(t,e,n,r,a,i,o){try{var s=t[i](o),c=s.value}catch(t){return void n(t)}s.done?e(c):Promise.resolve(c).then(r,a)}function A(t){return function(){var e=this,n=arguments;return new Promise((function(r,a){var i=t.apply(e,n);function o(t){O(i,r,a,o,s,"next",t)}function s(t){O(i,r,a,o,s,"throw",t)}o(void 0)}))}}function T(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,r)}return n}function I(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?T(Object(n),!0).forEach((function(e){P(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):T(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}function P(t,e,n){return(e=function(t){var e=function(t,e){if("object"!=j(t)||!t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var r=n.call(t,e||"default");if("object"!=j(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==j(e)?e:e+""}(e))in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function B(t){return function(t){if(Array.isArray(t))return R(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(t){if("string"==typeof t)return R(t,e);var n={}.toString.call(t).slice(8,-1);return"Object"===n&&t.constructor&&(n=t.constructor.name),"Map"===n||"Set"===n?Array.from(t):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?R(t,e):void 0}}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function R(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=Array(e);n<e;n++)r[n]=t[n];return r}var V={props:{auth:{type:Object,default:function(){}},currentFlashcard:{type:Object,default:function(){}},hasLesson:{type:Boolean,default:!0}},components:{Reply:S,CommentForm:E},data:function(){return{currentLesson:this.hasLesson?currentLesson:{},isLoading:!1,page:1,adminName:"Dũng Mori",openningReplies:[],comments:[],comment_content:"",last_page:1,isSubmitting:!1,files:[],loadingDeleteReply:!1}},mounted:function(){this.hasLesson&&this.getComments()},watch:{currentFlashcard:{handler:function(t,e){!t||e&&t.id===e.id||(this.comments=[],console.log("newVal: ",t),console.log("Flashcard changed, getting new comments"),this.getComments())},deep:!0}},methods:{getComments:function(){arguments.length>1&&void 0!==arguments[1]||this.currentLesson.id;"flashcard"===this.currentLesson.type&&this.$emit("update-unread");var t,e={page:this.page,lesson_id:this.currentLesson.id};"flashcard"===this.currentLesson.type&&(e.id=null===(t=this.currentFlashcard)||void 0===t?void 0:t.id,e.name="flashcard");var n=new URLSearchParams(window.location.search).get("focus_fc");n&&(e.id=n);var r=this;console.log("self.currentFlashcard: ",r.currentFlashcard),r.isLoading||(!this.hasLesson&&this.currentFlashcard&&17===this.currentFlashcard.type&&(e.id=this.currentFlashcard.id,e.name="flashcard"),console.log("data: ",e),r.isLoading=!0,d().get("/api/comment/list",{withCredentials:!0,params:e}).then((function(t){var e=t.data;console.log("res getComments(): ",e),r.comments=[].concat(B(r.comments),B(e.data)),r.last_page=e.paginate.last_page})).catch((function(t){console.log(t)})).finally((function(){r.isLoading=!1})))},escapeHtml:function(t){var e=document.createElement("textarea");return e.textContent=t,e.innerHTML},toogleReply:function(t){this.openningReplies.includes(t.id)?this.openningReplies=this.openningReplies.filter((function(e){return e!==t.id})):(this.openningReplies.push(t.id),t.is_owner&&t.replies.length>0&&(d().post("/api/comment/update-read-reply",{comment_id:t.id}),this.$emit("update-unread")))},onUpdateReply:function(t){var e=this,n={id:t.id,is_admin:!1,is_owner:!0,image:t.img,content:t.content,created_at:t.time_created,user_info:{name:e.auth.name,avatar:e.auth.avatar},parent_id:t.parent_id,count_like:0,comment_like:[]};e.comments=e.comments.map((function(e){return e.id===t.parent_id?I(I({},e),{},{replies:[].concat(B(e.replies),[n])}):e}))},onLoadMore:function(){this.page>=this.last_page||(this.page++,this.getComments())},onImageClick:function(){this.files.length>=3?alert("Bạn chỉ được phép đăng tối đa 3 ảnh"):this.$refs.imageRef.click()},onChangeImage:function(t){var e=this,n=t.target.files[0];if(e.files.length>=3)alert("Bạn chỉ được phép đăng tối đa 3 ảnh");else if(n){var r=new FormData;r.append("image",n),r.append("object","comment"),d().post("/upload-image",r,{headers:{"Content-Type":"multipart/form-data"}}).then((function(t){e.files.push(t.data),e.$refs.imageRef.value=""})).catch((function(t){console.log(t)}))}},onSendComment:function(){window.location.href.includes("n4")?ga("send","event","hoc_thu_cate","comment_new_n4","comment_label"):window.location.href.includes("n5")&&ga("send","event","hoc_thu_cate","comment_new_n5","comment_label");var t=this;if(!t.isSubmitting){t.isSubmitting=!0;var e=t.files.join(",");d().post("/api/comments/add-new-comment",{tbid:"flashcard"===t.currentLesson.type||17===t.currentFlashcard.type?t.currentFlashcard.id:t.currentLesson.id,tbname:"flashcard"===t.currentLesson.type||17===t.currentFlashcard.type?"flashcard":"lesson",content:t.comment_content,images:e}).then((function(e){t.comment_content="";var n={id:e.data.id,is_admin:!1,is_owner:!0,image:e.data.img,content:e.data.content,created_at:e.data.time_created,replies:[],user_info:{name:t.auth.name,avatar:t.auth.avatar},comment_like:[]};t.files=[],t.comments=[n].concat(B(t.comments)),n.count_like=0,n.pin=!1,t.$emit("comment-added",{flashcardId:t.currentFlashcard?t.currentFlashcard.id:null,comment:n});var r=new CustomEvent("comment-added",{detail:{flashcardId:t.currentFlashcard?t.currentFlashcard.id:null,comment:n}});document.dispatchEvent(r)})).catch((function(t){console.log(t)})).finally((function(){t.isSubmitting=!1}))}},onDeleteComment:function(t){self=this,d().post("/api/comments/delete-comment",{id:t.id}).then((function(e){self.comments=self.comments.filter((function(e){return e.id!==t.id}))})).catch((function(t){console.log(t)}))},onDeleteReply:function(t,e){var n=this;return A(D().mark((function r(){return D().wrap((function(r){for(;;)switch(r.prev=r.next){case 0:if(!self.loadingDeleteReply){r.next=2;break}return r.abrupt("return");case 2:return self=n,self.loadingDeleteReply=!0,r.next=6,d().post("/api/comments/delete-comment",{id:e.id}).then(function(){var n=A(D().mark((function n(r){var a;return D().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:return a=self.comments.findIndex((function(e){return e.id===t.id})),self.$set(self.comments[a],"replies",B(t.replies).filter((function(t){return t.id!==e.id}))),self.loadingDeleteReply=!1,n.next=5,self.$nextTick();case 5:case"end":return n.stop()}}),n)})));return function(t){return n.apply(this,arguments)}}()).catch((function(t){console.log(t)}));case 6:case"end":return r.stop()}}),r)})))()},likeCmt:function(t){var e=this,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"comment";console.log("cmt: ",t),console.log("this.auth.id: ",this.auth.id),console.log("this.comments: ",this.comments);var r={id:t.id,lesson_id:this.currentLesson.id};console.log("this.comments: ",this.comments),d().post("/api/comments/like-comment",r).then((function(r){e.comments=e.comments.map((function(r){return"comment"===n?r.id===t.id&&(r.comment_like.includes(parseInt(e.auth.id))?r.comment_like=r.comment_like.filter((function(t){return t!==parseInt(e.auth.id)})):r.comment_like.push(parseInt(e.auth.id))):r.id===t.parent_id&&(r.replies=r.replies.map((function(n){return n.id===t.id&&(n.comment_like.includes(parseInt(e.auth.id))?n.comment_like=n.comment_like.filter((function(t){return t!==parseInt(e.auth.id)})):n.comment_like.push(parseInt(e.auth.id))),n}))),r})),console.log(r)})).catch((function(t){console.log(t)}))}}},z=r(V,(function(){var t=this,e=t._self._c;return e("div",{staticClass:"relative",style:"height: calc(100vh - ".concat(t.hasLesson?245:175,"px);")},[t.isLoading&&0===t.comments.length?[t._m(0)]:t._e(),t._v(" "),e("div",{ref:"commentContainer",staticClass:"mt-5 font-averta-regular h-[calc(100vh-300px)] pb-10 overflow-y-auto font-averta-regular relative"},[t.comments.length>0?t._l(t.comments,(function(n){return e("div",{key:"comment-".concat(n.id),staticClass:"border-b mb-2 pb-2"},[e("div",{staticClass:"flex items-start"},[e("div",{staticClass:"flex-none"},[e("img",{staticClass:"w-[40px] h-[40px] rounded-full",attrs:{src:n.user_info.avatar,alt:"avatar"}})]),t._v(" "),e("div",{staticClass:"ml-3 w-full relative"},[t.openningReplies.includes(n.id)?e("div",{staticClass:"absolute h-[calc(100%-72px)] w-[1px] top-[60px] left-[-32px] bg-[#D0D3DA]"}):t._e(),t._v(" "),e("div",{staticClass:"relative"},[e("div",{staticClass:"font-averta-semibold text-xl"},[t._v("\n                "+t._s(n.user_info.name)+"\n              ")]),t._v(" "),e("div",{staticClass:"text-[15px]"},[e("span",[t._v(t._s(n.created_at))]),t._v(" "),n.is_owner?e("span",{staticClass:"ml-1"},[e("img",{staticClass:"w-[16px] h-[16px]",attrs:{src:"/images/icons/pin.png",alt:"pin"}})]):t._e()]),t._v(" "),n.is_owner?e("div",{staticClass:"absolute right-0 top-0"},[e("img",{staticClass:"w-[36px] h-[36px] cursor-pointer",attrs:{src:"/images/icons/delete.png",alt:"delete.png"},on:{click:function(e){return t.onDeleteComment(n)}}})]):t._e()]),t._v(" "),e("div",{staticClass:"mt-3 text-base whitespace-pre-line",domProps:{innerHTML:t._s(t.escapeHtml(n.content))}}),t._v(" "),n.image?e("div",{staticClass:"pr-2 mb-1"},[e("a",{attrs:{"data-fancybox":"images",href:n.image.replace("small","default")}},[e("img",{attrs:{src:n.image}})])]):t._e(),t._v(" "),e("div",[e("div",{staticClass:"text-[#009951] cursor-pointer flex items-center"},[e("svg",{staticClass:"mr-1 noFlip",attrs:{"data-v-7dd4604c":"",width:"13",height:"12",viewBox:"0 0 13 12",fill:"none",xmlns:"http://www.w3.org/2000/svg"},on:{click:function(e){return t.likeCmt(n)}}},[e("path",{attrs:{"data-v-7dd4604c":"",d:"M9.38889 0.723633C11.1046 0.723633 12.4996 2.1159 12.5 3.84852C12.493 5.95749 11.518 7.64456 10.2933 8.89844C9.06415 10.1569 7.61344 10.9461 6.74434 11.2477C6.70298 11.2611 6.61724 11.2756 6.50542 11.2756C6.39725 11.2756 6.30516 11.262 6.25054 11.2459C5.38046 10.9429 3.92943 10.1541 2.70106 8.89818C1.47455 7.64419 0.5 5.95749 0.5 3.8492C0.5 2.11626 1.89522 0.723633 3.61111 0.723633C4.6312 0.723633 5.52625 1.20724 6.10079 1.96912L6.5 2.49851L6.89921 1.96912C7.47375 1.20724 8.3688 0.723633 9.38889 0.723633Z",fill:n.comment_like.includes(parseInt(t.auth.id))?"#009951":"none",stroke:"#009951"}})]),t._v(" "),e("div",{staticClass:"count-like"},[t._v("\n                  "+t._s(n.comment_like.length)+"\n                ")]),t._v(" "),e("div",{on:{click:function(e){return t.toogleReply(n)}}},[t._v("\n                  ・"+t._s(n.replies.length)+" phản hồi\n                ")])]),t._v(" "),t.openningReplies.includes(n.id)?e("div",{staticClass:"my-3"},[t._l(n.replies,(function(r){return e("div",{key:"reply-".concat(r.id),staticClass:"my-2"},[e("div",{staticClass:"flex items-start"},[e("div",{staticClass:"image flex-none"},[e("img",{staticClass:"w-[40px] h-[40px] rounded-full",attrs:{src:r.user_info.avatar,alt:"avatar"}})]),t._v(" "),e("div",{staticClass:"ml-2 relative w-full group mb-2"},[r.is_owner?e("div",{staticClass:"absolute right-0 top-0 hidden group-hover:inline-flex"},[e("img",{staticClass:"w-[36px] h-[36px] cursor-pointer",attrs:{src:"/images/icons/delete.png",alt:"delete.png"},on:{click:function(e){return t.onDeleteReply(n,r)}}})]):t._e(),t._v(" "),e("div",{staticClass:"font-averta-semibold text-xl"},[t._v("\n                        "+t._s(r.user_info.name)+"\n                      ")]),t._v(" "),e("div",{staticClass:"text-[15px]"},[t._v("\n                        "+t._s(r.created_at)+"\n                      ")]),t._v(" "),e("div",{staticClass:"text-base mt-2"},[e("div",{staticClass:"whitespace-pre-line",domProps:{innerHTML:t._s(t.escapeHtml(r.content))}})]),t._v(" "),e("div",{staticClass:"flex items-center"},[e("svg",{staticClass:"mr-1 noFlip cursor-pointer",attrs:{"data-v-7dd4604c":"",width:"13",height:"12",viewBox:"0 0 13 12",fill:"none",xmlns:"http://www.w3.org/2000/svg"},on:{click:function(e){return t.likeCmt(r,"reply")}}},[e("path",{attrs:{"data-v-7dd4604c":"",d:"M9.38889 0.723633C11.1046 0.723633 12.4996 2.1159 12.5 3.84852C12.493 5.95749 11.518 7.64456 10.2933 8.89844C9.06415 10.1569 7.61344 10.9461 6.74434 11.2477C6.70298 11.2611 6.61724 11.2756 6.50542 11.2756C6.39725 11.2756 6.30516 11.262 6.25054 11.2459C5.38046 10.9429 3.92943 10.1541 2.70106 8.89818C1.47455 7.64419 0.5 5.95749 0.5 3.8492C0.5 2.11626 1.89522 0.723633 3.61111 0.723633C4.6312 0.723633 5.52625 1.20724 6.10079 1.96912L6.5 2.49851L6.89921 1.96912C7.47375 1.20724 8.3688 0.723633 9.38889 0.723633Z",fill:r.comment_like.includes(parseInt(t.auth.id))?"#009951":"none",stroke:"#009951"}})]),t._v(" "),e("div",{staticClass:"count-like text-[#009951]"},[t._v("\n                          "+t._s(r.comment_like.length)+"\n                        ")])])])])])})),t._v(" "),t.auth.id?e("div",{staticClass:"flex items-start"},[e("div",{staticClass:"flex-none"},[e("img",{staticClass:"w-[40px] h-[40px] rounded-full",attrs:{src:t.auth.avatar,alt:"avatar"}})]),t._v(" "),t.auth.id?e("div",{staticClass:"w-full ml-2"},[e("div",{staticClass:"relative"},[e("Reply",{attrs:{hasLesson:t.hasLesson,comment:n,auth:t.auth,"current-flashcard":t.currentFlashcard},on:{"update-reply":t.onUpdateReply}})],1)]):t._e()]):t._e()],2):t._e()])])])])})):t._e(),t._v(" "),t.isLoading&&t.comments.length>0?[t._m(1)]:t._e(),t._v(" "),t.page<t.last_page&&t.comments.length>0&&0==t.isLoading?e("div",{staticClass:"text-[#57D061] text-base w-full flex items-center justify-center mt-3 py-2 border border-dashed border-[#57D061] rounded cursor-pointer",on:{click:t.onLoadMore}},[t._v("\n      Tải thêm bình luận\n    ")]):t._e()],2),t._v(" "),t.auth.id?e("div",{staticClass:"absolute bottom-0 left-0 w-full flex items-start"},[e("div",{staticClass:"flex-none"},[e("img",{staticClass:"w-[40px] h-[40px] rounded-full",attrs:{src:t.auth.avatar,alt:"avatar"}})]),t._v(" "),e("div",{staticClass:"relative ml-3 w-full"},[e("CommentForm",{staticClass:"bg-[#F5F5F5] text-[#1E1E1E] placeholder:text-[#757575] p-3 rounded pr-[55px] w-[calc(100%-10px)]",attrs:{"has-file":t.files.length>0},model:{value:t.comment_content,callback:function(e){t.comment_content=e},expression:"comment_content"}}),t._v(" "),e("div",{staticClass:"absolute left-[16px] bottom-[10px]"},[e("div",{staticClass:"relative flex items-center justify-start gap-x-4"},t._l(t.files,(function(n,r){return e("div",{key:"image-key-".concat(r),staticClass:"w-[46px] h-[46px] rounded relative bg-cover bg-center",style:"background-image: url(/cdn/comment/small/".concat(n,")"),attrs:{alt:"image"}},[e("img",{staticClass:"w-[20px] absolute right-0.5 top-0.5 cursor-pointer",attrs:{src:"/images/icons/delete2.png",alt:"delete2.png"},on:{click:function(e){return t.files.splice(r,1)}}})])})),0)]),t._v(" "),e("input",{ref:"imageRef",staticStyle:{display:"none"},attrs:{type:"file"},on:{change:t.onChangeImage}}),t._v(" "),e("img",{staticClass:"cursor-pointer w-[20px] h-[20px] absolute right-[50px] -translate-y-1/2",staticStyle:{top:"calc(50%)"},attrs:{src:"/images/icons/image.png",alt:"image click"},on:{click:t.onImageClick}}),t._v(" "),e("img",{staticClass:"cursor-pointer w-[20px] h-[20px] absolute right-[20px] -translate-y-1/2",staticStyle:{top:"calc(50%)"},attrs:{src:t.comment_content.length>0?"/images/icons/send-available.png":"/images/icons/send.png",alt:"send"},on:{click:t.onSendComment}})],1)]):e("div",{staticClass:"absolute bottom-0 left-0 w-full flex items-items justify-center h-[50px] font-averta-regular text-base items-center"},[t._m(2),t._v(" "),e("span",{staticClass:"ml-1"},[t._v("để thêm bình luận")])])],2)}),[function(){var t=this._self._c;return t("div",{staticClass:"mt-5 flex items-center justify-center h-[430px] sp:h-[calc(100vh-300px)]"},[t("img",{staticClass:"w-[60px] h-[60px]",attrs:{src:"/images/icons/loading.gif",alt:"loading"}})])},function(){var t=this._self._c;return t("div",{staticClass:"flex items-center justify-center mt-3"},[t("img",{staticClass:"w-[40px] h-[40px]",attrs:{src:"/images/icons/loading.gif",alt:"loading"}})])},function(){var t=this._self._c;return t("a",{attrs:{"data-fancybox":"","data-animation-duration":"300","data-src":"#auth-container"}},[t("span",{staticClass:"text-[#EF6D13] cursor-pointer underline font-averta-bold",attrs:{onclick:"swichTab('login')"}},[this._v("Đăng nhập")])])}],!1,null,null,null).exports,M=r({props:{currentTab:{type:String,default:"lesson"},totalUnread:{type:Number,default:0},auth:{type:Object,default:function(){}},type:{type:String,default:"flashcard"},hasLesson:{type:Boolean,default:!0}},data:function(){return{nextLesson:this.hasLesson?nextLesson:{},user:this.hasLesson?userLesson:{}}},computed:{tab:{get:function(){return this.currentTab},set:function(t){this.$emit("update-tab",t)}}},mounted:function(){"notice"===new URLSearchParams(window.location.search).get("ref")&&(this.tab="comment")}},(function(){var t=this,e=t._self._c;return e("div",[e("div",{staticClass:"flex items-center p-4",staticStyle:{"box-shadow":"0 -4px 6px rgba(0, 0, 0, 0.1)"}},[e("div",{staticClass:"rounded-full w-[44px] h-[44px] flex-none flex items-center justify-center",class:"lesson"===t.tab?"bg-[#C1EACA]":"",attrs:{id:"menuTabLessonList"},on:{click:function(e){t.tab="lesson"}}},[e("img",{staticClass:"w-[22px] h-[22px]",attrs:{src:"/images/icons/lesson-list.png",alt:"lesson list"}})]),t._v(" "),["exam","last_exam"].includes(t.type)?t._e():e("div",{staticClass:"rounded-full w-[44px] h-[44px] flex-none flex items-center justify-center relative mr-3",class:"comment"===t.tab?"bg-[#C1EACA]":"",attrs:{id:"menuTabComment"},on:{click:function(e){t.tab="comment"}}},[e("img",{staticClass:"w-[22px] h-[22px]",attrs:{src:"/images/icons/comment.png",alt:"comment"}}),t._v(" "),t.auth.id?e("span",{staticClass:"text-white font-averta-regular text-xs px-[5px] leading-[14px] absolute top-[6px] right-[4px] rounded-[5px]",class:t.totalUnread>0?"bg-[#EF6D13]":"bg-[#D9D9D9]"},[t._v(t._s(t.totalUnread))]):t._e()]),t._v(" "),t.nextLesson?e("div",{staticClass:"ml-auto w-full"},[e("a",{staticClass:"ml-auto w-[99%] max-w-[219px] border-2 border-[#57D061] text-[#57D061] hover:text-[#57D061] font-averta-regular text-sm h-[44px] px-10 flex items-center justify-center rounded-full",attrs:{href:"/khoa-hoc/".concat(t.nextLesson.course_slug,"/lesson/").concat(t.nextLesson.id,"-").concat(t.nextLesson.SEOurl)}},[e("div",{staticClass:"truncate"},[t._v("\n            "+t._s(t.nextLesson.stage_name)+" >>\n          ")])])]):t._e()])])}),[],!1,null,null,null).exports;function N(t){return N="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},N(t)}function U(){U=function(){return e};var t,e={},n=Object.prototype,r=n.hasOwnProperty,a=Object.defineProperty||function(t,e,n){t[e]=n.value},i="function"==typeof Symbol?Symbol:{},o=i.iterator||"@@iterator",s=i.asyncIterator||"@@asyncIterator",c=i.toStringTag||"@@toStringTag";function u(t,e,n){return Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{u({},"")}catch(t){u=function(t,e,n){return t[e]=n}}function l(t,e,n,r){var i=e&&e.prototype instanceof g?e:g,o=Object.create(i.prototype),s=new D(r||[]);return a(o,"_invoke",{value:L(t,n,s)}),o}function f(t,e,n){try{return{type:"normal",arg:t.call(e,n)}}catch(t){return{type:"throw",arg:t}}}e.wrap=l;var d="suspendedStart",h="suspendedYield",p="executing",v="completed",m={};function g(){}function _(){}function y(){}var x={};u(x,o,(function(){return this}));var b=Object.getPrototypeOf,w=b&&b(b(O([])));w&&w!==n&&r.call(w,o)&&(x=w);var C=y.prototype=g.prototype=Object.create(x);function k(t){["next","throw","return"].forEach((function(e){u(t,e,(function(t){return this._invoke(e,t)}))}))}function F(t,e){function n(a,i,o,s){var c=f(t[a],t,i);if("throw"!==c.type){var u=c.arg,l=u.value;return l&&"object"==N(l)&&r.call(l,"__await")?e.resolve(l.__await).then((function(t){n("next",t,o,s)}),(function(t){n("throw",t,o,s)})):e.resolve(l).then((function(t){u.value=t,o(u)}),(function(t){return n("throw",t,o,s)}))}s(c.arg)}var i;a(this,"_invoke",{value:function(t,r){function a(){return new e((function(e,a){n(t,r,e,a)}))}return i=i?i.then(a,a):a()}})}function L(e,n,r){var a=d;return function(i,o){if(a===p)throw Error("Generator is already running");if(a===v){if("throw"===i)throw o;return{value:t,done:!0}}for(r.method=i,r.arg=o;;){var s=r.delegate;if(s){var c=E(s,r);if(c){if(c===m)continue;return c}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if(a===d)throw a=v,r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);a=p;var u=f(e,n,r);if("normal"===u.type){if(a=r.done?v:h,u.arg===m)continue;return{value:u.arg,done:r.done}}"throw"===u.type&&(a=v,r.method="throw",r.arg=u.arg)}}}function E(e,n){var r=n.method,a=e.iterator[r];if(a===t)return n.delegate=null,"throw"===r&&e.iterator.return&&(n.method="return",n.arg=t,E(e,n),"throw"===n.method)||"return"!==r&&(n.method="throw",n.arg=new TypeError("The iterator does not provide a '"+r+"' method")),m;var i=f(a,e.iterator,n.arg);if("throw"===i.type)return n.method="throw",n.arg=i.arg,n.delegate=null,m;var o=i.arg;return o?o.done?(n[e.resultName]=o.value,n.next=e.nextLoc,"return"!==n.method&&(n.method="next",n.arg=t),n.delegate=null,m):o:(n.method="throw",n.arg=new TypeError("iterator result is not an object"),n.delegate=null,m)}function S(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function j(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function D(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(S,this),this.reset(!0)}function O(e){if(e||""===e){var n=e[o];if(n)return n.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var a=-1,i=function n(){for(;++a<e.length;)if(r.call(e,a))return n.value=e[a],n.done=!1,n;return n.value=t,n.done=!0,n};return i.next=i}}throw new TypeError(N(e)+" is not iterable")}return _.prototype=y,a(C,"constructor",{value:y,configurable:!0}),a(y,"constructor",{value:_,configurable:!0}),_.displayName=u(y,c,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===_||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,y):(t.__proto__=y,u(t,c,"GeneratorFunction")),t.prototype=Object.create(C),t},e.awrap=function(t){return{__await:t}},k(F.prototype),u(F.prototype,s,(function(){return this})),e.AsyncIterator=F,e.async=function(t,n,r,a,i){void 0===i&&(i=Promise);var o=new F(l(t,n,r,a),i);return e.isGeneratorFunction(n)?o:o.next().then((function(t){return t.done?t.value:o.next()}))},k(C),u(C,c,"Generator"),u(C,o,(function(){return this})),u(C,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),n=[];for(var r in e)n.push(r);return n.reverse(),function t(){for(;n.length;){var r=n.pop();if(r in e)return t.value=r,t.done=!1,t}return t.done=!0,t}},e.values=O,D.prototype={constructor:D,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(j),!e)for(var n in this)"t"===n.charAt(0)&&r.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var n=this;function a(r,a){return s.type="throw",s.arg=e,n.next=r,a&&(n.method="next",n.arg=t),!!a}for(var i=this.tryEntries.length-1;i>=0;--i){var o=this.tryEntries[i],s=o.completion;if("root"===o.tryLoc)return a("end");if(o.tryLoc<=this.prev){var c=r.call(o,"catchLoc"),u=r.call(o,"finallyLoc");if(c&&u){if(this.prev<o.catchLoc)return a(o.catchLoc,!0);if(this.prev<o.finallyLoc)return a(o.finallyLoc)}else if(c){if(this.prev<o.catchLoc)return a(o.catchLoc,!0)}else{if(!u)throw Error("try statement without catch or finally");if(this.prev<o.finallyLoc)return a(o.finallyLoc)}}}},abrupt:function(t,e){for(var n=this.tryEntries.length-1;n>=0;--n){var a=this.tryEntries[n];if(a.tryLoc<=this.prev&&r.call(a,"finallyLoc")&&this.prev<a.finallyLoc){var i=a;break}}i&&("break"===t||"continue"===t)&&i.tryLoc<=e&&e<=i.finallyLoc&&(i=null);var o=i?i.completion:{};return o.type=t,o.arg=e,i?(this.method="next",this.next=i.finallyLoc,m):this.complete(o)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),m},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.finallyLoc===t)return this.complete(n.completion,n.afterLoc),j(n),m}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.tryLoc===t){var r=n.completion;if("throw"===r.type){var a=r.arg;j(n)}return a}}throw Error("illegal catch attempt")},delegateYield:function(e,n,r){return this.delegate={iterator:O(e),resultName:n,nextLoc:r},"next"===this.method&&(this.arg=t),m}},e}function H(t,e,n,r,a,i,o){try{var s=t[i](o),c=s.value}catch(t){return void n(t)}s.done?e(c):Promise.resolve(c).then(r,a)}var q={name:"container",props:{isUnlock:{type:Boolean,default:!1},stageName:{type:String,default:""},type:{type:String,default:"flashcard"},examStage:{type:Number,default:1},hasLesson:{type:Boolean,default:!0}},data:function(){return{isActive:!(window.screen.width<1024||["exam","last_exam","flashcard"].includes(this.type)),search:"",currentTab:"lesson",totalUnread:0,authUser:authUser,currentLesson:this.hasLesson?currentLesson:{},currentFlashcard:null}},components:{SearchBar:a,SearchList:x,LessonList:L,Comment:z,Footer:M},mounted:function(){var t=this;this.getTotalUnread();var e=document.getElementById("container-list-course"),n=document.getElementById("explanationPopup");function r(){if(n){var t=e.getBoundingClientRect();0===t.right?(n.style.right="".concat(window.innerWidth-t.right,"px"),n.style.maxWidth="".concat(t.right-t.width,"px")):(n.style.right="0",n.style.maxWidth="100vw")}}r(),window.addEventListener("resize",r),document.addEventListener("open-comment-tab",(function(e){console.log("Container received open-comment-tab event with data:",e.detail);var n=e.detail||{},r=n.handle||"open",a=n.flashcard;a&&t.setCurrentFlashcard(a),"open"===r?(t.isActive=!0,t.currentTab="comment"):"close"===r?(t.isActive=!1,t.currentTab="lesson"):"toggle"===r&&(t.isActive=!t.isActive,t.isActive&&(t.currentTab="comment"))}))},watch:{isActive:function(t,e){console.log("Container isActive changed from",e,"to",t),console.log("Container currentTab:",this.currentFlashcard),t?($("#lesson-main").removeClass("menu-hidden"),$("#explanationPopup").css("max-width","".concat(screen.width-465,"px")),$("#explanationPopup").css("right","15px"),$("#screen-mobile-lesson").removeClass("w-full"),$("#screen-mobile-lesson").addClass("w-2/3"),"comment"===this.currentTab&&"flashcard"===this.currentLesson.type&&this.sendEventGA("flc_comment")):($("#lesson-main").addClass("menu-hidden"),$("#explanationPopup").css("max-width","".concat(screen.width,"px")),$("#explanationPopup").css("right",0),$("#screen-mobile-lesson").addClass("w-full"),$("#screen-mobile-lesson").removeClass("w-2/3")),this.$emit("active-changed",this.isActive)},currentFlashcard:{handler:function(t,e){!t||e&&t.id===e.id||this.getTotalUnread()},deep:!0},currentTab:function(t,e){"comment"===t&&this.isActive&&"flashcard"===this.currentLesson.type&&this.sendEventGA("flc_comment")}},methods:{hideMenu:function(){this.isActive=!1},setCurrentFlashcard:function(t){console.log("Setting current flashcard:",t),this.currentFlashcard=t,console.log("Container currentFlashcard updated:",this.currentFlashcard)},handleCommentAdded:function(t){console.log("Container received comment-added event with data:",t);var e=new CustomEvent("comment-added",{detail:{flashcardId:t.flashcardId,comment:t.comment}});document.dispatchEvent(e)},getTotalUnread:function(){var t;if(this.hasLesson&&(!this.currentLesson||"flashcard"!==this.currentLesson.type||0!==this.isActive)){var e=this;d().get("/api/comment/total-unread-replies?lesson_id=".concat(currentLesson.id,"&type=").concat(this.currentLesson.type,"&flashcard_id=").concat(null===(t=this.currentFlashcard)||void 0===t?void 0:t.id),{withCredentials:!0}).then((function(t){e.totalUnread=t.data})).catch((function(t){console.log(t)}))}},sendEventGA:function(){var t,e=arguments,n=this;return(t=U().mark((function t(){var r,a;return U().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(r=e.length>0&&void 0!==e[0]?e[0]:null,[39,40].includes(parseInt(n.currentLesson.course_id))&&1!==parseInt(n.authUser.isTester)&&["production","prod"].includes("production")){t.next=4;break}return t.abrupt("return");case 4:a=39===parseInt(n.currentLesson.course_id)?"n5":"n4",r=r?"".concat(a,"_").concat(r):"".concat(a,"_flc_behind"),ga("send","event","nx_".concat(r),r,r);case 7:case"end":return t.stop()}}),t)})),function(){var e=this,n=arguments;return new Promise((function(r,a){var i=t.apply(e,n);function o(t){H(i,r,a,o,s,"next",t)}function s(t){H(i,r,a,o,s,"throw",t)}o(void 0)}))})()}}},W=r(q,(function(){var t=this,e=t._self._c;return e("div",[e("div",[e("button",{staticClass:"btn-show-menu fixed duration-500 top-20 sp:top-[100px] w-[46px] h-[44px] border-none rounded-l-full hover:w-[75px] bg-white text-left z-50",class:t.isActive?"right-[-200px]":"right-0",staticStyle:{"box-shadow":"0px 2px 4px 0px #4c5d703d"},on:{click:function(e){t.isActive=!t.isActive}}},[e("img",{staticClass:"h-[14px] rotate-180 relative left-[15px]",attrs:{src:"/images/icons/hide.png",alt:"hide.png"}}),t._v(" "),e("span",{staticClass:"tooltip-menu absolute text-[#07403F] bg-[#C1EACA] px-3 whitespace-nowrap top-[8px] py-1 text-sm font-averta-regular rounded-full duration-500"},[t._v("Mở danh mục bên")])]),t._v(" "),e("div",{staticClass:"fixed duration-500 top-[76px] sp:top-[72px] bg-white w-[450px] h-[calc(100vh-70px)] sp:w-full z-50 sp:bottom-0",class:t.isActive?"right-0":"right-[-1500px]",attrs:{id:"container-list-course"}},[e("div",{staticClass:"p-4 relative z-50 menu-content"},[e("SearchBar",{attrs:{"has-lesson":t.hasLesson,"current-tab":t.currentTab},on:{"update-search":function(e){return t.search=e},"hide-menu":t.hideMenu}}),t._v(" "),"lesson"==t.currentTab&&t.hasLesson?[e("LessonList",{directives:[{name:"show",rawName:"v-show",value:t.search.length<=0,expression:"search.length <= 0"}],attrs:{"has-lesson":t.hasLesson,"stage-name":t.stageName,"is-unlock":t.isUnlock,"exam-stage":t.examStage}}),t._v(" "),e("SearchList",{directives:[{name:"show",rawName:"v-show",value:t.search.length>0,expression:"search.length > 0"}],attrs:{"stage-name":t.stageName,"current-lesson":t.currentLesson,search:t.search,"has-lesson":t.hasLesson}})]:t._e(),t._v(" "),"comment"!=t.currentTab&&t.hasLesson?t._e():[e("Comment",{attrs:{"has-lesson":t.hasLesson,auth:t.authUser,"current-flashcard":t.currentFlashcard},on:{"update-unread":t.getTotalUnread,"comment-added":t.handleCommentAdded}})]],2),t._v(" "),t.hasLesson?e("Footer",{attrs:{"has-lesson":t.hasLesson,"current-tab":t.currentTab,"total-unread":t.totalUnread,auth:t.authUser,type:t.type},on:{"update-tab":function(e){return t.currentTab=e}}}):t._e()],1)])])}),[],!1,null,null,null).exports;function Z(t){return Z="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Z(t)}function G(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var n=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=n){var r,a,i,o,s=[],c=!0,u=!1;try{if(i=(n=n.call(t)).next,0===e){if(Object(n)!==n)return;c=!1}else for(;!(c=(r=i.call(n)).done)&&(s.push(r.value),s.length!==e);c=!0);}catch(t){u=!0,a=t}finally{try{if(!c&&null!=n.return&&(o=n.return(),Object(o)!==o))return}finally{if(u)throw a}}return s}}(t,e)||Q(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function J(t){return function(t){if(Array.isArray(t))return Y(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||Q(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Q(t,e){if(t){if("string"==typeof t)return Y(t,e);var n={}.toString.call(t).slice(8,-1);return"Object"===n&&t.constructor&&(n=t.constructor.name),"Map"===n||"Set"===n?Array.from(t):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?Y(t,e):void 0}}function Y(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=Array(e);n<e;n++)r[n]=t[n];return r}function K(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,r)}return n}function X(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?K(Object(n),!0).forEach((function(e){tt(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):K(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}function tt(t,e,n){return(e=function(t){var e=function(t,e){if("object"!=Z(t)||!t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var r=n.call(t,e||"default");if("object"!=Z(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==Z(e)?e:e+""}(e))in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function et(){et=function(){return e};var t,e={},n=Object.prototype,r=n.hasOwnProperty,a=Object.defineProperty||function(t,e,n){t[e]=n.value},i="function"==typeof Symbol?Symbol:{},o=i.iterator||"@@iterator",s=i.asyncIterator||"@@asyncIterator",c=i.toStringTag||"@@toStringTag";function u(t,e,n){return Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{u({},"")}catch(t){u=function(t,e,n){return t[e]=n}}function l(t,e,n,r){var i=e&&e.prototype instanceof g?e:g,o=Object.create(i.prototype),s=new D(r||[]);return a(o,"_invoke",{value:L(t,n,s)}),o}function f(t,e,n){try{return{type:"normal",arg:t.call(e,n)}}catch(t){return{type:"throw",arg:t}}}e.wrap=l;var d="suspendedStart",h="suspendedYield",p="executing",v="completed",m={};function g(){}function _(){}function y(){}var x={};u(x,o,(function(){return this}));var b=Object.getPrototypeOf,w=b&&b(b(O([])));w&&w!==n&&r.call(w,o)&&(x=w);var C=y.prototype=g.prototype=Object.create(x);function k(t){["next","throw","return"].forEach((function(e){u(t,e,(function(t){return this._invoke(e,t)}))}))}function F(t,e){function n(a,i,o,s){var c=f(t[a],t,i);if("throw"!==c.type){var u=c.arg,l=u.value;return l&&"object"==Z(l)&&r.call(l,"__await")?e.resolve(l.__await).then((function(t){n("next",t,o,s)}),(function(t){n("throw",t,o,s)})):e.resolve(l).then((function(t){u.value=t,o(u)}),(function(t){return n("throw",t,o,s)}))}s(c.arg)}var i;a(this,"_invoke",{value:function(t,r){function a(){return new e((function(e,a){n(t,r,e,a)}))}return i=i?i.then(a,a):a()}})}function L(e,n,r){var a=d;return function(i,o){if(a===p)throw Error("Generator is already running");if(a===v){if("throw"===i)throw o;return{value:t,done:!0}}for(r.method=i,r.arg=o;;){var s=r.delegate;if(s){var c=E(s,r);if(c){if(c===m)continue;return c}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if(a===d)throw a=v,r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);a=p;var u=f(e,n,r);if("normal"===u.type){if(a=r.done?v:h,u.arg===m)continue;return{value:u.arg,done:r.done}}"throw"===u.type&&(a=v,r.method="throw",r.arg=u.arg)}}}function E(e,n){var r=n.method,a=e.iterator[r];if(a===t)return n.delegate=null,"throw"===r&&e.iterator.return&&(n.method="return",n.arg=t,E(e,n),"throw"===n.method)||"return"!==r&&(n.method="throw",n.arg=new TypeError("The iterator does not provide a '"+r+"' method")),m;var i=f(a,e.iterator,n.arg);if("throw"===i.type)return n.method="throw",n.arg=i.arg,n.delegate=null,m;var o=i.arg;return o?o.done?(n[e.resultName]=o.value,n.next=e.nextLoc,"return"!==n.method&&(n.method="next",n.arg=t),n.delegate=null,m):o:(n.method="throw",n.arg=new TypeError("iterator result is not an object"),n.delegate=null,m)}function S(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function j(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function D(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(S,this),this.reset(!0)}function O(e){if(e||""===e){var n=e[o];if(n)return n.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var a=-1,i=function n(){for(;++a<e.length;)if(r.call(e,a))return n.value=e[a],n.done=!1,n;return n.value=t,n.done=!0,n};return i.next=i}}throw new TypeError(Z(e)+" is not iterable")}return _.prototype=y,a(C,"constructor",{value:y,configurable:!0}),a(y,"constructor",{value:_,configurable:!0}),_.displayName=u(y,c,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===_||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,y):(t.__proto__=y,u(t,c,"GeneratorFunction")),t.prototype=Object.create(C),t},e.awrap=function(t){return{__await:t}},k(F.prototype),u(F.prototype,s,(function(){return this})),e.AsyncIterator=F,e.async=function(t,n,r,a,i){void 0===i&&(i=Promise);var o=new F(l(t,n,r,a),i);return e.isGeneratorFunction(n)?o:o.next().then((function(t){return t.done?t.value:o.next()}))},k(C),u(C,c,"Generator"),u(C,o,(function(){return this})),u(C,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),n=[];for(var r in e)n.push(r);return n.reverse(),function t(){for(;n.length;){var r=n.pop();if(r in e)return t.value=r,t.done=!1,t}return t.done=!0,t}},e.values=O,D.prototype={constructor:D,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(j),!e)for(var n in this)"t"===n.charAt(0)&&r.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var n=this;function a(r,a){return s.type="throw",s.arg=e,n.next=r,a&&(n.method="next",n.arg=t),!!a}for(var i=this.tryEntries.length-1;i>=0;--i){var o=this.tryEntries[i],s=o.completion;if("root"===o.tryLoc)return a("end");if(o.tryLoc<=this.prev){var c=r.call(o,"catchLoc"),u=r.call(o,"finallyLoc");if(c&&u){if(this.prev<o.catchLoc)return a(o.catchLoc,!0);if(this.prev<o.finallyLoc)return a(o.finallyLoc)}else if(c){if(this.prev<o.catchLoc)return a(o.catchLoc,!0)}else{if(!u)throw Error("try statement without catch or finally");if(this.prev<o.finallyLoc)return a(o.finallyLoc)}}}},abrupt:function(t,e){for(var n=this.tryEntries.length-1;n>=0;--n){var a=this.tryEntries[n];if(a.tryLoc<=this.prev&&r.call(a,"finallyLoc")&&this.prev<a.finallyLoc){var i=a;break}}i&&("break"===t||"continue"===t)&&i.tryLoc<=e&&e<=i.finallyLoc&&(i=null);var o=i?i.completion:{};return o.type=t,o.arg=e,i?(this.method="next",this.next=i.finallyLoc,m):this.complete(o)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),m},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.finallyLoc===t)return this.complete(n.completion,n.afterLoc),j(n),m}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.tryLoc===t){var r=n.completion;if("throw"===r.type){var a=r.arg;j(n)}return a}}throw Error("illegal catch attempt")},delegateYield:function(e,n,r){return this.delegate={iterator:O(e),resultName:n,nextLoc:r},"next"===this.method&&(this.arg=t),m}},e}function nt(t,e,n,r,a,i,o){try{var s=t[i](o),c=s.value}catch(t){return void n(t)}s.done?e(c):Promise.resolve(c).then(r,a)}function rt(t){return function(){var e=this,n=arguments;return new Promise((function(r,a){var i=t.apply(e,n);function o(t){nt(i,r,a,o,s,"next",t)}function s(t){nt(i,r,a,o,s,"throw",t)}o(void 0)}))}}var at={OVERVIEW:1,SEARCH:2,STACKED_CARD:3,RESULT:4},it={components:{ChartVocabulary:g,ProgressBar:l,Container:W},props:{courseId:{type:Number,required:!0,default:0},categoryData:{type:Object,default:function(){return{name:"",data:{learned:0,temporary:0,memorized:0,mastered:0},word_count:0,selected:!1,id:0}}}},name:"FlashCard",data:function(){return{listVocabulary:[],TYPES_VIEW:at,currentView:at.OVERVIEW,searchResults:[],searchQuery:"",dataFlashCard:[{id:1,lesson_id:1,type:17,take_break:0,value:{word:"一",word_stress:"",word_type:"Động từ",audio:null,front_image:null,example:[],meaning:"Từ điển",back_image:null,meaning_example:[],quiz_question:null,kanji_meaning:null}}],isJapanese:!0,currentCard:null,chartData:{learned:0,temporary:0,memorized:0,mastered:0},isLoadDataFlashcard:!1,arrCardLearned:[],swipeCardList:[],selectedAnswerIndex:null,allCards:[],idsFree:[103631,103632,103612,103613],isActiveContainerCmt:!1,countSwipeCard:0,completedCards:[],isShuffle:!1,limitCardFree:2,showUnlockCoursePopup:!1}},created:function(){this.getListVocabulary(),console.log("this.categoryData: ",this.categoryData)},watch:{searchQuery:function(t,e){console.log("searchQuery: ",t),t?this.searchWord(t):this.searchResults=this.allCards},currentView:function(t,e){var n=this;return rt(et().mark((function e(){return et().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(console.log("currentView: ",t),t!==at.SEARCH){e.next=7;break}if(0!==n.allCards.length){e.next=7;break}return e.next=5,n.getAllFlashCard();case 5:n.allCards=e.sent,n.searchResults=n.allCards;case 7:t===at.RESULT?$("#default").css("margin-top","0"):$("#default").css("margin-top","58px");case 8:case"end":return e.stop()}}),e)})))()}},methods:{getListVocabulary:function(){var t=this;this.listVocabulary=this.listVocabulary.filter((function(e){return e.course_id===t.courseId}))},playAudio:function(t,e){console.log("Play audio for card id: ".concat(t,", url: ").concat(e))},toggleCommentTab:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"open",e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};console.log("mo tab cmt with handle: ".concat(t));var n=X({handle:t,flashcard:this.dataFlashCard[0]},e);this.$emit("open-comment-tab",n);var r=new CustomEvent("open-comment-tab",{detail:n});document.dispatchEvent(r)},initStackedCards:function(){var t=this;this.stackedCardsInstance&&this.stackedCardsInstance.destroyEventListenersCards(),this.$nextTick((function(){t.stackedCardsInstance=new h.Ay({visibleItems:3,margin:22,rotate:!0,useOverlays:!0}),t.updateCurrentFlashcard()}))},updateCurrentFlashcard:function(){this.currentCard=this.dataFlashCard[0]},swipeCard:function(t){var e=this;if(this.stackedCardsInstance&&this.dataFlashCard[0]){if(console.log("this.categoryData: ",this.categoryData),!1===this.categoryData.hasCourseOwner)return this.countSwipeCard++,this.currentCard=this.dataFlashCard[this.countSwipeCard],console.log("this.currentCard: ",this.currentCard),"left"===t?this.stackedCardsInstance.swipeLeft():this.stackedCardsInstance.swipeRight(),void(this.currentCard=this.dataFlashCard[0]);if(console.log("countSwipeCard: ",this.countSwipeCard),0===this.dataFlashCard[0].id)return this.dataFlashCard=this.dataFlashCard.filter((function(t){return 0!==t.id})),this.swipeCardList.push(this.dataFlashCard[0]),this.dataFlashCard.shift(),this.initStackedCards(),void(this.countSwipeCard=0);if(this.countSwipeCard&&this.countSwipeCard%5==0){console.log("this.swipeCardList: ",this.swipeCardList);var n=this.swipeCardList.filter((function(t){return"right"===t.type_swipe})).length/this.swipeCardList.length;console.log("ratio: ",n);var r={id:0,lesson_id:1,type:17,take_break:1};if(n>=.8||n<=.2){var a=n>=.8?Math.floor(4*Math.random())+1:Math.floor(5*Math.random())+1;return r.img_break="".concat(n>=.8?"right":"left","-").concat(a),this.dataFlashCard.unshift(r),document.querySelectorAll(".card-inner").forEach((function(t){t.classList.remove("flip")})),void this.initStackedCards()}}else this.dataFlashCard[0].is_relearn||this.countSwipeCard++;var i=this.dataFlashCard[0];if(i.type_swipe=t,i.t=Math.floor(Date.now()/1e3),i.is_relearn=!1,"left"===t&&i.is_test)return i.is_test=!1,i.is_relearn=!0,this.stackedCardsInstance.swipeLeft(),this.dataFlashCard[0]=i,void setTimeout((function(){e.initStackedCards(),e.currentCard=e.dataFlashCard[0]}),500);"left"===t?(this.stackedCardsInstance.swipeLeft(),i.box=i.box||0):(i.box=(i.box||0)+1,this.stackedCardsInstance.swipeRight()),i.p=i.box,this.swipeCardList=this.swipeCardList.filter((function(t){return t.id!==i.id})),this.swipeCardList.push(i),console.log("this.swipeCardList: ",this.swipeCardList),this.dataFlashCard=this.dataFlashCard.filter((function(t){return t.id!==i.id}));var o=[].concat(J(this.dataFlashCard),J(this.arrCardLearned),J(this.swipeCardList));o=y().uniqBy(o,"id");var s=(0,h.Er)(o),c=s.sortCards;s.completedCards;console.log("sortCards: ",c);var u=o.every((function(t){return t.box>=5})),l=[],f=Math.floor(Date.now()/1e3);console.log("currentTime: ",f),c.forEach((function(t){l.push({id:t.id,box:t.box,p:t.p,t:t.t,ti:f-t.t})})),console.log("arrD: ",l),this.dataFlashCard=c.slice(0,6),this.arrCardLearned=c.slice(6),console.log("this.swipeCardList: ",this.swipeCardList),setTimeout((function(){var t=document.querySelector(".stackedcards-container");null!=t&&t.firstChild&&Object.assign(t.firstChild.style,{transform:"none",transition:"none"}),e.$nextTick((function(){e.isJapanese&&document.querySelectorAll(".card-inner").forEach((function(t){t.classList.remove("flip")}));if(!e.dataFlashCard.length||u)return e.saveDataFlashcard(),void(e.currentView=at.RESULT);e.initStackedCards(),e.currentCard=e.dataFlashCard[0]||null}))}),400)}},undo:function(){this.stackedCardsInstance&&this.dataFlashCard[0]&&(console.log("undo: "),this.dataFlashCard.unshift(this.swipeCardList.pop()),this.initStackedCards())},startStudy:function(){var t=this;return rt(et().mark((function e(){var n,r,a,i,o,s;return et().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(t.currentView=at.STACKED_CARD,t.isLoadDataFlashcard=!0,0!==t.allCards.length){e.next=6;break}return e.next=5,t.getAllFlashCard();case 5:t.allCards=e.sent;case 6:if(n=(0,h.Er)(t.allCards),r=n.sortCards,a=n.completedCards,t.arrCardLearned=r,t.completedCards=a,i=[],o=Math.floor(Date.now()/1e3),console.log("currentTime: ",o),t.arrCardLearned.forEach((function(t){i.push({id:t.id,box:t.box,p:t.p,t:t.t,ti:o-t.t})})),0!==t.arrCardLearned.length){e.next=17;break}return t.currentView=at.RESULT,t.updateDataChart(a),e.abrupt("return");case 17:console.log("arrD: ",i),console.log("this.arrCardLearned: ",t.arrCardLearned),t.dataFlashCard=t.arrCardLearned.slice(0,6),t.arrCardLearned=t.arrCardLearned.slice(6),t.categoryData.hasCourseOwner||(s={id:0,hasCourseOwner:0,take_break:0,value:{word:"一",word_stress:"",word_type:"Động từ",audio:null,front_image:null,example:[],meaning:"Từ điển",back_image:null,meaning_example:[],quiz_question:null,kanji_meaning:null}},t.dataFlashCard.splice(2,0,s)),console.log("dataFlashCard: ",t.dataFlashCard),console.log("arrCardLearned: ",t.arrCardLearned),t.initStackedCards(),t.isLoadDataFlashcard=!1,console.log("this.dataFlashCard: ",t.dataFlashCard),t.currentCard=t.dataFlashCard[0],console.log("this.currentCard: ",t.currentCard);case 29:case"end":return e.stop()}}),e)})))()},getAllFlashCard:function(){var t=this;return rt(et().mark((function e(){var n,r,a,i,o;return et().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return n=[],e.next=3,Promise.all([t.getHistory(),t.getFlashCardByCourse()]);case 3:return r=e.sent,a=G(r,2),i=a[0],o=a[1],console.log("history_learn_flc: ",i),o.forEach((function(t){t.value.quiz_question&&t.value.quiz_question.length>0&&(t.value.quiz_question=t.value.quiz_question.reduce((function(t,e,n){return t[n]={index:n,question:e},t}),{}),t.value.quiz_question=y().shuffle(t.value.quiz_question));var e=i.find((function(e){return e.i===t.id}));e?t=X(X({},t),e):(t.t=null,t.p=null),n.push(t)})),console.log("arrCard: ",n),e.abrupt("return",n);case 11:case"end":return e.stop()}}),e)})))()},getHistory:function(){var t=this;return rt(et().mark((function e(){var n,r;return et().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return n=[],r={course_id:t.courseId,type:"normal",query:"find"},e.next=4,d().get("/vocabulary/get-course",{params:r}).then((function(t){console.log("response: ",t),t.data.data&&(n=JSON.parse(t.data.data.data))})).catch((function(t){console.error(t)}));case 4:return e.abrupt("return",n);case 5:case"end":return e.stop()}}),e)})))()},getFlashCardByCourse:function(){var t=this;return rt(et().mark((function e(){var n,r;return et().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return n=[],r={course_id:t.courseId,type:"normal"},e.next=4,d().get("/vocabulary/get-flashcard-by-course",{params:r}).then((function(t){console.log("response: ",t),(n=t.data.data).map((function(t){return t.value=JSON.parse(t.value),t.value.word=(0,h.UW)(t.value.word),t}))})).catch((function(t){console.error(t)}));case 4:return e.abrupt("return",n);case 5:case"end":return e.stop()}}),e)})))()},getFlcById:function(t){return rt(et().mark((function e(){var n,r;return et().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return n=[],r={ids:t},e.next=4,d().get("/vocabulary/get-flashcard-by-id",{params:r}).then((function(t){console.log("response: ",t),n=t.data.data})).catch((function(t){console.error(t)}));case 4:return e.abrupt("return",n);case 5:case"end":return e.stop()}}),e)})))()},selectAnswer:function(t,e){var n=this;this.currentCard&&this.currentCard.id!==t&&(this.selectedAnswerIndex=null),null===this.selectedAnswerIndex&&(this.selectedAnswerIndex=e,console.log("Selected answer ".concat(e," for card ").concat(t)),setTimeout((function(){n.selectedAnswerIndex=null,document.querySelectorAll(".input-radio-test").forEach((function(t){t.checked=!1})),e?n.swipeCard("left"):n.swipeCard("right")}),700))},searchWord:function(t){this.searchResults=this.allCards.filter((function(e){return e.value.word.toLowerCase().includes(t.toLowerCase())}))},saveDataFlashcard:function(){var t=this;return rt(et().mark((function e(){var n,r,a;return et().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(0!==t.swipeCardList.length){e.next=2;break}return e.abrupt("return");case 2:if(n=[].concat(J(t.dataFlashCard),J(t.arrCardLearned),J(t.swipeCardList),J(t.completedCards)),console.log("allCards: ",n),r={},n.forEach((function(t){(!r[t.id]||t.box>r[t.id].box)&&(r[t.id]=t)})),(n=Object.values(r)).length&&t.currentView===at.STACKED_CARD){e.next=9;break}return e.abrupt("return");case 9:if(a=t.updateDataChart(n),console.log("dataSave: ",a),0!==a.length){e.next=13;break}return e.abrupt("return");case 13:return e.next=15,d().post("/vocabulary/save-flashcard",{course_id:t.courseId,data:a}).then((function(t){console.log("response: ",t)})).catch((function(t){console.error(t)}));case 15:case"end":return e.stop()}}),e)})))()},handleBeforeUnload:function(){this.saveDataFlashcard()},updateDataChart:function(t){var e={non:0,learned:0,temporary:0,memorized:0,mastered:0,super_mastered:0,totalWords:0},n=[];return t.forEach((function(t){1===t.p&&(e.learned+=1),2===t.p&&(e.temporary+=1),3===t.p&&(e.memorized+=1),4===t.p&&(e.mastered+=1),5===t.p&&(e.super_mastered+=1),e.totalWords+=1,t.t&&n.push({i:t.id,p:t.p,t:t.t})})),console.log("chartData: ",e),this.chartData=e,n},likeCard:function(t){var e=this;d().post("/vocabulary/like-card",{id:t,course_id:this.courseId,t:Math.floor(Date.now()/1e3)}).then((function(t){console.log("response: ",t),e.dataFlashCard[0].isLike=t.data.isLike})).catch((function(t){console.error(t)}))},handleContainerActive:function(t){console.log("Container active changed: ",t),this.isActiveContainerCmt=t},handleShuffleChange:function(){console.log("handleShuffleChange: ",this.isShuffle)},handleLanguageChange:function(){console.log("handleLanguageChange: ",this.isJapanese)},handleVocabularyItemClick:function(t){console.log("Clicked vocabulary item: ",t),this.idsFree.includes(t.id)||this.categoryData.hasCourseOwner?console.log("User can access this vocabulary item"):this.showUnlockCoursePopup=!0},closeUnlockCoursePopup:function(){this.showUnlockCoursePopup=!1},redirectToCoursePurchase:function(){console.log("Redirecting to course purchase page for course: ",this.categoryData),this.closeUnlockCoursePopup()}},mounted:function(){window.addEventListener("beforeunload",this.handleBeforeUnload)},beforeDestroy:function(){var t=this;return rt(et().mark((function e(){return et().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return console.log("beforeDestroy"),$("#default").css("margin-top","58px"),e.next=4,t.saveDataFlashcard();case 4:window.removeEventListener("beforeunload",t.handleBeforeUnload);case 5:case"end":return e.stop()}}),e)})))()}},ot=it,st=n(10639),ct={insert:"head",singleton:!1},ut=(s()(st.A,ct),st.A.locals,r(ot,(function(){var t,e,n,r,a=this,i=a._self._c;return i("div",[i("div",{staticClass:"flashcard-wrap"},[a.currentView===a.TYPES_VIEW.OVERVIEW?i("div",{staticClass:"flex flex-col justify-center items-center w-[90%] max-w-[1000px]"},[i("div",{staticClass:"w-full text-center p-4 rounded-3xl mb-6",staticStyle:{background:"linear-gradient(to bottom, #FFFFFF, #F4F5FA)"}},[a._m(0),a._v(" "),i("div",{staticClass:"title text-center text-[#07403F]"},[i("div",{staticClass:"font-beanbag-medium text-[20px] uppercase"},[a._v("Bộ từ vựng")]),a._v(" "),i("div",{staticClass:"font-zuume-semibold text-[48px] text-bold uppercase"},[a._v("jlpt "+a._s(a.categoryData.level))]),a._v(" "),i("div",{staticClass:"font-averta-regular text-[20px]"},[a._v("Học và ghi nhớ "+a._s(a.categoryData.data.word_count)+" từ vựng thuộc\n            cấp độ "+a._s(a.categoryData.level)+"\n          ")])])]),a._v(" "),i("div",{staticClass:"w-full"},[i("div",{staticClass:"max-w-[420px] mx-auto progress-item grid grid-cols-3 gap-4 items-center"},[i("div",{staticClass:"font-averta-regular text-[20px] text-[#1E1E1E]"},[a._v("Đã học")]),a._v(" "),i("progress-bar",{attrs:{percent:(a.categoryData.data.learned+a.categoryData.data.temporary+a.categoryData.data.memorized+a.categoryData.data.mastered)/a.categoryData.data.word_count*100,color:(a.categoryData.data.learned+a.categoryData.data.temporary+a.categoryData.data.memorized+a.categoryData.data.mastered)/a.categoryData.data.word_count*100<85?"#E8B931":"#57D061"}}),a._v(" "),i("div",{staticClass:"font-averta-regular text-[20px] text-[#757575] text-end"},[a._v("\n            "+a._s(Math.round((a.categoryData.data.learned+a.categoryData.data.temporary+a.categoryData.data.memorized+a.categoryData.data.mastered)/a.categoryData.data.word_count*100))+"%・"+a._s(a.categoryData.data.learned+a.categoryData.data.temporary+a.categoryData.data.memorized+a.categoryData.data.mastered)+"\n            từ\n          ")]),a._v(" "),i("div",{staticClass:"font-averta-regular text-[20px] text-[#1E1E1E]"},[a._v("Tạm nhớ")]),a._v(" "),i("progress-bar",{attrs:{percent:(a.categoryData.data.temporary+a.categoryData.data.memorized+a.categoryData.data.mastered)/a.categoryData.data.word_count*100,color:(a.categoryData.data.temporary+a.categoryData.data.memorized+a.categoryData.data.mastered)/a.categoryData.data.word_count*100<85?"#E8B931":"#57D061"}}),a._v(" "),i("div",{staticClass:"font-averta-regular text-[20px] text-[#757575] text-end"},[a._v("\n            "+a._s(Math.round((a.categoryData.data.temporary+a.categoryData.data.memorized+a.categoryData.data.mastered)/a.categoryData.data.word_count*100))+"%・"+a._s(a.categoryData.data.temporary+a.categoryData.data.memorized+a.categoryData.data.mastered)+"\n            từ\n          ")]),a._v(" "),i("div",{staticClass:"font-averta-regular text-[20px] text-[#1E1E1E]"},[a._v(" Ghi nhớ")]),a._v(" "),i("progress-bar",{attrs:{percent:(a.categoryData.data.memorized+a.categoryData.data.mastered)/a.categoryData.data.word_count*100,color:(a.categoryData.data.memorized+a.categoryData.data.mastered)/a.categoryData.data.word_count*100<85?"#E8B931":"#57D061"}}),a._v(" "),i("div",{staticClass:"font-averta-regular text-[20px] text-[#757575] text-end"},[a._v("\n            "+a._s(Math.round((a.categoryData.data.memorized+a.categoryData.data.mastered)/a.categoryData.data.word_count*100))+"%・"+a._s(a.categoryData.data.memorized+a.categoryData.data.mastered)+"\n            từ\n          ")]),a._v(" "),i("div",{staticClass:"font-averta-regular text-[20px] text-[#1E1E1E]"},[a._v("Thuộc lòng")]),a._v(" "),i("progress-bar",{attrs:{percent:a.categoryData.data.mastered/a.categoryData.data.word_count*100,color:a.categoryData.data.mastered/a.categoryData.data.word_count*100<85?"#E8B931":"#57D061"}}),a._v(" "),i("div",{staticClass:"font-averta-regular text-[20px] text-[#757575] text-end"},[a._v("\n            "+a._s(Math.round(a.categoryData.data.mastered/a.categoryData.data.word_count*100))+"%・"+a._s(a.categoryData.data.mastered)+"\n            từ\n          ")])],1)]),a._v(" "),i("div",{staticClass:"w-[340px]"},[i("div",{staticClass:"w-full text-xl text-[#07403F] font-beanbag rounded-full p-3 mt-10 flex justify-between items-center border-[1px] border-[#07403F] cursor-pointer",on:{click:function(t){a.currentView=a.TYPES_VIEW.SEARCH}}},[i("div",{staticClass:"text-[#07403F] font-averta-regular text-xl"},[a._v("\n            Học bộ từ\n          ")]),a._v(" "),i("div",[i("svg",{attrs:{width:"8",height:"8",viewBox:"0 0 8 8",fill:"none",xmlns:"http://www.w3.org/2000/svg"}},[i("path",{attrs:{d:"M0.5 4H7.5M7.5 4L4 0.5M7.5 4L4 7.5",stroke:"#07403F","stroke-linecap":"round","stroke-linejoin":"round"}})])])]),a._v(" "),a.categoryData.hasCourseOwner?a._e():i("div",{staticClass:"text-center font-averta-regular text-xl italic mt-3 text-[#EF6D13]"},[a._v("\n          *Học thử 300 từ đầu tiên trong bộ từ vựng JLPT N5\n        ")])]),a._v(" "),i("button",{staticClass:"bg-[#57D061] text-xl text-[#07403F] font-beanbag rounded-full py-3 uppercase shadow-lg shadow-[#4C5D7052] mt-12 w-[340px] cursor-pointer",on:{click:function(t){return a.startStudy()}}},[a._v("\n        Bắt đầu\n      ")])]):a._e(),a._v(" "),a.currentView===a.TYPES_VIEW.SEARCH?i("div",[i("div",{staticClass:"flex justify-between items-center mb-10"},[i("div",{staticClass:"flex items-center"},[i("div",{staticClass:"vocabulary-search-btn-back h-[65px] w-[65px]",on:{click:function(t){a.currentView=a.TYPES_VIEW.OVERVIEW}}},[i("i",{staticClass:"fas fa-arrow-left"})]),a._v(" "),i("div",{staticClass:"vocabulary-section-favorite-title"},[i("div",{staticClass:"flex font-beanbag-medium text-[24px] text-[#07403F] items-center"},[a._v("\n              "+a._s(a.categoryData.name)+"\n              "),i("i",{staticClass:"fas fa-heart text-[#FF7C79] ml-2"})]),a._v(" "),i("div",{staticClass:"font-beanbag-regular text-[20px] text-[#07403F]"},[a._v(a._s(a.categoryData.data.word_count)+" từ")])])]),a._v(" "),i("div",{staticClass:"vocabulary-favorite-search-bar flex items-center rounded-full bg-white shadow-md h-[65px] px-[20px] transition-all duration-300 flex-1 min-w-0 max-w-[400px]"},[a._m(1),a._v(" "),i("input",{directives:[{name:"model",rawName:"v-model",value:a.searchQuery,expression:"searchQuery"}],staticClass:"search-input leading-[65px]",attrs:{type:"text",placeholder:"Nhập từ vựng muốn tìm kiếm"},domProps:{value:a.searchQuery},on:{input:function(t){t.target.composing||(a.searchQuery=t.target.value)}}})])]),a._v(" "),a.searchResults.length>0?i("div",{staticClass:"search-result list-favorite"},[i("div",{staticClass:"search-result-items"},a._l(a.searchResults,(function(t,e){return i("div",{key:e,staticClass:"search-result-item cursor-pointer relative",on:{click:function(e){return a.handleVocabularyItemClick(t)}}},[i("div",{staticClass:"search-result-item-content border-b border-[#D0D3DA] border-bottom-[1px] pt-3 pb-3"},[i("div",{staticClass:"search-result-item-title font-gen-jyuu-gothic-regular text-black text-[20px]"},[i("div",{domProps:{innerHTML:a._s(t.value.word)}})]),a._v(" "),i("div",{staticClass:"search-result-item-description font-gen-jyuu-gothic-regular text-[18px] text-[#757575]"},[i("div",{domProps:{innerHTML:a._s(t.value.meaning)}})]),a._v(" "),i("div",{staticClass:"search-result-item-specialized w-fit font-averta-regular text-[16px] text-[#FFFFFF] bg-[#B3B3B3] rounded-full px-3 py-1"},[a._v("\n                "+a._s(t.course_name)+"\n              ")])]),a._v(" "),a.idsFree.includes(t.id)||a.categoryData.hasCourseOwner?a._e():i("div",{staticClass:"absolute h-full top-0 left-0 w-full flex items-center",staticStyle:{background:"linear-gradient(to right, rgba(234, 246, 235, 1), rgba(255, 255, 255, 0))"}},[i("svg",{staticClass:"m-[20px]",attrs:{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"}},[i("path",{attrs:{d:"M12 1.25C15.0376 1.25 17.5 3.71243 17.5 6.75V8.52734C19.0676 9.0071 20.2509 10.3866 20.4756 12.0557C20.6237 13.1559 20.75 14.312 20.75 15.5C20.75 16.688 20.6237 17.8441 20.4756 18.9443C20.204 20.9613 18.5327 22.5558 16.4746 22.6504C15.0462 22.7161 13.5958 22.75 12 22.75C10.4042 22.75 8.95376 22.7161 7.52539 22.6504C5.46733 22.5558 3.79598 20.9613 3.52441 18.9443C3.37629 17.8441 3.25 16.688 3.25 15.5C3.25 14.312 3.37629 13.1559 3.52441 12.0557C3.74914 10.3866 4.93236 9.0071 6.5 8.52734V6.75C6.5 3.71243 8.96243 1.25 12 1.25ZM11.9932 14C11.1685 14.0001 10.5 14.6716 10.5 15.5C10.5 16.3284 11.1685 16.9999 11.9932 17H12.0068C12.8315 16.9999 13.5 16.3284 13.5 15.5C13.5 14.6716 12.8315 14.0001 12.0068 14H11.9932ZM12 3.25C10.067 3.25 8.5 4.817 8.5 6.75V8.31055C9.61773 8.27087 10.7654 8.25 12 8.25C13.2346 8.25 14.3823 8.27087 15.5 8.31055V6.75C15.5 4.817 13.933 3.25 12 3.25Z",fill:"#757575"}})])])])})),0)]):a._e(),a._v(" "),i("button",{staticClass:"fixed bottom-[56px] left-[50%] transform bg-[#57D061] text-xl text-[#07403F] font-beanbag rounded-full py-3 uppercase shadow-lg shadow-[#4C5D7052] w-[340px] cursor-pointer",on:{click:function(t){return a.startStudy()}}},[a._v("\n        bắt đầu\n      ")])]):a._e(),a._v(" "),a.currentView===a.TYPES_VIEW.STACKED_CARD?i("div",{directives:[{name:"loading",rawName:"v-loading",value:a.isLoadDataFlashcard,expression:"isLoadDataFlashcard"}],staticClass:"flashcards-wrap"},[i("div",{staticClass:"mx-auto w-[80%] max-w-[648px] relative"},[i("div",{staticClass:"tag_card rounded-full px-2 py-1 font-beanbag-medium text-white text-base flex items-center mb-4",staticStyle:{"line-height":"1",width:"fit-content","align-self":"flex-start"},style:{background:null!==(t=a.currentCard)&&void 0!==t&&t.is_relearn?"#14AE5C":null!==(e=a.currentCard)&&void 0!==e&&e.is_test?"#FF7C79":"#4E87FF"}},[null!==(n=a.currentCard)&&void 0!==n&&n.is_test?i("span",[a._v("Kiểm tra")]):null!==(r=a.currentCard)&&void 0!==r&&r.is_relearn?i("span",[a._v("Từ vựng ôn lại")]):i("span",[a._v("Từ mới")])]),a._v(" "),i("div",{staticClass:"card_setting cursor-pointer popover_setting absolute right-[-88px] top-[-15px] group z-50"},[i("svg",{attrs:{width:"48",height:"48",viewBox:"0 0 48 48",fill:"none",xmlns:"http://www.w3.org/2000/svg"}},[i("path",{attrs:{d:"M18 44H30C40 44 44 40 44 30V18C44 8 40 4 30 4H18C8 4 4 8 4 18V30C4 40 8 44 18 44Z",stroke:"#757575","stroke-width":"3","stroke-linecap":"round","stroke-linejoin":"round"}}),a._v(" "),i("path",{attrs:{d:"M31.1401 37V29.2",stroke:"#757575","stroke-width":"3","stroke-miterlimit":"10","stroke-linecap":"round","stroke-linejoin":"round"}}),a._v(" "),i("path",{attrs:{d:"M31.1401 14.9V11",stroke:"#757575","stroke-width":"3","stroke-miterlimit":"10","stroke-linecap":"round","stroke-linejoin":"round"}}),a._v(" "),i("path",{attrs:{d:"M31.1399 25.2999C34.0118 25.2999 36.3399 22.9718 36.3399 20.0999C36.3399 17.228 34.0118 14.8999 31.1399 14.8999C28.2681 14.8999 25.9399 17.228 25.9399 20.0999C25.9399 22.9718 28.2681 25.2999 31.1399 25.2999Z",stroke:"#757575","stroke-width":"3","stroke-miterlimit":"10","stroke-linecap":"round","stroke-linejoin":"round"}}),a._v(" "),i("path",{attrs:{d:"M16.8599 37.0001V33.1001",stroke:"#757575","stroke-width":"3","stroke-miterlimit":"10","stroke-linecap":"round","stroke-linejoin":"round"}}),a._v(" "),i("path",{attrs:{d:"M16.8599 18.8V11",stroke:"#757575","stroke-width":"3","stroke-miterlimit":"10","stroke-linecap":"round","stroke-linejoin":"round"}}),a._v(" "),i("path",{attrs:{d:"M16.8602 33.0999C19.732 33.0999 22.0602 30.7718 22.0602 27.9C22.0602 25.0281 19.732 22.7 16.8602 22.7C13.9883 22.7 11.6602 25.0281 11.6602 27.9C11.6602 30.7718 13.9883 33.0999 16.8602 33.0999Z",stroke:"#757575","stroke-width":"3","stroke-miterlimit":"10","stroke-linecap":"round","stroke-linejoin":"round"}})]),a._v(" "),i("div",{staticClass:"popover_setting_content hidden group-hover:block absolute right-[-30px] top-full bg-white shadow-lg rounded-[40px] p-[21px] min-w-[225px] z-10"},[i("div",{staticClass:"relative z-10 bg-white"},[i("div",{staticClass:"popover_setting_content_item flex justify-between items-center py-2"},[i("div",{staticClass:"popover_setting_content_item_title font-averta-regular text-[#07403F] text-base"},[a._v("\n                  Mặt trước thẻ\n                ")]),a._v(" "),i("label",{staticClass:"language-switch",class:{disabled:0===a.categoryData.hasCourseOwner&&a.countSwipeCard===a.limitCardFree}},[i("input",{directives:[{name:"model",rawName:"v-model",value:a.isJapanese,expression:"isJapanese"}],attrs:{disabled:0===a.categoryData.hasCourseOwner&&a.countSwipeCard===a.limitCardFree,type:"checkbox"},domProps:{checked:Array.isArray(a.isJapanese)?a._i(a.isJapanese,null)>-1:a.isJapanese},on:{change:[function(t){var e=a.isJapanese,n=t.target,r=!!n.checked;if(Array.isArray(e)){var i=a._i(e,null);n.checked?i<0&&(a.isJapanese=e.concat([null])):i>-1&&(a.isJapanese=e.slice(0,i).concat(e.slice(i+1)))}else a.isJapanese=r},a.handleLanguageChange]}}),a._v(" "),i("span",{staticClass:"language-slider round"},[i("span",{staticClass:"slider-text"},[a._v(a._s(a.isJapanese?"JA":"VN"))]),a._v(" "),i("span",{staticClass:"flag",class:a.isJapanese?"ja":"vi"})])])])])])])]),a._v(" "),i("div",{staticClass:"cards-wrap content mx-auto w-[80%] max-w-[648px]"},[i("div",{staticClass:"content-wrap mx-auto mb-6"},[i("div",{staticClass:"stackedcards stackedcards--animatable a_cursor--pointer",attrs:{id:"stacked-cards-block"}},[i("div",{staticClass:"stackedcards-container",staticStyle:{"margin-bottom":"20px"}},a._l(a.dataFlashCard,(function(t,e){return i("div",{staticClass:"card-item",class:{"stackedcards-active":0===e,"stackedcards-top":!0,"stackedcards--animatable":!0,"stackedcards-origin-top":!0},attrs:{"data-id":t.id}},[i("div",{staticClass:"card-inner",class:{flip:!(a.isJapanese||t.is_test||0===t.hasCourseOwner),noFlip:t.is_test||t.hasCourseOwner},attrs:{"data-id":t.id}},[1===t.take_break?[i("div",{staticClass:"card__face card__face--jp card__face--front noFlip",staticStyle:{padding:"unset"}},[i("img",{staticClass:"rounded-[32px] h-full w-auto",attrs:{src:"/images/vocabulary/take-break/".concat(t.img_break,".svg"),alt:""}})])]:0===t.hasCourseOwner?[i("div",{staticClass:"card__face card__face--jp card__face--front text-wrap noFlip text-center"},[i("div",[i("svg",{staticClass:"m-[20px]",attrs:{width:"40",height:"40",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"}},[i("path",{attrs:{d:"M12 1.25C15.0376 1.25 17.5 3.71243 17.5 6.75V8.52734C19.0676 9.0071 20.2509 10.3866 20.4756 12.0557C20.6237 13.1559 20.75 14.312 20.75 15.5C20.75 16.688 20.6237 17.8441 20.4756 18.9443C20.204 20.9613 18.5327 22.5558 16.4746 22.6504C15.0462 22.7161 13.5958 22.75 12 22.75C10.4042 22.75 8.95376 22.7161 7.52539 22.6504C5.46733 22.5558 3.79598 20.9613 3.52441 18.9443C3.37629 17.8441 3.25 16.688 3.25 15.5C3.25 14.312 3.37629 13.1559 3.52441 12.0557C3.74914 10.3866 4.93236 9.0071 6.5 8.52734V6.75C6.5 3.71243 8.96243 1.25 12 1.25ZM11.9932 14C11.1685 14.0001 10.5 14.6716 10.5 15.5C10.5 16.3284 11.1685 16.9999 11.9932 17H12.0068C12.8315 16.9999 13.5 16.3284 13.5 15.5C13.5 14.6716 12.8315 14.0001 12.0068 14H11.9932ZM12 3.25C10.067 3.25 8.5 4.817 8.5 6.75V8.31055C9.61773 8.27087 10.7654 8.25 12 8.25C13.2346 8.25 14.3823 8.27087 15.5 8.31055V6.75C15.5 4.817 13.933 3.25 12 3.25Z",fill:"#757575"}})])]),a._v(" "),i("div",{staticClass:"font-gen-jyuu-gothic-medium text-base text-center text-[#07403F]"},[a._v("\n                        Sở hữu khóa học để mở toàn bộ\n                      ")]),a._v(" "),i("div",{staticClass:"font-gen-jyuu-gothic-medium text-2xl text-center text-[#07403F]"},[a._v("\n                        "+a._s(a.categoryData.data.word_count)+" từ vựng\n                      ")]),a._v(" "),a._m(2,!0),a._v(" "),i("a",{staticClass:"bg-[#57D061] text-xl text-[#07403F] font-beanbag rounded-full py-3 uppercase shadow-lg shadow-[#4C5D7052] mt-12 w-[340px] cursor-pointer",attrs:{href:"/khoa-hoc/".concat(a.categoryData.SEOurl)}},[a._v("\n                        Mở khóa toàn bộ\n                      ")])])]:[i("div",{staticClass:"card__face card__face--jp card__face--front",class:{noFlip:t.is_test},staticStyle:{width:"100%",height:"100%"}},[i("div",{staticClass:"card-wrap p-4 h-[90%] overflow-y-auto relative",class:{noFlip:t.is_test}},[i("div",{staticClass:"card_header flex items-center"},[t.value&&t.value.audio?i("div",{staticClass:"card_audio noFlip w-[48px] h-[48px] rounded-full bg-[#E1EBFF] flex items-center justify-center mr-4 cursor-pointer",on:{click:function(e){return a.playAudio(t.id,t.value.audio)}}},[i("svg",{staticClass:"noFlip",attrs:{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"}},[i("path",{attrs:{d:"M18 16.7503C17.84 16.7503 17.69 16.7003 17.55 16.6003C17.22 16.3503 17.15 15.8803 17.4 15.5503C18.97 13.4603 18.97 10.5403 17.4 8.45027C17.15 8.12027 17.22 7.65027 17.55 7.40027C17.88 7.15027 18.35 7.22027 18.6 7.55027C20.56 10.1703 20.56 13.8303 18.6 16.4503C18.45 16.6503 18.23 16.7503 18 16.7503Z",fill:"#4E87FF"}}),a._v(" "),i("path",{attrs:{d:"M19.83 19.2503C19.67 19.2503 19.52 19.2003 19.38 19.1003C19.05 18.8503 18.98 18.3803 19.23 18.0503C21.9 14.4903 21.9 9.51027 19.23 5.95027C18.98 5.62027 19.05 5.15027 19.38 4.90027C19.71 4.65027 20.18 4.72027 20.43 5.05027C23.5 9.14027 23.5 14.8603 20.43 18.9503C20.29 19.1503 20.06 19.2503 19.83 19.2503Z",fill:"#4E87FF"}}),a._v(" "),i("path",{attrs:{opacity:"0.4",d:"M15.75 7.40972V16.5897C15.75 18.3097 15.13 19.5997 14.02 20.2197C13.57 20.4697 13.07 20.5897 12.55 20.5897C11.75 20.5897 10.89 20.3197 10.01 19.7697L7.09 17.9397C6.89 17.8197 6.66 17.7497 6.43 17.7497H5.5V6.24972H6.43C6.66 6.24972 6.89 6.17972 7.09 6.05972L10.01 4.22972C11.47 3.31972 12.9 3.15972 14.02 3.77972C15.13 4.39972 15.75 5.68972 15.75 7.40972Z",fill:"#4E87FF"}}),a._v(" "),i("path",{attrs:{d:"M5.5 6.25V17.75H5C2.58 17.75 1.25 16.42 1.25 14V10C1.25 7.58 2.58 6.25 5 6.25H5.5Z",fill:"#4E87FF"}})])]):a._e(),a._v(" "),i("div",{staticClass:"card_title font-gen-jyuu-gothic-medium text-2xl text-[#757575]",domProps:{innerHTML:a._s(t.value.word_stress)}})]),a._v(" "),i("div",{staticClass:"card_content min-h-[calc(100%-34px)] flex flex-col",class:{noFlip:t.is_test}},[i("div",{staticClass:"content-text font-gen-jyuu-gothic-medium text-[56px] text-[#07403F] items-center justify-center flex text-center",staticStyle:{"flex-grow":"1"},domProps:{innerHTML:a._s(t.value.word)}}),a._v(" "),t.is_test?i("div",{staticClass:"text-center font-gen-jyuu-gothic-medium text-[20px] text-[#07403F]"},[a._v("\n                            có nghĩa là gì?\n                          ")]):a._e(),a._v(" "),t.value.front_image&&!t.is_test?i("div",{staticClass:"content-img text-center p-[40px]"},[i("img",{attrs:{src:"https://video-test.dungmori.com/images/".concat(t.value.front_image)}})]):a._e(),a._v(" "),t.value.example.length&&!t.is_test?i("div",{staticClass:"example-wrap"},[i("p",{staticClass:"w-fit font-beanbag text-base text-[#07403F] bg-[#CEFFD8] rounded-[10px] p-2 mb-2"},[a._v("\n                              Ví dụ\n                            ")]),a._v(" "),i("div",{staticClass:"list-example"},[a._l(t.value.example,(function(e,n){return[i("div",{staticClass:"example-item flex items-start mb-1"},[e.audio?i("svg",{staticClass:"w-[36px] h-[36px] noFlip cursor-pointer",attrs:{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},on:{click:function(r){return a.playAudio(t.id+"_example_"+n,e.audio)}}},[i("path",{attrs:{d:"M18 16.7503C17.84 16.7503 17.69 16.7003 17.55 16.6003C17.22 16.3503 17.15 15.8803 17.4 15.5503C18.97 13.4603 18.97 10.5403 17.4 8.45027C17.15 8.12027 17.22 7.65027 17.55 7.40027C17.88 7.15027 18.35 7.22027 18.6 7.55027C20.56 10.1703 20.56 13.8303 18.6 16.4503C18.45 16.6503 18.23 16.7503 18 16.7503Z",fill:"#4E87FF"}}),a._v(" "),i("path",{attrs:{d:"M19.83 19.2503C19.67 19.2503 19.52 19.2003 19.38 19.1003C19.05 18.8503 18.98 18.3803 19.23 18.0503C21.9 14.4903 21.9 9.51027 19.23 5.95027C18.98 5.62027 19.05 5.15027 19.38 4.90027C19.71 4.65027 20.18 4.72027 20.43 5.05027C23.5 9.14027 23.5 14.8603 20.43 18.9503C20.29 19.1503 20.06 19.2503 19.83 19.2503Z",fill:"#4E87FF"}}),a._v(" "),i("path",{attrs:{opacity:"0.4",d:"M15.75 7.40972V16.5897C15.75 18.3097 15.13 19.5997 14.02 20.2197C13.57 20.4697 13.07 20.5897 12.55 20.5897C11.75 20.5897 10.89 20.3197 10.01 19.7697L7.09 17.9397C6.89 17.8197 6.66 17.7497 6.43 17.7497H5.5V6.24972H6.43C6.66 6.24972 6.89 6.17972 7.09 6.05972L10.01 4.22972C11.47 3.31972 12.9 3.15972 14.02 3.77972C15.13 4.39972 15.75 5.68972 15.75 7.40972Z",fill:"#4E87FF"}}),a._v(" "),i("path",{attrs:{d:"M5.5 6.25V17.75H5C2.58 17.75 1.25 16.42 1.25 14V10C1.25 7.58 2.58 6.25 5 6.25H5.5Z",fill:"#4E87FF"}})]):a._e(),a._v(" "),i("div",{staticClass:"ml-2 font-beanbag-regular text-2xl flex items-start w-[90%]"},[a._v("\n                                    "+a._s(n+1)+". "),i("span",{staticClass:"ml-2",domProps:{innerHTML:a._s(e.example)}})])])]}))],2)]):a._e(),a._v(" "),t.is_test?i("div",{staticClass:"test-wrap flex flex-col noFlip mb-5"},a._l(t.value.quiz_question,(function(e,n){return i("div",{key:n,staticClass:"radio-wrap"},[i("input",{staticClass:"input-radio-test",class:{false:e.index},attrs:{type:"radio",id:"".concat(t.id,"-").concat(n),name:"select",disabled:e.index!==a.selectedAnswerIndex&&null!==a.selectedAnswerIndex},domProps:{value:n},on:{click:function(n){return a.selectAnswer(t.id,e.index)}}}),a._v(" "),i("label",{staticClass:"label-radio-test",attrs:{for:"".concat(t.id,"-").concat(n)}},[i("div",{staticClass:"number-radio-test"},[a._v(a._s(n+1))]),a._v(" "),i("p",[a._v(a._s(e.question))])])])})),0):a._e()])]),a._v(" "),t.is_test?a._e():i("div",{staticClass:"card-footer noFlip flex justify-end border-t-[1px] border-[#D0D3DA]"},[i("svg",{staticClass:"m-3 noFlip",attrs:{width:"33",height:"28",viewBox:"0 0 33 28",fill:"none",xmlns:"http://www.w3.org/2000/svg"},on:{click:function(e){return a.likeCard(t.id)}}},[i("path",{attrs:{d:"M23.8333 0.5C28.6377 0.5 32.4997 4.21737 32.5 8.78727C32.4819 14.1271 29.8923 18.3826 26.6876 21.5156C23.4787 24.6525 19.6885 26.627 17.3726 27.393C17.163 27.4593 16.8527 27.5 16.5137 27.5C16.1782 27.5 15.8562 27.4601 15.6229 27.3915C13.3062 26.6243 9.50962 24.6502 6.29845 21.5153C3.08901 18.3822 0.5 14.127 0.5 8.78779C0.5 4.21765 4.36212 0.5 9.16667 0.5C12.0183 0.5 14.5145 1.79375 16.1076 3.81099L16.5 4.30783L16.8924 3.81099C18.4855 1.79375 20.9817 0.5 23.8333 0.5Z",fill:t.isLike?"#FF7C79":"#ffffff",stroke:"#FF7C79"}})])])]),a._v(" "),i("div",{staticClass:"card__face card__face--vi card__face--back",staticStyle:{width:"100%",height:"100%"}},[i("div",{staticClass:"card-wrap-back p-4 h-[60%] overflow-y-auto flex flex-col relative"},[i("div",{staticClass:"card_content_back font-beanbag-medium text-5xl items-center justify-center flex text-[#07403F] mb-5",staticStyle:{"flex-grow":"1"}},[i("div",{staticClass:"text-center",domProps:{innerHTML:a._s(t.value.meaning)}})]),a._v(" "),i("div",{staticClass:"font-beanbag-medium text-[#757575] text-xl text-center text-[#07403F]",domProps:{innerHTML:a._s(t.value.kanji_meaning)}}),a._v(" "),t.value.back_image?i("div",{staticClass:"card_img_back p-[40px] content-img text-center"},[i("img",{attrs:{src:"https://video-test.dungmori.com/images/".concat(t.value.back_image)}})]):a._e(),a._v(" "),t.value.meaning_example.length?i("div",{staticClass:"example-wrap"},[i("p",{staticClass:"w-fit font-beanbag text-base text-[#07403F] bg-[#CEFFD8] rounded-[10px] p-2 mb-2"},[a._v("\n                            Ví dụ\n                          ")]),a._v(" "),i("div",{staticClass:"list-example"},[a._l(t.value.meaning_example,(function(t,e){return[i("div",{staticClass:"example-item flex items-center mb-1"},[i("div",{staticClass:"ml-2 font-averta-regular text-2xl flex items-start"},[a._v("\n                                  "+a._s(e+1)+". "),i("span",{staticClass:"ml-1",domProps:{innerHTML:a._s(t)}})])])]}))],2)]):a._e()]),a._v(" "),i("div",{staticClass:"how-remember-wrap px-4 h-[40%] flex flex-col"},[a._m(3,!0),a._v(" "),t.comment&&t.comment.user_info?i("div",{staticClass:"how-remember-wrap-content grid grid-cols-[40px_auto] gap-4"},[i("div",{staticClass:"how-remember-wrap-avatar w-[28px]"},[i("img",{staticClass:"rounded-full",attrs:{src:"/cdn/avatar/small/".concat(t.comment.user_info.avatar)}})]),a._v(" "),i("div",{staticClass:"how-remember-wrap-info flex text-[#073A3B]"},[i("span",{staticClass:"font-averta-bold"},[a._v("\n                          "+a._s(t.comment.user_info.name)+"・\n                        ")]),a._v(" "),i("span",{staticClass:"font-averta-regular"},[a._v("\n                          "+a._s(t.comment.time_created)+"\n                        ")]),a._v(" "),t.comment.pin?i("svg",{attrs:{width:"16",height:"16",viewBox:"0 0 16 16",fill:"none",xmlns:"http://www.w3.org/2000/svg"}},[i("path",{attrs:{d:"M4.54059 9.86604C4.49626 9.83523 4.45013 9.81231 4.41581 9.77792C3.83372 9.198 3.25271 8.617 2.67312 8.03457C2.59482 7.95577 2.51759 7.87338 2.45323 7.78312C2.23084 7.47256 2.23799 7.15555 2.48076 6.86004C2.75322 6.52871 3.11005 6.30626 3.49405 6.13218C4.1902 5.81697 4.90958 5.70163 5.65114 5.95953C5.851 6.02902 6.03657 6.14078 6.21856 6.22854C6.924 5.52396 7.63981 4.80935 8.36242 4.08758C8.13788 3.75446 8.06279 3.37477 8.17649 2.96821C8.44966 1.99177 9.64601 1.67476 10.3836 2.38543C10.7887 2.77586 11.1827 3.17776 11.5803 3.57608C12.2554 4.25271 12.9347 4.92469 13.6019 5.60885C14.4236 6.45133 13.8447 7.6316 13.0213 7.84616C12.6641 7.93929 12.3312 7.88628 12.0212 7.69106C11.9354 7.63697 11.8721 7.63733 11.7981 7.71148C11.1216 8.39277 10.4426 9.07191 9.76472 9.75177C9.75864 9.75786 9.75542 9.76682 9.74648 9.78186C9.75363 9.79691 9.76078 9.81732 9.77223 9.83523C10.3089 10.6745 10.3089 11.5489 9.90023 12.4272C9.74755 12.7553 9.5248 13.0526 9.31814 13.3527C9.24735 13.4555 9.13579 13.5365 9.0296 13.6074C8.81114 13.7532 8.57373 13.7629 8.34561 13.635C8.22297 13.5662 8.10677 13.4767 8.00701 13.3775C7.36128 12.7352 6.71877 12.089 6.08019 11.4392C5.99331 11.3508 5.94289 11.3429 5.84278 11.4246C4.85559 12.2341 3.86411 13.0382 2.87299 13.8431C2.60555 14.0605 2.32702 14.0509 2.12286 13.822C1.99378 13.6773 1.96304 13.515 2.04277 13.3384C2.07745 13.2618 2.12286 13.1887 2.17256 13.1206C2.9209 12.0915 3.67104 11.0635 4.42046 10.0351C4.45407 9.9889 4.4866 9.94198 4.53988 9.8664L4.54059 9.86604Z",fill:"#B3B3B3"}})]):a._e()]),a._v(" "),i("div",{staticClass:"col-start-2 font-averta-regular text-[#07403F]",staticStyle:{display:"-webkit-box","-webkit-line-clamp":"2","-webkit-box-orient":"vertical",overflow:"hidden","text-overflow":"ellipsis"}},[a._v("\n                            "+a._s(t.comment.content)+"\n                          ")]),a._v(" "),i("div",{staticClass:"col-start-2 flex justify-between items-center"},[i("div",{staticClass:"font-averta-regular text-[#009951] flex"},[i("div",{staticClass:"flex items-center mr-5"},[i("svg",{staticClass:"mr-1 noFlip",attrs:{width:"13",height:"12",viewBox:"0 0 13 12",fill:"none",xmlns:"http://www.w3.org/2000/svg"}},[i("path",{attrs:{d:"M9.38889 0.723633C11.1046 0.723633 12.4996 2.1159 12.5 3.84852C12.493 5.95749 11.518 7.64456 10.2933 8.89844C9.06415 10.1569 7.61344 10.9461 6.74434 11.2477C6.70298 11.2611 6.61724 11.2756 6.50542 11.2756C6.39725 11.2756 6.30516 11.262 6.25054 11.2459C5.38046 10.9429 3.92943 10.1541 2.70106 8.89818C1.47455 7.64419 0.5 5.95749 0.5 3.8492C0.5 2.11626 1.89522 0.723633 3.61111 0.723633C4.6312 0.723633 5.52625 1.20724 6.10079 1.96912L6.5 2.49851L6.89921 1.96912C7.47375 1.20724 8.3688 0.723633 9.38889 0.723633Z",fill:t.comment.comment_like.length?"#009951":"none",stroke:"#009951"}})]),a._v("\n                                "+a._s(t.comment.count_like)+"\n                              ")]),a._v(" "),t.comment&&t.comment.replies?i("div",[a._v("\n                                "+a._s(t.comment.replies.length)+" Trả lời\n                              ")]):a._e()]),a._v(" "),i("div",{staticClass:"underline decoration-solid text-[#009951] cursor-pointer noFlip",on:{click:function(t){return a.toggleCommentTab("open")}}},[a._v("\n                              Xem thêm >>\n                            ")])])]):i("div",{staticClass:"items-center flex grow",staticStyle:{"flex-grow":"1"}},[i("div",{staticClass:"underline decoration-solid font-averta-regular text-[#009951] cursor-pointer noFlip",on:{click:function(t){return a.toggleCommentTab("open")}}},[a._v("\n                            Đóng góp cách nhớ của bạn >>\n                          ")])])])])]],2)])})),0),a._v(" "),i("div",{staticClass:"stackedcards--animatable stackedcards-overlay left stackedcards-origin-top font-averta-bold text-[#975102]"},[a._v("\n              Chưa nhớ\n            ")]),a._v(" "),i("div",{staticClass:"stackedcards--animatable stackedcards-overlay right stackedcards-origin-top font-averta-bold text-[#02542D]"},[a._v("\n              Đã học\n            ")])])]),a._v(" "),i("div",{staticClass:"group-actions flex items-center",class:a.currentCard&&1===a.currentCard.take_break?"justify-center":"justify-between"},[a.currentCard&&1===a.currentCard.take_break?[i("button",{staticClass:"bg-[#8FFFA6] text-xl text-[#07403F] font-beanbag rounded-full py-3 uppercase shadow-lg shadow-[#4C5D7052] mt-12 w-[340px] cursor-pointer",on:{click:function(t){return a.swipeCard("right")}}},[a._v("\n              Đi tiếp nào!\n            ")])]:[i("button",{staticClass:"w-[37%] flex justify-center items-center a-cursor-pointer rounded-full py-[21px] px-[43px] font-beanbag-medium text-2xl drop-shadow-2xl",class:a.categoryData.hasCourseOwner?"left-action ":"",style:a.categoryData.hasCourseOwner||!a.categoryData.hasCourseOwner&&a.countSwipeCard<a.limitCardFree?{background:"#FFF1BB",color:"#07403F"}:{background:"#D9D9D9",color:"#B3B3B3"},attrs:{disabled:a.currentCard&&a.currentCard.is_test||!a.categoryData.hasCourseOwner&&a.countSwipeCard===a.limitCardFree},on:{click:function(t){return a.swipeCard("left")}}},[i("svg",{staticClass:"mr-2",attrs:{width:"23",height:"18",viewBox:"0 0 23 18",fill:"none",xmlns:"http://www.w3.org/2000/svg"}},[i("path",{attrs:{d:"M21.2842 7.80392C21.9448 7.80392 22.4803 8.33943 22.4803 9C22.4803 9.66057 21.9448 10.1961 21.2842 10.1961V7.80392ZM1.30128 9.84575C0.834185 9.37866 0.834185 8.62134 1.30128 8.15425L8.91306 0.542469C9.38015 0.0753727 10.1375 0.0753727 10.6046 0.542469C11.0717 1.00957 11.0717 1.76688 10.6046 2.23398L3.83854 9L10.6046 15.766C11.0717 16.2331 11.0717 16.9904 10.6046 17.4575C10.1375 17.9246 9.38015 17.9246 8.91306 17.4575L1.30128 9.84575ZM21.2842 10.1961H2.14703V7.80392H21.2842V10.1961Z",fill:a.categoryData.hasCourseOwner||!a.categoryData.hasCourseOwner&&a.countSwipeCard<a.limitCardFree?"#07403F":"#B3B3B3"}})]),a._v("\n              Chưa nhớ\n            ")]),a._v(" "),i("button",{staticClass:"rounded-full drop-shadow-2xl p-4 cursor-pointer",style:a.categoryData.hasCourseOwner||!a.categoryData.hasCourseOwner&&a.countSwipeCard<a.limitCardFree?{background:"#FFFFFF"}:{background:"#D9D9D9"},attrs:{disabled:a.currentCard&&(a.currentCard.is_test||a.swipeCardList.length>0&&a.swipeCardList[a.swipeCardList.length-1].is_test)||!a.categoryData.hasCourseOwner&&a.countSwipeCard===a.limitCardFree},on:{click:a.undo}},[i("svg",{attrs:{width:"40",height:"40",viewBox:"0 0 40 40",fill:"none",xmlns:"http://www.w3.org/2000/svg"}},[i("path",{attrs:{d:"M11.8833 30.5168H25.2166C29.8166 30.5168 33.5499 26.7834 33.5499 22.1834C33.5499 17.5834 29.8166 13.8501 25.2166 13.8501H6.88327",stroke:a.categoryData.hasCourseOwner||!a.categoryData.hasCourseOwner&&a.countSwipeCard<a.limitCardFree?"#07403F":"#B3B3B3","stroke-width":"2.5","stroke-miterlimit":"10","stroke-linecap":"round","stroke-linejoin":"round"}}),a._v(" "),i("path",{attrs:{d:"M10.7166 18.0167L6.44993 13.7501L10.7166 9.4834",stroke:a.categoryData.hasCourseOwner||!a.categoryData.hasCourseOwner&&a.countSwipeCard<a.limitCardFree?"#07403F":"#B3B3B3","stroke-width":"2.5","stroke-linecap":"round","stroke-linejoin":"round"}})])]),a._v(" "),i("button",{staticClass:"w-[37%] flex justify-center items-center a-cursor-pointer rounded-full py-[21px] px-[43px] font-beanbag-medium text-2xl drop-shadow-2xl",class:a.categoryData.hasCourseOwner||!a.categoryData.hasCourseOwner&&a.countSwipeCard<a.limitCardFree?"right-action ":"",style:a.categoryData.hasCourseOwner||!a.categoryData.hasCourseOwner&&a.countSwipeCard<a.limitCardFree?{background:"#CEFFD8",color:"#07403F"}:{background:"#D9D9D9",color:"#B3B3B3"},attrs:{disabled:a.currentCard&&a.currentCard.is_test||!a.categoryData.hasCourseOwner&&a.countSwipeCard===a.limitCardFree},on:{click:function(t){return a.swipeCard("right")}}},[a._v("\n              Đã học\n              "),i("svg",{staticClass:"ml-2",attrs:{width:"22",height:"18",viewBox:"0 0 22 18",fill:"none",xmlns:"http://www.w3.org/2000/svg"}},[i("path",{attrs:{d:"M1.19994 7.8C0.537194 7.8 -6.41346e-05 8.33726 -6.41346e-05 9C-6.41346e-05 9.66274 0.537194 10.2 1.19994 10.2V7.8ZM21.2485 9.84853C21.7171 9.3799 21.7171 8.6201 21.2485 8.15147L13.6117 0.514718C13.1431 0.0460892 12.3833 0.0460892 11.9147 0.514718C11.446 0.983348 11.446 1.74315 11.9147 2.21177L18.7029 9L11.9147 15.7882C11.446 16.2569 11.446 17.0167 11.9147 17.4853C12.3833 17.9539 13.1431 17.9539 13.6117 17.4853L21.2485 9.84853ZM1.19994 10.2H20.3999V7.8H1.19994V10.2Z",fill:a.categoryData.hasCourseOwner||!a.categoryData.hasCourseOwner&&a.countSwipeCard<a.limitCardFree?"#07403F":"#B3B3B3"}})])])]],2)])]):a._e(),a._v(" "),a.currentView===a.TYPES_VIEW.RESULT?i("div",{staticClass:"w-[90%] max-w-[1000px] mx-auto h-[450px] my-auto"},[i("ChartVocabulary",{attrs:{options:{showTextMaxLearnWord:!0},chartData:a.chartData}}),a._v(" "),i("div",{staticClass:"flex items-center justify-center relative z-10"},[i("button",{staticClass:"w-[340px] bg-[#FFF193] text-xl text-[#07403F] font-beanbag rounded-full py-3 uppercase shadow-lg shadow-[#4C5D7052] mt-12",on:{click:function(t){a.currentView=a.TYPES_VIEW.OVERVIEW}}},[a._v("\n          Học lại\n        ")]),a._v(" "),i("button",{staticClass:"w-[340px] bg-[#57D061] text-xl text-[#07403F] font-beanbag rounded-full py-3 uppercase shadow-lg shadow-[#4C5D7052] mt-12 ml-10",on:{click:function(t){return a.startStudy()}}},[a._v("\n          Học bộ từ khác\n        ")])]),a._v(" "),a._m(4)],1):a._e(),a._v(" "),a.currentView===a.TYPES_VIEW.STACKED_CARD?i("div",{attrs:{id:"lesson-list"}},[i("Container",{attrs:{hasLesson:!1},on:{"active-changed":function(t){return a.handleContainerActive(t)}}})],1):a._e()]),a._v(" "),a.showUnlockCoursePopup?i("div",{staticClass:"unlock-course-popup-overlay",on:{click:function(t){return t.target!==t.currentTarget?null:a.closeUnlockCoursePopup.apply(null,arguments)}}},[i("div",{staticClass:"unlock-course-popup"},[i("div",{staticClass:"unlock-course-popup-content"},[i("button",{staticClass:"unlock-course-close-btn",on:{click:a.closeUnlockCoursePopup}},[i("i",{staticClass:"fas fa-times"})]),a._v(" "),a._m(5),a._v(" "),a._m(6),a._v(" "),i("div",{staticClass:"unlock-course-description text-center text-2xl text-[#07403F] font-beanbag-medium"},[a._v("\n          "+a._s(a.categoryData.data.word_count)+" từ vựng\n        ")]),a._v(" "),i("div",{staticClass:"unlock-course-actions text-center"},[i("a",{staticClass:"bg-[#57D061] text-xl text-[#07403F] font-beanbag rounded-full py-3 uppercase shadow-lg shadow-[#4C5D7052] mt-2 w-[340px] cursor-pointer",attrs:{href:"/khoa-hoc/".concat(a.categoryData.SEOurl)}},[a._v("\n            mua ngay\n          ")])])])])]):a._e()])}),[function(){var t=this._self._c;return t("div",{staticClass:"img mb-7"},[t("img",{attrs:{src:"/images/vocabulary/img-over-view-course.svg",alt:""}})])},function(){var t=this._self._c;return t("div",{staticClass:"search-icon"},[t("i",{staticClass:"fas fa-search"})])},function(){var t=this._self._c;return t("div",{staticClass:"img text-center"},[t("img",{attrs:{src:"/images/vocabulary/card-not-has-course.svg",alt:""}})])},function(){var t=this,e=t._self._c;return e("div",{staticClass:"how-remember-wrap-header flex mb-5"},[e("div",{staticClass:"font-beanbag-medium text-[#757575] text-xl"},[t._v("\n                            Cách nhớ\n                          ")]),t._v(" "),e("div",{staticClass:"border-b-[1px] border-[#D0D3DA] flex-1 m-[7px]"})])},function(){var t=this._self._c;return t("div",{staticClass:"img-fix-botton fixed bottom-0 w-[calc(100%-280px)] right-0 text-center z-[1]"},[t("img",{attrs:{src:"/images/vocabulary/img-bot-result-1.svg",alt:""}})])},function(){var t=this._self._c;return t("div",{staticClass:"unlock-course-icon"},[t("img",{attrs:{src:"/images/vocabulary/card-not-has-course.svg",alt:"Icon khóa"}})])},function(){var t=this._self._c;return t("div",{staticClass:"unlock-course-title"},[t("div",{staticClass:"font-beanbag-medium text-base text-[#07403F] text-center"},[this._v("\n            Mua khóa học để mở khóa toàn bộ\n          ")])])}],!1,null,"6a9236dc",null)),lt=ut.exports;function ft(t){return ft="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},ft(t)}function dt(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var n=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=n){var r,a,i,o,s=[],c=!0,u=!1;try{if(i=(n=n.call(t)).next,0===e){if(Object(n)!==n)return;c=!1}else for(;!(c=(r=i.call(n)).done)&&(s.push(r.value),s.length!==e);c=!0);}catch(t){u=!0,a=t}finally{try{if(!c&&null!=n.return&&(o=n.return(),Object(o)!==o))return}finally{if(u)throw a}}return s}}(t,e)||function(t,e){if(t){if("string"==typeof t)return ht(t,e);var n={}.toString.call(t).slice(8,-1);return"Object"===n&&t.constructor&&(n=t.constructor.name),"Map"===n||"Set"===n?Array.from(t):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?ht(t,e):void 0}}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function ht(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=Array(e);n<e;n++)r[n]=t[n];return r}function pt(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,r)}return n}function vt(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?pt(Object(n),!0).forEach((function(e){mt(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):pt(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}function mt(t,e,n){return(e=function(t){var e=function(t,e){if("object"!=ft(t)||!t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var r=n.call(t,e||"default");if("object"!=ft(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==ft(e)?e:e+""}(e))in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}var gt={OVERVIEW:1,SEARCH:2,SPECIALIZED:3,FAVORITE:4},_t={name:"vocabulary",components:{ChartVocabulary:g,FlashCard:lt,SearchBar:a},props:{jlpt:{type:Object,required:!0,default:function(){}},specialized:{type:Object,required:!0,default:function(){}},favorite:{type:Object,required:!0,default:function(){return[]}}},watch:{searchFocused:function(t,e){t&&(this.currentView=gt.SEARCH)},searchQuery:function(t,e){console.log("searchQuery: ",t),t?(this.currentView=gt.SEARCH,this.searchWord()):this.searchResults=[]},searchQueryFavorite:function(t,e){t?this.searchWordFavorite():this.favoriteFlashcards=this.allFavoriteFlashcards},currentView:function(t,e){console.log("currentView: ",t),t===gt.OVERVIEW&&(this.getOverviewFavorite(),this.getCourse())}},data:function(){return{searchQuery:"",searchFocused:!1,searchQueryFavorite:"",showFlashcardPopup:!1,isFlashcardFlipped:!1,currentFlashcardIndex:0,favoriteFlashcards:[],allFavoriteFlashcards:[],currentView:gt.OVERVIEW,typesView:gt,chart:null,categories:[],searchResults:[],showDialogInfo:!1,card:{id:103569,lesson_id:10846,exam_part_id:null,skill:1,mondai_id:null,type:17,type_ld:null,server:null,video_name:null,video_title:null,value:{word:"<p>わたし</p>",word_stress:'<p>わ<span style="text-decoration:overline">たしr</span></p>',word_type:"Động từ",meaning:"<p>3</p>",audio:null,front_image:null,back_image:null,example:[{example:'<p><span style="color:#ef6d13">わたし</span>はきょうしです。</p>',audio:null}],meaning_example:['<p><span style="color:#ef6d13">T&ocirc;i</span> l&agrave; gi&aacute;o vi&ecirc;n.</p>'],quiz_question:[],kanji_meaning:null},suggest:null,explain:null,explain_mp3:null,value_data:null,grade:"",sort:3,show:1,is_quiz:0,video_full:0,updated_at:"2025-04-25T01:45:14.000000Z",created_at:"2025-04-10T01:40:02.000000Z",answers:[],comment:{id:463617,content:"hi",user_id:540309,count_like:0,created_at:"2025-05-16 09:54:28",pin:0,parent_id:0,table_id:103569,table_name:"flashcard",user_info:{email:"<EMAIL>",name:"Dinhsuu",avatar:"1711587724_0_76076.jpeg",userId:540309},time_created:"16/05/2025 09:54",replies:[{id:463618,table_id:103569,table_name:"flashcard",user_id:540308,admin_log:null,kwadmin_id:null,content:"122",img:null,audio:null,rate:0,is_tester:0,parent_id:463617,count_like:0,ulikes:null,readed:0,tag_data:null,status:0,pin:0,is_correct:0,updated_at:"2025-05-16 09:54:44",created_at:"2025-05-16 09:54:44",user_info:{email:"<EMAIL>",name:"Thu Vũ",avatar:"1711587724_0_76076.jpeg",userId:540308},time_created:"16/05/2025 09:54",replies:null,table_info:{id:"",course_id:"",name:"",SEOurl:"",course_url:""}}],table_info:{id:"",course_id:"",name:"",SEOurl:"",course_url:""},comment_like:[]}},courseSpecializedActive:null,isLoadingChart:!1,isLoadingSearch:!1,isLoadingFavorite:!1,favorite:[],authUser:authUser}},computed:{chartData:function(){var t={learned:0,temporary:0,memorized:0,mastered:0,super_mastered:0,totalWords:0};return console.log("categories: ",this.categories),this.categories.forEach((function(e){e.selected&&(console.log("category: ",e),t.learned=e.data.learned+t.learned+e.data.temporary+e.data.memorized+e.data.mastered,t.temporary=e.data.temporary+t.temporary+e.data.memorized+e.data.mastered,t.memorized=e.data.memorized+t.memorized+e.data.mastered,t.mastered=e.data.mastered+t.mastered,t.super_mastered=e.data.super_mastered+t.super_mastered,t.totalWords=e.data.word_count?e.data.word_count+t.totalWords:t.totalWords)})),console.log("chartData: ",t),t}},methods:{openFlashcardPopup:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0;this.currentFlashcardIndex=t,this.isFlashcardFlipped=!1,this.showFlashcardPopup=!0,document.body.style.overflow="hidden"},closeFlashcardPopup:function(){this.showFlashcardPopup=!1,document.body.style.overflow=""},flipCard:function(t,e){if(console.log("id: ",t),e.target.closest(".noFlip, .card_audio, button"))console.log("Click on non-flippable element, skipping flip");else{var n=document.querySelector("#card-inner-".concat(t));n&&n.classList.toggle("flip")}},prevFlashcard:function(){this.currentFlashcardIndex>0&&(this.currentFlashcardIndex--,this.isFlashcardFlipped=!1)},nextFlashcard:function(){this.currentFlashcardIndex<this.favoriteFlashcards.length-1&&(this.currentFlashcardIndex++,this.isFlashcardFlipped=!1)},initCategories:function(){var t=this;this.categories=[],this.jlpt&&this.jlpt.forEach((function(e){console.log("item: ",e);var n={learned:0,temporary:0,memorized:0,mastered:0,super_mastered:0,word_count:e.word_count};t.categories.push(vt({name:"Từ vựng ".concat(e.name),level:e.name,id:e.id,selected:e.name.includes("N5"),data:n},Object.fromEntries(Object.entries(e).filter((function(t){return"name"!==dt(t,1)[0]})))))})),this.specialized&&this.specialized.data&&this.list_vocabulary.specialized.data.forEach((function(e){t.categories.push(vt({name:e.name,id:e.id,selected:!1,data:{learned:0,temporary:0,memorized:0,mastered:0,super_mastered:0}},Object.fromEntries(Object.entries(e).filter((function(t){return"name"!==dt(t,1)[0]})))))})),console.log("categories: ",this.categories)},playAudio:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null,e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"";console.log("Play audio for id: ".concat(t,", url: ").concat(e)),console.log("this.$root: ",this.$root),this.$root.toggleMp3?this.$root.toggleMp3(t,"flashcard","https://video-test.dungmori.com/audio/".concat(e)):console.error("toggleMp3 method not found in root instance")},openCourseFlashCardDetail:function(t){if(this.authUser.id){console.log("courseId: ",t);var e=this.categories.find((function(e){return e.id===t}));console.log("course: ",e),!e||!e.word_count||e.word_count,console.log("openCourseFlashCardDetail: ",t),this.courseSpecializedActive=t,this.currentView=this.typesView.SPECIALIZED}else this.$emit("open-login-popup")},getCourse:function(){var t=this;this.isLoadingChart=!0,console.log("getCourse---------------------\x3e");var e={course_id:this.categories.map((function(t){return t.id})),type:"normal"};d().get("/vocabulary/get-course",{params:e}).then((function(e){t.isLoadingChart=!1,console.log("response: ",e),e.data.data.forEach((function(e){console.log("course: ",e);var n=JSON.parse(e.data);console.log("data: ",n);var r=y().groupBy(n,"p");console.log("groupedData: ",r);var a=t.categories.find((function(t){return t.id===e.course_id}));console.log("category: ",a),a&&(a.data.learned=r[1]?r[1].length:0,a.data.temporary=r[2]?r[2].length:0,a.data.memorized=r[3]?r[3].length:0,a.data.mastered=r[4]?r[4].length:0,a.data.super_mastered=r[5]?r[5].length:0),console.log("category: ",a),console.log("categories: ",t.categories),t.chartData=t.chartData}))})).catch((function(e){console.error(e),t.isLoadingChart=!1}))},getOverviewFavorite:function(){var t=this;this.isLoadingFavorite=!0,d().get("/vocabulary/get-overview-favorite").then((function(e){t.isLoadingFavorite=!1,console.log("response: ",e),t.favorite=e.data.data})).catch((function(e){console.error(e),t.isLoadingFavorite=!1}))},scrollToSection:function(t,e){if(console.log("scrollToSection: ",t,e),console.log("this.courseSpecializedActive: ",this.courseSpecializedActive),console.log("this.currentView: ",this.currentView),"vocabulary-card-favorite"===t&&(this.currentView===this.typesView.SPECIALIZED||this.currentView===this.typesView.SEARCH))return this.courseSpecializedActive=e,void this.openViewFavorite();if(e!==this.courseSpecializedActive&&(this.currentView===this.typesView.SPECIALIZED||this.currentView===this.typesView.FAVORITE))return this.courseSpecializedActive=e,void this.openCourseFlashCardDetail(e);var n=document.getElementById(t);n&&(n.scrollIntoView({behavior:"smooth",block:"center"}),n.classList.add("highlight"),setTimeout((function(){n.classList.remove("highlight")}),4e3))},searchWord:function(){var t=this,e=this.searchQuery.trim();""!==e?(this.isLoadingSearch=!0,d().get("/vocabulary/search-word",{params:{searchQuery:e}}).then((function(e){t.isLoadingSearch=!1,t.searchResults=e.data.data.map((function(t){return t.value.word=(0,h.UW)(t.value.word),t}))})).catch((function(e){console.error(e),t.isLoadingSearch=!1}))):this.$message.error("Vui lòng nhập từ vựng để tìm kiếm")},openViewFavorite:function(){var t=this;this.currentView=this.typesView.FAVORITE,this.isLoadingFavorite=!0,d().get("/vocabulary/get-favorite").then((function(e){t.isLoadingFavorite=!1,console.log("response: ",e);var n=e.data.data.flashcards;n.map((function(t){return t.value=JSON.parse(t.value),t.value.word=(0,h.UW)(t.value.word),t})),t.favoriteFlashcards=n,t.allFavoriteFlashcards=n,console.log("favoriteFlashcards: ",t.favoriteFlashcards)})).catch((function(e){console.error(e),t.isLoadingFavorite=!1}))},searchWordFavorite:function(){var t=this;this.isLoadingFavorite=!0,this.favoriteFlashcards=this.allFavoriteFlashcards.filter((function(e){return e.value.word.toLowerCase().includes(t.searchQueryFavorite.toLowerCase())||e.value.meaning.toLowerCase().includes(t.searchQueryFavorite.toLowerCase())})),this.isLoadingFavorite=!1}},mounted:function(){console.log("Component mounted, chart data:",this.chartData)},created:function(){console.log("jlpt",this.jlpt),this.initCategories(),this.getCourse()}},yt=r(_t,(function(){var t=this,e=t._self._c;return e("div",{staticClass:"vocabulary-container"},[e("div",{staticClass:"nav-menu",attrs:{id:"navMenu"}},[e("div",{staticClass:"nav-menu-header"},[e("div",{staticClass:"nav-menu-title cursor-pointer font-beanbag-medium text-[20px] text-[#07403F]",on:{click:function(e){t.currentView=t.typesView.OVERVIEW}}},[t._v("Kho từ vựng\n      ")]),t._v(" "),e("div",{staticClass:"toggle-menu",attrs:{onclick:"toggleMenu()"}},[e("svg",{attrs:{width:"32",height:"31",viewBox:"0 0 32 31",fill:"none",xmlns:"http://www.w3.org/2000/svg"}},[e("path",{attrs:{d:"M12.6667 6V26M8.22222 6H23.7778C25.0051 6 26 6.99492 26 8.22222V23.7778C26 25.0051 25.0051 26 23.7778 26H8.22222C6.99492 26 6 25.0051 6 23.7778V8.22222C6 6.99492 6.99492 6 8.22222 6Z",stroke:"#07403F","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"}})])])]),t._v(" "),e("div",{staticClass:"nav-menu-items"},[t._m(0),t._v(" "),e("div",{staticClass:"nav-submenu",attrs:{id:"sub-menu-jlpt"}},t._l(t.jlpt,(function(n,r){return e("div",{staticClass:"nav-submenu-item font-beanbag-regular text-[18px] text-black",on:{click:function(e){return t.scrollToSection("jlpt-"+r,n.id)}}},[e("span",{staticClass:"text-black"},[t._v("Từ vựng "+t._s(n.name))])])})),0),t._v(" "),t.specialized?e("div",{staticClass:"nav-menu-item",attrs:{onclick:"toggleSubmenu('sub-menu-specialized')"}},[e("span",{staticClass:"nav-menu-item-text font-beanbag-medium text-[20px] text-[#07403F]"},[t._v("Chuyên ngành")]),t._v(" "),e("i",{staticClass:"fas fa-chevron-down ml-auto"})]):t._e(),t._v(" "),t.specialized?e("div",{staticClass:"nav-submenu",attrs:{id:"sub-menu-specialized"}},t._l(t.specialized,(function(n,r){return e("div",{staticClass:"nav-submenu-item font-beanbag-regular text-[18px] text-black"},[e("span",{staticClass:"text-black"},[t._v(t._s(n.name))])])})),0):t._e(),t._v(" "),e("div",{staticClass:"nav-menu-item justify-between",on:{click:function(e){return t.scrollToSection("vocabulary-card-favorite")}}},[e("span",{staticClass:"nav-menu-item-text font-beanbag-medium text-[20px] text-[#07403F]"},[t._v("Yêu thích")]),t._v(" "),e("i",{staticClass:"fas fa-heart text-[#FF7C79]"})])])]),t._v(" "),e("div",{staticClass:"content-area"},[e("div",{staticClass:"content-section active max-w-[1096px] mx-auto mt-[58px]",attrs:{id:"default"}},[t.currentView!==t.typesView.FAVORITE&&t.currentView!==t.typesView.SPECIALIZED?e("div",{staticClass:"vocabulary-search-container relative z-10"},[t.currentView===t.typesView.SEARCH?e("div",{staticClass:"decoration-search absolute top-[-15px] left-1/2 transform -translate-y-1/2 z-1"},[e("img",{attrs:{src:"/images/vocabulary/img-decoration-search-1.svg",alt:"decoration-search"}})]):t._e(),t._v(" "),t.currentView===t.typesView.SEARCH?e("div",{staticClass:"vocabulary-search-btn-back h-[80px] w-[80px]",on:{click:function(e){t.currentView=t.typesView.OVERVIEW,t.isLoadingSearch=!1,t.searchQuery=""}}},[e("svg",{staticClass:"mr-[4px]",attrs:{width:"18",height:"30",viewBox:"0 0 18 30",fill:"none",xmlns:"http://www.w3.org/2000/svg"}},[e("path",{attrs:{d:"M15.25 2.5L2.75 15L15.25 27.5",stroke:"#1E1E1E","stroke-width":"3.57143","stroke-linecap":"round","stroke-linejoin":"round"}})])]):t._e(),t._v(" "),e("div",{staticClass:"vocabulary-search-bar flex items-center rounded-full bg-white shadow-md h-[80px] px-[20px] transition-all duration-300 flex-1 min-w-0 z-10"},[e("div",{staticClass:"search-icon"},[e("svg",{attrs:{width:"37",height:"38",viewBox:"0 0 37 38",fill:"none",xmlns:"http://www.w3.org/2000/svg"}},[e("path",{attrs:{d:"M31.6697 32.5732L25.1095 26.013M28.6535 17.4924C28.6535 24.1555 23.252 29.557 16.5889 29.557C9.92576 29.557 4.52423 24.1555 4.52423 17.4924C4.52423 10.8293 9.92576 5.42773 16.5889 5.42773C23.252 5.42773 28.6535 10.8293 28.6535 17.4924Z",stroke:"#5A5A5A","stroke-width":"2.01077","stroke-linecap":"round","stroke-linejoin":"round"}})])]),t._v(" "),e("input",{directives:[{name:"model",rawName:"v-model",value:t.searchQuery,expression:"searchQuery"}],staticClass:"search-input",attrs:{type:"text",placeholder:"Nhập từ vựng muốn tìm kiếm"},domProps:{value:t.searchQuery},on:{focus:function(e){t.searchFocused=!0},blur:function(e){t.searchFocused=!1},input:function(e){e.target.composing||(t.searchQuery=e.target.value)}}}),t._v(" "),t.searchQuery?e("div",{staticClass:"search-clear cursor-pointer bg-[#757575] px-[10px] py-[5px] rounded-full",on:{click:function(e){t.searchQuery=""}}},[e("i",{staticClass:"fas fa-times text-white"})]):t._e()])]):t._e(),t._v(" "),t.currentView===t.typesView.OVERVIEW?[e("div",{staticClass:"section-content overview-content"},[t.authUser.id?e("div",{directives:[{name:"loading",rawName:"v-loading",value:t.isLoadingChart,expression:"isLoadingChart"}],staticClass:"chart-overview-container flex mb-[66px]"},[e("div",{staticClass:"w-2/3 h-full relative"},[e("div",{staticClass:"text-right absolute top-[25px] right-[25px] z-[1]"},[e("button",{staticClass:"btn-info-chart bg-[#FFB98F] text-white rounded-full w-[32px] h-[32px] flex items-center justify-center",on:{click:function(e){t.showDialogInfo=!0}}},[e("span",{staticClass:"text-[20px] font-beanbag-medium"},[t._v("i")])])]),t._v(" "),e("ChartVocabulary",{attrs:{chartData:t.chartData}})],1),t._v(" "),e("div",{staticClass:"chart-header w-1/3"},[e("div",{staticClass:"flex justify-between items-center border-b-[1px] pb-3",staticStyle:{"border-bottom-style":"dashed","border-bottom-color":"#176867"}},[e("div",{staticClass:"chart-title font-beanbag-medium text-[20px] text-[#176867]"},[t._v("Tổng số từ vựng")]),t._v(" "),e("div",{staticClass:"total-word rounded-full text-[20px] font-beanbag-medium text-[#176867] px-[15px] py-[2px] bg-[#CEFFD8]"},[t._v("\n                  "+t._s(t.chartData.totalWords)+"\n                ")])]),t._v(" "),e("div",{staticClass:"chart-filters pt-3"},[e("div",{staticClass:"filter-checkboxes overflow-y-auto h-[310px]"},t._l(t.categories,(function(n,r){return e("div",{key:r,staticClass:"flex items-center justify-between"},[e("label",{staticClass:"checkbox-container"},[e("input",{directives:[{name:"model",rawName:"v-model",value:n.selected,expression:"category.selected"}],attrs:{type:"checkbox"},domProps:{checked:Array.isArray(n.selected)?t._i(n.selected,null)>-1:n.selected},on:{change:function(e){var r=n.selected,a=e.target,i=!!a.checked;if(Array.isArray(r)){var o=t._i(r,null);a.checked?o<0&&t.$set(n,"selected",r.concat([null])):o>-1&&t.$set(n,"selected",r.slice(0,o).concat(r.slice(o+1)))}else t.$set(n,"selected",i)}}}),t._v(" "),e("span",{staticClass:"checkbox-label font-beanbag-medium font-medium"},[t._v(t._s(n.name))])]),t._v(" "),e("div",{staticClass:"font-beanbag-medium text-[18px] text-[#176867]"},[t._v(t._s(n.data.word_count)+" từ\n                    ")])])})),0)])])]):t._e(),t._v(" "),e("div",{staticClass:"vocabulary-section",attrs:{id:"section-jlpt"}},[t._m(1),t._v(" "),e("div",{staticClass:"vocabulary-cards"},t._l(t.jlpt,(function(n,r){return e("div",{key:r,staticClass:"vocabulary-card min-h-[260px]",class:0===n.word_count?"vocabulary-card-incoming":"",attrs:{id:"jlpt-".concat(r),"data-key":r},on:{click:function(e){return t.openCourseFlashCardDetail(n.id)}}},[e("div",{staticClass:"vocabulary-card-content"},[n.word_count>0?e("div",{staticClass:"vocabulary-card-status"},[e("span",{staticClass:"badge badge-new font-gen-jyuu-gothic-medium text-[12px]"},[t._v("NEW")])]):t._e(),t._v(" "),e("div",{staticClass:"vocabulary-line bg-[#57D061]"}),t._v(" "),e("div",{staticClass:"vocabulary-card-title font-gen-jyuu-gothic-medium text-bold text-[20px]"},[t._v("\n                    Từ vựng "+t._s(n.name)+"\n                  ")]),t._v(" "),e("div",{staticClass:"vocabulary-card-count font-gen-jyuu-gothic text-[20px]"},[t._v("\n                    "+t._s(n.word_count)+" từ vựng\n                  ")])]),t._v(" "),n.word_count>0?e("div",{staticClass:"vocabulary-card-name font-zuume-semibold"},[e("h1",[t._v("\n                    "+t._s(n.name)+"\n                  ")])]):e("div",{staticClass:"vocabulary-card-incoming-text"},[e("p",{staticClass:"text-center text-[23px] font-gen-jyuu-gothic-medium text-[#757575]"},[t._v("Sắp ra mắt")])])])})),0)]),t._v(" "),t.specialized?e("div",{staticClass:"vocabulary-section",attrs:{id:"section-specialized"}},[t._m(2),t._v(" "),e("div",{staticClass:"vocabulary-cards"},t._l(t.specialized.data,(function(n,r){return e("div",{key:r,staticClass:"vocabulary-card",class:"incoming"===n.status?"vocabulary-card-incoming":"",attrs:{"data-key":r}},[e("div",{staticClass:"vocabulary-card-content"},["hot"===n.status?e("div",{staticClass:"vocabulary-card-status"},[e("span",{staticClass:"badge badge-new font-gen-jyuu-gothic-medium text-[12px]"},[t._v("HOT")])]):t._e(),t._v(" "),e("div",{staticClass:"vocabulary-line bg-[#4E87FF]"}),t._v(" "),e("div",{staticClass:"vocabulary-card-title font-gen-jyuu-gothic-medium text-bold text-[20px] leading-[20px]"},[t._v("\n                    "+t._s(n.name)+"\n                  ")]),t._v(" "),e("div",{staticClass:"vocabulary-card-count font-gen-jyuu-gothic text-[20px]"},[t._v("\n                    "+t._s(n.word_count)+" từ vựng\n                  ")])]),t._v(" "),"incoming"!==n.status?e("img",{staticClass:"vocabulary-card-image",attrs:{src:"/images/vocabulary/test.png",alt:n.name}}):e("div",{staticClass:"vocabulary-card-incoming-text"},[e("p",{staticClass:"text-center text-[23px] font-gen-jyuu-gothic-medium text-[#757575]"},[t._v("Sắp ra mắt")])])])})),0)]):t._e(),t._v(" "),e("div",{staticClass:"vocabulary-section",attrs:{id:"section-favorite"}},[t._m(3),t._v(" "),e("div",{directives:[{name:"loading",rawName:"v-loading",value:t.isLoadingFavorite,expression:"isLoadingFavorite"}],staticClass:"vocabulary-card card-favorite",attrs:{id:"vocabulary-card-favorite"},on:{click:function(e){return t.openViewFavorite()}}},[e("div",{staticClass:"vocabulary-card-content-favorite"},[e("div",{staticClass:"absolute h-[50px] bg-[#FF7C79] top-[40px] left-[25px] w-[3px] bg-[#FF7C79] rounded-full"}),t._v(" "),e("div",{staticClass:"w-full flex justify-between"},[e("div",{staticClass:"flex flex-col justify-around"},[e("div",{staticClass:"pt-[23px] pl-[32px] pb-[13px]"},[e("div",{staticClass:"text-[26px] font-gen-jyuu-gothic-medium text-bold"},[t._v("\n                        Từ vựng của tôi\n                      ")]),t._v(" "),e("div",{staticClass:"vocabulary-card-count font-gen-jyuu-gothic text-[20px]"},[t._v("\n                        "+t._s(t.favorite?t.favorite.data.length:0)+" từ vựng\n                      ")])]),t._v(" "),e("div",{staticClass:"pl-[32px] pb-[23px]"},[e("div",{staticClass:"font-gen-jyuu-gothic text-[20px]"},[t._v("Từ thêm gần đây")]),t._v(" "),e("div",{staticClass:"favorite-word-new text-[24px]"},[e("span",{domProps:{innerHTML:t._s(t.favorite?t.favorite.lastFlashcard.value.word:"")}})]),t._v(" "),e("div",{staticClass:"time-add-new-word font-beanbag-regular text-[20px] text-[#757575]"},[t._v("\n                        "+t._s(t.favorite?t.favorite.data[0].convert_time:"")+"\n                      ")])])]),t._v(" "),t._m(4)])])])])])]:t._e(),t._v(" "),t.currentView===t.typesView.SEARCH?[e("div",{directives:[{name:"loading",rawName:"v-loading",value:t.isLoadingSearch,expression:"isLoadingSearch"}],staticClass:"section-content search-content"},[t.searchResults.length>0?e("div",{staticClass:"search-result"},[e("div",{staticClass:"search-result-items"},t._l(t.searchResults,(function(n,r){return e("div",{key:r,staticClass:"search-result-item"},[e("div",{staticClass:"search-result-item-content border-b border-[#D0D3DA] border-bottom-[1px] mb-3 pb-3"},[e("div",{staticClass:"search-result-item-title font-gen-jyuu-gothic-medium text-black text-[20px]"},[e("div",{domProps:{innerHTML:t._s(n.value.word)}})]),t._v(" "),e("div",{staticClass:"search-result-item-description font-gen-jyuu-gothic text-[18px] text-[#757575]"},[t._v("\n                    "+t._s(n.value.meaning)+"\n                  ")]),t._v(" "),e("div",{staticClass:"search-result-item-specialized w-fit font-averta-regular text-[16px] text-[#FFFFFF] bg-[#B3B3B3] rounded-full px-3 py-1"},[t._v("\n                    "+t._s(n.course_name)+"\n                  ")])])])})),0)]):e("div",{staticClass:"search-not-found"},[t._m(5)])])]:t._e(),t._v(" "),t.currentView===t.typesView.SPECIALIZED?[e("div",{staticClass:"section-content specialized-content"},[e("flash-card",{attrs:{"course-id":t.courseSpecializedActive,"category-data":t.categories.find((function(e){return e.id===t.courseSpecializedActive}))}})],1)]:t._e(),t._v(" "),t.currentView===t.typesView.FAVORITE?[e("div",{staticClass:"section-content favorite-content"},[e("div",{staticClass:"flex justify-between items-center mb-10"},[e("div",{staticClass:"flex items-center"},[e("div",{staticClass:"vocabulary-search-btn-back h-[65px] w-[65px]",on:{click:function(e){t.currentView=t.typesView.OVERVIEW,t.isLoadingFavorite=!1,t.searchResults=[],t.searchQuery=""}}},[e("i",{staticClass:"fas fa-arrow-left"})]),t._v(" "),e("div",{staticClass:"vocabulary-section-favorite-title"},[t._m(6),t._v(" "),e("div",{staticClass:"font-beanbag-regular text-[20px] text-[#07403F]"},[t._v(t._s(t.allFavoriteFlashcards.length)+" từ\n                ")])])]),t._v(" "),e("div",{staticClass:"vocabulary-favorite-search-bar flex items-center rounded-full bg-white shadow-md h-[65px] px-[20px] transition-all duration-300 flex-1 min-w-0 max-w-[400px]"},[t._m(7),t._v(" "),e("input",{directives:[{name:"model",rawName:"v-model",value:t.searchQueryFavorite,expression:"searchQueryFavorite"}],staticClass:"search-input leading-[65px]",attrs:{type:"text",placeholder:"Nhập từ vựng muốn tìm kiếm"},domProps:{value:t.searchQueryFavorite},on:{input:function(e){e.target.composing||(t.searchQueryFavorite=e.target.value)}}})])]),t._v(" "),t._m(8),t._v(" "),t.favoriteFlashcards.length>0?e("div",{directives:[{name:"loading",rawName:"v-loading",value:t.isLoadingFavorite,expression:"isLoadingFavorite"}],staticClass:"search-result list-favorite"},[e("div",{staticClass:"search-result-items"},t._l(t.favoriteFlashcards,(function(n,r){return e("div",{key:r,staticClass:"search-result-item cursor-pointer",on:{click:function(e){return t.openFlashcardPopup(r)}}},[e("div",{staticClass:"search-result-item-content border-b border-[#D0D3DA] border-bottom-[1px] mb-3 pb-3"},[e("div",{staticClass:"search-result-item-title font-gen-jyuu-gothic-medium text-black text-[20px]"},[e("span",{domProps:{innerHTML:t._s(n.value.word)}})]),t._v(" "),e("div",{staticClass:"search-result-item-description font-gen-jyuu-gothic text-[18px] text-[#757575]"},[e("span",{domProps:{innerHTML:t._s(n.value.meaning)}})]),t._v(" "),e("div",{staticClass:"search-result-item-specialized w-fit font-averta-regular text-[16px] text-[#FFFFFF] bg-[#B3B3B3] rounded-full px-3 py-1"},[t._v("\n                    "+t._s(n.course_name)+"\n                  ")])])])})),0)]):t._e()])]:t._e()],2)]),t._v(" "),e("el-dialog",{staticClass:"dialog-vocabulary-info",attrs:{visible:t.showDialogInfo,"max-width":"1097px",top:"8vh",width:"80%",height:"80vh",center:"","show-close":!1,"close-on-click-modal":!0},on:{"update:visible":function(e){t.showDialogInfo=e}},scopedSlots:t._u([{key:"title",fn:function(){return[e("div",{staticClass:"custom-title pt-[29px] pl-[43px] pr-[38px] flex justify-between items-end rounded-[32px]"},[e("div",{staticClass:"flex items-center gap-2"},[e("div",{staticClass:"text-[#073A3B] font-beanbag text-[24px] bg-[#CCF8D1] px-5 py-1 rounded-full"},[t._v("\n            Rừng ngôn từ\n          ")]),t._v(" "),e("div",{staticClass:"text-[#0C403F] text-[27px]"},[t._v("\n            言葉の森\n          ")])]),t._v(" "),e("el-button",{attrs:{icon:"el-icon-close",circle:""},on:{click:function(e){t.showDialogInfo=!1}}})],1)]},proxy:!0}])},[t._v(" "),e("div",{staticClass:"dialog-wrapper overflow-y-scroll h-[60vh] pl-[43px] py-[12px] pr-[38px] mx-[43px] my-[12px] custom-scrollbar"},[e("div",{staticClass:"font-beanbag-regular text-[#07403F] text-[20px] mb-3 break-normal"},[t._v("\n        Là nơi thống kê chi tiết số lượng từ vựng nằm ở các "),e("span",{staticClass:"italic font-beanbag-medium break-normal"},[t._v("mức độ ghi nhớ")]),t._v("\n        khác nhau thông qua phương pháp học "),e("span",{staticClass:"italic font-beanbag-medium break-normal"},[t._v("Lặp lại ngắt quãng (Spaced Repetition)")]),t._v(",\n        giúp bạn hoàn toàn làm chủ quá trình học của bản thân!\n      ")]),t._v(" "),e("div",{staticClass:"text-center mb-5"},[e("img",{attrs:{src:"/images/vocabulary/img-popup-info.svg",alt:"Thông tin"}})]),t._v(" "),e("div",{staticClass:"mb-5"},[e("ul",{staticClass:"list-disc font-beanbag-regular text-[#07403F] text-[20px]"},[e("li",{staticClass:"py-[5px]",staticStyle:{"list-style":"disc"}},[e("span",{staticClass:"font-beanbag-medium bg-[#FFD1B0] rounded-[8px]"},[t._v("Đã học")]),t._v(":\n            Những từ bạn mới được học.\n          ")]),t._v(" "),e("li",{staticClass:"py-[5px]",staticStyle:{"list-style":"disc"}},[e("span",{staticClass:"font-beanbag-medium bg-[#FFEF94] rounded-[8px]"},[t._v("Tạm nhớ")]),t._v(":\n            Những từ bạn vẫn nhớ sau 1 thời gian ngắn không sử dụng tới.\n          ")]),t._v(" "),e("li",{staticClass:"py-[5px]",staticStyle:{"list-style":"disc"}},[e("span",{staticClass:"font-beanbag-medium bg-[#D0FCA1] rounded-[8px]"},[t._v("Ghi nhớ")]),t._v(":\n            Từ vựng đã được lưu vào trí nhớ dài hạn của bạn rồi!\n          ")]),t._v(" "),e("li",{staticClass:"py-[5px]",staticStyle:{"list-style":"disc"}},[e("span",{staticClass:"font-beanbag-medium bg-[#80FBA6] rounded-[8px]"},[t._v("Thuộc lòng")]),t._v(":\n            Tuyệt vời! Những từ vựng này giờ là của bạn!\n          ")])])]),t._v(" "),e("div",{staticClass:"font-beanbag-regular text-[#07403F] text-[20px]"},[t._v("\n        Chăm chỉ học tập, bạn sẽ có được một "),e("span",{staticClass:"font-beanbag-medium italic"},[t._v("Rừng ngôn từ")]),t._v(" thật phong\n        phú, đa dạng! Cùng học bạn nhé!\n      ")]),t._v(" "),e("div",{staticClass:"flex justify-center mt-5"},[e("button",{staticClass:"px-12 py-4 flex items-center justify-center bg-[#57D061] rounded-full cursor-pointer drop-shadow",on:{click:function(e){t.showDialogInfo=!1}}},[e("span",{staticClass:"text-btn font-beanbag-medium text-[#07403F] text-xl mr-1 text-[20px]"},[t._v("\n            Mình đã hiểu!\n          ")])])])])]),t._v(" "),t.showFlashcardPopup?e("div",{staticClass:"flashcard-popup-overlay",on:{click:function(e){return e.target!==e.currentTarget?null:t.closeFlashcardPopup.apply(null,arguments)}}},[e("div",{staticClass:"flashcard-popup"},[e("div",{staticClass:"flashcard-popup-content"},[e("button",{staticClass:"flashcard-nav-button prev",attrs:{disabled:0===t.currentFlashcardIndex},on:{click:t.prevFlashcard}},[e("i",{staticClass:"fas fa-chevron-left"})]),t._v(" "),e("div",{staticClass:"flashcards-wrap cursor-pointer",staticStyle:{width:"600px"}},t._l(t.favoriteFlashcards,(function(n,r){return e("div",{directives:[{name:"show",rawName:"v-show",value:r===t.currentFlashcardIndex,expression:"index === currentFlashcardIndex"}],key:n.id,staticClass:"card-item",class:{"stackedcards-active":0===r,"stackedcards-top":!0,"stackedcards--animatable":!0,"stackedcards-origin-top":!0},attrs:{"data-id":n.id}},[e("div",{staticClass:"card-inner",attrs:{id:"card-inner-".concat(n.id)},on:{click:function(e){return t.flipCard(n.id,e)}}},[e("div",{staticClass:"card__face card__face--jp card__face--front",class:{noFlip:n.is_test}},[e("div",{staticClass:"card-wrap p-4 h-[90%] overflow-y-auto relative"},[e("div",{staticClass:"card_header flex items-center"},[e("div",{staticClass:"card_audio noFlip w-[48px] h-[48px] rounded-full bg-[#E1EBFF] flex items-center justify-center mr-4 cursor-pointer",on:{click:function(e){return t.playAudio("audio")}}},[e("svg",{staticClass:"noFlip",attrs:{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"}},[e("path",{attrs:{d:"M18 16.7503C17.84 16.7503 17.69 16.7003 17.55 16.6003C17.22 16.3503 17.15 15.8803 17.4 15.5503C18.97 13.4603 18.97 10.5403 17.4 8.45027C17.15 8.12027 17.22 7.65027 17.55 7.40027C17.88 7.15027 18.35 7.22027 18.6 7.55027C20.56 10.1703 20.56 13.8303 18.6 16.4503C18.45 16.6503 18.23 16.7503 18 16.7503Z",fill:"#4E87FF"}}),t._v(" "),e("path",{attrs:{d:"M19.83 19.2503C19.67 19.2503 19.52 19.2003 19.38 19.1003C19.05 18.8503 18.98 18.3803 19.23 18.0503C21.9 14.4903 21.9 9.51027 19.23 5.95027C18.98 5.62027 19.05 5.15027 19.38 4.90027C19.71 4.65027 20.18 4.72027 20.43 5.05027C23.5 9.14027 23.5 14.8603 20.43 18.9503C20.29 19.1503 20.06 19.2503 19.83 19.2503Z",fill:"#4E87FF"}}),t._v(" "),e("path",{attrs:{opacity:"0.4",d:"M15.75 7.40972V16.5897C15.75 18.3097 15.13 19.5997 14.02 20.2197C13.57 20.4697 13.07 20.5897 12.55 20.5897C11.75 20.5897 10.89 20.3197 10.01 19.7697L7.09 17.9397C6.89 17.8197 6.66 17.7497 6.43 17.7497H5.5V6.24972H6.43C6.66 6.24972 6.89 6.17972 7.09 6.05972L10.01 4.22972C11.47 3.31972 12.9 3.15972 14.02 3.77972C15.13 4.39972 15.75 5.68972 15.75 7.40972Z",fill:"#4E87FF"}}),t._v(" "),e("path",{attrs:{d:"M5.5 6.25V17.75H5C2.58 17.75 1.25 16.42 1.25 14V10C1.25 7.58 2.58 6.25 5 6.25H5.5Z",fill:"#4E87FF"}})])]),t._v(" "),e("div",{staticClass:"card_title font-gen-jyuu-gothic-medium text-2xl text-[#757575]",domProps:{innerHTML:t._s(n.value.word_stress)}})]),t._v(" "),e("div",{staticClass:"card_content min-h-[calc(100%-34px)] flex flex-col"},[e("div",{staticClass:"content-text font-gen-jyuu-gothic-medium text-[56px] text-[#07403F] items-center justify-center flex",staticStyle:{"flex-grow":"1"},domProps:{innerHTML:t._s(n.value.word)}}),t._v(" "),n.value.front_image?e("div",{staticClass:"content-img text-center p-[40px]"},[e("img",{attrs:{src:"https://video-test.dungmori.com/images/".concat(n.value.front_image)}})]):t._e(),t._v(" "),n.value.example.length?e("div",{staticClass:"example-wrap"},[e("p",{staticClass:"w-fit font-beanbag text-base text-[#07403F] bg-[#CEFFD8] rounded-[10px] p-2 mb-2"},[t._v("\n                        Ví dụ\n                      ")]),t._v(" "),e("div",{staticClass:"list-example"},[t._l(n.value.example,(function(r,a){return[e("div",{staticClass:"example-item mb-1"},[r.audio?e("svg",{staticClass:"w-[36px] h-[36px] noFlip cursor-pointer",attrs:{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},on:{click:function(e){return t.playAudio(n.id+"_example_"+a,r.audio)}}},[e("path",{attrs:{d:"M18 16.7503C17.84 16.7503 17.69 16.7003 17.55 16.6003C17.22 16.3503 17.15 15.8803 17.4 15.5503C18.97 13.4603 18.97 10.5403 17.4 8.45027C17.15 8.12027 17.22 7.65027 17.55 7.40027C17.88 7.15027 18.35 7.22027 18.6 7.55027C20.56 10.1703 20.56 13.8303 18.6 16.4503C18.45 16.6503 18.23 16.7503 18 16.7503Z",fill:"#4E87FF"}}),t._v(" "),e("path",{attrs:{d:"M19.83 19.2503C19.67 19.2503 19.52 19.2003 19.38 19.1003C19.05 18.8503 18.98 18.3803 19.23 18.0503C21.9 14.4903 21.9 9.51027 19.23 5.95027C18.98 5.62027 19.05 5.15027 19.38 4.90027C19.71 4.65027 20.18 4.72027 20.43 5.05027C23.5 9.14027 23.5 14.8603 20.43 18.9503C20.29 19.1503 20.06 19.2503 19.83 19.2503Z",fill:"#4E87FF"}}),t._v(" "),e("path",{attrs:{opacity:"0.4",d:"M15.75 7.40972V16.5897C15.75 18.3097 15.13 19.5997 14.02 20.2197C13.57 20.4697 13.07 20.5897 12.55 20.5897C11.75 20.5897 10.89 20.3197 10.01 19.7697L7.09 17.9397C6.89 17.8197 6.66 17.7497 6.43 17.7497H5.5V6.24972H6.43C6.66 6.24972 6.89 6.17972 7.09 6.05972L10.01 4.22972C11.47 3.31972 12.9 3.15972 14.02 3.77972C15.13 4.39972 15.75 5.68972 15.75 7.40972Z",fill:"#4E87FF"}}),t._v(" "),e("path",{attrs:{d:"M5.5 6.25V17.75H5C2.58 17.75 1.25 16.42 1.25 14V10C1.25 7.58 2.58 6.25 5 6.25H5.5Z",fill:"#4E87FF"}})]):t._e(),t._v(" "),e("div",{staticClass:"ml-2 font-beanbag-regular text-2xl flex items-start text-balance"},[t._v("\n                              "+t._s(a+1)+". "),e("span",{staticClass:"ml-2",domProps:{innerHTML:t._s(r.example)}})])])]}))],2)]):t._e()])]),t._v(" "),e("div",{staticClass:"card-footer noFlip flex justify-end border-t-[1px] border-[#D0D3DA]"},[e("svg",{staticClass:"m-3 noFlip",attrs:{width:"33",height:"28",viewBox:"0 0 33 28",fill:"none",xmlns:"http://www.w3.org/2000/svg"}},[e("path",{attrs:{d:"M23.8333 0.5C28.6377 0.5 32.4997 4.21737 32.5 8.78727C32.4819 14.1271 29.8923 18.3826 26.6876 21.5156C23.4787 24.6525 19.6885 26.627 17.3726 27.393C17.163 27.4593 16.8527 27.5 16.5137 27.5C16.1782 27.5 15.8562 27.4601 15.6229 27.3915C13.3062 26.6243 9.50962 24.6502 6.29845 21.5153C3.08901 18.3822 0.5 14.127 0.5 8.78779C0.5 4.21765 4.36212 0.5 9.16667 0.5C12.0183 0.5 14.5145 1.79375 16.1076 3.81099L16.5 4.30783L16.8924 3.81099C18.4855 1.79375 20.9817 0.5 23.8333 0.5Z",fill:"#FF7C79",stroke:"#FF7C79"}})])])]),t._v(" "),e("div",{staticClass:"card__face card__face--vi card__face--back"},[e("div",{staticClass:"card-wrap-back p-4 h-[60%] overflow-y-auto flex flex-col relative"},[e("div",{staticClass:"card_content_back font-beanbag-medium text-5xl items-center justify-center flex text-[#07403F] mb-5",staticStyle:{"flex-grow":"1"}},[e("div",{staticClass:"text-center",domProps:{innerHTML:t._s(n.value.meaning)}})]),t._v(" "),e("div",{staticClass:"font-beanbag-medium text-[#757575] text-xl text-center text-[#07403F]",domProps:{innerHTML:t._s(n.value.kanji_meaning)}}),t._v(" "),n.value.back_image?e("div",{staticClass:"card_img_back p-[40px] content-img text-center"},[e("img",{attrs:{src:"https://video-test.dungmori.com/images/".concat(n.value.back_image)}})]):t._e(),t._v(" "),n.value.meaning_example.length?e("div",{staticClass:"example-wrap"},[e("p",{staticClass:"w-fit font-beanbag text-base text-[#07403F] bg-[#CEFFD8] rounded-[10px] p-2 mb-2"},[t._v("\n                      Ví dụ\n                    ")]),t._v(" "),e("div",{staticClass:"list-example"},[t._l(n.value.meaning_example,(function(n,r){return[e("div",{staticClass:"example-item flex items-center mb-1"},[e("div",{staticClass:"ml-2 font-averta-regular text-2xl flex items-start"},[t._v("\n                            "+t._s(r+1)+". "),e("span",{staticClass:"ml-1",domProps:{innerHTML:t._s(n)}})])])]}))],2)]):t._e()]),t._v(" "),e("div",{staticClass:"how-remember-wrap px-4 h-[40%] flex flex-col"},[t._m(9,!0),t._v(" "),n.comment&&n.comment.user_info?e("div",{staticClass:"how-remember-wrap-content grid grid-cols-[40px_auto] gap-4"},[e("div",{staticClass:"how-remember-wrap-avatar w-[28px]"},[e("img",{staticClass:"rounded-full",attrs:{src:"/cdn/avatar/small/".concat(n.comment.user_info.avatar)}})]),t._v(" "),e("div",{staticClass:"how-remember-wrap-info flex text-[#073A3B]"},[e("span",{staticClass:"font-averta-bold"},[t._v("\n                          "+t._s(n.comment.user_info.name)+"・\n                        ")]),t._v(" "),e("span",{staticClass:"font-averta-regular"},[t._v("\n                          "+t._s(n.comment.time_created)+"\n                        ")]),t._v(" "),n.comment.pin?e("svg",{attrs:{width:"16",height:"16",viewBox:"0 0 16 16",fill:"none",xmlns:"http://www.w3.org/2000/svg"}},[e("path",{attrs:{d:"M4.54059 9.86604C4.49626 9.83523 4.45013 9.81231 4.41581 9.77792C3.83372 9.198 3.25271 8.617 2.67312 8.03457C2.59482 7.95577 2.51759 7.87338 2.45323 7.78312C2.23084 7.47256 2.23799 7.15555 2.48076 6.86004C2.75322 6.52871 3.11005 6.30626 3.49405 6.13218C4.1902 5.81697 4.90958 5.70163 5.65114 5.95953C5.851 6.02902 6.03657 6.14078 6.21856 6.22854C6.924 5.52396 7.63981 4.80935 8.36242 4.08758C8.13788 3.75446 8.06279 3.37477 8.17649 2.96821C8.44966 1.99177 9.64601 1.67476 10.3836 2.38543C10.7887 2.77586 11.1827 3.17776 11.5803 3.57608C12.2554 4.25271 12.9347 4.92469 13.6019 5.60885C14.4236 6.45133 13.8447 7.6316 13.0213 7.84616C12.6641 7.93929 12.3312 7.88628 12.0212 7.69106C11.9354 7.63697 11.8721 7.63733 11.7981 7.71148C11.1216 8.39277 10.4426 9.07191 9.76472 9.75177C9.75864 9.75786 9.75542 9.76682 9.74648 9.78186C9.75363 9.79691 9.76078 9.81732 9.77223 9.83523C10.3089 10.6745 10.3089 11.5489 9.90023 12.4272C9.74755 12.7553 9.5248 13.0526 9.31814 13.3527C9.24735 13.4555 9.13579 13.5365 9.0296 13.6074C8.81114 13.7532 8.57373 13.7629 8.34561 13.635C8.22297 13.5662 8.10677 13.4767 8.00701 13.3775C7.36128 12.7352 6.71877 12.089 6.08019 11.4392C5.99331 11.3508 5.94289 11.3429 5.84278 11.4246C4.85559 12.2341 3.86411 13.0382 2.87299 13.8431C2.60555 14.0605 2.32702 14.0509 2.12286 13.822C1.99378 13.6773 1.96304 13.515 2.04277 13.3384C2.07745 13.2618 2.12286 13.1887 2.17256 13.1206C2.9209 12.0915 3.67104 11.0635 4.42046 10.0351C4.45407 9.9889 4.4866 9.94198 4.53988 9.8664L4.54059 9.86604Z",fill:"#B3B3B3"}})]):t._e()]),t._v(" "),e("div",{staticClass:"col-start-2 font-averta-regular text-[#07403F]",staticStyle:{display:"-webkit-box","-webkit-line-clamp":"2","-webkit-box-orient":"vertical",overflow:"hidden","text-overflow":"ellipsis"}},[t._v("\n                      "+t._s(n.comment.content)+"\n                    ")]),t._v(" "),e("div",{staticClass:"col-start-2 flex justify-between items-center"},[e("div",{staticClass:"font-averta-regular text-[#009951] flex"},[e("div",{staticClass:"flex items-center mr-5"},[e("svg",{staticClass:"mr-1 noFlip",attrs:{width:"13",height:"12",viewBox:"0 0 13 12",fill:"none",xmlns:"http://www.w3.org/2000/svg"}},[e("path",{attrs:{d:"M9.38889 0.723633C11.1046 0.723633 12.4996 2.1159 12.5 3.84852C12.493 5.95749 11.518 7.64456 10.2933 8.89844C9.06415 10.1569 7.61344 10.9461 6.74434 11.2477C6.70298 11.2611 6.61724 11.2756 6.50542 11.2756C6.39725 11.2756 6.30516 11.262 6.25054 11.2459C5.38046 10.9429 3.92943 10.1541 2.70106 8.89818C1.47455 7.64419 0.5 5.95749 0.5 3.8492C0.5 2.11626 1.89522 0.723633 3.61111 0.723633C4.6312 0.723633 5.52625 1.20724 6.10079 1.96912L6.5 2.49851L6.89921 1.96912C7.47375 1.20724 8.3688 0.723633 9.38889 0.723633Z",fill:n.comment.comment_like.length?"#009951":"none",stroke:"#009951"}})]),t._v("\n                          "+t._s(n.comment.count_like)+"\n                        ")]),t._v(" "),n.comment&&n.comment.replies?e("div",[t._v("\n                          "+t._s(n.comment.replies.length)+" Trả lời\n                        ")]):t._e()]),t._v(" "),e("div",{staticClass:"underline decoration-solid text-[#009951] cursor-pointer noFlip",on:{click:function(e){return t.toggleCommentTab("open")}}},[t._v("\n                        Xem thêm >>\n                      ")])])]):e("div",{staticClass:"items-center flex grow",staticStyle:{"flex-grow":"1"}},[e("div",{staticClass:"underline decoration-solid font-averta-regular text-[#009951] cursor-pointer noFlip",on:{click:function(e){return t.toggleCommentTab("open")}}},[t._v("\n                      Đóng góp cách nhớ của bạn >>\n                    ")])])])])])])})),0),t._v(" "),e("button",{staticClass:"flashcard-nav-button next",attrs:{disabled:t.currentFlashcardIndex===t.favoriteFlashcards.length-1},on:{click:t.nextFlashcard}},[e("i",{staticClass:"fas fa-chevron-right"})])])])]):t._e()],1)}),[function(){var t=this,e=t._self._c;return e("div",{staticClass:"nav-menu-item",attrs:{onclick:"toggleSubmenu('sub-menu-jlpt')"}},[e("span",{staticClass:"nav-menu-item-text font-beanbag-medium text-[20px] text-[#07403F]"},[t._v("JLPT")]),t._v(" "),e("i",{staticClass:"fas fa-chevron-down ml-auto"})])},function(){var t=this,e=t._self._c;return e("div",{staticClass:"vocabulary-section-title font-beanbag-medium text-[#07403F] text-[32px]"},[t._v("\n              JLPT\n              "),e("span",{staticClass:"badge badge-vip font-beanbag-medium text-[18px]"},[t._v("VIP")])])},function(){var t=this,e=t._self._c;return e("div",{staticClass:"vocabulary-section-title"},[t._v("\n              Chuyên ngành\n              "),e("span",{staticClass:"badge badge-free"},[t._v("Free")])])},function(){var t=this._self._c;return t("div",{staticClass:"vocabulary-section-title font-beanbag-medium text-[#07403F] text-[32px]"},[this._v("\n              Từ vựng yêu thích\n              "),t("i",{staticClass:"fas fa-heart text-[#FF7C79] ml-2"})])},function(){var t=this._self._c;return t("div",[t("img",{staticClass:"h-full object-cover",attrs:{src:"/images/vocabulary/bg-favorite-overview.svg",alt:""}})])},function(){var t=this._self._c;return t("div",{staticClass:"search-not-found-image"},[t("img",{attrs:{src:"/images/vocabulary/bg-search-not-found.svg",alt:"Không tìm thấy kết quả"}})])},function(){var t=this._self._c;return t("div",{staticClass:"flex font-beanbag-medium text-[24px] text-[#07403F] items-center"},[this._v("\n                  Từ vựng yêu thích\n                  "),t("i",{staticClass:"fas fa-heart text-[#FF7C79] ml-2"})])},function(){var t=this._self._c;return t("div",{staticClass:"search-icon"},[t("i",{staticClass:"fas fa-search"})])},function(){var t=this,e=t._self._c;return e("div",{staticClass:"flex items-center mb-10"},[e("div",{staticClass:"bg-[#CCF8D1] py-2 px-3 rounded-full flex font-beanbag-medium text-[20px] text-[#07403F] items-center mr-5"},[e("i",{staticClass:"fas fa-list mr-2"}),t._v("\n              Danh sách\n            ")]),t._v(" "),e("div",{staticClass:"font-beanbag-regular text-[20px] text-[#B3B3B3] flex items-center"},[e("img",{attrs:{src:"/images/icons/tag-flashcard.svg",alt:"tag"}}),t._v("\n              Thẻ\n            ")])])},function(){var t=this,e=t._self._c;return e("div",{staticClass:"how-remember-wrap-header flex mb-5"},[e("div",{staticClass:"font-beanbag-medium text-[#757575] text-xl"},[t._v("\n                      Cách nhớ\n                    ")]),t._v(" "),e("div",{staticClass:"border-b-[1px] border-[#D0D3DA] flex-1 m-[7px]"})])}],!1,null,null,null),xt=yt.exports},42537:function(t){"use strict";function e(){this.message="String contains an invalid character"}e.prototype=new Error,e.prototype.code=5,e.prototype.name="InvalidCharacterError",t.exports=function(t){for(var n,r,a=String(t),i="",o=0,s="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";a.charAt(0|o)||(s="=",o%1);i+=s.charAt(63&n>>8-o%1*8)){if((r=a.charCodeAt(o+=3/4))>255)throw new e;n=n<<8|r}return i}},47763:function(t,e,n){"use strict";var r=n(5449);t.exports=function(t,e,n,a,i){var o=new Error(t);return r(o,e,n,a,i)}},62012:function(t,e,n){"use strict";var r=n(9516);t.exports=function(t){var e,n,a,i={};return t?(r.forEach(t.split("\n"),(function(t){a=t.indexOf(":"),e=r.trim(t.substr(0,a)).toLowerCase(),n=r.trim(t.substr(a+1)),e&&(i[e]=i[e]?i[e]+", "+n:n)})),i):i}},64202:function(t,e,n){"use strict";var r=n(9516);t.exports=r.isStandardBrowserEnv()?function(){var t,e=/(msie|trident)/i.test(navigator.userAgent),n=document.createElement("a");function a(t){var r=t;return e&&(n.setAttribute("href",r),r=n.href),n.setAttribute("href",r),{href:n.href,protocol:n.protocol?n.protocol.replace(/:$/,""):"",host:n.host,search:n.search?n.search.replace(/^\?/,""):"",hash:n.hash?n.hash.replace(/^#/,""):"",hostname:n.hostname,port:n.port,pathname:"/"===n.pathname.charAt(0)?n.pathname:"/"+n.pathname}}return t=a(window.location.href),function(e){var n=r.isString(e)?a(e):e;return n.protocol===t.protocol&&n.host===t.host}}():function(){return!0}},64490:function(t,e,n){"use strict";var r=n(9516),a=n(82881),i=n(93864),o=n(96987);function s(t){t.cancelToken&&t.cancelToken.throwIfRequested()}t.exports=function(t){return s(t),t.headers=t.headers||{},t.data=a(t.data,t.headers,t.transformRequest),t.headers=r.merge(t.headers.common||{},t.headers[t.method]||{},t.headers||{}),r.forEach(["delete","get","head","post","put","patch","common"],(function(e){delete t.headers[e]})),(t.adapter||o.adapter)(t).then((function(e){return s(t),e.data=a(e.data,e.headers,t.transformResponse),e}),(function(e){return i(e)||(s(t),e&&e.response&&(e.response.data=a(e.response.data,e.response.headers,t.transformResponse))),Promise.reject(e)}))}},65606:function(t){var e,n,r=t.exports={};function a(){throw new Error("setTimeout has not been defined")}function i(){throw new Error("clearTimeout has not been defined")}function o(t){if(e===setTimeout)return setTimeout(t,0);if((e===a||!e)&&setTimeout)return e=setTimeout,setTimeout(t,0);try{return e(t,0)}catch(n){try{return e.call(null,t,0)}catch(n){return e.call(this,t,0)}}}!function(){try{e="function"==typeof setTimeout?setTimeout:a}catch(t){e=a}try{n="function"==typeof clearTimeout?clearTimeout:i}catch(t){n=i}}();var s,c=[],u=!1,l=-1;function f(){u&&s&&(u=!1,s.length?c=s.concat(c):l=-1,c.length&&d())}function d(){if(!u){var t=o(f);u=!0;for(var e=c.length;e;){for(s=c,c=[];++l<e;)s&&s[l].run();l=-1,e=c.length}s=null,u=!1,function(t){if(n===clearTimeout)return clearTimeout(t);if((n===i||!n)&&clearTimeout)return n=clearTimeout,clearTimeout(t);try{return n(t)}catch(e){try{return n.call(null,t)}catch(e){return n.call(this,t)}}}(t)}}function h(t,e){this.fun=t,this.array=e}function p(){}r.nextTick=function(t){var e=new Array(arguments.length-1);if(arguments.length>1)for(var n=1;n<arguments.length;n++)e[n-1]=arguments[n];c.push(new h(t,e)),1!==c.length||u||o(d)},h.prototype.run=function(){this.fun.apply(null,this.array)},r.title="browser",r.browser=!0,r.env={},r.argv=[],r.version="",r.versions={},r.on=p,r.addListener=p,r.once=p,r.off=p,r.removeListener=p,r.removeAllListeners=p,r.emit=p,r.prependListener=p,r.prependOnceListener=p,r.listeners=function(t){return[]},r.binding=function(t){throw new Error("process.binding is not supported")},r.cwd=function(){return"/"},r.chdir=function(t){throw new Error("process.chdir is not supported")},r.umask=function(){return 0}},69012:function(t){"use strict";t.exports=function(t,e){return function(){for(var n=new Array(arguments.length),r=0;r<n.length;r++)n[r]=arguments[r];return t.apply(e,n)}}},72505:function(t,e,n){t.exports=n(18015)},75903:function(t,e,n){"use strict";var r=n(76798),a=n.n(r)()((function(t){return t[1]}));a.push([t.id,'.chart-container[data-v-3fc31493]{background-color:#fff;border-radius:32px;-webkit-box-shadow:0 4px 12px rgba(0,0,0,.1);box-shadow:0 4px 12px rgba(0,0,0,.1);height:100%;padding:30px;position:relative}.chart-html[data-v-3fc31493]{-webkit-box-orient:vertical;-webkit-box-direction:normal;-webkit-box-align:center;-ms-flex-align:center;-webkit-box-pack:center;-ms-flex-pack:center;-webkit-align-items:center;align-items:center;background-color:transparent;background-position:top;background-size:auto 150px;-webkit-flex-direction:column;-ms-flex-direction:column;flex-direction:column;-webkit-justify-content:center;justify-content:center;padding-bottom:50px;position:relative}.chart-columns[data-v-3fc31493],.chart-html[data-v-3fc31493]{background-repeat:no-repeat;display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;height:100%;width:100%}.chart-columns[data-v-3fc31493]{-ms-flex-pack:distribute;-webkit-box-align:end;-ms-flex-align:end;-webkit-align-items:flex-end;align-items:flex-end;background-image:url(/images/vocabulary/bg-chart-overview.svg);background-position:bottom;background-size:contain;border-bottom:2px solid #176867;-webkit-justify-content:space-around;justify-content:space-around;padding-left:45px;padding-right:45px}.chart-column[data-v-3fc31493]{-webkit-box-orient:vertical;-webkit-box-direction:normal;-webkit-box-align:center;-ms-flex-align:center;-webkit-align-items:center;align-items:center;display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-flex-direction:column;-ms-flex-direction:column;flex-direction:column;max-width:75px;position:relative;-webkit-transition:all .5s cubic-bezier(.34,1.56,.64,1);transition:all .5s cubic-bezier(.34,1.56,.64,1);width:20%}.column-value[data-v-3fc31493]{fill:hsla(0,0%,100%,.6);-webkit-backdrop-filter:blur(3.6785595417022705px);backdrop-filter:blur(3.6785595417022705px);background:#fff;border-radius:8px;color:#1f7274;-webkit-filter:drop-shadow(0 5.518px 5.518px rgba(97,124,154,.15));filter:drop-shadow(0 5.518px 5.518px rgba(97,124,154,.15));font-size:18px;font-weight:700;margin-bottom:5px;position:absolute;text-align:center;top:-40px;width:100%}.column-value[data-v-3fc31493]:after{border:5px solid transparent;border-top-color:#fff;content:"";left:50%;margin-left:-5px;position:absolute;top:100%}.column-bar[data-v-3fc31493]{border-radius:8px 8px 0 0;-webkit-box-shadow:0 0 10px rgba(0,0,0,.1);box-shadow:0 0 10px rgba(0,0,0,.1);height:100%;overflow:hidden;position:relative;-webkit-transform-origin:bottom center;-ms-transform-origin:bottom center;transform-origin:bottom center;-webkit-transition:all .6s cubic-bezier(.34,1.56,.64,1);transition:all .6s cubic-bezier(.34,1.56,.64,1);width:100%}.learned-bar[data-v-3fc31493]{background-color:rgba(255,106,0,.31)}.temporary-bar[data-v-3fc31493]{background-color:rgba(255,217,0,.42)}.memorized-bar[data-v-3fc31493]{background-color:rgba(128,255,0,.37)}.mastered-bar[data-v-3fc31493]{background:url(/images/vocabulary/bg-mastered-bar-column-chart-overview.svg),-webkit-linear-gradient(58deg,rgba(0,255,115,.5) 32.16%,rgba(0,255,84,.5) 50.05%,rgba(0,255,22,.5) 71.77%,rgba(255,255,0,.5) 96.05%);background:url(/images/vocabulary/bg-mastered-bar-column-chart-overview.svg),linear-gradient(32deg,rgba(0,255,115,.5) 32.16%,rgba(0,255,84,.5) 50.05%,rgba(0,255,22,.5) 71.77%,rgba(255,255,0,.5) 96.05%);background-position:top;background-repeat:no-repeat}.column-label[data-v-3fc31493]{bottom:-35px;color:#07403f;font-family:Beanbag_Dungmori_Rounded_Medium;font-size:22px;position:absolute;text-align:center;width:-webkit-max-content;width:-moz-max-content;width:max-content}',""]),e.A=a},76798:function(t){"use strict";t.exports=function(t){var e=[];return e.toString=function(){return this.map((function(e){var n=t(e);return e[2]?"@media ".concat(e[2]," {").concat(n,"}"):n})).join("")},e.i=function(t,n,r){"string"==typeof t&&(t=[[null,t,""]]);var a={};if(r)for(var i=0;i<this.length;i++){var o=this[i][0];null!=o&&(a[o]=!0)}for(var s=0;s<t.length;s++){var c=[].concat(t[s]);r&&a[c[0]]||(n&&(c[2]?c[2]="".concat(n," and ").concat(c[2]):c[2]=n),e.push(c))}},e}},79106:function(t,e,n){"use strict";var r=n(9516);function a(t){return encodeURIComponent(t).replace(/%40/gi,"@").replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}t.exports=function(t,e,n){if(!e)return t;var i;if(n)i=n(e);else if(r.isURLSearchParams(e))i=e.toString();else{var o=[];r.forEach(e,(function(t,e){null!=t&&(r.isArray(t)&&(e+="[]"),r.isArray(t)||(t=[t]),r.forEach(t,(function(t){r.isDate(t)?t=t.toISOString():r.isObject(t)&&(t=JSON.stringify(t)),o.push(a(e)+"="+a(t))})))})),i=o.join("&")}return i&&(t+=(-1===t.indexOf("?")?"?":"&")+i),t}},82881:function(t,e,n){"use strict";var r=n(9516);t.exports=function(t,e,n){return r.forEach(n,(function(n){t=n(t,e)})),t}},83471:function(t,e,n){"use strict";var r=n(9516);function a(){this.handlers=[]}a.prototype.use=function(t,e){return this.handlers.push({fulfilled:t,rejected:e}),this.handlers.length-1},a.prototype.eject=function(t){this.handlers[t]&&(this.handlers[t]=null)},a.prototype.forEach=function(t){r.forEach(this.handlers,(function(e){null!==e&&t(e)}))},t.exports=a},84680:function(t){"use strict";t.exports=function(t,e){return e?t.replace(/\/+$/,"")+"/"+e.replace(/^\/+/,""):t}},85072:function(t,e,n){"use strict";var r,a=function(){return void 0===r&&(r=Boolean(window&&document&&document.all&&!window.atob)),r},i=function(){var t={};return function(e){if(void 0===t[e]){var n=document.querySelector(e);if(window.HTMLIFrameElement&&n instanceof window.HTMLIFrameElement)try{n=n.contentDocument.head}catch(t){n=null}t[e]=n}return t[e]}}(),o=[];function s(t){for(var e=-1,n=0;n<o.length;n++)if(o[n].identifier===t){e=n;break}return e}function c(t,e){for(var n={},r=[],a=0;a<t.length;a++){var i=t[a],c=e.base?i[0]+e.base:i[0],u=n[c]||0,l="".concat(c," ").concat(u);n[c]=u+1;var f=s(l),d={css:i[1],media:i[2],sourceMap:i[3]};-1!==f?(o[f].references++,o[f].updater(d)):o.push({identifier:l,updater:m(d,e),references:1}),r.push(l)}return r}function u(t){var e=document.createElement("style"),r=t.attributes||{};if(void 0===r.nonce){var a=n.nc;a&&(r.nonce=a)}if(Object.keys(r).forEach((function(t){e.setAttribute(t,r[t])})),"function"==typeof t.insert)t.insert(e);else{var o=i(t.insert||"head");if(!o)throw new Error("Couldn't find a style target. This probably means that the value for the 'insert' parameter is invalid.");o.appendChild(e)}return e}var l,f=(l=[],function(t,e){return l[t]=e,l.filter(Boolean).join("\n")});function d(t,e,n,r){var a=n?"":r.media?"@media ".concat(r.media," {").concat(r.css,"}"):r.css;if(t.styleSheet)t.styleSheet.cssText=f(e,a);else{var i=document.createTextNode(a),o=t.childNodes;o[e]&&t.removeChild(o[e]),o.length?t.insertBefore(i,o[e]):t.appendChild(i)}}function h(t,e,n){var r=n.css,a=n.media,i=n.sourceMap;if(a?t.setAttribute("media",a):t.removeAttribute("media"),i&&"undefined"!=typeof btoa&&(r+="\n/*# sourceMappingURL=data:application/json;base64,".concat(btoa(unescape(encodeURIComponent(JSON.stringify(i))))," */")),t.styleSheet)t.styleSheet.cssText=r;else{for(;t.firstChild;)t.removeChild(t.firstChild);t.appendChild(document.createTextNode(r))}}var p=null,v=0;function m(t,e){var n,r,a;if(e.singleton){var i=v++;n=p||(p=u(e)),r=d.bind(null,n,i,!1),a=d.bind(null,n,i,!0)}else n=u(e),r=h.bind(null,n,e),a=function(){!function(t){if(null===t.parentNode)return!1;t.parentNode.removeChild(t)}(n)};return r(t),function(e){if(e){if(e.css===t.css&&e.media===t.media&&e.sourceMap===t.sourceMap)return;r(t=e)}else a()}}t.exports=function(t,e){(e=e||{}).singleton||"boolean"==typeof e.singleton||(e.singleton=a());var n=c(t=t||[],e);return function(t){if(t=t||[],"[object Array]"===Object.prototype.toString.call(t)){for(var r=0;r<n.length;r++){var a=s(n[r]);o[a].references--}for(var i=c(t,e),u=0;u<n.length;u++){var l=s(n[u]);0===o[l].references&&(o[l].updater(),o.splice(l,1))}n=i}}}},87206:function(t){function e(t){return!!t.constructor&&"function"==typeof t.constructor.isBuffer&&t.constructor.isBuffer(t)}t.exports=function(t){return null!=t&&(e(t)||function(t){return"function"==typeof t.readFloatLE&&"function"==typeof t.slice&&e(t.slice(0,0))}(t)||!!t._isBuffer)}},93799:function(t,e,n){"use strict";var r=n(76798),a=n.n(r)()((function(t){return t[1]}));a.push([t.id,".progress-bar[data-v-2b96e190]{-webkit-box-flex:1;-webkit-flex:1;-ms-flex:1;flex:1;margin:0 15px;overflow:hidden;position:relative}.progress-bar[data-v-2b96e190],.progress-bar-inner[data-v-2b96e190]{border-radius:9999px;-webkit-box-shadow:none;box-shadow:none}.progress-bar-inner[data-v-2b96e190]{height:100%;left:0;position:absolute;top:0;-webkit-transition:width .6s ease;transition:width .6s ease}",""]),e.A=a},93864:function(t){"use strict";t.exports=function(t){return!(!t||!t.__CANCEL__)}},96987:function(t,e,n){"use strict";var r=n(65606),a=n(9516),i=n(7018),o={"Content-Type":"application/x-www-form-urlencoded"};function s(t,e){!a.isUndefined(t)&&a.isUndefined(t["Content-Type"])&&(t["Content-Type"]=e)}var c,u={adapter:(("undefined"!=typeof XMLHttpRequest||void 0!==r)&&(c=n(35592)),c),transformRequest:[function(t,e){return i(e,"Content-Type"),a.isFormData(t)||a.isArrayBuffer(t)||a.isBuffer(t)||a.isStream(t)||a.isFile(t)||a.isBlob(t)?t:a.isArrayBufferView(t)?t.buffer:a.isURLSearchParams(t)?(s(e,"application/x-www-form-urlencoded;charset=utf-8"),t.toString()):a.isObject(t)?(s(e,"application/json;charset=utf-8"),JSON.stringify(t)):t}],transformResponse:[function(t){if("string"==typeof t)try{t=JSON.parse(t)}catch(t){}return t}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,validateStatus:function(t){return t>=200&&t<300}};u.headers={common:{Accept:"application/json, text/plain, */*"}},a.forEach(["delete","get","head"],(function(t){u.headers[t]={}})),a.forEach(["post","put","patch"],(function(t){u.headers[t]=a.merge(o)})),t.exports=u}},e={};function n(r){var a=e[r];if(void 0!==a)return a.exports;var i=e[r]={id:r,loaded:!1,exports:{}};return t[r].call(i.exports,i,i.exports,n),i.loaded=!0,i.exports}n.n=function(t){var e=t&&t.__esModule?function(){return t.default}:function(){return t};return n.d(e,{a:e}),e},n.d=function(t,e){for(var r in e)n.o(e,r)&&!n.o(t,r)&&Object.defineProperty(t,r,{enumerable:!0,get:e[r]})},n.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(t){if("object"==typeof window)return window}}(),n.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},n.nmd=function(t){return t.paths=[],t.children||(t.children=[]),t},n.nc=void 0,new Vue({el:"#vocabulary_content",data:{jlpt:jlpt,specialized:specialized,favorite:favorite},components:{vocabulary:n(38361).A},methods:{showContent:function(t){$(".content-section").removeClass("active"),$("#content-"+t).addClass("active"),$("#navMenu").removeClass("active")}}})}();