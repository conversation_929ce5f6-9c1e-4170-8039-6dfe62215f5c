!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?module.exports=e():"function"==typeof define&&define.amd?define(e):t.videojs=e()}(this,function(){function t(t,e){return e={exports:{}},t(e,e.exports),e.exports}function e(t){return t.replace(/\n\r?\s*/g,"")}function n(t,e){Ne(t).forEach(function(n){return e(t[n],n)})}function r(t,e){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0;return Ne(t).reduce(function(n,r){return e(n,t[r],r)},n)}function i(t){for(var e=arguments.length,r=Array(e>1?e-1:0),i=1;i<e;i++)r[i-1]=arguments[i];return Object.assign?Object.assign.apply(Object,[t].concat(r)):(r.forEach(function(e){e&&n(e,function(e,n){t[n]=e})}),t)}function o(t){return!!t&&"object"===("undefined"==typeof t?"undefined":xe(t))}function s(t){return o(t)&&"[object Object]"===Oe.call(t)&&t.constructor===Object}function a(t,e){if(!t||!e)return"";if("function"==typeof ve.getComputedStyle){var n=ve.getComputedStyle(t);return n?n[e]:""}return""}function l(t){return"string"==typeof t&&/\S/.test(t)}function c(t){if(/\s/.test(t))throw new Error("class has illegal whitespace characters")}function u(t){return new RegExp("(^|\\s)"+t+"($|\\s)")}function h(){return be===ve.document}function p(t){return o(t)&&1===t.nodeType}function d(){try{return ve.parent!==ve.self}catch(t){return!0}}function f(t){return function(e,n){if(!l(e))return be[t](null);l(n)&&(n=be.querySelector(n));var r=p(n)?n:be;return r[t]&&r[t](e)}}function v(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"div",e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r=arguments[3],i=be.createElement(t);return Object.getOwnPropertyNames(e).forEach(function(t){var n=e[t];t.indexOf("aria-")!==-1||"role"===t||"type"===t?(Ee.warn(Se(Ie,t,n)),i.setAttribute(t,n)):"textContent"===t?y(i,n):i[t]=n}),Object.getOwnPropertyNames(n).forEach(function(t){i.setAttribute(t,n[t])}),r&&L(i,r),i}function y(t,e){return"undefined"==typeof t.textContent?t.innerText=e:t.textContent=e,t}function g(t,e){e.firstChild?e.insertBefore(t,e.firstChild):e.appendChild(t)}function m(t,e){return c(e),t.classList?t.classList.contains(e):u(e).test(t.className)}function _(t,e){return t.classList?t.classList.add(e):m(t,e)||(t.className=(t.className+" "+e).trim()),t}function b(t,e){return t.classList?t.classList.remove(e):(c(e),t.className=t.className.split(/\s+/).filter(function(t){return t!==e}).join(" ")),t}function T(t,e,n){var r=m(t,e);if("function"==typeof n&&(n=n(t,e)),"boolean"!=typeof n&&(n=!r),n!==r)return n?_(t,e):b(t,e),t}function C(t,e){Object.getOwnPropertyNames(e).forEach(function(n){var r=e[n];null===r||"undefined"==typeof r||r===!1?t.removeAttribute(n):t.setAttribute(n,r===!0?"":r)})}function k(t){var e={},n=",autoplay,controls,playsinline,loop,muted,default,defaultMuted,";if(t&&t.attributes&&t.attributes.length>0)for(var r=t.attributes,i=r.length-1;i>=0;i--){var o=r[i].name,s=r[i].value;"boolean"!=typeof t[o]&&n.indexOf(","+o+",")===-1||(s=null!==s),e[o]=s}return e}function w(t,e){return t.getAttribute(e)}function E(t,e,n){t.setAttribute(e,n)}function S(t,e){t.removeAttribute(e)}function x(){be.body.focus(),be.onselectstart=function(){return!1}}function j(){be.onselectstart=function(){return!0}}function A(t){if(t&&t.getBoundingClientRect&&t.parentNode){var e=t.getBoundingClientRect(),n={};return["bottom","height","left","right","top","width"].forEach(function(t){void 0!==e[t]&&(n[t]=e[t])}),n.height||(n.height=parseFloat(a(t,"height"))),n.width||(n.width=parseFloat(a(t,"width"))),n}}function P(t){var e=void 0;if(t.getBoundingClientRect&&t.parentNode&&(e=t.getBoundingClientRect()),!e)return{left:0,top:0};var n=be.documentElement,r=be.body,i=n.clientLeft||r.clientLeft||0,o=ve.pageXOffset||r.scrollLeft,s=e.left+o-i,a=n.clientTop||r.clientTop||0,l=ve.pageYOffset||r.scrollTop,c=e.top+l-a;return{left:Math.round(s),top:Math.round(c)}}function M(t,e){var n={},r=P(t),i=t.offsetWidth,o=t.offsetHeight,s=r.top,a=r.left,l=e.pageY,c=e.pageX;return e.changedTouches&&(c=e.changedTouches[0].pageX,l=e.changedTouches[0].pageY),n.y=Math.max(0,Math.min(1,(s-l+o)/o)),n.x=Math.max(0,Math.min(1,(c-a)/i)),n}function O(t){return o(t)&&3===t.nodeType}function N(t){for(;t.firstChild;)t.removeChild(t.firstChild);return t}function I(t){return"function"==typeof t&&(t=t()),(Array.isArray(t)?t:[t]).map(function(t){return"function"==typeof t&&(t=t()),p(t)||O(t)?t:"string"==typeof t&&/\S/.test(t)?be.createTextNode(t):void 0}).filter(function(t){return t})}function L(t,e){return I(e).forEach(function(e){return t.appendChild(e)}),t}function D(t,e){return L(N(t),e)}function R(t){return void 0===t.button&&void 0===t.buttons||(0===t.button&&void 0===t.buttons||0===t.button&&1===t.buttons)}function F(){return Fe++}function B(t){var e=t[He];return e||(e=t[He]=F()),Be[e]||(Be[e]={}),Be[e]}function H(t){var e=t[He];return!!e&&!!Object.getOwnPropertyNames(Be[e]).length}function V(t){var e=t[He];if(e){delete Be[e];try{delete t[He]}catch(n){t.removeAttribute?t.removeAttribute(He):t[He]=null}}}function z(t,e){var n=B(t);0===n.handlers[e].length&&(delete n.handlers[e],t.removeEventListener?t.removeEventListener(e,n.dispatcher,!1):t.detachEvent&&t.detachEvent("on"+e,n.dispatcher)),Object.getOwnPropertyNames(n.handlers).length<=0&&(delete n.handlers,delete n.dispatcher,delete n.disabled),0===Object.getOwnPropertyNames(n).length&&V(t)}function W(t,e,n,r){n.forEach(function(n){t(e,n,r)})}function U(t){function e(){return!0}function n(){return!1}if(!t||!t.isPropagationStopped){var r=t||ve.event;t={};for(var i in r)"layerX"!==i&&"layerY"!==i&&"keyLocation"!==i&&"webkitMovementX"!==i&&"webkitMovementY"!==i&&("returnValue"===i&&r.preventDefault||(t[i]=r[i]));if(t.target||(t.target=t.srcElement||be),t.relatedTarget||(t.relatedTarget=t.fromElement===t.target?t.toElement:t.fromElement),t.preventDefault=function(){r.preventDefault&&r.preventDefault(),t.returnValue=!1,r.returnValue=!1,t.defaultPrevented=!0},t.defaultPrevented=!1,t.stopPropagation=function(){r.stopPropagation&&r.stopPropagation(),t.cancelBubble=!0,r.cancelBubble=!0,t.isPropagationStopped=e},t.isPropagationStopped=n,t.stopImmediatePropagation=function(){r.stopImmediatePropagation&&r.stopImmediatePropagation(),t.isImmediatePropagationStopped=e,t.stopPropagation()},t.isImmediatePropagationStopped=n,null!==t.clientX&&void 0!==t.clientX){var o=be.documentElement,s=be.body;t.pageX=t.clientX+(o&&o.scrollLeft||s&&s.scrollLeft||0)-(o&&o.clientLeft||s&&s.clientLeft||0),t.pageY=t.clientY+(o&&o.scrollTop||s&&s.scrollTop||0)-(o&&o.clientTop||s&&s.clientTop||0)}t.which=t.charCode||t.keyCode,null!==t.button&&void 0!==t.button&&(t.button=1&t.button?0:4&t.button?1:2&t.button?2:0)}return t}function X(t,e,n){if(Array.isArray(e))return W(X,t,e,n);var r=B(t);if(r.handlers||(r.handlers={}),r.handlers[e]||(r.handlers[e]=[]),n.guid||(n.guid=F()),r.handlers[e].push(n),r.dispatcher||(r.disabled=!1,r.dispatcher=function(e,n){if(!r.disabled){e=U(e);var i=r.handlers[e.type];if(i)for(var o=i.slice(0),s=0,a=o.length;s<a&&!e.isImmediatePropagationStopped();s++)try{o[s].call(t,e,n)}catch(l){Ee.error(l)}}}),1===r.handlers[e].length)if(t.addEventListener){var i=!1;Ve&&ze.indexOf(e)>-1&&(i={passive:!0}),t.addEventListener(e,r.dispatcher,i)}else t.attachEvent&&t.attachEvent("on"+e,r.dispatcher)}function q(t,e,n){if(H(t)){var r=B(t);if(r.handlers){if(Array.isArray(e))return W(q,t,e,n);var i=function(t,e){r.handlers[e]=[],z(t,e)};if(void 0!==e){var o=r.handlers[e];if(o){if(!n)return void i(t,e);if(n.guid)for(var s=0;s<o.length;s++)o[s].guid===n.guid&&o.splice(s--,1);z(t,e)}}else for(var a in r.handlers)Object.prototype.hasOwnProperty.call(r.handlers||{},a)&&i(t,a)}}}function K(t,e,n){var r=H(t)?B(t):{},i=t.parentNode||t.ownerDocument;if("string"==typeof e?e={type:e,target:t}:e.target||(e.target=t),e=U(e),r.dispatcher&&r.dispatcher.call(t,e,n),i&&!e.isPropagationStopped()&&e.bubbles===!0)K.call(null,i,e,n);else if(!i&&!e.defaultPrevented){var o=B(e.target);e.target[e.type]&&(o.disabled=!0,"function"==typeof e.target[e.type]&&e.target[e.type](),o.disabled=!1)}return!e.defaultPrevented}function $(t,e,n){if(Array.isArray(e))return W($,t,e,n);var r=function i(){q(t,e,i),n.apply(this,arguments)};r.guid=n.guid=n.guid||F(),X(t,e,r)}function Y(t,e){e&&(Xe=e),ve.setTimeout(qe,t)}function G(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=e.eventBusKey;if(n){if(!t[n].nodeName)throw new Error('The eventBusKey "'+n+'" does not refer to an element.');t.eventBusEl_=t[n]}else t.eventBusEl_=v("span",{className:"vjs-event-bus"});return i(t,ln),t.on("dispose",function(){t.off(),ve.setTimeout(function(){t.eventBusEl_=null},0)}),t}function J(t,e){return i(t,cn),t.state=i({},t.state,e),"function"==typeof t.handleStateChanged&&tn(t)&&t.on("statechanged",t.handleStateChanged),t}function Q(t){return"string"!=typeof t?t:t.charAt(0).toUpperCase()+t.slice(1)}function Z(t,e){return Q(t)===Q(e)}function tt(){for(var t={},e=arguments.length,r=Array(e),i=0;i<e;i++)r[i]=arguments[i];return r.forEach(function(e){e&&n(e,function(e,n){return s(e)?(s(t[n])||(t[n]={}),void(t[n]=tt(t[n],e))):void(t[n]=e)})}),t}function et(t,e,n){if("number"!=typeof e||e<0||e>n)throw new Error("Failed to execute '"+t+"' on 'TimeRanges': The index provided ("+e+") is non-numeric or out of bounds (0-"+n+").")}function nt(t,e,n,r){return et(t,r,n.length-1),n[r][e]}function rt(t){return void 0===t||0===t.length?{length:0,start:function(){throw new Error("This TimeRanges object is empty")},end:function(){throw new Error("This TimeRanges object is empty")}}:{length:t.length,start:nt.bind(null,"start",0,t),end:nt.bind(null,"end",1,t)}}function it(t,e){return Array.isArray(t)?rt(t):void 0===t||void 0===e?rt():rt([[t,e]])}function ot(t,e){var n=0,r=void 0,i=void 0;if(!e)return 0;t&&t.length||(t=it(0,0));for(var o=0;o<t.length;o++)r=t.start(o),i=t.end(o),i>e&&(i=e),n+=i-r;return n/e}function st(t){return t instanceof st?t:("number"==typeof t?this.code=t:"string"==typeof t?this.message=t:o(t)&&("number"==typeof t.code&&(this.code=t.code),i(this,t)),void(this.message||(this.message=st.defaultMessages[this.code]||"")))}function at(t,e){var n,r=null;try{n=JSON.parse(t,e)}catch(i){r=i}return[r,n]}function lt(t){return void 0!==t&&null!==t&&"function"==typeof t.then}function ct(t){lt(t)&&t.then(null,function(t){})}function ut(t){var e=pr.call(t);return"[object Function]"===e||"function"==typeof t&&"[object RegExp]"!==e||"undefined"!=typeof window&&(t===window.setTimeout||t===window.alert||t===window.confirm||t===window.prompt)}function ht(t,e,n){if(!hr(e))throw new TypeError("iterator must be a function");arguments.length<3&&(n=this),"[object Array]"===vr.call(t)?pt(t,e,n):"string"==typeof t?dt(t,e,n):ft(t,e,n)}function pt(t,e,n){for(var r=0,i=t.length;r<i;r++)yr.call(t,r)&&e.call(n,t[r],r,t)}function dt(t,e,n){for(var r=0,i=t.length;r<i;r++)e.call(n,t.charAt(r),r,t)}function ft(t,e,n){for(var r in t)yr.call(t,r)&&e.call(n,t[r],r,t)}function vt(){for(var t={},e=0;e<arguments.length;e++){var n=arguments[e];for(var r in n)br.call(n,r)&&(t[r]=n[r])}return t}function yt(t,e){for(var n=0;n<t.length;n++)e(t[n])}function gt(t){for(var e in t)if(t.hasOwnProperty(e))return!1;return!0}function mt(t,e,n){var r=t;return hr(e)?(n=e,"string"==typeof t&&(r={uri:t})):r=_r(e,{uri:t}),r.callback=n,r}function _t(t,e,n){return e=mt(t,e,n),bt(e)}function bt(t){function e(){4===a.readyState&&setTimeout(i,0)}function n(){var t=void 0;if(t=a.response?a.response:a.responseText||Tt(a),y)try{t=JSON.parse(t)}catch(e){}return t}function r(t){return clearTimeout(u),t instanceof Error||(t=new Error(""+(t||"Unknown XMLHttpRequest Error"))),t.statusCode=0,s(t,g)}function i(){if(!c){var e;clearTimeout(u),e=t.useXDR&&void 0===a.status?200:1223===a.status?204:a.status;var r=g,i=null;return 0!==e?(r={body:n(),statusCode:e,method:p,headers:{},url:h,rawRequest:a},a.getAllResponseHeaders&&(r.headers=mr(a.getAllResponseHeaders()))):i=new Error("Internal XMLHttpRequest Error"),s(i,r,r.body)}}if("undefined"==typeof t.callback)throw new Error("callback argument missing");var o=!1,s=function(e,n,r){o||(o=!0,t.callback(e,n,r))},a=t.xhr||null;a||(a=t.cors||t.useXDR?new _t.XDomainRequest:new _t.XMLHttpRequest);var l,c,u,h=a.url=t.uri||t.url,p=a.method=t.method||"GET",d=t.body||t.data,f=a.headers=t.headers||{},v=!!t.sync,y=!1,g={body:void 0,headers:{},statusCode:0,method:p,url:h,rawRequest:a};if("json"in t&&t.json!==!1&&(y=!0,f.accept||f.Accept||(f.Accept="application/json"),"GET"!==p&&"HEAD"!==p&&(f["content-type"]||f["Content-Type"]||(f["Content-Type"]="application/json"),d=JSON.stringify(t.json===!0?d:t.json))),a.onreadystatechange=e,a.onload=i,a.onerror=r,a.onprogress=function(){},a.onabort=function(){c=!0},a.ontimeout=r,a.open(p,h,!v,t.username,t.password),v||(a.withCredentials=!!t.withCredentials),!v&&t.timeout>0&&(u=setTimeout(function(){if(!c){c=!0,a.abort("timeout");var t=new Error("XMLHttpRequest timeout");t.code="ETIMEDOUT",r(t)}},t.timeout)),a.setRequestHeader)for(l in f)f.hasOwnProperty(l)&&a.setRequestHeader(l,f[l]);else if(t.headers&&!gt(t.headers))throw new Error("Headers cannot be set on an XDomainRequest object");return"responseType"in t&&(a.responseType=t.responseType),"beforeSend"in t&&"function"==typeof t.beforeSend&&t.beforeSend(a),a.send(d||null),a}function Tt(t){if("document"===t.responseType)return t.responseXML;var e=t.responseXML&&"parsererror"===t.responseXML.documentElement.nodeName;return""!==t.responseType||e?null:t.responseXML}function Ct(){}function kt(t,e){this.name="ParsingError",this.code=t.code,this.message=e||t.message}function wt(t){function e(t,e,n,r){return 3600*(0|t)+60*(0|e)+(0|n)+(0|r)/1e3}var n=t.match(/^(\d+):(\d{2})(:\d{2})?\.(\d{3})/);return n?n[3]?e(n[1],n[2],n[3].replace(":",""),n[4]):n[1]>59?e(n[1],n[2],0,n[4]):e(0,n[1],n[2],n[4]):null}function Et(){this.values=Lr(null)}function St(t,e,n,r){var i=r?t.split(r):[t];for(var o in i)if("string"==typeof i[o]){var s=i[o].split(n);if(2===s.length){var a=s[0],l=s[1];e(a,l)}}}function xt(t,e,n){function r(){var e=wt(t);if(null===e)throw new kt(kt.Errors.BadTimeStamp,"Malformed timestamp: "+s);return t=t.replace(/^[^\sa-zA-Z-]+/,""),e}function i(t,e){var r=new Et;St(t,function(t,e){switch(t){case"region":for(var i=n.length-1;i>=0;i--)if(n[i].id===e){r.set(t,n[i].region);break}break;case"vertical":r.alt(t,e,["rl","lr"]);break;case"line":var o=e.split(","),s=o[0];r.integer(t,s),r.percent(t,s)?r.set("snapToLines",!1):null,r.alt(t,s,["auto"]),2===o.length&&r.alt("lineAlign",o[1],["start","middle","end"]);break;case"position":o=e.split(","),r.percent(t,o[0]),2===o.length&&r.alt("positionAlign",o[1],["start","middle","end"]);break;case"size":r.percent(t,e);break;case"align":r.alt(t,e,["start","middle","end","left","right"])}},/:/,/\s/),e.region=r.get("region",null),e.vertical=r.get("vertical",""),e.line=r.get("line","auto"),e.lineAlign=r.get("lineAlign","start"),e.snapToLines=r.get("snapToLines",!0),e.size=r.get("size",100),e.align=r.get("align","middle"),e.position=r.get("position",{start:0,left:0,middle:50,end:100,right:100},e.align),e.positionAlign=r.get("positionAlign",{start:"start",left:"start",middle:"middle",end:"end",right:"end"},e.align)}function o(){t=t.replace(/^\s+/,"")}var s=t;if(o(),e.startTime=r(),o(),"-->"!==t.substr(0,3))throw new kt(kt.Errors.BadTimeStamp,"Malformed time stamp (time stamps must be separated by '-->'): "+s);t=t.substr(3),o(),e.endTime=r(),o(),i(t,e)}function jt(t,e){function n(){function t(t){return e=e.substr(t.length),t}if(!e)return null;var n=e.match(/^([^<]*)(<[^>]*>?)?/);return t(n[1]?n[1]:n[2])}function r(t){return Dr[t]}function i(t){for(;d=t.match(/&(amp|lt|gt|lrm|rlm|nbsp);/);)t=t.replace(d[0],r);return t}function o(t,e){return!Br[e.localName]||Br[e.localName]===t.localName}function s(e,n){var r=Rr[e];if(!r)return null;var i=t.document.createElement(r);i.localName=r;var o=Fr[e];return o&&n&&(i[o]=n.trim()),i}for(var a,l=t.document.createElement("div"),c=l,u=[];null!==(a=n());)if("<"!==a[0])c.appendChild(t.document.createTextNode(i(a)));else{if("/"===a[1]){u.length&&u[u.length-1]===a.substr(2).replace(">","")&&(u.pop(),c=c.parentNode);continue}var h,p=wt(a.substr(1,a.length-2));if(p){h=t.document.createProcessingInstruction("timestamp",p),c.appendChild(h);continue}var d=a.match(/^<([^.\s/0-9>]+)(\.[^\s\\>]+)?([^>\\]+)?(\\?)>?$/);if(!d)continue;if(h=s(d[1],d[3]),!h)continue;if(!o(c,h))continue;d[2]&&(h.className=d[2].substr(1).replace("."," ")),u.push(d[1]),c.appendChild(h),c=h}return l}function At(t){for(var e=0;e<Hr.length;e++){var n=Hr[e];if(t>=n[0]&&t<=n[1])return!0}return!1}function Pt(t){function e(t,e){for(var n=e.childNodes.length-1;n>=0;n--)t.push(e.childNodes[n])}function n(t){if(!t||!t.length)return null;var r=t.pop(),i=r.textContent||r.innerText;if(i){var o=i.match(/^.*(\n|\r)/);return o?(t.length=0,o[0]):i}return"ruby"===r.tagName?n(t):r.childNodes?(e(t,r),n(t)):void 0}var r,i=[],o="";if(!t||!t.childNodes)return"ltr";for(e(i,t);o=n(i);)for(var s=0;s<o.length;s++)if(r=o.charCodeAt(s),At(r))return"rtl";return"ltr"}function Mt(t){if("number"==typeof t.line&&(t.snapToLines||t.line>=0&&t.line<=100))return t.line;if(!t.track||!t.track.textTrackList||!t.track.textTrackList.mediaElement)return-1;for(var e=t.track,n=e.textTrackList,r=0,i=0;i<n.length&&n[i]!==e;i++)"showing"===n[i].mode&&r++;return++r*-1}function Ot(){}function Nt(t,e,n){Ot.call(this),this.cue=e,this.cueDiv=jt(t,e.text);var r={color:"rgba(255, 255, 255, 1)",backgroundColor:"rgba(0, 0, 0, 0.8)",position:"relative",left:0,right:0,top:0,bottom:0,display:"inline",writingMode:""===e.vertical?"horizontal-tb":"lr"===e.vertical?"vertical-lr":"vertical-rl",unicodeBidi:"plaintext"};this.applyStyles(r,this.cueDiv),this.div=t.document.createElement("div"),r={direction:Pt(this.cueDiv),writingMode:""===e.vertical?"horizontal-tb":"lr"===e.vertical?"vertical-lr":"vertical-rl",unicodeBidi:"plaintext",textAlign:"middle"===e.align?"center":e.align,font:n.font,whiteSpace:"pre-line",position:"absolute"},this.applyStyles(r),this.div.appendChild(this.cueDiv);var i=0;switch(e.positionAlign){case"start":i=e.position;break;case"middle":i=e.position-e.size/2;break;case"end":i=e.position-e.size}""===e.vertical?this.applyStyles({left:this.formatStyle(i,"%"),width:this.formatStyle(e.size,"%")}):this.applyStyles({top:this.formatStyle(i,"%"),height:this.formatStyle(e.size,"%")}),this.move=function(t){this.applyStyles({top:this.formatStyle(t.top,"px"),bottom:this.formatStyle(t.bottom,"px"),left:this.formatStyle(t.left,"px"),right:this.formatStyle(t.right,"px"),height:this.formatStyle(t.height,"px"),width:this.formatStyle(t.width,"px")})}}function It(t){var e,n,r,i;if(t.div){n=t.div.offsetHeight,r=t.div.offsetWidth,i=t.div.offsetTop;var o=(o=t.div.childNodes)&&(o=o[0])&&o.getClientRects&&o.getClientRects();t=t.div.getBoundingClientRect(),e=o?Math.max(o[0]&&o[0].height||0,t.height/o.length):0}this.left=t.left,this.right=t.right,this.top=t.top||i,this.height=t.height||n,this.bottom=t.bottom||i+(t.height||n),this.width=t.width||r,this.lineHeight=void 0!==e?e:t.lineHeight}function Lt(t,e,n,r){function i(t,e){for(var i,o=new It(t),s=1,a=0;a<e.length;a++){for(;t.overlapsOppositeAxis(n,e[a])||t.within(n)&&t.overlapsAny(r);)t.move(e[a]);if(t.within(n))return t;var l=t.intersectPercentage(n);s>l&&(i=new It(t),s=l),t=new It(o)}return i||o}var o=new It(e),s=e.cue,a=Mt(s),l=[];if(s.snapToLines){var c;switch(s.vertical){case"":l=["+y","-y"],c="height";break;case"rl":l=["+x","-x"],c="width";break;case"lr":l=["-x","+x"],c="width"}var u=o.lineHeight,h=u*Math.round(a),p=n[c]+u,d=l[0];Math.abs(h)>p&&(h=h<0?-1:1,h*=Math.ceil(p/u)*u),a<0&&(h+=""===s.vertical?n.height:n.width,l=l.reverse()),o.move(d,h)}else{var f=o.lineHeight/n.height*100;switch(s.lineAlign){case"middle":a-=f/2;break;case"end":a-=f}switch(s.vertical){case"":e.applyStyles({top:e.formatStyle(a,"%")});break;case"rl":e.applyStyles({left:e.formatStyle(a,"%")});break;case"lr":e.applyStyles({right:e.formatStyle(a,"%")})}l=["+y","-x","+x","-y"],o=new It(e)}var v=i(o,l);e.move(v.toCSSCompatValues(n))}function Dt(){}function Rt(t){if("string"!=typeof t)return!1;var e=qr[t.toLowerCase()];return!!e&&t.toLowerCase()}function Ft(t){if("string"!=typeof t)return!1;var e=Kr[t.toLowerCase()];return!!e&&t.toLowerCase()}function Bt(t,e,n){this.hasBeenReset=!1;var r="",i=!1,o=t,s=e,a=n,l=null,c="",u=!0,h="auto",p="start",d=50,f="middle",v=50,y="middle";Object.defineProperties(this,{id:{enumerable:!0,get:function(){return r},set:function(t){r=""+t}},pauseOnExit:{enumerable:!0,get:function(){return i},set:function(t){i=!!t}},startTime:{enumerable:!0,get:function(){return o},set:function(t){if("number"!=typeof t)throw new TypeError("Start time must be set to a number.");o=t,this.hasBeenReset=!0}},endTime:{enumerable:!0,get:function(){return s},set:function(t){if("number"!=typeof t)throw new TypeError("End time must be set to a number.");s=t,this.hasBeenReset=!0}},text:{enumerable:!0,get:function(){return a},set:function(t){a=""+t,this.hasBeenReset=!0}},region:{enumerable:!0,get:function(){return l},set:function(t){l=t,this.hasBeenReset=!0}},vertical:{enumerable:!0,get:function(){return c},set:function(t){var e=Rt(t);if(e===!1)throw new SyntaxError("An invalid or illegal string was specified.");c=e,this.hasBeenReset=!0}},snapToLines:{enumerable:!0,get:function(){return u},set:function(t){u=!!t,this.hasBeenReset=!0}},line:{enumerable:!0,get:function(){return h},set:function(t){if("number"!=typeof t&&t!==Xr)throw new SyntaxError("An invalid number or illegal string was specified.");h=t,this.hasBeenReset=!0}},lineAlign:{enumerable:!0,get:function(){return p},set:function(t){var e=Ft(t);if(!e)throw new SyntaxError("An invalid or illegal string was specified.");p=e,this.hasBeenReset=!0}},position:{enumerable:!0,get:function(){return d},set:function(t){if(t<0||t>100)throw new Error("Position must be between 0 and 100.");d=t,this.hasBeenReset=!0}},positionAlign:{enumerable:!0,get:function(){return f},set:function(t){var e=Ft(t);if(!e)throw new SyntaxError("An invalid or illegal string was specified.");f=e,this.hasBeenReset=!0}},size:{enumerable:!0,get:function(){return v},set:function(t){if(t<0||t>100)throw new Error("Size must be between 0 and 100.");v=t,this.hasBeenReset=!0}},align:{enumerable:!0,get:function(){return y},set:function(t){var e=Ft(t);if(!e)throw new SyntaxError("An invalid or illegal string was specified.");y=e,this.hasBeenReset=!0}}}),this.displayState=void 0}function Ht(t){if("string"!=typeof t)return!1;var e=Yr[t.toLowerCase()];return!!e&&t.toLowerCase()}function Vt(t){return"number"==typeof t&&t>=0&&t<=100}function zt(){var t=100,e=3,n=0,r=100,i=0,o=100,s="";Object.defineProperties(this,{width:{enumerable:!0,get:function(){return t},set:function(e){if(!Vt(e))throw new Error("Width must be between 0 and 100.");t=e}},lines:{enumerable:!0,get:function(){return e},set:function(t){if("number"!=typeof t)throw new TypeError("Lines must be set to a number.");e=t}},regionAnchorY:{enumerable:!0,get:function(){return r},set:function(t){if(!Vt(t))throw new Error("RegionAnchorX must be between 0 and 100.");r=t}},regionAnchorX:{enumerable:!0,get:function(){return n},set:function(t){if(!Vt(t))throw new Error("RegionAnchorY must be between 0 and 100.");n=t}},viewportAnchorY:{enumerable:!0,get:function(){return o},set:function(t){if(!Vt(t))throw new Error("ViewportAnchorY must be between 0 and 100.");o=t}},viewportAnchorX:{enumerable:!0,get:function(){return i},set:function(t){if(!Vt(t))throw new Error("ViewportAnchorX must be between 0 and 100.");i=t}},scroll:{enumerable:!0,get:function(){return s},set:function(t){var e=Ht(t);if(e===!1)throw new SyntaxError("An invalid or illegal string was specified.");s=e}}})}function Wt(t,e,n,r){var i=arguments.length>4&&void 0!==arguments[4]?arguments[4]:{},o=t.textTracks();i.kind=e,n&&(i.label=n),r&&(i.language=r),i.tech=t;var s=new Ir.text.TrackClass(i);return o.addTrack(s),s}function Ut(t,e){Zr[t]=Zr[t]||[],Zr[t].push(e)}function Xt(t,e,n){t.setTimeout(function(){return te(e,Zr[e.type],n,t)},1)}function qt(t,e){t.forEach(function(t){return t.setTech&&t.setTech(e)})}function Kt(t,e,n){return t.reduceRight(Gt(n),e[n]())}function $t(t,e,n,r){return e[n](t.reduce(Gt(n),r))}function Yt(t,e,n){var r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:null,i="call"+Q(n),o=t.reduce(Gt(i),r),s=o===ei,a=s?null:e[n](o);return Jt(t,n,a,s),a}function Gt(t){return function(e,n){return e===ei?ei:n[t]?n[t](e):e}}function Jt(t,e,n,r){for(var i=t.length-1;i>=0;i--){var o=t[i];o[e]&&o[e](r,n)}}function Qt(t){ti[t.id()]=null}function Zt(t,e){var n=ti[t.id()],r=null;if(void 0===n||null===n)return r=e(t),ti[t.id()]=[[e,r]],r;for(var i=0;i<n.length;i++){var o=n[i],s=o[0],a=o[1];s===e&&(r=a)}return null===r&&(r=e(t),n.push([e,r])),r}function te(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],n=arguments[2],r=arguments[3],o=arguments.length>4&&void 0!==arguments[4]?arguments[4]:[],s=arguments.length>5&&void 0!==arguments[5]&&arguments[5],a=e[0],l=e.slice(1);if("string"==typeof a)te(t,Zr[a],n,r,o,s);else if(a){var c=Zt(r,a);if(!c.setSource)return o.push(c),te(t,l,n,r,o,s);c.setSource(i({},t),function(e,i){return e?te(t,l,n,r,o,s):(o.push(c),void te(i,t.type===i.type?l:Zr[i.type],n,r,o,s))})}else l.length?te(t,l,n,r,o,s):s?n(t,o):te(t,Zr["*"],n,r,o,!0)}function ee(t){var e=si(t.src);return!t.type&&e&&(t.type=e),t}function ne(t,e){var n=void 0;if(4===t.length)n=t[1]+t[1]+t[2]+t[2]+t[3]+t[3];else{if(7!==t.length)throw new Error("Invalid color code provided, "+t+"; must be formatted as e.g. #f0e or #f604e2.");n=t.slice(1)}return"rgba("+parseInt(n.slice(0,2),16)+","+parseInt(n.slice(2,4),16)+","+parseInt(n.slice(4,6),16)+","+e+")"}function re(t,e,n){try{t.style[e]=n}catch(r){return}}function ie(t){Ci=t}function oe(){Ci=Ti}function se(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:t;return Ci(t,e)}function ae(t,e){if(e&&(t=e(t)),t&&"none"!==t)return t}function le(t,e){var n=t.options[t.options.selectedIndex].value;return ae(n,e)}function ce(t,e,n){if(e)for(var r=0;r<t.options.length;r++)if(ae(t.options[r].value,n)===e){t.selectedIndex=r;break}}function ue(t,e,n){var r=ue.getPlayer(t);if(r)return e&&Ee.warn('Player "'+t+'" is already initialised. Options will not be applied.'),n&&r.ready(n),r;var i="string"==typeof t?Le("#"+as(t)):t;if(!p(i))throw new TypeError("The element or ID supplied is not valid. (videojs)");be.body.contains(i)||Ee.warn("The element supplied is not included in the DOM"),e=e||{},ue.hooks("beforesetup").forEach(function(t){var n=t(i,tt(e));return!o(n)||Array.isArray(n)?void Ee.error("please return an object in beforesetup hooks"):void(e=tt(e,n))});var s=un.getComponent("Player");return r=new s(i,e,n),ue.hooks("setup").forEach(function(t){return t(r)}),r}var he,pe="7.2.0",de="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};he="undefined"!=typeof window?window:"undefined"!=typeof de?de:"undefined"!=typeof self?self:{};var fe,ve=he,ye={},ge=Object.freeze({"default":ye}),me=ge&&ye||ge,_e="undefined"!=typeof de?de:"undefined"!=typeof window?window:{};"undefined"!=typeof document?fe=document:(fe=_e["__GLOBAL_DOCUMENT_CACHE@4"],fe||(fe=_e["__GLOBAL_DOCUMENT_CACHE@4"]=me));var be=fe,Te=void 0,Ce="info",ke=[],we=function(t,e){var n=Te.levels[Ce],r=new RegExp("^("+n+")$");if("log"!==t&&e.unshift(t.toUpperCase()+":"),ke&&ke.push([].concat(e)),e.unshift("VIDEOJS:"),ve.console){var i=ve.console[t];i||"debug"!==t||(i=ve.console.info||ve.console.log),i&&n&&r.test(t)&&i[Array.isArray(e)?"apply":"call"](ve.console,e)}};Te=function(){for(var t=arguments.length,e=Array(t),n=0;n<t;n++)e[n]=arguments[n];we("log",e)},Te.levels={all:"debug|log|warn|error",off:"",debug:"debug|log|warn|error",info:"log|warn|error",warn:"warn|error",error:"error",DEFAULT:Ce},Te.level=function(t){if("string"==typeof t){if(!Te.levels.hasOwnProperty(t))throw new Error('"'+t+'" in not a valid log level');Ce=t}return Ce},Te.history=function(){return ke?[].concat(ke):[]},Te.history.clear=function(){ke&&(ke.length=0)},Te.history.disable=function(){null!==ke&&(ke.length=0,ke=null)},Te.history.enable=function(){null===ke&&(ke=[])},Te.error=function(){for(var t=arguments.length,e=Array(t),n=0;n<t;n++)e[n]=arguments[n];return we("error",e)},Te.warn=function(){for(var t=arguments.length,e=Array(t),n=0;n<t;n++)e[n]=arguments[n];return we("warn",e)},Te.debug=function(){for(var t=arguments.length,e=Array(t),n=0;n<t;n++)e[n]=arguments[n];return we("debug",e)};var Ee=Te,Se=function(t){for(var n="",r=0;r<arguments.length;r++)n+=e(t[r])+(arguments[r+1]||"");return n},xe="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},je=function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")},Ae=function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)},Pe=function(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!=typeof e&&"function"!=typeof e?t:e},Me=function(t,e){return t.raw=e,t},Oe=Object.prototype.toString,Ne=function(t){return o(t)?Object.keys(t):[]},Ie=Me(["Setting attributes in the second argument of createEl()\n                has been deprecated. Use the third argument instead.\n                createEl(type, properties, attributes). Attempting to set "," to ","."],["Setting attributes in the second argument of createEl()\n                has been deprecated. Use the third argument instead.\n                createEl(type, properties, attributes). Attempting to set "," to ","."]),Le=f("querySelector"),De=f("querySelectorAll"),Re=Object.freeze({isReal:h,isEl:p,isInFrame:d,createEl:v,textContent:y,prependTo:g,hasClass:m,addClass:_,removeClass:b,toggleClass:T,setAttributes:C,getAttributes:k,getAttribute:w,setAttribute:E,removeAttribute:S,blockTextSelection:x,unblockTextSelection:j,getBoundingClientRect:A,findPosition:P,getPointerPosition:M,isTextNode:O,emptyEl:N,normalizeContent:I,appendContent:L,insertContent:D,isSingleLeftClick:R,$:Le,$$:De}),Fe=1,Be={},He="vdata"+(new Date).getTime(),Ve=!1;!function(){try{var t=Object.defineProperty({},"passive",{get:function(){Ve=!0}});ve.addEventListener("test",null,t),ve.removeEventListener("test",null,t)}catch(e){}}();var ze=["touchstart","touchmove"],We=Object.freeze({fixEvent:U,on:X,off:q,trigger:K,one:$}),Ue=!1,Xe=void 0,qe=function(){if(h()&&Xe.options.autoSetup!==!1){var t=Array.prototype.slice.call(be.getElementsByTagName("video")),e=Array.prototype.slice.call(be.getElementsByTagName("audio")),n=Array.prototype.slice.call(be.getElementsByTagName("video-js")),r=t.concat(e,n);if(r&&r.length>0)for(var i=0,o=r.length;i<o;i++){var s=r[i];if(!s||!s.getAttribute){Y(1);break}if(void 0===s.player){var a=s.getAttribute("data-setup");null!==a&&Xe(s)}}else Ue||Y(1)}};h()&&"complete"===be.readyState?Ue=!0:$(ve,"load",function(){Ue=!0});var Ke=function(t){var e=be.createElement("style");return e.className=t,e},$e=function(t,e){t.styleSheet?t.styleSheet.cssText=e:t.textContent=e},Ye=function(t,e,n){e.guid||(e.guid=F());var r=function(){return e.apply(t,arguments)};return r.guid=n?n+"_"+e.guid:e.guid,r},Ge=function(t,e){var n=Date.now(),r=function(){var r=Date.now();r-n>=e&&(t.apply(void 0,arguments),n=r)};return r},Je=function(t,e,n){var r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:ve,i=void 0;return function(){var o=this,s=arguments,a=function(){i=null,a=null,n||t.apply(o,s)};!i&&n&&t.apply(o,s),r.clearTimeout(i),i=r.setTimeout(a,e)}},Qe=function(){};
Qe.prototype.allowedEvents_={},Qe.prototype.on=function(t,e){var n=this.addEventListener;this.addEventListener=function(){},X(this,t,e),this.addEventListener=n},Qe.prototype.addEventListener=Qe.prototype.on,Qe.prototype.off=function(t,e){q(this,t,e)},Qe.prototype.removeEventListener=Qe.prototype.off,Qe.prototype.one=function(t,e){var n=this.addEventListener;this.addEventListener=function(){},$(this,t,e),this.addEventListener=n},Qe.prototype.trigger=function(t){var e=t.type||t;"string"==typeof t&&(t={type:e}),t=U(t),this.allowedEvents_[e]&&this["on"+e]&&this["on"+e](t),K(this,t)},Qe.prototype.dispatchEvent=Qe.prototype.trigger;var Ze=void 0;Qe.prototype.queueTrigger=function(t){var e=this;Ze||(Ze=new Map);var n=t.type||t,r=Ze.get(this);r||(r=new Map,Ze.set(this,r));var i=r.get(n);r["delete"](n),ve.clearTimeout(i);var o=ve.setTimeout(function(){0===r.size&&(r=null,Ze["delete"](e)),e.trigger(t)},0);r.set(n,o)};var tn=function(t){return t instanceof Qe||!!t.eventBusEl_&&["on","one","off","trigger"].every(function(e){return"function"==typeof t[e]})},en=function(t){return"string"==typeof t&&/\S/.test(t)||Array.isArray(t)&&!!t.length},nn=function(t){if(!t.nodeName&&!tn(t))throw new Error("Invalid target; must be a DOM node or evented object.")},rn=function(t){if(!en(t))throw new Error("Invalid event type; must be a non-empty string or array.")},on=function(t){if("function"!=typeof t)throw new Error("Invalid listener; must be a function.")},sn=function(t,e){var n=e.length<3||e[0]===t||e[0]===t.eventBusEl_,r=void 0,i=void 0,o=void 0;return n?(r=t.eventBusEl_,e.length>=3&&e.shift(),i=e[0],o=e[1]):(r=e[0],i=e[1],o=e[2]),nn(r),rn(i),on(o),o=Ye(t,o),{isTargetingSelf:n,target:r,type:i,listener:o}},an=function(t,e,n,r){nn(t),t.nodeName?We[e](t,n,r):t[e](n,r)},ln={on:function(){for(var t=this,e=arguments.length,n=Array(e),r=0;r<e;r++)n[r]=arguments[r];var i=sn(this,n),o=i.isTargetingSelf,s=i.target,a=i.type,l=i.listener;if(an(s,"on",a,l),!o){var c=function(){return t.off(s,a,l)};c.guid=l.guid;var u=function(){return t.off("dispose",c)};u.guid=l.guid,an(this,"on","dispose",c),an(s,"on","dispose",u)}},one:function(){for(var t=this,e=arguments.length,n=Array(e),r=0;r<e;r++)n[r]=arguments[r];var i=sn(this,n),o=i.isTargetingSelf,s=i.target,a=i.type,l=i.listener;if(o)an(s,"one",a,l);else{var c=function u(){for(var e=arguments.length,n=Array(e),r=0;r<e;r++)n[r]=arguments[r];t.off(s,a,u),l.apply(null,n)};c.guid=l.guid,an(s,"one",a,c)}},off:function(t,e,n){if(!t||en(t))q(this.eventBusEl_,t,e);else{var r=t,i=e;nn(r),rn(i),on(n),n=Ye(this,n),this.off("dispose",n),r.nodeName?(q(r,i,n),q(r,"dispose",n)):tn(r)&&(r.off(i,n),r.off("dispose",n))}},trigger:function(t,e){return K(this.eventBusEl_,t,e)}},cn={state:{},setState:function(t){var e=this;"function"==typeof t&&(t=t());var r=void 0;return n(t,function(t,n){e.state[n]!==t&&(r=r||{},r[n]={from:e.state[n],to:t}),e.state[n]=t}),r&&tn(this)&&this.trigger({changes:r,type:"statechanged"}),r}},un=function(){function t(e,n,r){if(je(this,t),!e&&this.play?this.player_=e=this:this.player_=e,this.options_=tt({},this.options_),n=this.options_=tt(this.options_,n),this.id_=n.id||n.el&&n.el.id,!this.id_){var i=e&&e.id&&e.id()||"no_player";this.id_=i+"_component_"+F()}this.name_=n.name||null,n.el?this.el_=n.el:n.createEl!==!1&&(this.el_=this.createEl()),n.evented!==!1&&G(this,{eventBusKey:this.el_?"el_":null}),J(this,this.constructor.defaultState),this.children_=[],this.childIndex_={},this.childNameIndex_={},n.initChildren!==!1&&this.initChildren(),this.ready(r),n.reportTouchActivity!==!1&&this.enableTouchActivity()}return t.prototype.dispose=function(){if(this.trigger({type:"dispose",bubbles:!1}),this.children_)for(var t=this.children_.length-1;t>=0;t--)this.children_[t].dispose&&this.children_[t].dispose();this.children_=null,this.childIndex_=null,this.childNameIndex_=null,this.el_&&(this.el_.parentNode&&this.el_.parentNode.removeChild(this.el_),V(this.el_),this.el_=null),this.player_=null},t.prototype.player=function(){return this.player_},t.prototype.options=function(t){return Ee.warn("this.options() has been deprecated and will be moved to the constructor in 6.0"),t?(this.options_=tt(this.options_,t),this.options_):this.options_},t.prototype.el=function(){return this.el_},t.prototype.createEl=function(t,e,n){return v(t,e,n)},t.prototype.localize=function(t,e){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:t,r=this.player_.language&&this.player_.language(),i=this.player_.languages&&this.player_.languages(),o=i&&i[r],s=r&&r.split("-")[0],a=i&&i[s],l=n;return o&&o[t]?l=o[t]:a&&a[t]&&(l=a[t]),e&&(l=l.replace(/\{(\d+)\}/g,function(t,n){var r=e[n-1],i=r;return"undefined"==typeof r&&(i=t),i})),l},t.prototype.contentEl=function(){return this.contentEl_||this.el_},t.prototype.id=function(){return this.id_},t.prototype.name=function(){return this.name_},t.prototype.children=function(){return this.children_},t.prototype.getChildById=function(t){return this.childIndex_[t]},t.prototype.getChild=function(t){if(t)return t=Q(t),this.childNameIndex_[t]},t.prototype.addChild=function(e){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:this.children_.length,i=void 0,o=void 0;if("string"==typeof e){o=Q(e);var s=n.componentClass||o;n.name=o;var a=t.getComponent(s);if(!a)throw new Error("Component "+s+" does not exist");if("function"!=typeof a)return null;i=new a(this.player_||this,n)}else i=e;if(this.children_.splice(r,0,i),"function"==typeof i.id&&(this.childIndex_[i.id()]=i),o=o||i.name&&Q(i.name()),o&&(this.childNameIndex_[o]=i),"function"==typeof i.el&&i.el()){var l=this.contentEl().children,c=l[r]||null;this.contentEl().insertBefore(i.el(),c)}return i},t.prototype.removeChild=function(t){if("string"==typeof t&&(t=this.getChild(t)),t&&this.children_){for(var e=!1,n=this.children_.length-1;n>=0;n--)if(this.children_[n]===t){e=!0,this.children_.splice(n,1);break}if(e){this.childIndex_[t.id()]=null,this.childNameIndex_[t.name()]=null;var r=t.el();r&&r.parentNode===this.contentEl()&&this.contentEl().removeChild(t.el())}}},t.prototype.initChildren=function(){var e=this,n=this.options_.children;if(n){var r=this.options_,i=function(t){var n=t.name,i=t.opts;if(void 0!==r[n]&&(i=r[n]),i!==!1){i===!0&&(i={}),i.playerOptions=e.options_.playerOptions;var o=e.addChild(n,i);o&&(e[n]=o)}},o=void 0,s=t.getComponent("Tech");o=Array.isArray(n)?n:Object.keys(n),o.concat(Object.keys(this.options_).filter(function(t){return!o.some(function(e){return"string"==typeof e?t===e:t===e.name})})).map(function(t){var r=void 0,i=void 0;return"string"==typeof t?(r=t,i=n[r]||e.options_[r]||{}):(r=t.name,i=t),{name:r,opts:i}}).filter(function(e){var n=t.getComponent(e.opts.componentClass||Q(e.name));return n&&!s.isTech(n)}).forEach(i)}},t.prototype.buildCSSClass=function(){return""},t.prototype.ready=function(t){var e=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(t)return this.isReady_?void(e?t.call(this):this.setTimeout(t,1)):(this.readyQueue_=this.readyQueue_||[],void this.readyQueue_.push(t))},t.prototype.triggerReady=function(){this.isReady_=!0,this.setTimeout(function(){var t=this.readyQueue_;this.readyQueue_=[],t&&t.length>0&&t.forEach(function(t){t.call(this)},this),this.trigger("ready")},1)},t.prototype.$=function(t,e){return Le(t,e||this.contentEl())},t.prototype.$$=function(t,e){return De(t,e||this.contentEl())},t.prototype.hasClass=function(t){return m(this.el_,t)},t.prototype.addClass=function(t){_(this.el_,t)},t.prototype.removeClass=function(t){b(this.el_,t)},t.prototype.toggleClass=function(t,e){T(this.el_,t,e)},t.prototype.show=function(){this.removeClass("vjs-hidden")},t.prototype.hide=function(){this.addClass("vjs-hidden")},t.prototype.lockShowing=function(){this.addClass("vjs-lock-showing")},t.prototype.unlockShowing=function(){this.removeClass("vjs-lock-showing")},t.prototype.getAttribute=function(t){return w(this.el_,t)},t.prototype.setAttribute=function(t,e){E(this.el_,t,e)},t.prototype.removeAttribute=function(t){S(this.el_,t)},t.prototype.width=function(t,e){return this.dimension("width",t,e)},t.prototype.height=function(t,e){return this.dimension("height",t,e)},t.prototype.dimensions=function(t,e){this.width(t,!0),this.height(e)},t.prototype.dimension=function(t,e,n){if(void 0!==e)return null!==e&&e===e||(e=0),(""+e).indexOf("%")!==-1||(""+e).indexOf("px")!==-1?this.el_.style[t]=e:"auto"===e?this.el_.style[t]="":this.el_.style[t]=e+"px",void(n||this.trigger("componentresize"));if(!this.el_)return 0;var r=this.el_.style[t],i=r.indexOf("px");return i!==-1?parseInt(r.slice(0,i),10):parseInt(this.el_["offset"+Q(t)],10)},t.prototype.currentDimension=function(t){var e=0;if("width"!==t&&"height"!==t)throw new Error("currentDimension only accepts width or height value");if("function"==typeof ve.getComputedStyle){var n=ve.getComputedStyle(this.el_);e=n.getPropertyValue(t)||n[t]}if(e=parseFloat(e),0===e){var r="offset"+Q(t);e=this.el_[r]}return e},t.prototype.currentDimensions=function(){return{width:this.currentDimension("width"),height:this.currentDimension("height")}},t.prototype.currentWidth=function(){return this.currentDimension("width")},t.prototype.currentHeight=function(){return this.currentDimension("height")},t.prototype.focus=function(){this.el_.focus()},t.prototype.blur=function(){this.el_.blur()},t.prototype.emitTapEvents=function(){var t=0,e=null,n=10,r=200,i=void 0;this.on("touchstart",function(n){1===n.touches.length&&(e={pageX:n.touches[0].pageX,pageY:n.touches[0].pageY},t=(new Date).getTime(),i=!0)}),this.on("touchmove",function(t){if(t.touches.length>1)i=!1;else if(e){var r=t.touches[0].pageX-e.pageX,o=t.touches[0].pageY-e.pageY,s=Math.sqrt(r*r+o*o);s>n&&(i=!1)}});var o=function(){i=!1};this.on("touchleave",o),this.on("touchcancel",o),this.on("touchend",function(n){if(e=null,i===!0){var o=(new Date).getTime()-t;o<r&&(n.preventDefault(),this.trigger("tap"))}})},t.prototype.enableTouchActivity=function(){if(this.player()&&this.player().reportUserActivity){var t=Ye(this.player(),this.player().reportUserActivity),e=void 0;this.on("touchstart",function(){t(),this.clearInterval(e),e=this.setInterval(t,250)});var n=function(n){t(),this.clearInterval(e)};this.on("touchmove",t),this.on("touchend",n),this.on("touchcancel",n)}},t.prototype.setTimeout=function(t,e){var n,r,i=this;return t=Ye(this,t),n=ve.setTimeout(function(){i.off("dispose",r),t()},e),r=function(){return i.clearTimeout(n)},r.guid="vjs-timeout-"+n,this.on("dispose",r),n},t.prototype.clearTimeout=function(t){ve.clearTimeout(t);var e=function(){};return e.guid="vjs-timeout-"+t,this.off("dispose",e),t},t.prototype.setInterval=function(t,e){var n=this;t=Ye(this,t);var r=ve.setInterval(t,e),i=function(){return n.clearInterval(r)};return i.guid="vjs-interval-"+r,this.on("dispose",i),r},t.prototype.clearInterval=function(t){ve.clearInterval(t);var e=function(){};return e.guid="vjs-interval-"+t,this.off("dispose",e),t},t.prototype.requestAnimationFrame=function(t){var e,n,r=this;return this.supportsRaf_?(t=Ye(this,t),e=ve.requestAnimationFrame(function(){r.off("dispose",n),t()}),n=function(){return r.cancelAnimationFrame(e)},n.guid="vjs-raf-"+e,this.on("dispose",n),e):this.setTimeout(t,1e3/60)},t.prototype.cancelAnimationFrame=function(t){if(this.supportsRaf_){ve.cancelAnimationFrame(t);var e=function(){};return e.guid="vjs-raf-"+t,this.off("dispose",e),t}return this.clearTimeout(t)},t.registerComponent=function(e,n){if("string"!=typeof e||!e)throw new Error('Illegal component name, "'+e+'"; must be a non-empty string.');var r=t.getComponent("Tech"),i=r&&r.isTech(n),o=t===n||t.prototype.isPrototypeOf(n.prototype);if(i||!o){var s=void 0;throw s=i?"techs must be registered using Tech.registerTech()":"must be a Component subclass",new Error('Illegal component, "'+e+'"; '+s+".")}e=Q(e),t.components_||(t.components_={});var a=t.getComponent("Player");if("Player"===e&&a&&a.players){var l=a.players,c=Object.keys(l);if(l&&c.length>0&&c.map(function(t){return l[t]}).every(Boolean))throw new Error("Can not register Player component after player has been created.")}return t.components_[e]=n,n},t.getComponent=function(e){if(e)return e=Q(e),t.components_&&t.components_[e]?t.components_[e]:void 0},t}();un.prototype.supportsRaf_="function"==typeof ve.requestAnimationFrame&&"function"==typeof ve.cancelAnimationFrame,un.registerComponent("Component",un);for(var hn=ve.navigator&&ve.navigator.userAgent||"",pn=/AppleWebKit\/([\d.]+)/i.exec(hn),dn=pn?parseFloat(pn.pop()):null,fn=/iPad/i.test(hn),vn=/iPhone/i.test(hn)&&!fn,yn=/iPod/i.test(hn),gn=vn||fn||yn,mn=function(){var t=hn.match(/OS (\d+)_/i);return t&&t[1]?t[1]:null}(),_n=/Android/i.test(hn),bn=function(){var t=hn.match(/Android (\d+)(?:\.(\d+))?(?:\.(\d+))*/i);if(!t)return null;var e=t[1]&&parseFloat(t[1]),n=t[2]&&parseFloat(t[2]);return e&&n?parseFloat(t[1]+"."+t[2]):e?e:null}(),Tn=_n&&bn<5&&dn<537,Cn=/Firefox/i.test(hn),kn=/Edge/i.test(hn),wn=!kn&&(/Chrome/i.test(hn)||/CriOS/i.test(hn)),En=function(){var t=hn.match(/(Chrome|CriOS)\/(\d+)/);return t&&t[2]?parseFloat(t[2]):null}(),Sn=function(){var t=/MSIE\s(\d+)\.\d/.exec(hn),e=t&&parseFloat(t[1]);return!e&&/Trident\/7.0/i.test(hn)&&/rv:11.0/.test(hn)&&(e=11),e}(),xn=/Safari/i.test(hn)&&!wn&&!_n&&!kn,jn=(xn||gn)&&!wn,An=(h()&&("ontouchstart"in ve||ve.navigator.maxTouchPoints||ve.DocumentTouch&&ve.document instanceof ve.DocumentTouch)),Pn=Object.freeze({IS_IPAD:fn,IS_IPHONE:vn,IS_IPOD:yn,IS_IOS:gn,IOS_VERSION:mn,IS_ANDROID:_n,ANDROID_VERSION:bn,IS_NATIVE_ANDROID:Tn,IS_FIREFOX:Cn,IS_EDGE:kn,IS_CHROME:wn,CHROME_VERSION:En,IE_VERSION:Sn,IS_SAFARI:xn,IS_ANY_SAFARI:jn,TOUCH_ENABLED:An}),Mn={},On=[["requestFullscreen","exitFullscreen","fullscreenElement","fullscreenEnabled","fullscreenchange","fullscreenerror"],["webkitRequestFullscreen","webkitExitFullscreen","webkitFullscreenElement","webkitFullscreenEnabled","webkitfullscreenchange","webkitfullscreenerror"],["webkitRequestFullScreen","webkitCancelFullScreen","webkitCurrentFullScreenElement","webkitCancelFullScreen","webkitfullscreenchange","webkitfullscreenerror"],["mozRequestFullScreen","mozCancelFullScreen","mozFullScreenElement","mozFullScreenEnabled","mozfullscreenchange","mozfullscreenerror"],["msRequestFullscreen","msExitFullscreen","msFullscreenElement","msFullscreenEnabled","MSFullscreenChange","MSFullscreenError"]],Nn=On[0],In=void 0,Ln=0;Ln<On.length;Ln++)if(On[Ln][1]in be){In=On[Ln];break}if(In)for(var Dn=0;Dn<In.length;Dn++)Mn[Nn[Dn]]=In[Dn];st.prototype.code=0,st.prototype.message="",st.prototype.status=null,st.errorTypes=["MEDIA_ERR_CUSTOM","MEDIA_ERR_ABORTED","MEDIA_ERR_NETWORK","MEDIA_ERR_DECODE","MEDIA_ERR_SRC_NOT_SUPPORTED","MEDIA_ERR_ENCRYPTED"],st.defaultMessages={1:"You aborted the media playback",2:"A network error caused the media download to fail part-way.",3:"The media playback was aborted due to a corruption problem or because the media used features your browser did not support.",4:"The media could not be loaded, either because the server or network failed or because the format is not supported.",5:"The media is encrypted and we do not have the keys to decrypt it."};for(var Rn=0;Rn<st.errorTypes.length;Rn++)st[st.errorTypes[Rn]]=Rn,st.prototype[st.errorTypes[Rn]]=Rn;var Fn=at,Bn=function(t){var e=["kind","label","language","id","inBandMetadataTrackDispatchType","mode","src"].reduce(function(e,n,r){return t[n]&&(e[n]=t[n]),e},{cues:t.cues&&Array.prototype.map.call(t.cues,function(t){return{startTime:t.startTime,endTime:t.endTime,text:t.text,id:t.id}})});return e},Hn=function(t){var e=t.$$("track"),n=Array.prototype.map.call(e,function(t){return t.track}),r=Array.prototype.map.call(e,function(t){var e=Bn(t.track);return t.src&&(e.src=t.src),e});return r.concat(Array.prototype.filter.call(t.textTracks(),function(t){return n.indexOf(t)===-1}).map(Bn))},Vn=function(t,e){return t.forEach(function(t){var n=e.addRemoteTextTrack(t).track;!t.src&&t.cues&&t.cues.forEach(function(t){return n.addCue(t)})}),e.textTracks()},zn={textTracksToJson:Hn,jsonToTextTracks:Vn,trackToJson_:Bn},Wn="vjs-modal-dialog",Un=27,Xn=function(t){function e(n,r){je(this,e);var i=Pe(this,t.call(this,n,r));return i.opened_=i.hasBeenOpened_=i.hasBeenFilled_=!1,i.closeable(!i.options_.uncloseable),i.content(i.options_.content),i.contentEl_=v("div",{className:Wn+"-content"},{role:"document"}),i.descEl_=v("p",{className:Wn+"-description vjs-control-text",id:i.el().getAttribute("aria-describedby")}),y(i.descEl_,i.description()),i.el_.appendChild(i.descEl_),i.el_.appendChild(i.contentEl_),i}return Ae(e,t),e.prototype.createEl=function(){return t.prototype.createEl.call(this,"div",{className:this.buildCSSClass(),tabIndex:-1},{"aria-describedby":this.id()+"_description","aria-hidden":"true","aria-label":this.label(),role:"dialog"})},e.prototype.dispose=function(){this.contentEl_=null,this.descEl_=null,this.previouslyActiveEl_=null,t.prototype.dispose.call(this)},e.prototype.buildCSSClass=function(){return Wn+" vjs-hidden "+t.prototype.buildCSSClass.call(this)},e.prototype.handleKeyPress=function(t){t.which===Un&&this.closeable()&&this.close()},e.prototype.label=function(){return this.localize(this.options_.label||"Modal Window")},e.prototype.description=function(){var t=this.options_.description||this.localize("This is a modal window.");return this.closeable()&&(t+=" "+this.localize("This modal can be closed by pressing the Escape key or activating the close button.")),t},e.prototype.open=function(){if(!this.opened_){var t=this.player();this.trigger("beforemodalopen"),this.opened_=!0,(this.options_.fillAlways||!this.hasBeenOpened_&&!this.hasBeenFilled_)&&this.fill(),this.wasPlaying_=!t.paused(),this.options_.pauseOnOpen&&this.wasPlaying_&&t.pause(),this.closeable()&&this.on(this.el_.ownerDocument,"keydown",Ye(this,this.handleKeyPress)),this.hadControls_=t.controls(),t.controls(!1),this.show(),this.conditionalFocus_(),this.el().setAttribute("aria-hidden","false"),this.trigger("modalopen"),this.hasBeenOpened_=!0}},e.prototype.opened=function(t){return"boolean"==typeof t&&this[t?"open":"close"](),this.opened_},e.prototype.close=function(){if(this.opened_){var t=this.player();this.trigger("beforemodalclose"),this.opened_=!1,this.wasPlaying_&&this.options_.pauseOnOpen&&t.play(),this.closeable()&&this.off(this.el_.ownerDocument,"keydown",Ye(this,this.handleKeyPress)),this.hadControls_&&t.controls(!0),this.hide(),this.el().setAttribute("aria-hidden","true"),this.trigger("modalclose"),this.conditionalBlur_(),this.options_.temporary&&this.dispose()}},e.prototype.closeable=function n(t){if("boolean"==typeof t){var n=this.closeable_=!!t,e=this.getChild("closeButton");if(n&&!e){var r=this.contentEl_;this.contentEl_=this.el_,e=this.addChild("closeButton",{controlText:"Close Modal Dialog"}),this.contentEl_=r,this.on(e,"close",this.close)}!n&&e&&(this.off(e,"close",this.close),this.removeChild(e),e.dispose())}return this.closeable_},e.prototype.fill=function(){this.fillWith(this.content())},e.prototype.fillWith=function(t){var e=this.contentEl(),n=e.parentNode,r=e.nextSibling;this.trigger("beforemodalfill"),this.hasBeenFilled_=!0,n.removeChild(e),this.empty(),D(e,t),this.trigger("modalfill"),r?n.insertBefore(e,r):n.appendChild(e);var i=this.getChild("closeButton");i&&n.appendChild(i.el_)},e.prototype.empty=function(){this.trigger("beforemodalempty"),N(this.contentEl()),this.trigger("modalempty")},e.prototype.content=function(t){return"undefined"!=typeof t&&(this.content_=t),this.content_},e.prototype.conditionalFocus_=function(){var t=be.activeElement,e=this.player_.el_;this.previouslyActiveEl_=null,(e.contains(t)||e===t)&&(this.previouslyActiveEl_=t,this.focus(),this.on(be,"keydown",this.handleKeyDown))},e.prototype.conditionalBlur_=function(){this.previouslyActiveEl_&&(this.previouslyActiveEl_.focus(),this.previouslyActiveEl_=null),this.off(be,"keydown",this.handleKeyDown)},e.prototype.handleKeyDown=function(t){if(9===t.which){for(var e=this.focusableEls_(),n=this.el_.querySelector(":focus"),r=void 0,i=0;i<e.length;i++)if(n===e[i]){r=i;break}be.activeElement===this.el_&&(r=0),t.shiftKey&&0===r?(e[e.length-1].focus(),t.preventDefault()):t.shiftKey||r!==e.length-1||(e[0].focus(),t.preventDefault())}},e.prototype.focusableEls_=function(){var t=this.el_.querySelectorAll("*");return Array.prototype.filter.call(t,function(t){return(t instanceof ve.HTMLAnchorElement||t instanceof ve.HTMLAreaElement)&&t.hasAttribute("href")||(t instanceof ve.HTMLInputElement||t instanceof ve.HTMLSelectElement||t instanceof ve.HTMLTextAreaElement||t instanceof ve.HTMLButtonElement)&&!t.hasAttribute("disabled")||t instanceof ve.HTMLIFrameElement||t instanceof ve.HTMLObjectElement||t instanceof ve.HTMLEmbedElement||t.hasAttribute("tabindex")&&t.getAttribute("tabindex")!==-1||t.hasAttribute("contenteditable")})},e}(un);Xn.prototype.options_={pauseOnOpen:!0,temporary:!0},un.registerComponent("ModalDialog",Xn);var qn=function(t){function e(){var n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];je(this,e);var r=Pe(this,t.call(this));r.tracks_=[],Object.defineProperty(r,"length",{get:function(){return this.tracks_.length}});for(var i=0;i<n.length;i++)r.addTrack(n[i]);return r}return Ae(e,t),e.prototype.addTrack=function(t){var e=this.tracks_.length;""+e in this||Object.defineProperty(this,e,{get:function(){return this.tracks_[e]}}),this.tracks_.indexOf(t)===-1&&(this.tracks_.push(t),this.trigger({track:t,type:"addtrack"}))},e.prototype.removeTrack=function(t){for(var e=void 0,n=0,r=this.length;n<r;n++)if(this[n]===t){e=this[n],e.off&&e.off(),this.tracks_.splice(n,1);break}e&&this.trigger({track:e,type:"removetrack"})},e.prototype.getTrackById=function(t){for(var e=null,n=0,r=this.length;n<r;n++){var i=this[n];if(i.id===t){e=i;break}}return e},e}(Qe);qn.prototype.allowedEvents_={change:"change",addtrack:"addtrack",removetrack:"removetrack"};for(var Kn in qn.prototype.allowedEvents_)qn.prototype["on"+Kn]=null;var $n=function(t,e){for(var n=0;n<t.length;n++)Object.keys(t[n]).length&&e.id!==t[n].id&&(t[n].enabled=!1)},Yn=function(t){function e(){var n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];je(this,e);for(var r=n.length-1;r>=0;r--)if(n[r].enabled){$n(n,n[r]);break}var i=Pe(this,t.call(this,n));return i.changing_=!1,i}return Ae(e,t),e.prototype.addTrack=function(e){var n=this;e.enabled&&$n(this,e),t.prototype.addTrack.call(this,e),e.addEventListener&&e.addEventListener("enabledchange",function(){n.changing_||(n.changing_=!0,$n(n,e),n.changing_=!1,n.trigger("change"))})},e}(qn),Gn=function(t,e){for(var n=0;n<t.length;n++)Object.keys(t[n]).length&&e.id!==t[n].id&&(t[n].selected=!1)},Jn=function(t){function e(){var n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];je(this,e);for(var r=n.length-1;r>=0;r--)if(n[r].selected){Gn(n,n[r]);break}var i=Pe(this,t.call(this,n));return i.changing_=!1,Object.defineProperty(i,"selectedIndex",{get:function(){for(var t=0;t<this.length;t++)if(this[t].selected)return t;return-1},set:function(){}}),i}return Ae(e,t),e.prototype.addTrack=function(e){var n=this;e.selected&&Gn(this,e),t.prototype.addTrack.call(this,e),e.addEventListener&&e.addEventListener("selectedchange",function(){n.changing_||(n.changing_=!0,Gn(n,e),n.changing_=!1,n.trigger("change"))})},e}(qn),Qn=function(t){function e(){return je(this,e),Pe(this,t.apply(this,arguments))}return Ae(e,t),e.prototype.addTrack=function(e){t.prototype.addTrack.call(this,e),e.addEventListener("modechange",Ye(this,function(){this.queueTrigger("change")}));var n=["metadata","chapters"];n.indexOf(e.kind)===-1&&e.addEventListener("modechange",Ye(this,function(){this.trigger("selectedlanguagechange")}))},e}(qn),Zn=function(){function t(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];je(this,t),this.trackElements_=[],Object.defineProperty(this,"length",{get:function(){return this.trackElements_.length}});for(var n=0,r=e.length;n<r;n++)this.addTrackElement_(e[n])}return t.prototype.addTrackElement_=function(t){var e=this.trackElements_.length;""+e in this||Object.defineProperty(this,e,{get:function(){return this.trackElements_[e]}}),this.trackElements_.indexOf(t)===-1&&this.trackElements_.push(t)},t.prototype.getTrackElementByTrack_=function(t){for(var e=void 0,n=0,r=this.trackElements_.length;n<r;n++)if(t===this.trackElements_[n].track){e=this.trackElements_[n];break}return e},t.prototype.removeTrackElement_=function(t){for(var e=0,n=this.trackElements_.length;e<n;e++)if(t===this.trackElements_[e]){this.trackElements_.splice(e,1);break}},t}(),tr=function(){function t(e){je(this,t),t.prototype.setCues_.call(this,e),Object.defineProperty(this,"length",{get:function(){return this.length_}})}return t.prototype.setCues_=function(t){var e=this.length||0,n=0,r=t.length;this.cues_=t,this.length_=t.length;var i=function(t){""+t in this||Object.defineProperty(this,""+t,{get:function(){return this.cues_[t]}})};if(e<r)for(n=e;n<r;n++)i.call(this,n)},t.prototype.getCueById=function(t){for(var e=null,n=0,r=this.length;n<r;n++){var i=this[n];if(i.id===t){e=i;break}}return e},t}(),er={alternative:"alternative",captions:"captions",main:"main",sign:"sign",subtitles:"subtitles",commentary:"commentary"},nr={alternative:"alternative",descriptions:"descriptions",main:"main","main-desc":"main-desc",translation:"translation",commentary:"commentary"},rr={subtitles:"subtitles",captions:"captions",descriptions:"descriptions",chapters:"chapters",metadata:"metadata"},ir={disabled:"disabled",hidden:"hidden",showing:"showing"},or=function(t){function e(){var n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};je(this,e);var r=Pe(this,t.call(this)),i={id:n.id||"vjs_track_"+F(),kind:n.kind||"",label:n.label||"",language:n.language||""},o=function(t){Object.defineProperty(r,t,{get:function(){return i[t]},set:function(){}})};for(var s in i)o(s);return r}return Ae(e,t),e}(Qe),sr=function(t){var e=["protocol","hostname","port","pathname","search","hash","host"],n=be.createElement("a");n.href=t;var r=""===n.host&&"file:"!==n.protocol,i=void 0;r&&(i=be.createElement("div"),i.innerHTML='<a href="'+t+'"></a>',n=i.firstChild,i.setAttribute("style","display:none; position:absolute;"),be.body.appendChild(i));for(var o={},s=0;s<e.length;s++)o[e[s]]=n[e[s]];return"http:"===o.protocol&&(o.host=o.host.replace(/:80$/,"")),"https:"===o.protocol&&(o.host=o.host.replace(/:443$/,"")),o.protocol||(o.protocol=ve.location.protocol),r&&be.body.removeChild(i),o},ar=function(t){if(!t.match(/^https?:\/\//)){var e=be.createElement("div");e.innerHTML='<a href="'+t+'">x</a>',t=e.firstChild.href}return t},lr=function(t){if("string"==typeof t){var e=/^(\/?)([\s\S]*?)((?:\.{1,2}|[^\/]+?)(\.([^\.\/\?]+)))(?:[\/]*|[\?].*)$/i,n=e.exec(t);if(n)return n.pop().toLowerCase()}return""},cr=function(t){var e=ve.location,n=sr(t),r=":"===n.protocol?e.protocol:n.protocol,i=r+n.host!==e.protocol+e.host;return i},ur=Object.freeze({parseUrl:sr,getAbsoluteURL:ar,getFileExtension:lr,isCrossOrigin:cr}),hr=ut,pr=Object.prototype.toString,dr=t(function(t,e){function n(t){return t.replace(/^\s*|\s*$/g,"")}e=t.exports=n,e.left=function(t){return t.replace(/^\s*/,"")},e.right=function(t){return t.replace(/\s*$/,"")}}),fr=(dr.left,dr.right,ht),vr=Object.prototype.toString,yr=Object.prototype.hasOwnProperty,gr=function(t){return"[object Array]"===Object.prototype.toString.call(t)},mr=function(t){if(!t)return{};var e={};return fr(dr(t).split("\n"),function(t){var n=t.indexOf(":"),r=dr(t.slice(0,n)).toLowerCase(),i=dr(t.slice(n+1));"undefined"==typeof e[r]?e[r]=i:gr(e[r])?e[r].push(i):e[r]=[e[r],i]}),e},_r=vt,br=Object.prototype.hasOwnProperty,Tr=_t;_t.XMLHttpRequest=ve.XMLHttpRequest||Ct,_t.XDomainRequest="withCredentials"in new _t.XMLHttpRequest?_t.XMLHttpRequest:ve.XDomainRequest,yt(["get","put","post","patch","head","delete"],function(t){_t["delete"===t?"del":t]=function(e,n,r){return n=mt(e,n,r),n.method=t.toUpperCase(),bt(n)}});var Cr=function(t,e){var n=new ve.WebVTT.Parser(ve,ve.vttjs,ve.WebVTT.StringDecoder()),r=[];n.oncue=function(t){e.addCue(t)},n.onparsingerror=function(t){r.push(t)},n.onflush=function(){e.trigger({type:"loadeddata",target:e})},n.parse(t),r.length>0&&(ve.console&&ve.console.groupCollapsed&&ve.console.groupCollapsed("Text Track parsing errors for "+e.src),r.forEach(function(t){return Ee.error(t)}),ve.console&&ve.console.groupEnd&&ve.console.groupEnd()),n.flush()},kr=function(t,e){var n={uri:t},r=cr(t);r&&(n.cors=r),Tr(n,Ye(this,function(t,n,r){if(t)return Ee.error(t,n);if(e.loaded_=!0,"function"!=typeof ve.WebVTT){if(e.tech_){var i=function(){return Cr(r,e)};e.tech_.on("vttjsloaded",i),e.tech_.on("vttjserror",function(){Ee.error("vttjs failed to load, stopping trying to process "+e.src),e.tech_.off("vttjsloaded",i)})}}else Cr(r,e)}))},wr=function(t){function e(){var n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};if(je(this,e),!n.tech)throw new Error("A tech was not provided.");var r=tt(n,{kind:rr[n.kind]||"subtitles",language:n.language||n.srclang||""}),i=ir[r.mode]||"disabled",o=r["default"];"metadata"!==r.kind&&"chapters"!==r.kind||(i="hidden");var s=Pe(this,t.call(this,r));s.tech_=r.tech,s.cues_=[],s.activeCues_=[];var a=new tr(s.cues_),l=new tr(s.activeCues_),c=!1,u=Ye(s,function(){this.activeCues=this.activeCues,c&&(this.trigger("cuechange"),c=!1)});return"disabled"!==i&&s.tech_.ready(function(){s.tech_.on("timeupdate",u)},!0),Object.defineProperties(s,{"default":{get:function(){return o},set:function(){}},mode:{get:function(){return i},set:function(t){var e=this;ir[t]&&(i=t,"showing"===i&&this.tech_.ready(function(){e.tech_.on("timeupdate",u)},!0),this.trigger("modechange"))}},cues:{get:function(){return this.loaded_?a:null},set:function(){}},activeCues:{get:function(){if(!this.loaded_)return null;if(0===this.cues.length)return l;for(var t=this.tech_.currentTime(),e=[],n=0,r=this.cues.length;n<r;n++){var i=this.cues[n];i.startTime<=t&&i.endTime>=t?e.push(i):i.startTime===i.endTime&&i.startTime<=t&&i.startTime+.5>=t&&e.push(i)}if(c=!1,e.length!==this.activeCues_.length)c=!0;else for(var o=0;o<e.length;o++)this.activeCues_.indexOf(e[o])===-1&&(c=!0);return this.activeCues_=e,l.setCues_(this.activeCues_),l},set:function(){}}}),r.src?(s.src=r.src,kr(r.src,s)):s.loaded_=!0,s}return Ae(e,t),e.prototype.addCue=function(t){var e=t;if(ve.vttjs&&!(t instanceof ve.vttjs.VTTCue)){e=new ve.vttjs.VTTCue(t.startTime,t.endTime,t.text);for(var n in t)n in e||(e[n]=t[n]);e.id=t.id,e.originalCue_=t}for(var r=this.tech_.textTracks(),i=0;i<r.length;i++)r[i]!==this&&r[i].removeCue(e);this.cues_.push(e),this.cues.setCues_(this.cues_)},e.prototype.removeCue=function(t){for(var e=this.cues_.length;e--;){var n=this.cues_[e];if(n===t||n.originalCue_&&n.originalCue_===t){this.cues_.splice(e,1),this.cues.setCues_(this.cues_);break}}},e}(or);wr.prototype.allowedEvents_={cuechange:"cuechange"};var Er=function(t){function e(){var n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};je(this,e);var r=tt(n,{kind:nr[n.kind]||""}),i=Pe(this,t.call(this,r)),o=!1;return Object.defineProperty(i,"enabled",{get:function(){return o},set:function(t){"boolean"==typeof t&&t!==o&&(o=t,this.trigger("enabledchange"))}}),r.enabled&&(i.enabled=r.enabled),i.loaded_=!0,i}return Ae(e,t),e}(or),Sr=function(t){function e(){var n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};je(this,e);var r=tt(n,{kind:er[n.kind]||""}),i=Pe(this,t.call(this,r)),o=!1;return Object.defineProperty(i,"selected",{get:function(){return o},set:function(t){"boolean"==typeof t&&t!==o&&(o=t,this.trigger("selectedchange"))}}),r.selected&&(i.selected=r.selected),i}return Ae(e,t),e}(or),xr=0,jr=1,Ar=2,Pr=3,Mr=function(t){function e(){var n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};je(this,e);var r=Pe(this,t.call(this)),i=void 0,o=new wr(n);return r.kind=o.kind,r.src=o.src,r.srclang=o.language,r.label=o.label,r["default"]=o["default"],Object.defineProperties(r,{readyState:{get:function(){return i}},track:{get:function(){return o}}}),i=xr,o.addEventListener("loadeddata",function(){
i=Ar,r.trigger({type:"load",target:r})}),r}return Ae(e,t),e}(Qe);Mr.prototype.allowedEvents_={load:"load"},Mr.NONE=xr,Mr.LOADING=jr,Mr.LOADED=Ar,Mr.ERROR=Pr;var Or={audio:{ListClass:Yn,TrackClass:Er,capitalName:"Audio"},video:{ListClass:Jn,TrackClass:Sr,capitalName:"Video"},text:{ListClass:Qn,TrackClass:wr,capitalName:"Text"}};Object.keys(Or).forEach(function(t){Or[t].getterName=t+"Tracks",Or[t].privateName=t+"Tracks_"});var Nr={remoteText:{ListClass:Qn,TrackClass:wr,capitalName:"RemoteText",getterName:"remoteTextTracks",privateName:"remoteTextTracks_"},remoteTextEl:{ListClass:Zn,TrackClass:Mr,capitalName:"RemoteTextTrackEls",getterName:"remoteTextTrackEls",privateName:"remoteTextTrackEls_"}},Ir=tt(Or,Nr);Nr.names=Object.keys(Nr),Or.names=Object.keys(Or),Ir.names=[].concat(Nr.names).concat(Or.names);var Lr=Object.create||function(){function t(){}return function(e){if(1!==arguments.length)throw new Error("Object.create shim only accepts one parameter.");return t.prototype=e,new t}}();kt.prototype=Lr(Error.prototype),kt.prototype.constructor=kt,kt.Errors={BadSignature:{code:0,message:"Malformed WebVTT signature."},BadTimeStamp:{code:1,message:"Malformed time stamp."}},Et.prototype={set:function(t,e){this.get(t)||""===e||(this.values[t]=e)},get:function(t,e,n){return n?this.has(t)?this.values[t]:e[n]:this.has(t)?this.values[t]:e},has:function(t){return t in this.values},alt:function(t,e,n){for(var r=0;r<n.length;++r)if(e===n[r]){this.set(t,e);break}},integer:function(t,e){/^-?\d+$/.test(e)&&this.set(t,parseInt(e,10))},percent:function(t,e){var n;return!!((n=e.match(/^([\d]{1,3})(\.[\d]*)?%$/))&&(e=parseFloat(e),e>=0&&e<=100))&&(this.set(t,e),!0)}};var Dr={"&amp;":"&","&lt;":"<","&gt;":">","&lrm;":"‎","&rlm;":"‏","&nbsp;":" "},Rr={c:"span",i:"i",b:"b",u:"u",ruby:"ruby",rt:"rt",v:"span",lang:"span"},Fr={v:"title",lang:"lang"},Br={rt:"ruby"},Hr=[[1470,1470],[1472,1472],[1475,1475],[1478,1478],[1488,1514],[1520,1524],[1544,1544],[1547,1547],[1549,1549],[1563,1563],[1566,1610],[1645,1647],[1649,1749],[1765,1766],[1774,1775],[1786,1805],[1807,1808],[1810,1839],[1869,1957],[1969,1969],[1984,2026],[2036,2037],[2042,2042],[2048,2069],[2074,2074],[2084,2084],[2088,2088],[2096,2110],[2112,2136],[2142,2142],[2208,2208],[2210,2220],[8207,8207],[64285,64285],[64287,64296],[64298,64310],[64312,64316],[64318,64318],[64320,64321],[64323,64324],[64326,64449],[64467,64829],[64848,64911],[64914,64967],[65008,65020],[65136,65140],[65142,65276],[67584,67589],[67592,67592],[67594,67637],[67639,67640],[67644,67644],[67647,67669],[67671,67679],[67840,67867],[67872,67897],[67903,67903],[67968,68023],[68030,68031],[68096,68096],[68112,68115],[68117,68119],[68121,68147],[68160,68167],[68176,68184],[68192,68223],[68352,68405],[68416,68437],[68440,68466],[68472,68479],[68608,68680],[126464,126467],[126469,126495],[126497,126498],[126500,126500],[126503,126503],[126505,126514],[126516,126519],[126521,126521],[126523,126523],[126530,126530],[126535,126535],[126537,126537],[126539,126539],[126541,126543],[126545,126546],[126548,126548],[126551,126551],[126553,126553],[126555,126555],[126557,126557],[126559,126559],[126561,126562],[126564,126564],[126567,126570],[126572,126578],[126580,126583],[126585,126588],[126590,126590],[126592,126601],[126603,126619],[126625,126627],[126629,126633],[126635,126651],[1114109,1114109]];Ot.prototype.applyStyles=function(t,e){e=e||this.div;for(var n in t)t.hasOwnProperty(n)&&(e.style[n]=t[n])},Ot.prototype.formatStyle=function(t,e){return 0===t?0:t+e},Nt.prototype=Lr(Ot.prototype),Nt.prototype.constructor=Nt,It.prototype.move=function(t,e){switch(e=void 0!==e?e:this.lineHeight,t){case"+x":this.left+=e,this.right+=e;break;case"-x":this.left-=e,this.right-=e;break;case"+y":this.top+=e,this.bottom+=e;break;case"-y":this.top-=e,this.bottom-=e}},It.prototype.overlaps=function(t){return this.left<t.right&&this.right>t.left&&this.top<t.bottom&&this.bottom>t.top},It.prototype.overlapsAny=function(t){for(var e=0;e<t.length;e++)if(this.overlaps(t[e]))return!0;return!1},It.prototype.within=function(t){return this.top>=t.top&&this.bottom<=t.bottom&&this.left>=t.left&&this.right<=t.right},It.prototype.overlapsOppositeAxis=function(t,e){switch(e){case"+x":return this.left<t.left;case"-x":return this.right>t.right;case"+y":return this.top<t.top;case"-y":return this.bottom>t.bottom}},It.prototype.intersectPercentage=function(t){var e=Math.max(0,Math.min(this.right,t.right)-Math.max(this.left,t.left)),n=Math.max(0,Math.min(this.bottom,t.bottom)-Math.max(this.top,t.top)),r=e*n;return r/(this.height*this.width)},It.prototype.toCSSCompatValues=function(t){return{top:this.top-t.top,bottom:t.bottom-this.bottom,left:this.left-t.left,right:t.right-this.right,height:this.height,width:this.width}},It.getSimpleBoxPosition=function(t){var e=t.div?t.div.offsetHeight:t.tagName?t.offsetHeight:0,n=t.div?t.div.offsetWidth:t.tagName?t.offsetWidth:0,r=t.div?t.div.offsetTop:t.tagName?t.offsetTop:0;t=t.div?t.div.getBoundingClientRect():t.tagName?t.getBoundingClientRect():t;var i={left:t.left,right:t.right,top:t.top||r,height:t.height||e,bottom:t.bottom||r+(t.height||e),width:t.width||n};return i},Dt.StringDecoder=function(){return{decode:function(t){if(!t)return"";if("string"!=typeof t)throw new Error("Error - expected string data.");return decodeURIComponent(encodeURIComponent(t))}}},Dt.convertCueToDOMTree=function(t,e){return t&&e?jt(t,e):null};var Vr=.05,zr="sans-serif",Wr="1.5%";Dt.processCues=function(t,e,n){function r(t){for(var e=0;e<t.length;e++)if(t[e].hasBeenReset||!t[e].displayState)return!0;return!1}if(!t||!e||!n)return null;for(;n.firstChild;)n.removeChild(n.firstChild);var i=t.document.createElement("div");if(i.style.position="absolute",i.style.left="0",i.style.right="0",i.style.top="0",i.style.bottom="0",i.style.margin=Wr,n.appendChild(i),r(e)){var o=[],s=It.getSimpleBoxPosition(i),a=Math.round(s.height*Vr*100)/100,l={font:a+"px "+zr};!function(){for(var n,r,a=0;a<e.length;a++)r=e[a],n=new Nt(t,r,l),i.appendChild(n.div),Lt(t,n,s,o),r.displayState=n.div,o.push(It.getSimpleBoxPosition(n))}()}else for(var c=0;c<e.length;c++)i.appendChild(e[c].displayState)},Dt.Parser=function(t,e,n){n||(n=e,e={}),e||(e={}),this.window=t,this.vttjs=e,this.state="INITIAL",this.buffer="",this.decoder=n||new TextDecoder("utf8"),this.regionList=[]},Dt.Parser.prototype={reportOrThrowError:function(t){if(!(t instanceof kt))throw t;this.onparsingerror&&this.onparsingerror(t)},parse:function(t){function e(){for(var t=o.buffer,e=0;e<t.length&&"\r"!==t[e]&&"\n"!==t[e];)++e;var n=t.substr(0,e);return"\r"===t[e]&&++e,"\n"===t[e]&&++e,o.buffer=t.substr(e),n}function n(t){var e=new Et;if(St(t,function(t,n){switch(t){case"id":e.set(t,n);break;case"width":e.percent(t,n);break;case"lines":e.integer(t,n);break;case"regionanchor":case"viewportanchor":var r=n.split(",");if(2!==r.length)break;var i=new Et;if(i.percent("x",r[0]),i.percent("y",r[1]),!i.has("x")||!i.has("y"))break;e.set(t+"X",i.get("x")),e.set(t+"Y",i.get("y"));break;case"scroll":e.alt(t,n,["up"])}},/=/,/\s/),e.has("id")){var n=new(o.vttjs.VTTRegion||o.window.VTTRegion);n.width=e.get("width",100),n.lines=e.get("lines",3),n.regionAnchorX=e.get("regionanchorX",0),n.regionAnchorY=e.get("regionanchorY",100),n.viewportAnchorX=e.get("viewportanchorX",0),n.viewportAnchorY=e.get("viewportanchorY",100),n.scroll=e.get("scroll",""),o.onregion&&o.onregion(n),o.regionList.push({id:e.get("id"),region:n})}}function r(t){var e=new Et;St(t,function(t,n){switch(t){case"MPEGT":e.integer(t+"S",n);break;case"LOCA":e.set(t+"L",wt(n))}},/[^\d]:/,/,/),o.ontimestampmap&&o.ontimestampmap({MPEGTS:e.get("MPEGTS"),LOCAL:e.get("LOCAL")})}function i(t){t.match(/X-TIMESTAMP-MAP/)?St(t,function(t,e){switch(t){case"X-TIMESTAMP-MAP":r(e)}},/=/):St(t,function(t,e){switch(t){case"Region":n(e)}},/:/)}var o=this;t&&(o.buffer+=o.decoder.decode(t,{stream:!0}));try{var s;if("INITIAL"===o.state){if(!/\r\n|\n/.test(o.buffer))return this;s=e();var a=s.match(/^WEBVTT([ \t].*)?$/);if(!a||!a[0])throw new kt(kt.Errors.BadSignature);o.state="HEADER"}for(var l=!1;o.buffer;){if(!/\r\n|\n/.test(o.buffer))return this;switch(l?l=!1:s=e(),o.state){case"HEADER":/:/.test(s)?i(s):s||(o.state="ID");continue;case"NOTE":s||(o.state="ID");continue;case"ID":if(/^NOTE($|[ \t])/.test(s)){o.state="NOTE";break}if(!s)continue;if(o.cue=new(o.vttjs.VTTCue||o.window.VTTCue)(0,0,""),o.state="CUE",s.indexOf("-->")===-1){o.cue.id=s;continue}case"CUE":try{xt(s,o.cue,o.regionList)}catch(c){o.reportOrThrowError(c),o.cue=null,o.state="BADCUE";continue}o.state="CUETEXT";continue;case"CUETEXT":var u=s.indexOf("-->")!==-1;if(!s||u&&(l=!0)){o.oncue&&o.oncue(o.cue),o.cue=null,o.state="ID";continue}o.cue.text&&(o.cue.text+="\n"),o.cue.text+=s;continue;case"BADCUE":s||(o.state="ID");continue}}}catch(c){o.reportOrThrowError(c),"CUETEXT"===o.state&&o.cue&&o.oncue&&o.oncue(o.cue),o.cue=null,o.state="INITIAL"===o.state?"BADWEBVTT":"BADCUE"}return this},flush:function(){var t=this;try{if(t.buffer+=t.decoder.decode(),(t.cue||"HEADER"===t.state)&&(t.buffer+="\n\n",t.parse()),"INITIAL"===t.state)throw new kt(kt.Errors.BadSignature)}catch(e){t.reportOrThrowError(e)}return t.onflush&&t.onflush(),this}};var Ur=Dt,Xr="auto",qr={"":1,lr:1,rl:1},Kr={start:1,middle:1,end:1,left:1,right:1};Bt.prototype.getCueAsHTML=function(){return WebVTT.convertCueToDOMTree(window,this.text)};var $r=Bt,Yr={"":!0,up:!0},Gr=zt,Jr=t(function(t){var e=t.exports={WebVTT:Ur,VTTCue:$r,VTTRegion:Gr};ve.vttjs=e,ve.WebVTT=e.WebVTT;var n=e.VTTCue,r=e.VTTRegion,i=ve.VTTCue,o=ve.VTTRegion;e.shim=function(){ve.VTTCue=n,ve.VTTRegion=r},e.restore=function(){ve.VTTCue=i,ve.VTTRegion=o},ve.VTTCue||e.shim()}),Qr=(Jr.WebVTT,Jr.VTTCue,Jr.VTTRegion,function(t){function e(){var n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:function(){};je(this,e),n.reportTouchActivity=!1;var i=Pe(this,t.call(this,null,n,r));return i.hasStarted_=!1,i.on("playing",function(){this.hasStarted_=!0}),i.on("loadstart",function(){this.hasStarted_=!1}),Ir.names.forEach(function(t){var e=Ir[t];n&&n[e.getterName]&&(i[e.privateName]=n[e.getterName])}),i.featuresProgressEvents||i.manualProgressOn(),i.featuresTimeupdateEvents||i.manualTimeUpdatesOn(),["Text","Audio","Video"].forEach(function(t){n["native"+t+"Tracks"]===!1&&(i["featuresNative"+t+"Tracks"]=!1)}),n.nativeCaptions===!1||n.nativeTextTracks===!1?i.featuresNativeTextTracks=!1:n.nativeCaptions!==!0&&n.nativeTextTracks!==!0||(i.featuresNativeTextTracks=!0),i.featuresNativeTextTracks||i.emulateTextTracks(),i.autoRemoteTextTracks_=new Ir.text.ListClass,i.initTrackListeners(),n.nativeControlsForTouch||i.emitTapEvents(),i.constructor&&(i.name_=i.constructor.name||"Unknown Tech"),i}return Ae(e,t),e.prototype.triggerSourceset=function(t){var e=this;this.isReady_||this.one("ready",function(){return e.setTimeout(function(){return e.triggerSourceset(t)},1)}),this.trigger({src:t,type:"sourceset"})},e.prototype.manualProgressOn=function(){this.on("durationchange",this.onDurationChange),this.manualProgress=!0,this.one("ready",this.trackProgress)},e.prototype.manualProgressOff=function(){this.manualProgress=!1,this.stopTrackingProgress(),this.off("durationchange",this.onDurationChange)},e.prototype.trackProgress=function(t){this.stopTrackingProgress(),this.progressInterval=this.setInterval(Ye(this,function(){var t=this.bufferedPercent();this.bufferedPercent_!==t&&this.trigger("progress"),this.bufferedPercent_=t,1===t&&this.stopTrackingProgress()}),500)},e.prototype.onDurationChange=function(t){this.duration_=this.duration()},e.prototype.buffered=function(){return it(0,0)},e.prototype.bufferedPercent=function(){return ot(this.buffered(),this.duration_)},e.prototype.stopTrackingProgress=function(){this.clearInterval(this.progressInterval)},e.prototype.manualTimeUpdatesOn=function(){this.manualTimeUpdates=!0,this.on("play",this.trackCurrentTime),this.on("pause",this.stopTrackingCurrentTime)},e.prototype.manualTimeUpdatesOff=function(){this.manualTimeUpdates=!1,this.stopTrackingCurrentTime(),this.off("play",this.trackCurrentTime),this.off("pause",this.stopTrackingCurrentTime)},e.prototype.trackCurrentTime=function(){this.currentTimeInterval&&this.stopTrackingCurrentTime(),this.currentTimeInterval=this.setInterval(function(){this.trigger({type:"timeupdate",target:this,manuallyTriggered:!0})},250)},e.prototype.stopTrackingCurrentTime=function(){this.clearInterval(this.currentTimeInterval),this.trigger({type:"timeupdate",target:this,manuallyTriggered:!0})},e.prototype.dispose=function(){this.clearTracks(Or.names),this.manualProgress&&this.manualProgressOff(),this.manualTimeUpdates&&this.manualTimeUpdatesOff(),t.prototype.dispose.call(this)},e.prototype.clearTracks=function(t){var e=this;t=[].concat(t),t.forEach(function(t){for(var n=e[t+"Tracks"]()||[],r=n.length;r--;){var i=n[r];"text"===t&&e.removeRemoteTextTrack(i),n.removeTrack(i)}})},e.prototype.cleanupAutoTextTracks=function(){for(var t=this.autoRemoteTextTracks_||[],e=t.length;e--;){var n=t[e];this.removeRemoteTextTrack(n)}},e.prototype.reset=function(){},e.prototype.error=function(t){return void 0!==t&&(this.error_=new st(t),this.trigger("error")),this.error_},e.prototype.played=function(){return this.hasStarted_?it(0,0):it()},e.prototype.setCurrentTime=function(){this.manualTimeUpdates&&this.trigger({type:"timeupdate",target:this,manuallyTriggered:!0})},e.prototype.initTrackListeners=function(){var t=this;Or.names.forEach(function(e){var n=Or[e],r=function(){t.trigger(e+"trackchange")},i=t[n.getterName]();i.addEventListener("removetrack",r),i.addEventListener("addtrack",r),t.on("dispose",function(){i.removeEventListener("removetrack",r),i.removeEventListener("addtrack",r)})})},e.prototype.addWebVttScript_=function(){var t=this;if(!ve.WebVTT)if(be.body.contains(this.el())){if(!this.options_["vtt.js"]&&s(Jr)&&Object.keys(Jr).length>0)return void this.trigger("vttjsloaded");var e=be.createElement("script");e.src=this.options_["vtt.js"]||"https://vjs.zencdn.net/vttjs/0.14.1/vtt.min.js",e.onload=function(){t.trigger("vttjsloaded")},e.onerror=function(){t.trigger("vttjserror")},this.on("dispose",function(){e.onload=null,e.onerror=null}),ve.WebVTT=!0,this.el().parentNode.appendChild(e)}else this.ready(this.addWebVttScript_)},e.prototype.emulateTextTracks=function(){var t=this,e=this.textTracks(),n=this.remoteTextTracks(),r=function(t){return e.addTrack(t.track)},i=function(t){return e.removeTrack(t.track)};n.on("addtrack",r),n.on("removetrack",i),this.addWebVttScript_();var o=function(){return t.trigger("texttrackchange")},s=function(){o();for(var t=0;t<e.length;t++){var n=e[t];n.removeEventListener("cuechange",o),"showing"===n.mode&&n.addEventListener("cuechange",o)}};s(),e.addEventListener("change",s),e.addEventListener("addtrack",s),e.addEventListener("removetrack",s),this.on("dispose",function(){n.off("addtrack",r),n.off("removetrack",i),e.removeEventListener("change",s),e.removeEventListener("addtrack",s),e.removeEventListener("removetrack",s);for(var t=0;t<e.length;t++){var a=e[t];a.removeEventListener("cuechange",o)}})},e.prototype.addTextTrack=function(t,e,n){if(!t)throw new Error("TextTrack kind is required but was not provided");return Wt(this,t,e,n)},e.prototype.createRemoteTextTrack=function(t){var e=tt(t,{tech:this});return new Nr.remoteTextEl.TrackClass(e)},e.prototype.addRemoteTextTrack=function(){var t=this,e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=arguments[1],r=this.createRemoteTextTrack(e);return n!==!0&&n!==!1&&(Ee.warn('Calling addRemoteTextTrack without explicitly setting the "manualCleanup" parameter to `true` is deprecated and default to `false` in future version of video.js'),n=!0),this.remoteTextTrackEls().addTrackElement_(r),this.remoteTextTracks().addTrack(r.track),n!==!0&&this.ready(function(){return t.autoRemoteTextTracks_.addTrack(r.track)}),r},e.prototype.removeRemoteTextTrack=function(t){var e=this.remoteTextTrackEls().getTrackElementByTrack_(t);this.remoteTextTrackEls().removeTrackElement_(e),this.remoteTextTracks().removeTrack(t),this.autoRemoteTextTracks_.removeTrack(t)},e.prototype.getVideoPlaybackQuality=function(){return{}},e.prototype.setPoster=function(){},e.prototype.playsinline=function(){},e.prototype.setPlaysinline=function(){},e.prototype.overrideNativeAudioTracks=function(){},e.prototype.overrideNativeVideoTracks=function(){},e.prototype.canPlayType=function(){return""},e.canPlayType=function(){return""},e.canPlaySource=function(t,n){return e.canPlayType(t.type)},e.isTech=function(t){return t.prototype instanceof e||t instanceof e||t===e},e.registerTech=function(t,n){if(e.techs_||(e.techs_={}),!e.isTech(n))throw new Error("Tech "+t+" must be a Tech");if(!e.canPlayType)throw new Error("Techs must have a static canPlayType method on them");if(!e.canPlaySource)throw new Error("Techs must have a static canPlaySource method on them");return t=Q(t),e.techs_[t]=n,"Tech"!==t&&e.defaultTechOrder_.push(t),n},e.getTech=function(t){if(t)return t=Q(t),e.techs_&&e.techs_[t]?e.techs_[t]:ve&&ve.videojs&&ve.videojs[t]?(Ee.warn("The "+t+" tech was added to the videojs object when it should be registered using videojs.registerTech(name, tech)"),ve.videojs[t]):void 0},e}(un));Ir.names.forEach(function(t){var e=Ir[t];Qr.prototype[e.getterName]=function(){return this[e.privateName]=this[e.privateName]||new e.ListClass,this[e.privateName]}}),Qr.prototype.featuresVolumeControl=!0,Qr.prototype.featuresMuteControl=!0,Qr.prototype.featuresFullscreenResize=!1,Qr.prototype.featuresPlaybackRate=!1,Qr.prototype.featuresProgressEvents=!1,Qr.prototype.featuresSourceset=!1,Qr.prototype.featuresTimeupdateEvents=!1,Qr.prototype.featuresNativeTextTracks=!1,Qr.withSourceHandlers=function(t){t.registerSourceHandler=function(e,n){var r=t.sourceHandlers;r||(r=t.sourceHandlers=[]),void 0===n&&(n=r.length),r.splice(n,0,e)},t.canPlayType=function(e){for(var n=t.sourceHandlers||[],r=void 0,i=0;i<n.length;i++)if(r=n[i].canPlayType(e))return r;return""},t.selectSourceHandler=function(e,n){for(var r=t.sourceHandlers||[],i=void 0,o=0;o<r.length;o++)if(i=r[o].canHandleSource(e,n))return r[o];return null},t.canPlaySource=function(e,n){var r=t.selectSourceHandler(e,n);return r?r.canHandleSource(e,n):""};var e=["seekable","seeking","duration"];e.forEach(function(t){var e=this[t];"function"==typeof e&&(this[t]=function(){return this.sourceHandler_&&this.sourceHandler_[t]?this.sourceHandler_[t].apply(this.sourceHandler_,arguments):e.apply(this,arguments)})},t.prototype),t.prototype.setSource=function(e){var n=t.selectSourceHandler(e,this.options_);n||(t.nativeSourceHandler?n=t.nativeSourceHandler:Ee.error("No source handler found for the current source.")),this.disposeSourceHandler(),this.off("dispose",this.disposeSourceHandler),n!==t.nativeSourceHandler&&(this.currentSource_=e),this.sourceHandler_=n.handleSource(e,this,this.options_),this.on("dispose",this.disposeSourceHandler)},t.prototype.disposeSourceHandler=function(){this.currentSource_&&(this.clearTracks(["audio","video"]),this.currentSource_=null),this.cleanupAutoTextTracks(),this.sourceHandler_&&(this.sourceHandler_.dispose&&this.sourceHandler_.dispose(),this.sourceHandler_=null)}},un.registerComponent("Tech",Qr),Qr.registerTech("Tech",Qr),Qr.defaultTechOrder_=[];var Zr={},ti={},ei={},ni={buffered:1,currentTime:1,duration:1,seekable:1,played:1,paused:1},ri={setCurrentTime:1},ii={play:1,pause:1},oi={opus:"video/ogg",ogv:"video/ogg",mp4:"video/mp4",mov:"video/mp4",m4v:"video/mp4",mkv:"video/x-matroska",mp3:"audio/mpeg",aac:"audio/aac",oga:"audio/ogg",m3u8:"application/x-mpegURL"},si=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",e=lr(t),n=oi[e.toLowerCase()];return n||""},ai=function(t,e){if(!e)return"";if(t.cache_.source.src===e&&t.cache_.source.type)return t.cache_.source.type;var n=t.cache_.sources.filter(function(t){return t.src===e});if(n.length)return n[0].type;for(var r=t.$$("source"),i=0;i<r.length;i++){var o=r[i];if(o.type&&o.src&&o.src===e)return o.type}return si(e)},li=function us(t){if(Array.isArray(t)){var e=[];t.forEach(function(t){t=us(t),Array.isArray(t)?e=e.concat(t):o(t)&&e.push(t)}),t=e}else t="string"==typeof t&&t.trim()?[ee({src:t})]:o(t)&&"string"==typeof t.src&&t.src&&t.src.trim()?[ee(t)]:[];return t},ci=function(t){function e(n,r,i){je(this,e);var o=tt({createEl:!1},r),s=Pe(this,t.call(this,n,o,i));if(r.playerOptions.sources&&0!==r.playerOptions.sources.length)n.src(r.playerOptions.sources);else for(var a=0,l=r.playerOptions.techOrder;a<l.length;a++){var c=Q(l[a]),u=Qr.getTech(c);if(c||(u=un.getComponent(c)),u&&u.isSupported()){n.loadTech_(c);break}}return s}return Ae(e,t),e}(un);un.registerComponent("MediaLoader",ci);var ui=function(t){function e(n,r){je(this,e);var i=Pe(this,t.call(this,n,r));return i.emitTapEvents(),i.enable(),i}return Ae(e,t),e.prototype.createEl=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"div",n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};n=i({innerHTML:'<span aria-hidden="true" class="vjs-icon-placeholder"></span>',className:this.buildCSSClass(),tabIndex:0},n),"button"===e&&Ee.error("Creating a ClickableComponent with an HTML element of "+e+" is not supported; use a Button instead."),r=i({role:"button"},r),this.tabIndex_=n.tabIndex;var o=t.prototype.createEl.call(this,e,n,r);return this.createControlTextEl(o),o},e.prototype.dispose=function(){this.controlTextEl_=null,t.prototype.dispose.call(this)},e.prototype.createControlTextEl=function(t){return this.controlTextEl_=v("span",{className:"vjs-control-text"},{"aria-live":"polite"}),t&&t.appendChild(this.controlTextEl_),this.controlText(this.controlText_,t),this.controlTextEl_},e.prototype.controlText=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.el();if(void 0===t)return this.controlText_||"Need Text";var n=this.localize(t);this.controlText_=t,y(this.controlTextEl_,n),this.nonIconControl||e.setAttribute("title",n)},e.prototype.buildCSSClass=function(){return"vjs-control vjs-button "+t.prototype.buildCSSClass.call(this)},e.prototype.enable=function(){this.enabled_||(this.enabled_=!0,this.removeClass("vjs-disabled"),this.el_.setAttribute("aria-disabled","false"),"undefined"!=typeof this.tabIndex_&&this.el_.setAttribute("tabIndex",this.tabIndex_),this.on(["tap","click"],this.handleClick),this.on("focus",this.handleFocus),this.on("blur",this.handleBlur))},e.prototype.disable=function(){this.enabled_=!1,this.addClass("vjs-disabled"),this.el_.setAttribute("aria-disabled","true"),"undefined"!=typeof this.tabIndex_&&this.el_.removeAttribute("tabIndex"),this.off(["tap","click"],this.handleClick),this.off("focus",this.handleFocus),this.off("blur",this.handleBlur)},e.prototype.handleClick=function(t){},e.prototype.handleFocus=function(t){X(be,"keydown",Ye(this,this.handleKeyPress))},e.prototype.handleKeyPress=function(e){32===e.which||13===e.which?(e.preventDefault(),this.trigger("click")):t.prototype.handleKeyPress&&t.prototype.handleKeyPress.call(this,e)},e.prototype.handleBlur=function(t){q(be,"keydown",Ye(this,this.handleKeyPress))},e}(un);un.registerComponent("ClickableComponent",ui);var hi=function(t){function e(n,r){je(this,e);var i=Pe(this,t.call(this,n,r));return i.update(),n.on("posterchange",Ye(i,i.update)),i}return Ae(e,t),e.prototype.dispose=function(){this.player().off("posterchange",this.update),t.prototype.dispose.call(this)},e.prototype.createEl=function(){var t=v("div",{className:"vjs-poster",tabIndex:-1});return t},e.prototype.update=function(t){var e=this.player().poster();this.setSrc(e),e?this.show():this.hide()},e.prototype.setSrc=function(t){var e="";t&&(e='url("'+t+'")'),this.el_.style.backgroundImage=e},e.prototype.handleClick=function(t){this.player_.controls()&&(this.player_.paused()?ct(this.player_.play()):this.player_.pause())},e}(ui);un.registerComponent("PosterImage",hi);var pi="#222",di="#ccc",fi={monospace:"monospace",sansSerif:"sans-serif",serif:"serif",monospaceSansSerif:'"Andale Mono", "Lucida Console", monospace',monospaceSerif:'"Courier New", monospace',proportionalSansSerif:"sans-serif",proportionalSerif:"serif",casual:'"Comic Sans MS", Impact, fantasy',script:'"Monotype Corsiva", cursive',smallcaps:'"Andale Mono", "Lucida Console", monospace, sans-serif'},vi=function(t){function e(n,r,i){je(this,e);var o=Pe(this,t.call(this,n,r,i));return n.on("loadstart",Ye(o,o.toggleDisplay)),n.on("texttrackchange",Ye(o,o.updateDisplay)),n.on("loadstart",Ye(o,o.preselectTrack)),n.ready(Ye(o,function(){if(n.tech_&&n.tech_.featuresNativeTextTracks)return void this.hide();n.on("fullscreenchange",Ye(this,this.updateDisplay));for(var t=this.options_.playerOptions.tracks||[],e=0;e<t.length;e++)this.player_.addRemoteTextTrack(t[e],!0);this.preselectTrack()})),o}return Ae(e,t),e.prototype.preselectTrack=function(){for(var t={captions:1,subtitles:1},e=this.player_.textTracks(),n=this.player_.cache_.selectedLanguage,r=void 0,i=void 0,o=void 0,s=0;s<e.length;s++){var a=e[s];n&&n.enabled&&n.language===a.language?a.kind===n.kind?o=a:o||(o=a):n&&!n.enabled?(o=null,r=null,i=null):a["default"]&&("descriptions"!==a.kind||r?a.kind in t&&!i&&(i=a):r=a)}o?o.mode="showing":i?i.mode="showing":r&&(r.mode="showing")},e.prototype.toggleDisplay=function(){this.player_.tech_&&this.player_.tech_.featuresNativeTextTracks?this.hide():this.show()},e.prototype.createEl=function(){return t.prototype.createEl.call(this,"div",{className:"vjs-text-track-display"},{"aria-live":"off","aria-atomic":"true"})},e.prototype.clearDisplay=function(){"function"==typeof ve.WebVTT&&ve.WebVTT.processCues(ve,[],this.el_)},e.prototype.updateDisplay=function(){var t=this.player_.textTracks();this.clearDisplay();for(var e=null,n=null,r=t.length;r--;){var i=t[r];"showing"===i.mode&&("descriptions"===i.kind?e=i:n=i)}n?("off"!==this.getAttribute("aria-live")&&this.setAttribute("aria-live","off"),this.updateForTrack(n)):e&&("assertive"!==this.getAttribute("aria-live")&&this.setAttribute("aria-live","assertive"),this.updateForTrack(e))},e.prototype.updateForTrack=function(t){if("function"==typeof ve.WebVTT&&t.activeCues){for(var e=[],n=0;n<t.activeCues.length;n++)e.push(t.activeCues[n]);if(ve.WebVTT.processCues(ve,e,this.el_),this.player_.textTrackSettings)for(var r=this.player_.textTrackSettings.getValues(),i=e.length;i--;){var o=e[i];if(o){var s=o.displayState;if(r.color&&(s.firstChild.style.color=r.color),r.textOpacity&&re(s.firstChild,"color",ne(r.color||"#fff",r.textOpacity)),r.backgroundColor&&(s.firstChild.style.backgroundColor=r.backgroundColor),r.backgroundOpacity&&re(s.firstChild,"backgroundColor",ne(r.backgroundColor||"#000",r.backgroundOpacity)),r.windowColor&&(r.windowOpacity?re(s,"backgroundColor",ne(r.windowColor,r.windowOpacity)):s.style.backgroundColor=r.windowColor),r.edgeStyle&&("dropshadow"===r.edgeStyle?s.firstChild.style.textShadow="2px 2px 3px "+pi+", 2px 2px 4px "+pi+", 2px 2px 5px "+pi:"raised"===r.edgeStyle?s.firstChild.style.textShadow="1px 1px "+pi+", 2px 2px "+pi+", 3px 3px "+pi:"depressed"===r.edgeStyle?s.firstChild.style.textShadow="1px 1px "+di+", 0 1px "+di+", -1px -1px "+pi+", 0 -1px "+pi:"uniform"===r.edgeStyle&&(s.firstChild.style.textShadow="0 0 4px "+pi+", 0 0 4px "+pi+", 0 0 4px "+pi+", 0 0 4px "+pi)),r.fontPercent&&1!==r.fontPercent){var a=ve.parseFloat(s.style.fontSize);s.style.fontSize=a*r.fontPercent+"px",s.style.height="auto",s.style.top="auto",s.style.bottom="2px"}r.fontFamily&&"default"!==r.fontFamily&&("small-caps"===r.fontFamily?s.firstChild.style.fontVariant="small-caps":s.firstChild.style.fontFamily=fi[r.fontFamily])}}}},e}(un);un.registerComponent("TextTrackDisplay",vi);var yi=function(t){function e(){return je(this,e),Pe(this,t.apply(this,arguments))}return Ae(e,t),e.prototype.createEl=function(){var e=this.player_.isAudio(),n=this.localize(e?"Audio Player":"Video Player"),r=v("span",{className:"vjs-control-text",innerHTML:this.localize("{1} is loading.",[n])}),i=t.prototype.createEl.call(this,"div",{className:"vjs-loading-spinner",dir:"ltr"});return i.appendChild(r),i},e}(un);un.registerComponent("LoadingSpinner",yi);var gi=function(t){function e(){return je(this,e),Pe(this,t.apply(this,arguments))}return Ae(e,t),e.prototype.createEl=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};t="button",e=i({innerHTML:'<span aria-hidden="true" class="vjs-icon-placeholder"></span>',className:this.buildCSSClass()},e),n=i({type:"button"},n);var r=un.prototype.createEl.call(this,t,e,n);return this.createControlTextEl(r),r},e.prototype.addChild=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=this.constructor.name;return Ee.warn("Adding an actionable (user controllable) child to a Button ("+n+") is not supported; use a ClickableComponent instead."),un.prototype.addChild.call(this,t,e)},e.prototype.enable=function(){t.prototype.enable.call(this),this.el_.removeAttribute("disabled")},e.prototype.disable=function(){t.prototype.disable.call(this),this.el_.setAttribute("disabled","disabled")},e.prototype.handleKeyPress=function(e){32!==e.which&&13!==e.which&&t.prototype.handleKeyPress.call(this,e)},e}(ui);un.registerComponent("Button",gi);var mi=function(t){function e(n,r){je(this,e);var i=Pe(this,t.call(this,n,r));return i.mouseused_=!1,i.on("mousedown",i.handleMouseDown),i}return Ae(e,t),e.prototype.buildCSSClass=function(){return"vjs-big-play-button"},e.prototype.handleClick=function(t){var e=this.player_.play();if(this.mouseused_&&t.clientX&&t.clientY)return void ct(e);var n=this.player_.getChild("controlBar"),r=n&&n.getChild("playToggle");if(!r)return void this.player_.focus();var i=function(){return r.focus()};lt(e)?e.then(i,function(){}):this.setTimeout(i,1)},e.prototype.handleKeyPress=function(e){this.mouseused_=!1,t.prototype.handleKeyPress.call(this,e)},e.prototype.handleMouseDown=function(t){this.mouseused_=!0},e}(gi);mi.prototype.controlText_="Play Video",un.registerComponent("BigPlayButton",mi);var _i=function(t){function e(n,r){je(this,e);var i=Pe(this,t.call(this,n,r));return i.controlText(r&&r.controlText||i.localize("Close")),i}return Ae(e,t),e.prototype.buildCSSClass=function(){return"vjs-close-button "+t.prototype.buildCSSClass.call(this)},e.prototype.handleClick=function(t){this.trigger({type:"close",bubbles:!1})},e}(gi);un.registerComponent("CloseButton",_i);var bi=function(t){function e(n,r){je(this,e);var i=Pe(this,t.call(this,n,r));return i.on(n,"play",i.handlePlay),i.on(n,"pause",i.handlePause),i.on(n,"ended",i.handleEnded),i}return Ae(e,t),e.prototype.buildCSSClass=function(){return"vjs-play-control "+t.prototype.buildCSSClass.call(this)},e.prototype.handleClick=function(t){this.player_.paused()?this.player_.play():this.player_.pause()},e.prototype.handleSeeked=function(t){this.removeClass("vjs-ended"),this.player_.paused()?this.handlePause(t):this.handlePlay(t)},e.prototype.handlePlay=function(t){this.removeClass("vjs-ended"),this.removeClass("vjs-paused"),this.addClass("vjs-playing"),this.controlText("Pause")},e.prototype.handlePause=function(t){this.removeClass("vjs-playing"),this.addClass("vjs-paused"),this.controlText("Play")},e.prototype.handleEnded=function(t){this.removeClass("vjs-playing"),this.addClass("vjs-ended"),this.controlText("Replay"),this.one(this.player_,"seeked",this.handleSeeked)},e}(gi);bi.prototype.controlText_="Play",un.registerComponent("PlayToggle",bi);var Ti=function(t,e){t=t<0?0:t;var n=Math.floor(t%60),r=Math.floor(t/60%60),i=Math.floor(t/3600),o=Math.floor(e/60%60),s=Math.floor(e/3600);return(isNaN(t)||t===1/0)&&(i=r=n="-"),i=i>0||s>0?i+":":"",r=((i||o>=10)&&r<10?"0"+r:r)+":",n=n<10?"0"+n:n,i+r+n},Ci=Ti,ki=function(t){function e(n,r){je(this,e);var i=Pe(this,t.call(this,n,r));return i.throttledUpdateContent=Ge(Ye(i,i.updateContent),25),i.on(n,"timeupdate",i.throttledUpdateContent),i}return Ae(e,t),e.prototype.createEl=function(e){
var n=this.buildCSSClass(),r=t.prototype.createEl.call(this,"div",{className:n+" vjs-time-control vjs-control",innerHTML:'<span class="vjs-control-text">'+this.localize(this.labelText_)+" </span>"});return this.contentEl_=v("span",{className:n+"-display"},{"aria-live":"off"}),this.updateTextNode_(),r.appendChild(this.contentEl_),r},e.prototype.dispose=function(){this.contentEl_=null,this.textNode_=null,t.prototype.dispose.call(this)},e.prototype.updateTextNode_=function(){if(this.contentEl_){for(;this.contentEl_.firstChild;)this.contentEl_.removeChild(this.contentEl_.firstChild);this.textNode_=be.createTextNode(this.formattedTime_||this.formatTime_(0)),this.contentEl_.appendChild(this.textNode_)}},e.prototype.formatTime_=function(t){return se(t)},e.prototype.updateFormattedTime_=function(t){var e=this.formatTime_(t);e!==this.formattedTime_&&(this.formattedTime_=e,this.requestAnimationFrame(this.updateTextNode_))},e.prototype.updateContent=function(t){},e}(un);ki.prototype.labelText_="Time",ki.prototype.controlText_="Time",un.registerComponent("TimeDisplay",ki);var wi=function(t){function e(n,r){je(this,e);var i=Pe(this,t.call(this,n,r));return i.on(n,"ended",i.handleEnded),i}return Ae(e,t),e.prototype.buildCSSClass=function(){return"vjs-current-time"},e.prototype.updateContent=function(t){var e=this.player_.scrubbing()?this.player_.getCache().currentTime:this.player_.currentTime();this.updateFormattedTime_(e)},e.prototype.handleEnded=function(t){this.player_.duration()&&this.updateFormattedTime_(this.player_.duration())},e}(ki);wi.prototype.labelText_="Current Time",wi.prototype.controlText_="Current Time",un.registerComponent("CurrentTimeDisplay",wi);var Ei=function(t){function e(n,r){je(this,e);var i=Pe(this,t.call(this,n,r));return i.on(n,"durationchange",i.updateContent),i.on(n,"loadedmetadata",i.throttledUpdateContent),i}return Ae(e,t),e.prototype.buildCSSClass=function(){return"vjs-duration"},e.prototype.updateContent=function(t){var e=this.player_.duration();e&&this.duration_!==e&&(this.duration_=e,this.updateFormattedTime_(e))},e}(ki);Ei.prototype.labelText_="Duration",Ei.prototype.controlText_="Duration",un.registerComponent("DurationDisplay",Ei);var Si=function(t){function e(){return je(this,e),Pe(this,t.apply(this,arguments))}return Ae(e,t),e.prototype.createEl=function(){return t.prototype.createEl.call(this,"div",{className:"vjs-time-control vjs-time-divider",innerHTML:"<div><span>/</span></div>"})},e}(un);un.registerComponent("TimeDivider",Si);var xi=function(t){function e(n,r){je(this,e);var i=Pe(this,t.call(this,n,r));return i.on(n,"durationchange",i.throttledUpdateContent),i.on(n,"ended",i.handleEnded),i}return Ae(e,t),e.prototype.buildCSSClass=function(){return"vjs-remaining-time"},e.prototype.formatTime_=function(e){return"-"+t.prototype.formatTime_.call(this,e)},e.prototype.updateContent=function(t){this.player_.duration()&&(this.player_.remainingTimeDisplay?this.updateFormattedTime_(this.player_.remainingTimeDisplay()):this.updateFormattedTime_(this.player_.remainingTime()))},e.prototype.handleEnded=function(t){this.player_.duration()&&this.updateFormattedTime_(0)},e}(ki);xi.prototype.labelText_="Remaining Time",xi.prototype.controlText_="Remaining Time",un.registerComponent("RemainingTimeDisplay",xi);var ji=function(t){function e(n,r){je(this,e);var i=Pe(this,t.call(this,n,r));return i.updateShowing(),i.on(i.player(),"durationchange",i.updateShowing),i}return Ae(e,t),e.prototype.createEl=function(){var e=t.prototype.createEl.call(this,"div",{className:"vjs-live-control vjs-control"});return this.contentEl_=v("div",{className:"vjs-live-display",innerHTML:'<span class="vjs-control-text">'+this.localize("Stream Type")+" </span>"+this.localize("LIVE")},{"aria-live":"off"}),e.appendChild(this.contentEl_),e},e.prototype.dispose=function(){this.contentEl_=null,t.prototype.dispose.call(this)},e.prototype.updateShowing=function(t){this.player().duration()===1/0?this.show():this.hide()},e}(un);un.registerComponent("LiveDisplay",ji);var Ai=function(t){function e(n,r){je(this,e);var i=Pe(this,t.call(this,n,r));return i.bar=i.getChild(i.options_.barName),i.vertical(!!i.options_.vertical),i.enable(),i}return Ae(e,t),e.prototype.enabled=function(){return this.enabled_},e.prototype.enable=function(){this.enabled()||(this.on("mousedown",this.handleMouseDown),this.on("touchstart",this.handleMouseDown),this.on("focus",this.handleFocus),this.on("blur",this.handleBlur),this.on("click",this.handleClick),this.on(this.player_,"controlsvisible",this.update),this.playerEvent&&this.on(this.player_,this.playerEvent,this.update),this.removeClass("disabled"),this.setAttribute("tabindex",0),this.enabled_=!0)},e.prototype.disable=function(){if(this.enabled()){var t=this.bar.el_.ownerDocument;this.off("mousedown",this.handleMouseDown),this.off("touchstart",this.handleMouseDown),this.off("focus",this.handleFocus),this.off("blur",this.handleBlur),this.off("click",this.handleClick),this.off(this.player_,"controlsvisible",this.update),this.off(t,"mousemove",this.handleMouseMove),this.off(t,"mouseup",this.handleMouseUp),this.off(t,"touchmove",this.handleMouseMove),this.off(t,"touchend",this.handleMouseUp),this.removeAttribute("tabindex"),this.addClass("disabled"),this.playerEvent&&this.off(this.player_,this.playerEvent,this.update),this.enabled_=!1}},e.prototype.createEl=function(e){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return n.className=n.className+" vjs-slider",n=i({tabIndex:0},n),r=i({role:"slider","aria-valuenow":0,"aria-valuemin":0,"aria-valuemax":100,tabIndex:0},r),t.prototype.createEl.call(this,e,n,r)},e.prototype.handleMouseDown=function(t){var e=this.bar.el_.ownerDocument;"mousedown"===t.type&&t.preventDefault(),"touchstart"!==t.type||wn||t.preventDefault(),x(),this.addClass("vjs-sliding"),this.trigger("slideractive"),this.on(e,"mousemove",this.handleMouseMove),this.on(e,"mouseup",this.handleMouseUp),this.on(e,"touchmove",this.handleMouseMove),this.on(e,"touchend",this.handleMouseUp),this.handleMouseMove(t)},e.prototype.handleMouseMove=function(t){},e.prototype.handleMouseUp=function(){var t=this.bar.el_.ownerDocument;j(),this.removeClass("vjs-sliding"),this.trigger("sliderinactive"),this.off(t,"mousemove",this.handleMouseMove),this.off(t,"mouseup",this.handleMouseUp),this.off(t,"touchmove",this.handleMouseMove),this.off(t,"touchend",this.handleMouseUp),this.update()},e.prototype.update=function(){if(this.el_){var t=this.getPercent(),e=this.bar;if(e){("number"!=typeof t||t!==t||t<0||t===1/0)&&(t=0);var n=(100*t).toFixed(2)+"%",r=e.el().style;return this.vertical()?r.height=n:r.width=n,t}}},e.prototype.calculateDistance=function(t){var e=M(this.el_,t);return this.vertical()?e.y:e.x},e.prototype.handleFocus=function(){this.on(this.bar.el_.ownerDocument,"keydown",this.handleKeyPress)},e.prototype.handleKeyPress=function(t){37===t.which||40===t.which?(t.preventDefault(),this.stepBack()):38!==t.which&&39!==t.which||(t.preventDefault(),this.stepForward())},e.prototype.handleBlur=function(){this.off(this.bar.el_.ownerDocument,"keydown",this.handleKeyPress)},e.prototype.handleClick=function(t){t.stopImmediatePropagation(),t.preventDefault()},e.prototype.vertical=function(t){return void 0===t?this.vertical_||!1:(this.vertical_=!!t,void(this.vertical_?this.addClass("vjs-slider-vertical"):this.addClass("vjs-slider-horizontal")))},e}(un);un.registerComponent("Slider",Ai);var Pi=function(t){function e(n,r){je(this,e);var i=Pe(this,t.call(this,n,r));return i.partEls_=[],i.on(n,"progress",i.update),i}return Ae(e,t),e.prototype.createEl=function(){return t.prototype.createEl.call(this,"div",{className:"vjs-load-progress",innerHTML:'<span class="vjs-control-text"><span>'+this.localize("Loaded")+"</span>: 0%</span>"})},e.prototype.dispose=function(){this.partEls_=null,t.prototype.dispose.call(this)},e.prototype.update=function(t){var e=this.player_.buffered(),n=this.player_.duration(),r=this.player_.bufferedEnd(),i=this.partEls_,o=function(t,e){var n=t/e||0;return 100*(n>=1?1:n)+"%"};this.el_.style.width=o(r,n);for(var s=0;s<e.length;s++){var a=e.start(s),l=e.end(s),c=i[s];c||(c=this.el_.appendChild(v()),i[s]=c),c.style.left=o(a,r),c.style.width=o(l-a,r)}for(var u=i.length;u>e.length;u--)this.el_.removeChild(i[u-1]);i.length=e.length},e}(un);un.registerComponent("LoadProgressBar",Pi);var Mi=function(t){function e(){return je(this,e),Pe(this,t.apply(this,arguments))}return Ae(e,t),e.prototype.createEl=function(){return t.prototype.createEl.call(this,"div",{className:"vjs-time-tooltip"})},e.prototype.update=function(t,e,n){var r=A(this.el_),i=A(this.player_.el()),o=t.width*e;if(i&&r){var s=t.left-i.left+o,a=t.width-o+(i.right-t.right),l=r.width/2;s<l?l+=l-s:a<l&&(l=a),l<0?l=0:l>r.width&&(l=r.width),this.el_.style.right="-"+l+"px",y(this.el_,n)}},e}(un);un.registerComponent("TimeTooltip",Mi);var Oi=function(t){function e(){return je(this,e),Pe(this,t.apply(this,arguments))}return Ae(e,t),e.prototype.createEl=function(){return t.prototype.createEl.call(this,"div",{className:"vjs-play-progress vjs-slider-bar",innerHTML:'<span class="vjs-control-text"><span>'+this.localize("Progress")+"</span>: 0%</span>"})},e.prototype.update=function(t,e){var n=this;this.rafId_&&this.cancelAnimationFrame(this.rafId_),this.rafId_=this.requestAnimationFrame(function(){var r=n.player_.scrubbing()?n.player_.getCache().currentTime:n.player_.currentTime(),i=se(r,n.player_.duration()),o=n.getChild("timeTooltip");o&&o.update(t,e,i)})},e}(un);Oi.prototype.options_={children:[]},gn||_n||Oi.prototype.options_.children.push("timeTooltip"),un.registerComponent("PlayProgressBar",Oi);var Ni=function(t){function e(n,r){je(this,e);var i=Pe(this,t.call(this,n,r));return i.update=Ge(Ye(i,i.update),25),i}return Ae(e,t),e.prototype.createEl=function(){return t.prototype.createEl.call(this,"div",{className:"vjs-mouse-display"})},e.prototype.update=function(t,e){var n=this;this.rafId_&&this.cancelAnimationFrame(this.rafId_),this.rafId_=this.requestAnimationFrame(function(){var r=n.player_.duration(),i=se(e*r,r);n.el_.style.left=t.width*e+"px",n.getChild("timeTooltip").update(t,e,i)})},e}(un);Ni.prototype.options_={children:["timeTooltip"]},un.registerComponent("MouseTimeDisplay",Ni);var Ii=5,Li=30,Di=function(t){function e(n,r){je(this,e);var i=Pe(this,t.call(this,n,r));return i.setEventHandlers_(),i}return Ae(e,t),e.prototype.setEventHandlers_=function(){var t=this;this.update=Ge(Ye(this,this.update),Li),this.on(this.player_,"timeupdate",this.update),this.on(this.player_,"ended",this.handleEnded),this.updateInterval=null,this.on(this.player_,["playing"],function(){t.clearInterval(t.updateInterval),t.updateInterval=t.setInterval(function(){t.requestAnimationFrame(function(){t.update()})},Li)}),this.on(this.player_,["ended","pause","waiting"],function(){t.clearInterval(t.updateInterval)}),this.on(this.player_,["timeupdate","ended"],this.update)},e.prototype.createEl=function(){return t.prototype.createEl.call(this,"div",{className:"vjs-progress-holder"},{"aria-label":this.localize("Progress Bar")})},e.prototype.update_=function(t,e){var n=this.player_.duration();this.el_.setAttribute("aria-valuenow",(100*e).toFixed(2)),this.el_.setAttribute("aria-valuetext",this.localize("progress bar timing: currentTime={1} duration={2}",[se(t,n),se(n,n)],"{1} of {2}")),this.bar.update(A(this.el_),e)},e.prototype.update=function(e){var n=t.prototype.update.call(this);return this.update_(this.getCurrentTime_(),n),n},e.prototype.getCurrentTime_=function(){return this.player_.scrubbing()?this.player_.getCache().currentTime:this.player_.currentTime()},e.prototype.handleEnded=function(t){this.update_(this.player_.duration(),1)},e.prototype.getPercent=function(){var t=this.getCurrentTime_()/this.player_.duration();return t>=1?1:t||0},e.prototype.handleMouseDown=function(e){R(e)&&(e.stopPropagation(),this.player_.scrubbing(!0),this.videoWasPlaying=!this.player_.paused(),this.player_.pause(),t.prototype.handleMouseDown.call(this,e))},e.prototype.handleMouseMove=function(t){if(R(t)){var e=this.calculateDistance(t)*this.player_.duration();e===this.player_.duration()&&(e-=.1),this.player_.currentTime(e)}},e.prototype.enable=function(){t.prototype.enable.call(this);var e=this.getChild("mouseTimeDisplay");e&&e.show()},e.prototype.disable=function(){t.prototype.disable.call(this);var e=this.getChild("mouseTimeDisplay");e&&e.hide()},e.prototype.handleMouseUp=function(e){t.prototype.handleMouseUp.call(this,e),e&&e.stopPropagation(),this.player_.scrubbing(!1),this.player_.trigger({type:"timeupdate",target:this,manuallyTriggered:!0}),this.videoWasPlaying&&ct(this.player_.play())},e.prototype.stepForward=function(){this.player_.currentTime(this.player_.currentTime()+Ii)},e.prototype.stepBack=function(){this.player_.currentTime(this.player_.currentTime()-Ii)},e.prototype.handleAction=function(t){this.player_.paused()?this.player_.play():this.player_.pause()},e.prototype.handleKeyPress=function(e){32===e.which||13===e.which?(e.preventDefault(),this.handleAction(e)):t.prototype.handleKeyPress&&t.prototype.handleKeyPress.call(this,e)},e}(Ai);Di.prototype.options_={children:["loadProgressBar","playProgressBar"],barName:"playProgressBar"},gn||_n||Di.prototype.options_.children.splice(1,0,"mouseTimeDisplay"),Di.prototype.playerEvent="timeupdate",un.registerComponent("SeekBar",Di);var Ri=function(t){function e(n,r){je(this,e);var i=Pe(this,t.call(this,n,r));return i.handleMouseMove=Ge(Ye(i,i.handleMouseMove),25),i.throttledHandleMouseSeek=Ge(Ye(i,i.handleMouseSeek),25),i.enable(),i}return Ae(e,t),e.prototype.createEl=function(){return t.prototype.createEl.call(this,"div",{className:"vjs-progress-control vjs-control"})},e.prototype.handleMouseMove=function(t){var e=this.getChild("seekBar");if(e){var n=e.getChild("mouseTimeDisplay"),r=e.el(),i=A(r),o=M(r,t).x;o>1?o=1:o<0&&(o=0),n&&n.update(i,o)}},e.prototype.handleMouseSeek=function(t){var e=this.getChild("seekBar");e&&e.handleMouseMove(t)},e.prototype.enabled=function(){return this.enabled_},e.prototype.disable=function(){this.children().forEach(function(t){return t.disable&&t.disable()}),this.enabled()&&(this.off(["mousedown","touchstart"],this.handleMouseDown),this.off(this.el_,"mousemove",this.handleMouseMove),this.handleMouseUp(),this.addClass("disabled"),this.enabled_=!1)},e.prototype.enable=function(){this.children().forEach(function(t){return t.enable&&t.enable()}),this.enabled()||(this.on(["mousedown","touchstart"],this.handleMouseDown),this.on(this.el_,"mousemove",this.handleMouseMove),this.removeClass("disabled"),this.enabled_=!0)},e.prototype.handleMouseDown=function(t){var e=this.el_.ownerDocument,n=this.getChild("seekBar");n&&n.handleMouseDown(t),this.on(e,"mousemove",this.throttledHandleMouseSeek),this.on(e,"touchmove",this.throttledHandleMouseSeek),this.on(e,"mouseup",this.handleMouseUp),this.on(e,"touchend",this.handleMouseUp)},e.prototype.handleMouseUp=function(t){var e=this.el_.ownerDocument,n=this.getChild("seekBar");n&&n.handleMouseUp(t),this.off(e,"mousemove",this.throttledHandleMouseSeek),this.off(e,"touchmove",this.throttledHandleMouseSeek),this.off(e,"mouseup",this.handleMouseUp),this.off(e,"touchend",this.handleMouseUp)},e}(un);Ri.prototype.options_={children:["seekBar"]},un.registerComponent("ProgressControl",Ri);var Fi=function(t){function e(n,r){je(this,e);var i=Pe(this,t.call(this,n,r));return i.on(n,"fullscreenchange",i.handleFullscreenChange),be[Mn.fullscreenEnabled]===!1&&i.disable(),i}return Ae(e,t),e.prototype.buildCSSClass=function(){return"vjs-fullscreen-control "+t.prototype.buildCSSClass.call(this)},e.prototype.handleFullscreenChange=function(t){this.player_.isFullscreen()?this.controlText("Non-Fullscreen"):this.controlText("Fullscreen")},e.prototype.handleClick=function(t){this.player_.isFullscreen()?this.player_.exitFullscreen():this.player_.requestFullscreen()},e}(gi);Fi.prototype.controlText_="Fullscreen",un.registerComponent("FullscreenToggle",Fi);var Bi=function(t,e){e.tech_&&!e.tech_.featuresVolumeControl&&t.addClass("vjs-hidden"),t.on(e,"loadstart",function(){e.tech_.featuresVolumeControl?t.removeClass("vjs-hidden"):t.addClass("vjs-hidden")})},Hi=function(t){function e(){return je(this,e),Pe(this,t.apply(this,arguments))}return Ae(e,t),e.prototype.createEl=function(){return t.prototype.createEl.call(this,"div",{className:"vjs-volume-level",innerHTML:'<span class="vjs-control-text"></span>'})},e}(un);un.registerComponent("VolumeLevel",Hi);var Vi=function(t){function e(n,r){je(this,e);var i=Pe(this,t.call(this,n,r));return i.on("slideractive",i.updateLastVolume_),i.on(n,"volumechange",i.updateARIAAttributes),n.ready(function(){return i.updateARIAAttributes()}),i}return Ae(e,t),e.prototype.createEl=function(){return t.prototype.createEl.call(this,"div",{className:"vjs-volume-bar vjs-slider-bar"},{"aria-label":this.localize("Volume Level"),"aria-live":"polite"})},e.prototype.handleMouseDown=function(e){R(e)&&t.prototype.handleMouseDown.call(this,e)},e.prototype.handleMouseMove=function(t){R(t)&&(this.checkMuted(),this.player_.volume(this.calculateDistance(t)))},e.prototype.checkMuted=function(){this.player_.muted()&&this.player_.muted(!1)},e.prototype.getPercent=function(){return this.player_.muted()?0:this.player_.volume()},e.prototype.stepForward=function(){this.checkMuted(),this.player_.volume(this.player_.volume()+.1)},e.prototype.stepBack=function(){this.checkMuted(),this.player_.volume(this.player_.volume()-.1)},e.prototype.updateARIAAttributes=function(t){var e=this.player_.muted()?0:this.volumeAsPercentage_();this.el_.setAttribute("aria-valuenow",e),this.el_.setAttribute("aria-valuetext",e+"%")},e.prototype.volumeAsPercentage_=function(){return Math.round(100*this.player_.volume())},e.prototype.updateLastVolume_=function(){var t=this,e=this.player_.volume();this.one("sliderinactive",function(){0===t.player_.volume()&&t.player_.lastVolume_(e)})},e}(Ai);Vi.prototype.options_={children:["volumeLevel"],barName:"volumeLevel"},Vi.prototype.playerEvent="volumechange",un.registerComponent("VolumeBar",Vi);var zi=function(t){function e(n){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};je(this,e),r.vertical=r.vertical||!1,("undefined"==typeof r.volumeBar||s(r.volumeBar))&&(r.volumeBar=r.volumeBar||{},r.volumeBar.vertical=r.vertical);var i=Pe(this,t.call(this,n,r));return Bi(i,n),i.throttledHandleMouseMove=Ge(Ye(i,i.handleMouseMove),25),i.on("mousedown",i.handleMouseDown),i.on("touchstart",i.handleMouseDown),i.on(i.volumeBar,["focus","slideractive"],function(){i.volumeBar.addClass("vjs-slider-active"),i.addClass("vjs-slider-active"),i.trigger("slideractive")}),i.on(i.volumeBar,["blur","sliderinactive"],function(){i.volumeBar.removeClass("vjs-slider-active"),i.removeClass("vjs-slider-active"),i.trigger("sliderinactive")}),i}return Ae(e,t),e.prototype.createEl=function(){var e="vjs-volume-horizontal";return this.options_.vertical&&(e="vjs-volume-vertical"),t.prototype.createEl.call(this,"div",{className:"vjs-volume-control vjs-control "+e})},e.prototype.handleMouseDown=function(t){var e=this.el_.ownerDocument;this.on(e,"mousemove",this.throttledHandleMouseMove),this.on(e,"touchmove",this.throttledHandleMouseMove),this.on(e,"mouseup",this.handleMouseUp),this.on(e,"touchend",this.handleMouseUp)},e.prototype.handleMouseUp=function(t){var e=this.el_.ownerDocument;this.off(e,"mousemove",this.throttledHandleMouseMove),this.off(e,"touchmove",this.throttledHandleMouseMove),this.off(e,"mouseup",this.handleMouseUp),this.off(e,"touchend",this.handleMouseUp)},e.prototype.handleMouseMove=function(t){this.volumeBar.handleMouseMove(t)},e}(un);zi.prototype.options_={children:["volumeBar"]},un.registerComponent("VolumeControl",zi);var Wi=function(t,e){e.tech_&&!e.tech_.featuresMuteControl&&t.addClass("vjs-hidden"),t.on(e,"loadstart",function(){e.tech_.featuresMuteControl?t.removeClass("vjs-hidden"):t.addClass("vjs-hidden")})},Ui=function(t){function e(n,r){je(this,e);var i=Pe(this,t.call(this,n,r));return Wi(i,n),i.on(n,["loadstart","volumechange"],i.update),i}return Ae(e,t),e.prototype.buildCSSClass=function(){return"vjs-mute-control "+t.prototype.buildCSSClass.call(this)},e.prototype.handleClick=function(t){var e=this.player_.volume(),n=this.player_.lastVolume_();if(0===e){var r=n<.1?.1:n;this.player_.volume(r),this.player_.muted(!1)}else this.player_.muted(!this.player_.muted())},e.prototype.update=function(t){this.updateIcon_(),this.updateControlText_()},e.prototype.updateIcon_=function(){var t=this.player_.volume(),e=3;gn&&this.player_.muted(this.player_.tech_.el_.muted),0===t||this.player_.muted()?e=0:t<.33?e=1:t<.67&&(e=2);for(var n=0;n<4;n++)b(this.el_,"vjs-vol-"+n);_(this.el_,"vjs-vol-"+e)},e.prototype.updateControlText_=function(){var t=this.player_.muted()||0===this.player_.volume(),e=t?"Unmute":"Mute";this.controlText()!==e&&this.controlText(e)},e}(gi);Ui.prototype.controlText_="Mute",un.registerComponent("MuteToggle",Ui);var Xi=function(t){function e(n){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};je(this,e),"undefined"!=typeof r.inline?r.inline=r.inline:r.inline=!0,("undefined"==typeof r.volumeControl||s(r.volumeControl))&&(r.volumeControl=r.volumeControl||{},r.volumeControl.vertical=!r.inline);var i=Pe(this,t.call(this,n,r));return i.on(n,["loadstart"],i.volumePanelState_),i.on(i.volumeControl,["slideractive"],i.sliderActive_),i.on(i.volumeControl,["sliderinactive"],i.sliderInactive_),i}return Ae(e,t),e.prototype.sliderActive_=function(){this.addClass("vjs-slider-active")},e.prototype.sliderInactive_=function(){this.removeClass("vjs-slider-active")},e.prototype.volumePanelState_=function(){this.volumeControl.hasClass("vjs-hidden")&&this.muteToggle.hasClass("vjs-hidden")&&this.addClass("vjs-hidden"),this.volumeControl.hasClass("vjs-hidden")&&!this.muteToggle.hasClass("vjs-hidden")&&this.addClass("vjs-mute-toggle-only")},e.prototype.createEl=function(){var e="vjs-volume-panel-horizontal";return this.options_.inline||(e="vjs-volume-panel-vertical"),t.prototype.createEl.call(this,"div",{className:"vjs-volume-panel vjs-control "+e})},e}(un);Xi.prototype.options_={children:["muteToggle","volumeControl"]},un.registerComponent("VolumePanel",Xi);var qi=function(t){function e(n,r){je(this,e);var i=Pe(this,t.call(this,n,r));return r&&(i.menuButton_=r.menuButton),i.focusedChild_=-1,i.on("keydown",i.handleKeyPress),i}return Ae(e,t),e.prototype.addItem=function(t){this.addChild(t),t.on("click",Ye(this,function(e){this.menuButton_&&(this.menuButton_.unpressButton(),"CaptionSettingsMenuItem"!==t.name()&&this.menuButton_.focus())}))},e.prototype.createEl=function(){var e=this.options_.contentElType||"ul";this.contentEl_=v(e,{className:"vjs-menu-content"}),this.contentEl_.setAttribute("role","menu");var n=t.prototype.createEl.call(this,"div",{append:this.contentEl_,className:"vjs-menu"});return n.appendChild(this.contentEl_),X(n,"click",function(t){t.preventDefault(),t.stopImmediatePropagation()}),n},e.prototype.dispose=function(){this.contentEl_=null,t.prototype.dispose.call(this)},e.prototype.handleKeyPress=function(t){37===t.which||40===t.which?(t.preventDefault(),this.stepForward()):38!==t.which&&39!==t.which||(t.preventDefault(),this.stepBack())},e.prototype.stepForward=function(){var t=0;void 0!==this.focusedChild_&&(t=this.focusedChild_+1),this.focus(t)},e.prototype.stepBack=function(){var t=0;void 0!==this.focusedChild_&&(t=this.focusedChild_-1),this.focus(t)},e.prototype.focus=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,e=this.children().slice(),n=e.length&&e[0].className&&/vjs-menu-title/.test(e[0].className);n&&e.shift(),e.length>0&&(t<0?t=0:t>=e.length&&(t=e.length-1),this.focusedChild_=t,e[t].el_.focus())},e}(un);un.registerComponent("Menu",qi);var Ki=function(t){function e(n){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};je(this,e);var i=Pe(this,t.call(this,n,r));i.menuButton_=new gi(n,r),i.menuButton_.controlText(i.controlText_),i.menuButton_.el_.setAttribute("aria-haspopup","true");var o=gi.prototype.buildCSSClass();return i.menuButton_.el_.className=i.buildCSSClass()+" "+o,i.menuButton_.removeClass("vjs-control"),i.addChild(i.menuButton_),i.update(),i.enabled_=!0,i.on(i.menuButton_,"tap",i.handleClick),i.on(i.menuButton_,"click",i.handleClick),i.on(i.menuButton_,"focus",i.handleFocus),i.on(i.menuButton_,"blur",i.handleBlur),i.on("keydown",i.handleSubmenuKeyPress),i}return Ae(e,t),e.prototype.update=function(){var t=this.createMenu();this.menu&&(this.menu.dispose(),this.removeChild(this.menu)),this.menu=t,this.addChild(t),this.buttonPressed_=!1,this.menuButton_.el_.setAttribute("aria-expanded","false"),this.items&&this.items.length<=this.hideThreshold_?this.hide():this.show()},e.prototype.createMenu=function(){var t=new qi(this.player_,{menuButton:this});if(this.hideThreshold_=0,this.options_.title){var e=v("li",{className:"vjs-menu-title",innerHTML:Q(this.options_.title),tabIndex:-1});this.hideThreshold_+=1,t.children_.unshift(e),g(e,t.contentEl())}if(this.items=this.createItems(),this.items)for(var n=0;n<this.items.length;n++)t.addItem(this.items[n]);return t},e.prototype.createItems=function(){},e.prototype.createEl=function(){return t.prototype.createEl.call(this,"div",{className:this.buildWrapperCSSClass()},{})},e.prototype.buildWrapperCSSClass=function(){var e="vjs-menu-button";e+=this.options_.inline===!0?"-inline":"-popup";var n=gi.prototype.buildCSSClass();return"vjs-menu-button "+e+" "+n+" "+t.prototype.buildCSSClass.call(this)},e.prototype.buildCSSClass=function(){var e="vjs-menu-button";return e+=this.options_.inline===!0?"-inline":"-popup","vjs-menu-button "+e+" "+t.prototype.buildCSSClass.call(this)},e.prototype.controlText=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.menuButton_.el();return this.menuButton_.controlText(t,e)},e.prototype.handleClick=function(t){this.one(this.menu.contentEl(),"mouseleave",Ye(this,function(t){this.unpressButton(),this.el_.blur()})),this.buttonPressed_?this.unpressButton():this.pressButton()},e.prototype.focus=function(){this.menuButton_.focus()},e.prototype.blur=function(){this.menuButton_.blur()},e.prototype.handleFocus=function(){X(be,"keydown",Ye(this,this.handleKeyPress))},e.prototype.handleBlur=function(){q(be,"keydown",Ye(this,this.handleKeyPress))},e.prototype.handleKeyPress=function(t){27===t.which||9===t.which?(this.buttonPressed_&&this.unpressButton(),9!==t.which&&(t.preventDefault(),this.menuButton_.el_.focus())):38!==t.which&&40!==t.which||this.buttonPressed_||(this.pressButton(),t.preventDefault())},e.prototype.handleSubmenuKeyPress=function(t){27!==t.which&&9!==t.which||(this.buttonPressed_&&this.unpressButton(),9!==t.which&&(t.preventDefault(),this.menuButton_.el_.focus()))},e.prototype.pressButton=function(){if(this.enabled_){if(this.buttonPressed_=!0,this.menu.lockShowing(),this.menuButton_.el_.setAttribute("aria-expanded","true"),gn&&d())return;this.menu.focus()}},e.prototype.unpressButton=function(){this.enabled_&&(this.buttonPressed_=!1,this.menu.unlockShowing(),this.menuButton_.el_.setAttribute("aria-expanded","false"))},e.prototype.disable=function(){this.unpressButton(),this.enabled_=!1,this.addClass("vjs-disabled"),this.menuButton_.disable()},e.prototype.enable=function(){this.enabled_=!0,this.removeClass("vjs-disabled"),this.menuButton_.enable()},e}(un);un.registerComponent("MenuButton",Ki);var $i=function(t){function e(n,r){je(this,e);var i=r.tracks,o=Pe(this,t.call(this,n,r));if(o.items.length<=1&&o.hide(),!i)return Pe(o);var s=Ye(o,o.update);return i.addEventListener("removetrack",s),i.addEventListener("addtrack",s),o.player_.on("ready",s),o.player_.on("dispose",function(){i.removeEventListener("removetrack",s),i.removeEventListener("addtrack",s)}),o}return Ae(e,t),e}(Ki);un.registerComponent("TrackButton",$i);var Yi=function(t){function e(n,r){je(this,e);var i=Pe(this,t.call(this,n,r));return i.selectable=r.selectable,i.isSelected_=r.selected||!1,i.multiSelectable=r.multiSelectable,i.selected(i.isSelected_),i.selectable?i.multiSelectable?i.el_.setAttribute("role","menuitemcheckbox"):i.el_.setAttribute("role","menuitemradio"):i.el_.setAttribute("role","menuitem"),i}return Ae(e,t),e.prototype.createEl=function(e,n,r){return this.nonIconControl=!0,t.prototype.createEl.call(this,"li",i({className:"vjs-menu-item",innerHTML:'<span class="vjs-menu-item-text">'+this.localize(this.options_.label)+"</span>",tabIndex:-1},n),r)},e.prototype.handleClick=function(t){this.selected(!0)},e.prototype.selected=function(t){this.selectable&&(t?(this.addClass("vjs-selected"),this.el_.setAttribute("aria-checked","true"),this.controlText(", selected"),this.isSelected_=!0):(this.removeClass("vjs-selected"),this.el_.setAttribute("aria-checked","false"),this.controlText(""),this.isSelected_=!1))},e}(ui);un.registerComponent("MenuItem",Yi);var Gi=function(t){function e(n,r){je(this,e);var i=r.track,o=n.textTracks();r.label=i.label||i.language||"Unknown",r.selected="showing"===i.mode;var s=Pe(this,t.call(this,n,r));s.track=i;var a=function(){for(var t=arguments.length,e=Array(t),n=0;n<t;n++)e[n]=arguments[n];s.handleTracksChange.apply(s,e)},l=function(){for(var t=arguments.length,e=Array(t),n=0;n<t;n++)e[n]=arguments[n];s.handleSelectedLanguageChange.apply(s,e)};if(n.on(["loadstart","texttrackchange"],a),o.addEventListener("change",a),o.addEventListener("selectedlanguagechange",l),s.on("dispose",function(){n.off(["loadstart","texttrackchange"],a),o.removeEventListener("change",a),o.removeEventListener("selectedlanguagechange",l)}),void 0===o.onchange){var c=void 0;s.on(["tap","click"],function(){if("object"!==xe(ve.Event))try{c=new ve.Event("change")}catch(t){}c||(c=be.createEvent("Event"),c.initEvent("change",!0,!0)),o.dispatchEvent(c)})}return s.handleTracksChange(),s}return Ae(e,t),e.prototype.handleClick=function(e){var n=this.track.kind,r=this.track.kinds,i=this.player_.textTracks();if(r||(r=[n]),t.prototype.handleClick.call(this,e),i)for(var o=0;o<i.length;o++){var s=i[o];s===this.track&&r.indexOf(s.kind)>-1?"showing"!==s.mode&&(s.mode="showing"):"disabled"!==s.mode&&(s.mode="disabled")}},e.prototype.handleTracksChange=function(t){var e="showing"===this.track.mode;e!==this.isSelected_&&this.selected(e)},e.prototype.handleSelectedLanguageChange=function(t){if("showing"===this.track.mode){var e=this.player_.cache_.selectedLanguage;if(e&&e.enabled&&e.language===this.track.language&&e.kind!==this.track.kind)return;this.player_.cache_.selectedLanguage={enabled:!0,language:this.track.language,kind:this.track.kind}}},e.prototype.dispose=function(){this.track=null,t.prototype.dispose.call(this)},e}(Yi);un.registerComponent("TextTrackMenuItem",Gi);var Ji=function(t){function e(n,r){return je(this,e),r.track={player:n,kind:r.kind,kinds:r.kinds,"default":!1,mode:"disabled"},r.kinds||(r.kinds=[r.kind]),r.label?r.track.label=r.label:r.track.label=r.kinds.join(" and ")+" off",r.selectable=!0,r.multiSelectable=!1,Pe(this,t.call(this,n,r))}return Ae(e,t),e.prototype.handleTracksChange=function(t){for(var e=this.player().textTracks(),n=!0,r=0,i=e.length;r<i;r++){var o=e[r];if(this.options_.kinds.indexOf(o.kind)>-1&&"showing"===o.mode){n=!1;break}}n!==this.isSelected_&&this.selected(n)},e.prototype.handleSelectedLanguageChange=function(t){for(var e=this.player().textTracks(),n=!0,r=0,i=e.length;r<i;r++){var o=e[r];if(["captions","descriptions","subtitles"].indexOf(o.kind)>-1&&"showing"===o.mode){n=!1;break}}n&&(this.player_.cache_.selectedLanguage={enabled:!1})},e}(Gi);un.registerComponent("OffTextTrackMenuItem",Ji);var Qi=function(t){function e(n){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return je(this,e),r.tracks=n.textTracks(),Pe(this,t.call(this,n,r))}return Ae(e,t),e.prototype.createItems=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:Gi,n=void 0;this.label_&&(n=this.label_+" off"),
t.push(new Ji(this.player_,{kinds:this.kinds_,kind:this.kind_,label:n})),this.hideThreshold_+=1;var r=this.player_.textTracks();Array.isArray(this.kinds_)||(this.kinds_=[this.kind_]);for(var i=0;i<r.length;i++){var o=r[i];if(this.kinds_.indexOf(o.kind)>-1){var s=new e(this.player_,{track:o,selectable:!0,multiSelectable:!1});s.addClass("vjs-"+o.kind+"-menu-item"),t.push(s)}}return t},e}($i);un.registerComponent("TextTrackButton",Qi);var Zi=function(t){function e(n,r){je(this,e);var i=r.track,o=r.cue,s=n.currentTime();r.selectable=!0,r.multiSelectable=!1,r.label=o.text,r.selected=o.startTime<=s&&s<o.endTime;var a=Pe(this,t.call(this,n,r));return a.track=i,a.cue=o,i.addEventListener("cuechange",Ye(a,a.update)),a}return Ae(e,t),e.prototype.handleClick=function(e){t.prototype.handleClick.call(this),this.player_.currentTime(this.cue.startTime),this.update(this.cue.startTime)},e.prototype.update=function(t){var e=this.cue,n=this.player_.currentTime();this.selected(e.startTime<=n&&n<e.endTime)},e}(Yi);un.registerComponent("ChaptersTrackMenuItem",Zi);var to=function(t){function e(n,r,i){return je(this,e),Pe(this,t.call(this,n,r,i))}return Ae(e,t),e.prototype.buildCSSClass=function(){return"vjs-chapters-button "+t.prototype.buildCSSClass.call(this)},e.prototype.buildWrapperCSSClass=function(){return"vjs-chapters-button "+t.prototype.buildWrapperCSSClass.call(this)},e.prototype.update=function(e){this.track_&&(!e||"addtrack"!==e.type&&"removetrack"!==e.type)||this.setTrack(this.findChaptersTrack()),t.prototype.update.call(this)},e.prototype.setTrack=function(t){if(this.track_!==t){if(this.updateHandler_||(this.updateHandler_=this.update.bind(this)),this.track_){var e=this.player_.remoteTextTrackEls().getTrackElementByTrack_(this.track_);e&&e.removeEventListener("load",this.updateHandler_),this.track_=null}if(this.track_=t,this.track_){this.track_.mode="hidden";var n=this.player_.remoteTextTrackEls().getTrackElementByTrack_(this.track_);n&&n.addEventListener("load",this.updateHandler_)}}},e.prototype.findChaptersTrack=function(){for(var t=this.player_.textTracks()||[],e=t.length-1;e>=0;e--){var n=t[e];if(n.kind===this.kind_)return n}},e.prototype.getMenuCaption=function(){return this.track_&&this.track_.label?this.track_.label:this.localize(Q(this.kind_))},e.prototype.createMenu=function(){return this.options_.title=this.getMenuCaption(),t.prototype.createMenu.call(this)},e.prototype.createItems=function(){var t=[];if(!this.track_)return t;var e=this.track_.cues;if(!e)return t;for(var n=0,r=e.length;n<r;n++){var i=e[n],o=new Zi(this.player_,{track:this.track_,cue:i});t.push(o)}return t},e}(Qi);to.prototype.kind_="chapters",to.prototype.controlText_="Chapters",un.registerComponent("ChaptersButton",to);var eo=function(t){function e(n,r,i){je(this,e);var o=Pe(this,t.call(this,n,r,i)),s=n.textTracks(),a=Ye(o,o.handleTracksChange);return s.addEventListener("change",a),o.on("dispose",function(){s.removeEventListener("change",a)}),o}return Ae(e,t),e.prototype.handleTracksChange=function(t){for(var e=this.player().textTracks(),n=!1,r=0,i=e.length;r<i;r++){var o=e[r];if(o.kind!==this.kind_&&"showing"===o.mode){n=!0;break}}n?this.disable():this.enable()},e.prototype.buildCSSClass=function(){return"vjs-descriptions-button "+t.prototype.buildCSSClass.call(this)},e.prototype.buildWrapperCSSClass=function(){return"vjs-descriptions-button "+t.prototype.buildWrapperCSSClass.call(this)},e}(Qi);eo.prototype.kind_="descriptions",eo.prototype.controlText_="Descriptions",un.registerComponent("DescriptionsButton",eo);var no=function(t){function e(n,r,i){return je(this,e),Pe(this,t.call(this,n,r,i))}return Ae(e,t),e.prototype.buildCSSClass=function(){return"vjs-subtitles-button "+t.prototype.buildCSSClass.call(this)},e.prototype.buildWrapperCSSClass=function(){return"vjs-subtitles-button "+t.prototype.buildWrapperCSSClass.call(this)},e}(Qi);no.prototype.kind_="subtitles",no.prototype.controlText_="Subtitles",un.registerComponent("SubtitlesButton",no);var ro=function(t){function e(n,r){je(this,e),r.track={player:n,kind:r.kind,label:r.kind+" settings",selectable:!1,"default":!1,mode:"disabled"},r.selectable=!1,r.name="CaptionSettingsMenuItem";var i=Pe(this,t.call(this,n,r));return i.addClass("vjs-texttrack-settings"),i.controlText(", opens "+r.kind+" settings dialog"),i}return Ae(e,t),e.prototype.handleClick=function(t){this.player().getChild("textTrackSettings").open()},e}(Gi);un.registerComponent("CaptionSettingsMenuItem",ro);var io=function(t){function e(n,r,i){return je(this,e),Pe(this,t.call(this,n,r,i))}return Ae(e,t),e.prototype.buildCSSClass=function(){return"vjs-captions-button "+t.prototype.buildCSSClass.call(this)},e.prototype.buildWrapperCSSClass=function(){return"vjs-captions-button "+t.prototype.buildWrapperCSSClass.call(this)},e.prototype.createItems=function(){var e=[];return this.player().tech_&&this.player().tech_.featuresNativeTextTracks||!this.player().getChild("textTrackSettings")||(e.push(new ro(this.player_,{kind:this.kind_})),this.hideThreshold_+=1),t.prototype.createItems.call(this,e)},e}(Qi);io.prototype.kind_="captions",io.prototype.controlText_="Captions",un.registerComponent("CaptionsButton",io);var oo=function(t){function e(){return je(this,e),Pe(this,t.apply(this,arguments))}return Ae(e,t),e.prototype.createEl=function(e,n,r){var o='<span class="vjs-menu-item-text">'+this.localize(this.options_.label);"captions"===this.options_.track.kind&&(o+='\n        <span aria-hidden="true" class="vjs-icon-placeholder"></span>\n        <span class="vjs-control-text"> '+this.localize("Captions")+"</span>\n      "),o+="</span>";var s=t.prototype.createEl.call(this,e,i({innerHTML:o},n),r);return s},e}(Gi);un.registerComponent("SubsCapsMenuItem",oo);var so=function(t){function e(n){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};je(this,e);var i=Pe(this,t.call(this,n,r));return i.label_="subtitles",["en","en-us","en-ca","fr-ca"].indexOf(i.player_.language_)>-1&&(i.label_="captions"),i.menuButton_.controlText(Q(i.label_)),i}return Ae(e,t),e.prototype.buildCSSClass=function(){return"vjs-subs-caps-button "+t.prototype.buildCSSClass.call(this)},e.prototype.buildWrapperCSSClass=function(){return"vjs-subs-caps-button "+t.prototype.buildWrapperCSSClass.call(this)},e.prototype.createItems=function(){var e=[];return this.player().tech_&&this.player().tech_.featuresNativeTextTracks||!this.player().getChild("textTrackSettings")||(e.push(new ro(this.player_,{kind:this.label_})),this.hideThreshold_+=1),e=t.prototype.createItems.call(this,e,oo)},e}(Qi);so.prototype.kinds_=["captions","subtitles"],so.prototype.controlText_="Subtitles",un.registerComponent("SubsCapsButton",so);var ao=function(t){function e(n,r){je(this,e);var i=r.track,o=n.audioTracks();r.label=i.label||i.language||"Unknown",r.selected=i.enabled;var s=Pe(this,t.call(this,n,r));s.track=i,s.addClass("vjs-"+i.kind+"-menu-item");var a=function(){for(var t=arguments.length,e=Array(t),n=0;n<t;n++)e[n]=arguments[n];s.handleTracksChange.apply(s,e)};return o.addEventListener("change",a),s.on("dispose",function(){o.removeEventListener("change",a)}),s}return Ae(e,t),e.prototype.createEl=function(e,n,r){var o='<span class="vjs-menu-item-text">'+this.localize(this.options_.label);"main-desc"===this.options_.track.kind&&(o+='\n        <span aria-hidden="true" class="vjs-icon-placeholder"></span>\n        <span class="vjs-control-text"> '+this.localize("Descriptions")+"</span>\n      "),o+="</span>";var s=t.prototype.createEl.call(this,e,i({innerHTML:o},n),r);return s},e.prototype.handleClick=function(e){var n=this.player_.audioTracks();t.prototype.handleClick.call(this,e);for(var r=0;r<n.length;r++){var i=n[r];i.enabled=i===this.track}},e.prototype.handleTracksChange=function(t){this.selected(this.track.enabled)},e}(Yi);un.registerComponent("AudioTrackMenuItem",ao);var lo=function(t){function e(n){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return je(this,e),r.tracks=n.audioTracks(),Pe(this,t.call(this,n,r))}return Ae(e,t),e.prototype.buildCSSClass=function(){return"vjs-audio-button "+t.prototype.buildCSSClass.call(this)},e.prototype.buildWrapperCSSClass=function(){return"vjs-audio-button "+t.prototype.buildWrapperCSSClass.call(this)},e.prototype.createItems=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];this.hideThreshold_=1;for(var e=this.player_.audioTracks(),n=0;n<e.length;n++){var r=e[n];t.push(new ao(this.player_,{track:r,selectable:!0,multiSelectable:!1}))}return t},e}($i);lo.prototype.controlText_="Audio Track",un.registerComponent("AudioTrackButton",lo);var co=function(t){function e(n,r){je(this,e);var i=r.rate,o=parseFloat(i,10);r.label=i,r.selected=1===o,r.selectable=!0,r.multiSelectable=!1;var s=Pe(this,t.call(this,n,r));return s.label=i,s.rate=o,s.on(n,"ratechange",s.update),s}return Ae(e,t),e.prototype.handleClick=function(e){t.prototype.handleClick.call(this),this.player().playbackRate(this.rate)},e.prototype.update=function(t){this.selected(this.player().playbackRate()===this.rate)},e}(Yi);co.prototype.contentElType="button",un.registerComponent("PlaybackRateMenuItem",co);var uo=function(t){function e(n,r){je(this,e);var i=Pe(this,t.call(this,n,r));return i.updateVisibility(),i.updateLabel(),i.on(n,"loadstart",i.updateVisibility),i.on(n,"ratechange",i.updateLabel),i}return Ae(e,t),e.prototype.createEl=function(){var e=t.prototype.createEl.call(this);return this.labelEl_=v("div",{className:"vjs-playback-rate-value",innerHTML:"1x"}),e.appendChild(this.labelEl_),e},e.prototype.dispose=function(){this.labelEl_=null,t.prototype.dispose.call(this)},e.prototype.buildCSSClass=function(){return"vjs-playback-rate "+t.prototype.buildCSSClass.call(this)},e.prototype.buildWrapperCSSClass=function(){return"vjs-playback-rate "+t.prototype.buildWrapperCSSClass.call(this)},e.prototype.createMenu=function(){var t=new qi(this.player()),e=this.playbackRates();if(e)for(var n=e.length-1;n>=0;n--)t.addChild(new co(this.player(),{rate:e[n]+"x"}));return t},e.prototype.updateARIAAttributes=function(){this.el().setAttribute("aria-valuenow",this.player().playbackRate())},e.prototype.handleClick=function(t){for(var e=this.player().playbackRate(),n=this.playbackRates(),r=n[0],i=0;i<n.length;i++)if(n[i]>e){r=n[i];break}this.player().playbackRate(r)},e.prototype.playbackRates=function(){return this.options_.playbackRates||this.options_.playerOptions&&this.options_.playerOptions.playbackRates},e.prototype.playbackRateSupported=function(){return this.player().tech_&&this.player().tech_.featuresPlaybackRate&&this.playbackRates()&&this.playbackRates().length>0},e.prototype.updateVisibility=function(t){this.playbackRateSupported()?this.removeClass("vjs-hidden"):this.addClass("vjs-hidden")},e.prototype.updateLabel=function(t){this.playbackRateSupported()&&(this.labelEl_.innerHTML=this.player().playbackRate()+"x")},e}(Ki);uo.prototype.controlText_="Playback Rate",un.registerComponent("PlaybackRateMenuButton",uo);var ho=function(t){function e(){return je(this,e),Pe(this,t.apply(this,arguments))}return Ae(e,t),e.prototype.buildCSSClass=function(){return"vjs-spacer "+t.prototype.buildCSSClass.call(this)},e.prototype.createEl=function(){return t.prototype.createEl.call(this,"div",{className:this.buildCSSClass()})},e}(un);un.registerComponent("Spacer",ho);var po=function(t){function e(){return je(this,e),Pe(this,t.apply(this,arguments))}return Ae(e,t),e.prototype.buildCSSClass=function(){return"vjs-custom-control-spacer "+t.prototype.buildCSSClass.call(this)},e.prototype.createEl=function(){var e=t.prototype.createEl.call(this,{className:this.buildCSSClass()});return e.innerHTML=" ",e},e}(ho);un.registerComponent("CustomControlSpacer",po);var fo=function(t){function e(){return je(this,e),Pe(this,t.apply(this,arguments))}return Ae(e,t),e.prototype.createEl=function(){return t.prototype.createEl.call(this,"div",{className:"vjs-control-bar",dir:"ltr"})},e}(un);fo.prototype.options_={children:["playToggle","volumePanel","currentTimeDisplay","timeDivider","durationDisplay","progressControl","liveDisplay","remainingTimeDisplay","customControlSpacer","playbackRateMenuButton","chaptersButton","descriptionsButton","subsCapsButton","audioTrackButton","fullscreenToggle"]},un.registerComponent("ControlBar",fo);var vo=function(t){function e(n,r){je(this,e);var i=Pe(this,t.call(this,n,r));return i.on(n,"error",i.open),i}return Ae(e,t),e.prototype.buildCSSClass=function(){return"vjs-error-display "+t.prototype.buildCSSClass.call(this)},e.prototype.content=function(){var t=this.player().error();return t?this.localize(t.message):""},e}(Xn);vo.prototype.options_=tt(Xn.prototype.options_,{pauseOnOpen:!1,fillAlways:!0,temporary:!1,uncloseable:!0}),un.registerComponent("ErrorDisplay",vo);var yo="vjs-text-track-settings",go=["#000","Black"],mo=["#00F","Blue"],_o=["#0FF","Cyan"],bo=["#0F0","Green"],To=["#F0F","Magenta"],Co=["#F00","Red"],ko=["#FFF","White"],wo=["#FF0","Yellow"],Eo=["1","Opaque"],So=["0.5","Semi-Transparent"],xo=["0","Transparent"],jo={backgroundColor:{selector:".vjs-bg-color > select",id:"captions-background-color-%s",label:"Color",options:[go,ko,Co,bo,mo,wo,To,_o]},backgroundOpacity:{selector:".vjs-bg-opacity > select",id:"captions-background-opacity-%s",label:"Transparency",options:[Eo,So,xo]},color:{selector:".vjs-fg-color > select",id:"captions-foreground-color-%s",label:"Color",options:[ko,go,Co,bo,mo,wo,To,_o]},edgeStyle:{selector:".vjs-edge-style > select",id:"%s",label:"Text Edge Style",options:[["none","None"],["raised","Raised"],["depressed","Depressed"],["uniform","Uniform"],["dropshadow","Dropshadow"]]},fontFamily:{selector:".vjs-font-family > select",id:"captions-font-family-%s",label:"Font Family",options:[["proportionalSansSerif","Proportional Sans-Serif"],["monospaceSansSerif","Monospace Sans-Serif"],["proportionalSerif","Proportional Serif"],["monospaceSerif","Monospace Serif"],["casual","Casual"],["script","Script"],["small-caps","Small Caps"]]},fontPercent:{selector:".vjs-font-percent > select",id:"captions-font-size-%s",label:"Font Size",options:[["0.50","50%"],["0.75","75%"],["1.00","100%"],["1.25","125%"],["1.50","150%"],["1.75","175%"],["2.00","200%"],["3.00","300%"],["4.00","400%"]],"default":2,parser:function(t){return"1.00"===t?null:Number(t)}},textOpacity:{selector:".vjs-text-opacity > select",id:"captions-foreground-opacity-%s",label:"Transparency",options:[Eo,So]},windowColor:{selector:".vjs-window-color > select",id:"captions-window-color-%s",label:"Color"},windowOpacity:{selector:".vjs-window-opacity > select",id:"captions-window-opacity-%s",label:"Transparency",options:[xo,So,Eo]}};jo.windowColor.options=jo.backgroundColor.options;var Ao=function(t){function e(r,i){je(this,e),i.temporary=!1;var o=Pe(this,t.call(this,r,i));return o.updateDisplay=Ye(o,o.updateDisplay),o.fill(),o.hasBeenOpened_=o.hasBeenFilled_=!0,o.endDialog=v("p",{className:"vjs-control-text",textContent:o.localize("End of dialog window.")}),o.el().appendChild(o.endDialog),o.setDefaults(),void 0===i.persistTextTrackSettings&&(o.options_.persistTextTrackSettings=o.options_.playerOptions.persistTextTrackSettings),o.on(o.$(".vjs-done-button"),"click",function(){o.saveSettings(),o.close()}),o.on(o.$(".vjs-default-button"),"click",function(){o.setDefaults(),o.updateDisplay()}),n(jo,function(t){o.on(o.$(t.selector),"change",o.updateDisplay)}),o.options_.persistTextTrackSettings&&o.restoreSettings(),o}return Ae(e,t),e.prototype.dispose=function(){this.endDialog=null,t.prototype.dispose.call(this)},e.prototype.createElSelect_=function(t){var e=this,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"label",i=jo[t],o=i.id.replace("%s",this.id_),s=[n,o].join(" ").trim();return["<"+r+' id="'+o+'" class="'+("label"===r?"vjs-label":"")+'">',this.localize(i.label),"</"+r+">",'<select aria-labelledby="'+s+'">'].concat(i.options.map(function(t){var n=o+"-"+t[1].replace(/\W+/g,"");return['<option id="'+n+'" value="'+t[0]+'" ','aria-labelledby="'+s+" "+n+'">',e.localize(t[1]),"</option>"].join("")})).concat("</select>").join("")},e.prototype.createElFgColor_=function(){var t="captions-text-legend-"+this.id_;return['<fieldset class="vjs-fg-color vjs-track-setting">','<legend id="'+t+'">',this.localize("Text"),"</legend>",this.createElSelect_("color",t),'<span class="vjs-text-opacity vjs-opacity">',this.createElSelect_("textOpacity",t),"</span>","</fieldset>"].join("")},e.prototype.createElBgColor_=function(){var t="captions-background-"+this.id_;return['<fieldset class="vjs-bg-color vjs-track-setting">','<legend id="'+t+'">',this.localize("Background"),"</legend>",this.createElSelect_("backgroundColor",t),'<span class="vjs-bg-opacity vjs-opacity">',this.createElSelect_("backgroundOpacity",t),"</span>","</fieldset>"].join("")},e.prototype.createElWinColor_=function(){var t="captions-window-"+this.id_;return['<fieldset class="vjs-window-color vjs-track-setting">','<legend id="'+t+'">',this.localize("Window"),"</legend>",this.createElSelect_("windowColor",t),'<span class="vjs-window-opacity vjs-opacity">',this.createElSelect_("windowOpacity",t),"</span>","</fieldset>"].join("")},e.prototype.createElColors_=function(){return v("div",{className:"vjs-track-settings-colors",innerHTML:[this.createElFgColor_(),this.createElBgColor_(),this.createElWinColor_()].join("")})},e.prototype.createElFont_=function(){return v("div",{className:"vjs-track-settings-font",innerHTML:['<fieldset class="vjs-font-percent vjs-track-setting">',this.createElSelect_("fontPercent","","legend"),"</fieldset>",'<fieldset class="vjs-edge-style vjs-track-setting">',this.createElSelect_("edgeStyle","","legend"),"</fieldset>",'<fieldset class="vjs-font-family vjs-track-setting">',this.createElSelect_("fontFamily","","legend"),"</fieldset>"].join("")})},e.prototype.createElControls_=function(){var t=this.localize("restore all settings to the default values");return v("div",{className:"vjs-track-settings-controls",innerHTML:['<button class="vjs-default-button" title="'+t+'">',this.localize("Reset"),'<span class="vjs-control-text"> '+t+"</span>","</button>",'<button class="vjs-done-button">'+this.localize("Done")+"</button>"].join("")})},e.prototype.content=function(){return[this.createElColors_(),this.createElFont_(),this.createElControls_()]},e.prototype.label=function(){return this.localize("Caption Settings Dialog")},e.prototype.description=function(){return this.localize("Beginning of dialog window. Escape will cancel and close the window.")},e.prototype.buildCSSClass=function(){return t.prototype.buildCSSClass.call(this)+" vjs-text-track-settings"},e.prototype.getValues=function(){var t=this;return r(jo,function(e,n,r){var i=le(t.$(n.selector),n.parser);return void 0!==i&&(e[r]=i),e},{})},e.prototype.setValues=function(t){var e=this;n(jo,function(n,r){ce(e.$(n.selector),t[r],n.parser)})},e.prototype.setDefaults=function(){var t=this;n(jo,function(e){var n=e.hasOwnProperty("default")?e["default"]:0;t.$(e.selector).selectedIndex=n})},e.prototype.restoreSettings=function(){var t=void 0;try{t=JSON.parse(ve.localStorage.getItem(yo))}catch(e){Ee.warn(e)}t&&this.setValues(t)},e.prototype.saveSettings=function(){if(this.options_.persistTextTrackSettings){var t=this.getValues();try{Object.keys(t).length?ve.localStorage.setItem(yo,JSON.stringify(t)):ve.localStorage.removeItem(yo)}catch(e){Ee.warn(e)}}},e.prototype.updateDisplay=function(){var t=this.player_.getChild("textTrackDisplay");t&&t.updateDisplay()},e.prototype.conditionalBlur_=function(){this.previouslyActiveEl_=null,this.off(be,"keydown",this.handleKeyDown);var t=this.player_.controlBar,e=t&&t.subsCapsButton,n=t&&t.captionsButton;e?e.focus():n&&n.focus()},e}(Xn);un.registerComponent("TextTrackSettings",Ao);var Po=function(t){function e(n,r){je(this,e);var i=r.ResizeObserver||ve.ResizeObserver;null===r.ResizeObserver&&(i=!1);var o=tt({createEl:!i},r),s=Pe(this,t.call(this,n,o));return s.ResizeObserver=r.ResizeObserver||ve.ResizeObserver,s.loadListener_=null,s.resizeObserver_=null,s.debouncedHandler_=Je(function(){s.resizeHandler()},100,!1,n),i?(s.resizeObserver_=new s.ResizeObserver(s.debouncedHandler_),s.resizeObserver_.observe(n.el())):(s.loadListener_=function(){s.el_.contentWindow&&X(s.el_.contentWindow,"resize",s.debouncedHandler_),s.off("load",s.loadListener_)},s.on("load",s.loadListener_)),s}return Ae(e,t),e.prototype.createEl=function(){return t.prototype.createEl.call(this,"iframe",{className:"vjs-resize-manager"})},e.prototype.resizeHandler=function(){this.player_.trigger("playerresize")},e.prototype.dispose=function(){this.resizeObserver_&&(this.player_.el()&&this.resizeObserver_.unobserve(this.player_.el()),this.resizeObserver_.disconnect()),this.el_&&this.el_.contentWindow&&q(this.el_.contentWindow,"resize",this.debouncedHandler_),this.loadListener_&&this.off("load",this.loadListener_),this.ResizeObserver=null,this.resizeObserver=null,this.debouncedHandler_=null,this.loadListener_=null},e}(un);un.registerComponent("ResizeManager",Po);var Mo=function(t){var e=t.el();if(e.hasAttribute("src"))return t.triggerSourceset(e.src),!0;var n=t.$$("source"),r=[],i="";if(!n.length)return!1;for(var o=0;o<n.length;o++){var s=n[o].src;s&&r.indexOf(s)===-1&&r.push(s)}return!!r.length&&(1===r.length&&(i=r[0]),t.triggerSourceset(i),!0)},Oo=Object.defineProperty({},"innerHTML",{get:function(){return this.cloneNode(!0).innerHTML},set:function(t){var e=be.createElement(this.nodeName.toLowerCase());e.innerHTML=t;for(var n=be.createDocumentFragment();e.childNodes.length;)n.appendChild(e.childNodes[0]);return this.innerText="",ve.Element.prototype.appendChild.call(this,n),this.innerHTML}}),No=function(t,e){for(var n={},r=0;r<t.length&&(n=Object.getOwnPropertyDescriptor(t[r],e),!(n&&n.set&&n.get));r++);return n.enumerable=!0,n.configurable=!0,n},Io=function(t){return No([t.el(),ve.HTMLMediaElement.prototype,ve.Element.prototype,Oo],"innerHTML")},Lo=function(t){var e=t.el();if(!e.resetSourceWatch_){var n={},r=Io(t),i=function(n){return function(){for(var r=arguments.length,i=Array(r),o=0;o<r;o++)i[o]=arguments[o];var s=n.apply(e,i);return Mo(t),s}};["append","appendChild","insertAdjacentHTML"].forEach(function(t){e[t]&&(n[t]=e[t],e[t]=i(n[t]))}),Object.defineProperty(e,"innerHTML",tt(r,{set:i(r.set)})),e.resetSourceWatch_=function(){e.resetSourceWatch_=null,Object.keys(n).forEach(function(t){e[t]=n[t]}),Object.defineProperty(e,"innerHTML",r)},t.one("sourceset",e.resetSourceWatch_)}},Do=Object.defineProperty({},"src",{get:function(){return this.hasAttribute("src")?ar(ve.Element.prototype.getAttribute.call(this,"src")):""},set:function(t){return ve.Element.prototype.setAttribute.call(this,"src",t),t}}),Ro=function(t){return No([t.el(),ve.HTMLMediaElement.prototype,Do],"src")},Fo=function(t){if(t.featuresSourceset){var e=t.el();if(!e.resetSourceset_){var n=Ro(t),r=e.setAttribute,i=e.load;Object.defineProperty(e,"src",tt(n,{set:function(r){var i=n.set.call(e,r);return t.triggerSourceset(e.src),i}})),e.setAttribute=function(n,i){var o=r.call(e,n,i);return/src/i.test(n)&&t.triggerSourceset(e.src),o},e.load=function(){var n=i.call(e);return Mo(t)||(t.triggerSourceset(""),Lo(t)),n},e.currentSrc?t.triggerSourceset(e.currentSrc):Mo(t)||Lo(t),e.resetSourceset_=function(){e.resetSourceset_=null,e.load=i,e.setAttribute=r,Object.defineProperty(e,"src",n),e.resetSourceWatch_&&e.resetSourceWatch_()}}}},Bo=Me(["Text Tracks are being loaded from another origin but the crossorigin attribute isn't used.\n            This may prevent text tracks from loading."],["Text Tracks are being loaded from another origin but the crossorigin attribute isn't used.\n            This may prevent text tracks from loading."]),Ho=function(t){function e(n,r){je(this,e);var i=Pe(this,t.call(this,n,r)),o=n.source,s=!1;if(o&&(i.el_.currentSrc!==o.src||n.tag&&3===n.tag.initNetworkState_)?i.setSource(o):i.handleLateInit_(i.el_),n.enableSourceset&&i.setupSourcesetHandling_(),i.el_.hasChildNodes()){for(var a=i.el_.childNodes,l=a.length,c=[];l--;){var u=a[l],h=u.nodeName.toLowerCase();"track"===h&&(i.featuresNativeTextTracks?(i.remoteTextTrackEls().addTrackElement_(u),i.remoteTextTracks().addTrack(u.track),i.textTracks().addTrack(u.track),s||i.el_.hasAttribute("crossorigin")||!cr(u.src)||(s=!0)):c.push(u))}for(var p=0;p<c.length;p++)i.el_.removeChild(c[p])}return i.proxyNativeTracks_(),i.featuresNativeTextTracks&&s&&Ee.warn(Se(Bo)),i.restoreMetadataTracksInIOSNativePlayer_(),(An||vn||Tn)&&n.nativeControlsForTouch===!0&&i.setControls(!0),i.proxyWebkitFullscreen_(),i.triggerReady(),i}return Ae(e,t),e.prototype.dispose=function(){this.el_&&this.el_.resetSourceset_&&this.el_.resetSourceset_(),e.disposeMediaElement(this.el_),this.options_=null,t.prototype.dispose.call(this)},e.prototype.setupSourcesetHandling_=function(){Fo(this)},e.prototype.restoreMetadataTracksInIOSNativePlayer_=function(){var t=this.textTracks(),e=void 0,n=function(){e=[];for(var n=0;n<t.length;n++){var r=t[n];"metadata"===r.kind&&e.push({track:r,storedMode:r.mode})}};n(),t.addEventListener("change",n),this.on("dispose",function(){return t.removeEventListener("change",n)});var r=function i(){for(var n=0;n<e.length;n++){var r=e[n];"disabled"===r.track.mode&&r.track.mode!==r.storedMode&&(r.track.mode=r.storedMode)}t.removeEventListener("change",i)};this.on("webkitbeginfullscreen",function(){t.removeEventListener("change",n),t.removeEventListener("change",r),t.addEventListener("change",r)}),this.on("webkitendfullscreen",function(){t.removeEventListener("change",n),t.addEventListener("change",n),t.removeEventListener("change",r)})},e.prototype.overrideNative_=function(t,e){var n=this;if(e===this["featuresNative"+t+"Tracks"]){var r=t.toLowerCase();this[r+"TracksListeners_"]&&Object.keys(this[r+"TracksListeners_"]).forEach(function(t){var e=n.el()[r+"Tracks"];e.removeEventListener(t,n[r+"TracksListeners_"][t])}),this["featuresNative"+t+"Tracks"]=!e,this[r+"TracksListeners_"]=null,this.proxyNativeTracksForType_(r)}},e.prototype.overrideNativeAudioTracks=function(t){this.overrideNative_("Audio",t)},e.prototype.overrideNativeVideoTracks=function(t){this.overrideNative_("Video",t)},e.prototype.proxyNativeTracksForType_=function(t){var e=this,n=Or[t],r=this.el()[n.getterName],i=this[n.getterName]();if(this["featuresNative"+n.capitalName+"Tracks"]&&r&&r.addEventListener){var o={change:function(t){i.trigger({type:"change",target:i,currentTarget:i,srcElement:i})},addtrack:function(t){i.addTrack(t.track)},removetrack:function(t){i.removeTrack(t.track)}},s=function(){for(var t=[],e=0;e<i.length;e++){for(var n=!1,o=0;o<r.length;o++)if(r[o]===i[e]){n=!0;break}n||t.push(i[e])}for(;t.length;)i.removeTrack(t.shift())};this[n.getterName+"Listeners_"]=o,Object.keys(o).forEach(function(t){var n=o[t];r.addEventListener(t,n),e.on("dispose",function(e){return r.removeEventListener(t,n)})}),this.on("loadstart",s),this.on("dispose",function(t){return e.off("loadstart",s)})}},e.prototype.proxyNativeTracks_=function(){var t=this;Or.names.forEach(function(e){t.proxyNativeTracksForType_(e)})},e.prototype.createEl=function(){var t=this.options_.tag;if(!t||!this.options_.playerElIngest&&!this.movingMediaElementInDOM){if(t){var n=t.cloneNode(!0);t.parentNode&&t.parentNode.insertBefore(n,t),e.disposeMediaElement(t),t=n}else{t=be.createElement("video");var r=this.options_.tag&&k(this.options_.tag),o=tt({},r);An&&this.options_.nativeControlsForTouch===!0||delete o.controls,C(t,i(o,{id:this.options_.techId,"class":"vjs-tech"}))}t.playerId=this.options_.playerId}"undefined"!=typeof this.options_.preload&&E(t,"preload",this.options_.preload);for(var s=["loop","muted","playsinline","autoplay"],a=0;a<s.length;a++){var l=s[a],c=this.options_[l];"undefined"!=typeof c&&(c?E(t,l,l):S(t,l),t[l]=c)}return t},e.prototype.handleLateInit_=function(t){if(0!==t.networkState&&3!==t.networkState){if(0===t.readyState){var e=!1,n=function(){e=!0};this.on("loadstart",n);var r=function(){e||this.trigger("loadstart")};return this.on("loadedmetadata",r),void this.ready(function(){this.off("loadstart",n),this.off("loadedmetadata",r),e||this.trigger("loadstart")})}var i=["loadstart"];i.push("loadedmetadata"),t.readyState>=2&&i.push("loadeddata"),t.readyState>=3&&i.push("canplay"),t.readyState>=4&&i.push("canplaythrough"),this.ready(function(){i.forEach(function(t){this.trigger(t)},this)})}},e.prototype.setCurrentTime=function(t){try{this.el_.currentTime=t}catch(e){Ee(e,"Video is not ready. (Video.js)")}},e.prototype.duration=function(){var t=this;if(this.el_.duration===1/0&&_n&&wn&&0===this.el_.currentTime){var e=function n(){t.el_.currentTime>0&&(t.el_.duration===1/0&&t.trigger("durationchange"),t.off("timeupdate",n))};return this.on("timeupdate",e),NaN}return this.el_.duration||NaN},e.prototype.width=function(){return this.el_.offsetWidth},e.prototype.height=function(){return this.el_.offsetHeight},e.prototype.proxyWebkitFullscreen_=function(){var t=this;if("webkitDisplayingFullscreen"in this.el_){var e=function(){this.trigger("fullscreenchange",{isFullscreen:!1})},n=function(){"webkitPresentationMode"in this.el_&&"picture-in-picture"!==this.el_.webkitPresentationMode&&(this.one("webkitendfullscreen",e),this.trigger("fullscreenchange",{isFullscreen:!0}))};this.on("webkitbeginfullscreen",n),this.on("dispose",function(){t.off("webkitbeginfullscreen",n),t.off("webkitendfullscreen",e)})}},e.prototype.supportsFullScreen=function(){if("function"==typeof this.el_.webkitEnterFullScreen){var t=ve.navigator&&ve.navigator.userAgent||"";if(/Android/.test(t)||!/Chrome|Mac OS X 10.5/.test(t))return!0}return!1},e.prototype.enterFullScreen=function(){var t=this.el_;t.paused&&t.networkState<=t.HAVE_METADATA?(this.el_.play(),this.setTimeout(function(){t.pause(),t.webkitEnterFullScreen()},0)):t.webkitEnterFullScreen()},e.prototype.exitFullScreen=function(){this.el_.webkitExitFullScreen()},e.prototype.src=function(t){return void 0===t?this.el_.src:void this.setSrc(t)},e.prototype.reset=function(){e.resetMediaElement(this.el_)},e.prototype.currentSrc=function(){return this.currentSource_?this.currentSource_.src:this.el_.currentSrc},e.prototype.setControls=function(t){this.el_.controls=!!t},e.prototype.addTextTrack=function(e,n,r){return this.featuresNativeTextTracks?this.el_.addTextTrack(e,n,r):t.prototype.addTextTrack.call(this,e,n,r)},e.prototype.createRemoteTextTrack=function(e){if(!this.featuresNativeTextTracks)return t.prototype.createRemoteTextTrack.call(this,e);var n=be.createElement("track");return e.kind&&(n.kind=e.kind),e.label&&(n.label=e.label),(e.language||e.srclang)&&(n.srclang=e.language||e.srclang),e["default"]&&(n["default"]=e["default"]),e.id&&(n.id=e.id),e.src&&(n.src=e.src),n},e.prototype.addRemoteTextTrack=function(e,n){var r=t.prototype.addRemoteTextTrack.call(this,e,n);return this.featuresNativeTextTracks&&this.el().appendChild(r),r},e.prototype.removeRemoteTextTrack=function(e){if(t.prototype.removeRemoteTextTrack.call(this,e),this.featuresNativeTextTracks)for(var n=this.$$("track"),r=n.length;r--;)e!==n[r]&&e!==n[r].track||this.el().removeChild(n[r])},e.prototype.getVideoPlaybackQuality=function(){if("function"==typeof this.el().getVideoPlaybackQuality)return this.el().getVideoPlaybackQuality();var t={};return"undefined"!=typeof this.el().webkitDroppedFrameCount&&"undefined"!=typeof this.el().webkitDecodedFrameCount&&(t.droppedVideoFrames=this.el().webkitDroppedFrameCount,t.totalVideoFrames=this.el().webkitDecodedFrameCount),ve.performance&&"function"==typeof ve.performance.now?t.creationTime=ve.performance.now():ve.performance&&ve.performance.timing&&"number"==typeof ve.performance.timing.navigationStart&&(t.creationTime=ve.Date.now()-ve.performance.timing.navigationStart),t},e}(Qr);if(h()){Ho.TEST_VID=be.createElement("video");var Vo=be.createElement("track");Vo.kind="captions",Vo.srclang="en",Vo.label="English",Ho.TEST_VID.appendChild(Vo)}Ho.isSupported=function(){try{Ho.TEST_VID.volume=.5}catch(t){return!1}return!(!Ho.TEST_VID||!Ho.TEST_VID.canPlayType);
},Ho.canPlayType=function(t){return Ho.TEST_VID.canPlayType(t)},Ho.canPlaySource=function(t,e){return Ho.canPlayType(t.type)},Ho.canControlVolume=function(){try{var t=Ho.TEST_VID.volume;return Ho.TEST_VID.volume=t/2+.1,t!==Ho.TEST_VID.volume}catch(e){return!1}},Ho.canMuteVolume=function(){try{var t=Ho.TEST_VID.muted;return Ho.TEST_VID.muted=!t,Ho.TEST_VID.muted?E(Ho.TEST_VID,"muted","muted"):S(Ho.TEST_VID,"muted","muted"),t!==Ho.TEST_VID.muted}catch(e){return!1}},Ho.canControlPlaybackRate=function(){if(_n&&wn&&En<58)return!1;try{var t=Ho.TEST_VID.playbackRate;return Ho.TEST_VID.playbackRate=t/2+.1,t!==Ho.TEST_VID.playbackRate}catch(e){return!1}},Ho.canOverrideAttributes=function(){try{var t=function(){};Object.defineProperty(be.createElement("video"),"src",{get:t,set:t}),Object.defineProperty(be.createElement("audio"),"src",{get:t,set:t}),Object.defineProperty(be.createElement("video"),"innerHTML",{get:t,set:t}),Object.defineProperty(be.createElement("audio"),"innerHTML",{get:t,set:t})}catch(e){return!1}return!0},Ho.supportsNativeTextTracks=function(){return jn||gn&&wn},Ho.supportsNativeVideoTracks=function(){return!(!Ho.TEST_VID||!Ho.TEST_VID.videoTracks)},Ho.supportsNativeAudioTracks=function(){return!(!Ho.TEST_VID||!Ho.TEST_VID.audioTracks)},Ho.Events=["loadstart","suspend","abort","error","emptied","stalled","loadedmetadata","loadeddata","canplay","canplaythrough","playing","waiting","seeking","seeked","ended","durationchange","timeupdate","progress","play","pause","ratechange","resize","volumechange"],Ho.prototype.featuresVolumeControl=Ho.canControlVolume(),Ho.prototype.featuresMuteControl=Ho.canMuteVolume(),Ho.prototype.featuresPlaybackRate=Ho.canControlPlaybackRate(),Ho.prototype.featuresSourceset=Ho.canOverrideAttributes(),Ho.prototype.movingMediaElementInDOM=!gn,Ho.prototype.featuresFullscreenResize=!0,Ho.prototype.featuresProgressEvents=!0,Ho.prototype.featuresTimeupdateEvents=!0,Ho.prototype.featuresNativeTextTracks=Ho.supportsNativeTextTracks(),Ho.prototype.featuresNativeVideoTracks=Ho.supportsNativeVideoTracks(),Ho.prototype.featuresNativeAudioTracks=Ho.supportsNativeAudioTracks();var zo=Ho.TEST_VID&&Ho.TEST_VID.constructor.prototype.canPlayType,Wo=/^application\/(?:x-|vnd\.apple\.)mpegurl/i;Ho.patchCanPlayType=function(){bn>=4&&!Cn&&!wn&&(Ho.TEST_VID.constructor.prototype.canPlayType=function(t){return t&&Wo.test(t)?"maybe":zo.call(this,t)})},Ho.unpatchCanPlayType=function(){var t=Ho.TEST_VID.constructor.prototype.canPlayType;return Ho.TEST_VID.constructor.prototype.canPlayType=zo,t},Ho.patchCanPlayType(),Ho.disposeMediaElement=function(t){if(t){for(t.parentNode&&t.parentNode.removeChild(t);t.hasChildNodes();)t.removeChild(t.firstChild);t.removeAttribute("src"),"function"==typeof t.load&&!function(){try{t.load()}catch(e){}}()}},Ho.resetMediaElement=function(t){if(t){for(var e=t.querySelectorAll("source"),n=e.length;n--;)t.removeChild(e[n]);t.removeAttribute("src"),"function"==typeof t.load&&!function(){try{t.load()}catch(e){}}()}},["muted","defaultMuted","autoplay","controls","loop","playsinline"].forEach(function(t){Ho.prototype[t]=function(){return this.el_[t]||this.el_.hasAttribute(t)}}),["muted","defaultMuted","autoplay","loop","playsinline"].forEach(function(t){Ho.prototype["set"+Q(t)]=function(e){this.el_[t]=e,e?this.el_.setAttribute(t,t):this.el_.removeAttribute(t)}}),["paused","currentTime","buffered","volume","poster","preload","error","seeking","seekable","ended","playbackRate","defaultPlaybackRate","played","networkState","readyState","videoWidth","videoHeight"].forEach(function(t){Ho.prototype[t]=function(){return this.el_[t]}}),["volume","src","poster","preload","playbackRate","defaultPlaybackRate"].forEach(function(t){Ho.prototype["set"+Q(t)]=function(e){this.el_[t]=e}}),["pause","load","play"].forEach(function(t){Ho.prototype[t]=function(){return this.el_[t]()}}),Qr.withSourceHandlers(Ho),Ho.nativeSourceHandler={},Ho.nativeSourceHandler.canPlayType=function(t){try{return Ho.TEST_VID.canPlayType(t)}catch(e){return""}},Ho.nativeSourceHandler.canHandleSource=function(t,e){if(t.type)return Ho.nativeSourceHandler.canPlayType(t.type);if(t.src){var n=lr(t.src);return Ho.nativeSourceHandler.canPlayType("video/"+n)}return""},Ho.nativeSourceHandler.handleSource=function(t,e,n){e.setSrc(t.src)},Ho.nativeSourceHandler.dispose=function(){},Ho.registerSourceHandler(Ho.nativeSourceHandler),Qr.registerTech("Html5",Ho);var Uo=Me(["\n        Using the tech directly can be dangerous. I hope you know what you're doing.\n        See https://github.com/videojs/video.js/issues/2617 for more info.\n      "],["\n        Using the tech directly can be dangerous. I hope you know what you're doing.\n        See https://github.com/videojs/video.js/issues/2617 for more info.\n      "]),Xo=["progress","abort","suspend","emptied","stalled","loadedmetadata","loadeddata","timeupdate","resize","volumechange","texttrackchange"],qo={canplay:"CanPlay",canplaythrough:"CanPlayThrough",playing:"Playing",seeked:"Seeked"},Ko=function(t){function e(n,r,o){if(je(this,e),n.id=n.id||r.id||"vjs_video_"+F(),r=i(e.getTagSettings(n),r),r.initChildren=!1,r.createEl=!1,r.evented=!1,r.reportTouchActivity=!1,!r.language)if("function"==typeof n.closest){var s=n.closest("[lang]");s&&s.getAttribute&&(r.language=s.getAttribute("lang"))}else for(var a=n;a&&1===a.nodeType;){if(k(a).hasOwnProperty("lang")){r.language=a.getAttribute("lang");break}a=a.parentNode}var l=Pe(this,t.call(this,null,r,o));if(l.isPosterFromTech_=!1,l.queuedCallbacks_=[],l.isReady_=!1,l.hasStarted_=!1,l.userActive_=!1,!l.options_||!l.options_.techOrder||!l.options_.techOrder.length)throw new Error("No techOrder specified. Did you overwrite videojs.options instead of just changing the properties you want to override?");if(l.tag=n,l.tagAttributes=n&&k(n),l.language(l.options_.language),r.languages){var c={};Object.getOwnPropertyNames(r.languages).forEach(function(t){c[t.toLowerCase()]=r.languages[t]}),l.languages_=c}else l.languages_=e.prototype.options_.languages;l.cache_={},l.poster_=r.poster||"",l.controls_=!!r.controls,l.cache_.lastVolume=1,n.controls=!1,n.removeAttribute("controls"),n.hasAttribute("autoplay")?l.options_.autoplay=!0:l.autoplay(l.options_.autoplay),l.scrubbing_=!1,l.el_=l.createEl(),l.cache_.lastPlaybackRate=l.defaultPlaybackRate(),G(l,{eventBusKey:"el_"});var u=tt(l.options_);if(r.plugins){var h=r.plugins;Object.keys(h).forEach(function(t){if("function"!=typeof this[t])throw new Error('plugin "'+t+'" does not exist');this[t](h[t])},l)}l.options_.playerOptions=u,l.middleware_=[],l.initChildren(),l.isAudio("audio"===n.nodeName.toLowerCase()),l.controls()?l.addClass("vjs-controls-enabled"):l.addClass("vjs-controls-disabled"),l.el_.setAttribute("role","region"),l.isAudio()?l.el_.setAttribute("aria-label",l.localize("Audio Player")):l.el_.setAttribute("aria-label",l.localize("Video Player")),l.isAudio()&&l.addClass("vjs-audio"),l.flexNotSupported_()&&l.addClass("vjs-no-flex"),gn||l.addClass("vjs-workinghover"),e.players[l.id_]=l;var p=pe.split(".")[0];return l.addClass("vjs-v"+p),l.userActive(!0),l.reportUserActivity(),l.one("play",l.listenForUserActivity_),l.on("fullscreenchange",l.handleFullscreenChange_),l.on("stageclick",l.handleStageClick_),l.changingSrc_=!1,l.playWaitingForReady_=!1,l.playOnLoadstart_=null,l}return Ae(e,t),e.prototype.dispose=function(){this.trigger("dispose"),this.off("dispose"),this.styleEl_&&this.styleEl_.parentNode&&(this.styleEl_.parentNode.removeChild(this.styleEl_),this.styleEl_=null),e.players[this.id_]=null,this.tag&&this.tag.player&&(this.tag.player=null),this.el_&&this.el_.player&&(this.el_.player=null),this.tech_&&(this.tech_.dispose(),this.isPosterFromTech_=!1,this.poster_=""),this.playerElIngest_&&(this.playerElIngest_=null),this.tag&&(this.tag=null),Qt(this),t.prototype.dispose.call(this)},e.prototype.createEl=function(){var e=this.tag,n=void 0,r=this.playerElIngest_=e.parentNode&&e.parentNode.hasAttribute&&e.parentNode.hasAttribute("data-vjs-player"),i="video-js"===this.tag.tagName.toLowerCase();r?n=this.el_=e.parentNode:i||(n=this.el_=t.prototype.createEl.call(this,"div"));var o=k(e);if(i){for(n=this.el_=e,e=this.tag=be.createElement("video");n.children.length;)e.appendChild(n.firstChild);m(n,"video-js")||_(n,"video-js"),n.appendChild(e),r=this.playerElIngest_=n,Object.keys(n).forEach(function(t){e[t]=n[t]})}if(e.setAttribute("tabindex","-1"),Sn&&e.setAttribute("role","application"),e.removeAttribute("width"),e.removeAttribute("height"),Object.getOwnPropertyNames(o).forEach(function(t){i&&"class"===t||n.setAttribute(t,o[t]),i&&e.setAttribute(t,o[t])}),e.playerId=e.id,e.id+="_html5_api",e.className="vjs-tech",e.player=n.player=this,this.addClass("vjs-paused"),ve.VIDEOJS_NO_DYNAMIC_STYLE!==!0){this.styleEl_=Ke("vjs-styles-dimensions");var s=Le(".vjs-styles-defaults"),a=Le("head");a.insertBefore(this.styleEl_,s?s.nextSibling:a.firstChild)}this.width(this.options_.width),this.height(this.options_.height),this.fluid(this.options_.fluid),this.aspectRatio(this.options_.aspectRatio);for(var l=e.getElementsByTagName("a"),c=0;c<l.length;c++){var u=l.item(c);_(u,"vjs-hidden"),u.setAttribute("hidden","hidden")}return e.initNetworkState_=e.networkState,e.parentNode&&!r&&e.parentNode.insertBefore(n,e),g(e,n),this.children_.unshift(e),this.el_.setAttribute("lang",this.language_),this.el_=n,n},e.prototype.width=function(t){return this.dimension("width",t)},e.prototype.height=function(t){return this.dimension("height",t)},e.prototype.dimension=function(t,e){var n=t+"_";if(void 0===e)return this[n]||0;if(""===e)return this[n]=void 0,void this.updateStyleEl_();var r=parseFloat(e);return isNaN(r)?void Ee.error('Improper value "'+e+'" supplied for for '+t):(this[n]=r,void this.updateStyleEl_())},e.prototype.fluid=function(t){return void 0===t?!!this.fluid_:(this.fluid_=!!t,t?this.addClass("vjs-fluid"):this.removeClass("vjs-fluid"),void this.updateStyleEl_())},e.prototype.aspectRatio=function(t){if(void 0===t)return this.aspectRatio_;if(!/^\d+\:\d+$/.test(t))throw new Error("Improper value supplied for aspect ratio. The format should be width:height, for example 16:9.");this.aspectRatio_=t,this.fluid(!0),this.updateStyleEl_()},e.prototype.updateStyleEl_=function(){if(ve.VIDEOJS_NO_DYNAMIC_STYLE===!0){var t="number"==typeof this.width_?this.width_:this.options_.width,e="number"==typeof this.height_?this.height_:this.options_.height,n=this.tech_&&this.tech_.el();return void(n&&(t>=0&&(n.width=t),e>=0&&(n.height=e)))}var r=void 0,i=void 0,o=void 0,s=void 0;o=void 0!==this.aspectRatio_&&"auto"!==this.aspectRatio_?this.aspectRatio_:this.videoWidth()>0?this.videoWidth()+":"+this.videoHeight():"16:9";var a=o.split(":"),l=a[1]/a[0];r=void 0!==this.width_?this.width_:void 0!==this.height_?this.height_/l:this.videoWidth()||300,i=void 0!==this.height_?this.height_:r*l,s=/^[^a-zA-Z]/.test(this.id())?"dimensions-"+this.id():this.id()+"-dimensions",this.addClass(s),$e(this.styleEl_,"\n      ."+s+" {\n        width: "+r+"px;\n        height: "+i+"px;\n      }\n\n      ."+s+".vjs-fluid {\n        padding-top: "+100*l+"%;\n      }\n    ")},e.prototype.loadTech_=function(t,e){var n=this;this.tech_&&this.unloadTech_();var r=Q(t),o=t.charAt(0).toLowerCase()+t.slice(1);"Html5"!==r&&this.tag&&(Qr.getTech("Html5").disposeMediaElement(this.tag),this.tag.player=null,this.tag=null),this.techName_=r,this.isReady_=!1;var s="string"!=typeof this.autoplay()&&this.autoplay(),a={source:e,autoplay:s,nativeControlsForTouch:this.options_.nativeControlsForTouch,playerId:this.id(),techId:this.id()+"_"+r+"_api",playsinline:this.options_.playsinline,preload:this.options_.preload,loop:this.options_.loop,muted:this.options_.muted,poster:this.poster(),language:this.language(),playerElIngest:this.playerElIngest_||!1,"vtt.js":this.options_["vtt.js"],canOverridePoster:!!this.options_.techCanOverridePoster,enableSourceset:this.options_.enableSourceset};Ir.names.forEach(function(t){var e=Ir[t];a[e.getterName]=n[e.privateName]}),i(a,this.options_[r]),i(a,this.options_[o]),i(a,this.options_[t.toLowerCase()]),this.tag&&(a.tag=this.tag),e&&e.src===this.cache_.src&&this.cache_.currentTime>0&&(a.startTime=this.cache_.currentTime);var l=Qr.getTech(t);if(!l)throw new Error("No Tech named '"+r+"' exists! '"+r+"' should be registered using videojs.registerTech()'");this.tech_=new l(a),this.tech_.ready(Ye(this,this.handleTechReady_),!0),zn.jsonToTextTracks(this.textTracksJson_||[],this.tech_),Xo.forEach(function(t){n.on(n.tech_,t,n["handleTech"+Q(t)+"_"])}),Object.keys(qo).forEach(function(t){n.on(n.tech_,t,function(e){return 0===n.tech_.playbackRate()&&n.tech_.seeking()?void n.queuedCallbacks_.push({callback:n["handleTech"+qo[t]+"_"].bind(n),event:e}):void n["handleTech"+qo[t]+"_"](e)})}),this.on(this.tech_,"loadstart",this.handleTechLoadStart_),this.on(this.tech_,"sourceset",this.handleTechSourceset_),this.on(this.tech_,"waiting",this.handleTechWaiting_),this.on(this.tech_,"ended",this.handleTechEnded_),this.on(this.tech_,"seeking",this.handleTechSeeking_),this.on(this.tech_,"play",this.handleTechPlay_),this.on(this.tech_,"firstplay",this.handleTechFirstPlay_),this.on(this.tech_,"pause",this.handleTechPause_),this.on(this.tech_,"durationchange",this.handleTechDurationChange_),this.on(this.tech_,"fullscreenchange",this.handleTechFullscreenChange_),this.on(this.tech_,"error",this.handleTechError_),this.on(this.tech_,"loadedmetadata",this.updateStyleEl_),this.on(this.tech_,"posterchange",this.handleTechPosterChange_),this.on(this.tech_,"textdata",this.handleTechTextData_),this.on(this.tech_,"ratechange",this.handleTechRateChange_),this.usingNativeControls(this.techGet_("controls")),this.controls()&&!this.usingNativeControls()&&this.addTechControlsListeners_(),this.tech_.el().parentNode===this.el()||"Html5"===r&&this.tag||g(this.tech_.el(),this.el()),this.tag&&(this.tag.player=null,this.tag=null)},e.prototype.unloadTech_=function(){var t=this;Ir.names.forEach(function(e){var n=Ir[e];t[n.privateName]=t[n.getterName]()}),this.textTracksJson_=zn.textTracksToJson(this.tech_),this.isReady_=!1,this.tech_.dispose(),this.tech_=!1,this.isPosterFromTech_&&(this.poster_="",this.trigger("posterchange")),this.isPosterFromTech_=!1},e.prototype.tech=function(t){return void 0===t&&Ee.warn(Se(Uo)),this.tech_},e.prototype.addTechControlsListeners_=function(){this.removeTechControlsListeners_(),this.on(this.tech_,"mousedown",this.handleTechClick_),this.on(this.tech_,"dblclick",this.handleTechDoubleClick_),this.on(this.tech_,"touchstart",this.handleTechTouchStart_),this.on(this.tech_,"touchmove",this.handleTechTouchMove_),this.on(this.tech_,"touchend",this.handleTechTouchEnd_),this.on(this.tech_,"tap",this.handleTechTap_)},e.prototype.removeTechControlsListeners_=function(){this.off(this.tech_,"tap",this.handleTechTap_),this.off(this.tech_,"touchstart",this.handleTechTouchStart_),this.off(this.tech_,"touchmove",this.handleTechTouchMove_),this.off(this.tech_,"touchend",this.handleTechTouchEnd_),this.off(this.tech_,"mousedown",this.handleTechClick_),this.off(this.tech_,"dblclick",this.handleTechDoubleClick_)},e.prototype.handleTechReady_=function(){this.triggerReady(),this.cache_.volume&&this.techCall_("setVolume",this.cache_.volume),this.handleTechPosterChange_(),this.handleTechDurationChange_()},e.prototype.handleTechLoadStart_=function(){this.removeClass("vjs-ended"),this.removeClass("vjs-seeking"),this.error(null),this.paused()?(this.hasStarted(!1),this.trigger("loadstart")):(this.trigger("loadstart"),this.trigger("firstplay")),this.manualAutoplay_(this.autoplay())},e.prototype.manualAutoplay_=function(t){var e=this;if(this.tech_&&"string"==typeof t){var n=function(){var t=e.muted();e.muted(!0);var n=e.play();if(n&&n.then&&n["catch"])return n["catch"](function(n){e.muted(t)})},r=void 0;if("any"===t?(r=this.play(),r&&r.then&&r["catch"]&&r["catch"](function(){return n()})):r="muted"===t?n():this.play(),r&&r.then&&r["catch"])return r.then(function(){e.trigger({type:"autoplay-success",autoplay:t})})["catch"](function(n){e.trigger({type:"autoplay-failure",autoplay:t})})}},e.prototype.updateSourceCaches_=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",e=t,n="";"string"!=typeof e&&(e=t.src,n=t.type),this.cache_.source=this.cache_.source||{},this.cache_.sources=this.cache_.sources||[],e&&!n&&(n=ai(this,e)),this.cache_.source=tt({},t,{src:e,type:n});for(var r=this.cache_.sources.filter(function(t){return t.src&&t.src===e}),i=[],o=this.$$("source"),s=[],a=0;a<o.length;a++){var l=k(o[a]);i.push(l),l.src&&l.src===e&&s.push(l.src)}s.length&&!r.length?this.cache_.sources=i:r.length||(this.cache_.sources=[this.cache_.source]),this.cache_.src=e},e.prototype.handleTechSourceset_=function(t){var e=this;if(!this.changingSrc_&&(this.updateSourceCaches_(t.src),!t.src)){var n=function r(t){"sourceset"!==t.type&&e.updateSourceCaches_(e.techGet_("currentSrc")),e.tech_.off(["sourceset","loadstart"],r)};this.tech_.one(["sourceset","loadstart"],n)}this.trigger({src:t.src,type:"sourceset"})},e.prototype.hasStarted=function(t){return void 0===t?this.hasStarted_:void(t!==this.hasStarted_&&(this.hasStarted_=t,this.hasStarted_?(this.addClass("vjs-has-started"),this.trigger("firstplay")):this.removeClass("vjs-has-started")))},e.prototype.handleTechPlay_=function(){this.removeClass("vjs-ended"),this.removeClass("vjs-paused"),this.addClass("vjs-playing"),this.hasStarted(!0),this.trigger("play")},e.prototype.handleTechRateChange_=function(){this.tech_.playbackRate()>0&&0===this.cache_.lastPlaybackRate&&(this.queuedCallbacks_.forEach(function(t){return t.callback(t.event)}),this.queuedCallbacks_=[]),this.cache_.lastPlaybackRate=this.tech_.playbackRate(),this.trigger("ratechange")},e.prototype.handleTechWaiting_=function(){var t=this;this.addClass("vjs-waiting"),this.trigger("waiting"),this.one("timeupdate",function(){return t.removeClass("vjs-waiting")})},e.prototype.handleTechCanPlay_=function(){this.removeClass("vjs-waiting"),this.trigger("canplay")},e.prototype.handleTechCanPlayThrough_=function(){this.removeClass("vjs-waiting"),this.trigger("canplaythrough")},e.prototype.handleTechPlaying_=function(){this.removeClass("vjs-waiting"),this.trigger("playing")},e.prototype.handleTechSeeking_=function(){this.addClass("vjs-seeking"),this.trigger("seeking")},e.prototype.handleTechSeeked_=function(){this.removeClass("vjs-seeking"),this.trigger("seeked")},e.prototype.handleTechFirstPlay_=function(){this.options_.starttime&&(Ee.warn("Passing the `starttime` option to the player will be deprecated in 6.0"),this.currentTime(this.options_.starttime)),this.addClass("vjs-has-started"),this.trigger("firstplay")},e.prototype.handleTechPause_=function(){this.removeClass("vjs-playing"),this.addClass("vjs-paused"),this.trigger("pause")},e.prototype.handleTechEnded_=function(){this.addClass("vjs-ended"),this.options_.loop?(this.currentTime(0),this.play()):this.paused()||this.pause(),this.trigger("ended")},e.prototype.handleTechDurationChange_=function(){this.duration(this.techGet_("duration"))},e.prototype.handleTechClick_=function(t){R(t)&&this.controls_&&(this.paused()?ct(this.play()):this.pause())},e.prototype.handleTechDoubleClick_=function(t){if(this.controls_){var e=Array.prototype.some.call(this.$$(".vjs-control-bar, .vjs-modal-dialog"),function(e){return e.contains(t.target)});e||(this.isFullscreen()?this.exitFullscreen():this.requestFullscreen())}},e.prototype.handleTechTap_=function(){this.userActive(!this.userActive())},e.prototype.handleTechTouchStart_=function(){this.userWasActive=this.userActive()},e.prototype.handleTechTouchMove_=function(){this.userWasActive&&this.reportUserActivity()},e.prototype.handleTechTouchEnd_=function(t){t.preventDefault()},e.prototype.handleFullscreenChange_=function(){this.isFullscreen()?this.addClass("vjs-fullscreen"):this.removeClass("vjs-fullscreen")},e.prototype.handleStageClick_=function(){this.reportUserActivity()},e.prototype.handleTechFullscreenChange_=function(t,e){e&&this.isFullscreen(e.isFullscreen),this.trigger("fullscreenchange")},e.prototype.handleTechError_=function(){var t=this.tech_.error();this.error(t)},e.prototype.handleTechTextData_=function(){var t=null;arguments.length>1&&(t=arguments[1]),this.trigger("textdata",t)},e.prototype.getCache=function(){return this.cache_},e.prototype.techCall_=function(t,e){this.ready(function(){if(t in ri)return $t(this.middleware_,this.tech_,t,e);if(t in ii)return Yt(this.middleware_,this.tech_,t,e);try{this.tech_&&this.tech_[t](e)}catch(n){throw Ee(n),n}},!0)},e.prototype.techGet_=function(t){if(this.tech_&&this.tech_.isReady_){if(t in ni)return Kt(this.middleware_,this.tech_,t);if(t in ii)return Yt(this.middleware_,this.tech_,t);try{return this.tech_[t]()}catch(e){if(void 0===this.tech_[t])throw Ee("Video.js: "+t+" method not defined for "+this.techName_+" playback technology.",e),e;if("TypeError"===e.name)throw Ee("Video.js: "+t+" unavailable on "+this.techName_+" playback technology element.",e),this.tech_.isReady_=!1,e;throw Ee(e),e}}},e.prototype.play=function(){var t=this;if(this.playOnLoadstart_&&this.off("loadstart",this.playOnLoadstart_),this.isReady_){if(!this.changingSrc_&&(this.src()||this.currentSrc()))return this.techGet_("play");this.playOnLoadstart_=function(){t.playOnLoadstart_=null,ct(t.play())},this.one("loadstart",this.playOnLoadstart_)}else{if(this.playWaitingForReady_)return;this.playWaitingForReady_=!0,this.ready(function(){t.playWaitingForReady_=!1,ct(t.play())})}},e.prototype.pause=function(){this.techCall_("pause")},e.prototype.paused=function(){return this.techGet_("paused")!==!1},e.prototype.played=function(){return this.techGet_("played")||it(0,0)},e.prototype.scrubbing=function(t){return"undefined"==typeof t?this.scrubbing_:(this.scrubbing_=!!t,void(t?this.addClass("vjs-scrubbing"):this.removeClass("vjs-scrubbing")))},e.prototype.currentTime=function(t){return"undefined"!=typeof t?(t<0&&(t=0),void this.techCall_("setCurrentTime",t)):(this.cache_.currentTime=this.techGet_("currentTime")||0,this.cache_.currentTime)},e.prototype.duration=function(t){return void 0===t?void 0!==this.cache_.duration?this.cache_.duration:NaN:(t=parseFloat(t),t<0&&(t=1/0),void(t!==this.cache_.duration&&(this.cache_.duration=t,t===1/0?this.addClass("vjs-live"):this.removeClass("vjs-live"),this.trigger("durationchange"))))},e.prototype.remainingTime=function(){return this.duration()-this.currentTime()},e.prototype.remainingTimeDisplay=function(){return Math.floor(this.duration())-Math.floor(this.currentTime())},e.prototype.buffered=function n(){var n=this.techGet_("buffered");return n&&n.length||(n=it(0,0)),n},e.prototype.bufferedPercent=function(){return ot(this.buffered(),this.duration())},e.prototype.bufferedEnd=function(){var t=this.buffered(),e=this.duration(),n=t.end(t.length-1);return n>e&&(n=e),n},e.prototype.volume=function(t){var e=void 0;return void 0!==t?(e=Math.max(0,Math.min(1,parseFloat(t))),this.cache_.volume=e,this.techCall_("setVolume",e),void(e>0&&this.lastVolume_(e))):(e=parseFloat(this.techGet_("volume")),isNaN(e)?1:e)},e.prototype.muted=function(t){return void 0!==t?void this.techCall_("setMuted",t):this.techGet_("muted")||!1},e.prototype.defaultMuted=function(t){return void 0!==t?this.techCall_("setDefaultMuted",t):this.techGet_("defaultMuted")||!1},e.prototype.lastVolume_=function(t){return void 0!==t&&0!==t?void(this.cache_.lastVolume=t):this.cache_.lastVolume},e.prototype.supportsFullScreen=function(){return this.techGet_("supportsFullScreen")||!1},e.prototype.isFullscreen=function(t){return void 0!==t?void(this.isFullscreen_=!!t):!!this.isFullscreen_},e.prototype.requestFullscreen=function(){var t=Mn;this.isFullscreen(!0),t.requestFullscreen?(X(be,t.fullscreenchange,Ye(this,function e(n){this.isFullscreen(be[t.fullscreenElement]),this.isFullscreen()===!1&&q(be,t.fullscreenchange,e),this.trigger("fullscreenchange")})),this.el_[t.requestFullscreen]()):this.tech_.supportsFullScreen()?this.techCall_("enterFullScreen"):(this.enterFullWindow(),this.trigger("fullscreenchange"))},e.prototype.exitFullscreen=function(){var t=Mn;this.isFullscreen(!1),t.requestFullscreen?be[t.exitFullscreen]():this.tech_.supportsFullScreen()?this.techCall_("exitFullScreen"):(this.exitFullWindow(),this.trigger("fullscreenchange"))},e.prototype.enterFullWindow=function(){this.isFullWindow=!0,this.docOrigOverflow=be.documentElement.style.overflow,X(be,"keydown",Ye(this,this.fullWindowOnEscKey)),be.documentElement.style.overflow="hidden",_(be.body,"vjs-full-window"),this.trigger("enterFullWindow")},e.prototype.fullWindowOnEscKey=function(t){27===t.keyCode&&(this.isFullscreen()===!0?this.exitFullscreen():this.exitFullWindow())},e.prototype.exitFullWindow=function(){this.isFullWindow=!1,q(be,"keydown",this.fullWindowOnEscKey),be.documentElement.style.overflow=this.docOrigOverflow,b(be.body,"vjs-full-window"),this.trigger("exitFullWindow")},e.prototype.canPlayType=function(t){for(var e=void 0,n=0,r=this.options_.techOrder;n<r.length;n++){var i=r[n],o=Qr.getTech(i);if(o||(o=un.getComponent(i)),o){if(o.isSupported()&&(e=o.canPlayType(t)))return e}else Ee.error('The "'+i+'" tech is undefined. Skipped browser support check for that tech.')}return""},e.prototype.selectSource=function(t){var e=this,n=this.options_.techOrder.map(function(t){return[t,Qr.getTech(t)]}).filter(function(t){var e=t[0],n=t[1];return n?n.isSupported():(Ee.error('The "'+e+'" tech is undefined. Skipped browser support check for that tech.'),!1)}),r=function(t,e,n){var r=void 0;return t.some(function(t){return e.some(function(e){if(r=n(t,e))return!0})}),r},i=void 0,o=function(t){return function(e,n){return t(n,e)}},s=function(t,n){var r=t[0],i=t[1];if(i.canPlaySource(n,e.options_[r.toLowerCase()]))return{source:n,tech:r}};return i=this.options_.sourceOrder?r(t,n,o(s)):r(n,t,s),i||!1},e.prototype.src=function(t){var e=this;if("undefined"==typeof t)return this.cache_.src||"";var n=li(t);return n.length?(this.changingSrc_=!0,this.cache_.sources=n,this.updateSourceCaches_(n[0]),void Xt(this,n[0],function(t,r){e.middleware_=r,e.cache_.sources=n,e.updateSourceCaches_(t);var i=e.src_(t);return i?n.length>1?e.src(n.slice(1)):(e.changingSrc_=!1,e.setTimeout(function(){this.error({code:4,message:this.localize(this.options_.notSupportedMessage)})},0),void e.triggerReady()):void qt(r,e.tech_)})):void this.setTimeout(function(){this.error({code:4,message:this.localize(this.options_.notSupportedMessage)})},0)},e.prototype.src_=function(t){var e=this,n=this.selectSource([t]);return!n||(Z(n.tech,this.techName_)?(this.ready(function(){this.tech_.constructor.prototype.hasOwnProperty("setSource")?this.techCall_("setSource",t):this.techCall_("src",t.src),this.changingSrc_=!1},!0),!1):(this.changingSrc_=!0,this.loadTech_(n.tech,n.source),this.tech_.ready(function(){e.changingSrc_=!1}),!1))},e.prototype.load=function(){this.techCall_("load")},e.prototype.reset=function(){this.tech_&&this.tech_.clearTracks("text"),this.loadTech_(this.options_.techOrder[0],null),this.techCall_("reset")},e.prototype.currentSources=function(){var t=this.currentSource(),e=[];return 0!==Object.keys(t).length&&e.push(t),this.cache_.sources||e},e.prototype.currentSource=function(){return this.cache_.source||{}},e.prototype.currentSrc=function(){return this.currentSource()&&this.currentSource().src||""},e.prototype.currentType=function(){return this.currentSource()&&this.currentSource().type||""},e.prototype.preload=function(t){return void 0!==t?(this.techCall_("setPreload",t),void(this.options_.preload=t)):this.techGet_("preload")},e.prototype.autoplay=function(t){if(void 0===t)return this.options_.autoplay||!1;var e=void 0;"string"==typeof t&&/(any|play|muted)/.test(t)?(this.options_.autoplay=t,this.manualAutoplay_(t),e=!1):t?this.options_.autoplay=!0:this.options_.autoplay=!1,e=e||this.options_.autoplay,this.tech_&&this.techCall_("setAutoplay",e)},e.prototype.playsinline=function(t){return void 0!==t?(this.techCall_("setPlaysinline",t),this.options_.playsinline=t,this):this.techGet_("playsinline")},e.prototype.loop=function(t){return void 0!==t?(this.techCall_("setLoop",t),void(this.options_.loop=t)):this.techGet_("loop")},e.prototype.poster=function(t){return void 0===t?this.poster_:(t||(t=""),void(t!==this.poster_&&(this.poster_=t,this.techCall_("setPoster",t),this.isPosterFromTech_=!1,this.trigger("posterchange"))))},e.prototype.handleTechPosterChange_=function(){if((!this.poster_||this.options_.techCanOverridePoster)&&this.tech_&&this.tech_.poster){var t=this.tech_.poster()||"";t!==this.poster_&&(this.poster_=t,this.isPosterFromTech_=!0,this.trigger("posterchange"))}},e.prototype.controls=function(t){return void 0===t?!!this.controls_:(t=!!t,void(this.controls_!==t&&(this.controls_=t,this.usingNativeControls()&&this.techCall_("setControls",t),this.controls_?(this.removeClass("vjs-controls-disabled"),this.addClass("vjs-controls-enabled"),this.trigger("controlsenabled"),this.usingNativeControls()||this.addTechControlsListeners_()):(this.removeClass("vjs-controls-enabled"),this.addClass("vjs-controls-disabled"),this.trigger("controlsdisabled"),this.usingNativeControls()||this.removeTechControlsListeners_()))))},e.prototype.usingNativeControls=function(t){return void 0===t?!!this.usingNativeControls_:(t=!!t,void(this.usingNativeControls_!==t&&(this.usingNativeControls_=t,this.usingNativeControls_?(this.addClass("vjs-using-native-controls"),this.trigger("usingnativecontrols")):(this.removeClass("vjs-using-native-controls"),this.trigger("usingcustomcontrols")))))},e.prototype.error=function(t){return void 0===t?this.error_||null:null===t?(this.error_=t,this.removeClass("vjs-error"),void(this.errorDisplay&&this.errorDisplay.close())):(this.error_=new st(t),this.addClass("vjs-error"),Ee.error("(CODE:"+this.error_.code+" "+st.errorTypes[this.error_.code]+")",this.error_.message,this.error_),void this.trigger("error"))},e.prototype.reportUserActivity=function(t){this.userActivity_=!0},e.prototype.userActive=function(t){if(void 0===t)return this.userActive_;if(t=!!t,t!==this.userActive_){if(this.userActive_=t,this.userActive_)return this.userActivity_=!0,this.removeClass("vjs-user-inactive"),this.addClass("vjs-user-active"),void this.trigger("useractive");this.tech_&&this.tech_.one("mousemove",function(t){t.stopPropagation(),t.preventDefault()}),this.userActivity_=!1,this.removeClass("vjs-user-active"),this.addClass("vjs-user-inactive"),this.trigger("userinactive")}},e.prototype.listenForUserActivity_=function(){var t=void 0,e=void 0,n=void 0,r=Ye(this,this.reportUserActivity),i=function(t){t.screenX===e&&t.screenY===n||(e=t.screenX,n=t.screenY,r())},o=function(){r(),this.clearInterval(t),t=this.setInterval(r,250)},s=function(e){r(),this.clearInterval(t)};this.on("mousedown",o),this.on("mousemove",i),this.on("mouseup",s),this.on("keydown",r),this.on("keyup",r);var a=void 0;this.setInterval(function(){if(this.userActivity_){this.userActivity_=!1,this.userActive(!0),this.clearTimeout(a);var t=this.options_.inactivityTimeout;t<=0||(a=this.setTimeout(function(){this.userActivity_||this.userActive(!1)},t))}},250)},e.prototype.playbackRate=function(t){return void 0!==t?void this.techCall_("setPlaybackRate",t):this.tech_&&this.tech_.featuresPlaybackRate?this.cache_.lastPlaybackRate||this.techGet_("playbackRate"):1},e.prototype.defaultPlaybackRate=function(t){return void 0!==t?this.techCall_("setDefaultPlaybackRate",t):this.tech_&&this.tech_.featuresPlaybackRate?this.techGet_("defaultPlaybackRate"):1},e.prototype.isAudio=function(t){return void 0!==t?void(this.isAudio_=!!t):!!this.isAudio_},e.prototype.addTextTrack=function(t,e,n){if(this.tech_)return this.tech_.addTextTrack(t,e,n)},e.prototype.addRemoteTextTrack=function(t,e){if(this.tech_)return this.tech_.addRemoteTextTrack(t,e)},e.prototype.removeRemoteTextTrack=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=t.track,n=void 0===e?arguments[0]:e;if(this.tech_)return this.tech_.removeRemoteTextTrack(n)},e.prototype.getVideoPlaybackQuality=function(){
return this.techGet_("getVideoPlaybackQuality")},e.prototype.videoWidth=function(){return this.tech_&&this.tech_.videoWidth&&this.tech_.videoWidth()||0},e.prototype.videoHeight=function(){return this.tech_&&this.tech_.videoHeight&&this.tech_.videoHeight()||0},e.prototype.language=function(t){return void 0===t?this.language_:void(this.language_=String(t).toLowerCase())},e.prototype.languages=function(){return tt(e.prototype.options_.languages,this.languages_)},e.prototype.toJSON=function(){var t=tt(this.options_),e=t.tracks;t.tracks=[];for(var n=0;n<e.length;n++){var r=e[n];r=tt(r),r.player=void 0,t.tracks[n]=r}return t},e.prototype.createModal=function(t,e){var n=this;e=e||{},e.content=t||"";var r=new Xn(this,e);return this.addChild(r),r.on("dispose",function(){n.removeChild(r)}),r.open(),r},e.getTagSettings=function(t){var e={sources:[],tracks:[]},n=k(t),r=n["data-setup"];if(m(t,"vjs-fluid")&&(n.fluid=!0),null!==r){var o=Fn(r||"{}"),s=o[0],a=o[1];s&&Ee.error(s),i(n,a)}if(i(e,n),t.hasChildNodes())for(var l=t.childNodes,c=0,u=l.length;c<u;c++){var h=l[c],p=h.nodeName.toLowerCase();"source"===p?e.sources.push(k(h)):"track"===p&&e.tracks.push(k(h))}return e},e.prototype.flexNotSupported_=function(){var t=be.createElement("i");return!("flexBasis"in t.style||"webkitFlexBasis"in t.style||"mozFlexBasis"in t.style||"msFlexBasis"in t.style||"msFlexOrder"in t.style)},e}(un);Ir.names.forEach(function(t){var e=Ir[t];Ko.prototype[e.getterName]=function(){return this.tech_?this.tech_[e.getterName]():(this[e.privateName]=this[e.privateName]||new e.ListClass,this[e.privateName])}}),Ko.players={};var $o=ve.navigator;Ko.prototype.options_={techOrder:Qr.defaultTechOrder_,html5:{},flash:{},inactivityTimeout:2e3,playbackRates:[],children:["mediaLoader","posterImage","textTrackDisplay","loadingSpinner","bigPlayButton","controlBar","errorDisplay","textTrackSettings","resizeManager"],language:$o&&($o.languages&&$o.languages[0]||$o.userLanguage||$o.language)||"en",languages:{},notSupportedMessage:"No compatible source was found for this media."},["ended","seeking","seekable","networkState","readyState"].forEach(function(t){Ko.prototype[t]=function(){return this.techGet_(t)}}),Xo.forEach(function(t){Ko.prototype["handleTech"+Q(t)+"_"]=function(){return this.trigger(t)}}),un.registerComponent("Player",Ko);var Yo="plugin",Go="activePlugins_",Jo={},Qo=function(t){return Jo.hasOwnProperty(t)},Zo=function(t){return Qo(t)?Jo[t]:void 0},ts=function(t,e){t[Go]=t[Go]||{},t[Go][e]=!0},es=function(t,e,n){var r=(n?"before":"")+"pluginsetup";t.trigger(r,e),t.trigger(r+":"+e.name,e)},ns=function(t,e){var n=function(){es(this,{name:t,plugin:e,instance:null},!0);var n=e.apply(this,arguments);return ts(this,t),es(this,{name:t,plugin:e,instance:n}),n};return Object.keys(e).forEach(function(t){n[t]=e[t]}),n},rs=function(t,e){return e.prototype.name=t,function(){es(this,{name:t,plugin:e,instance:null},!0);for(var n=arguments.length,r=Array(n),i=0;i<n;i++)r[i]=arguments[i];var o=new(Function.prototype.bind.apply(e,[null].concat([this].concat(r))));return this[t]=function(){return o},es(this,o.getEventHash()),o}},is=function(){function t(e){if(je(this,t),this.constructor===t)throw new Error("Plugin must be sub-classed; not directly instantiated.");this.player=e,G(this),delete this.trigger,J(this,this.constructor.defaultState),ts(e,this.name),this.dispose=Ye(this,this.dispose),e.on("dispose",this.dispose)}return t.prototype.version=function(){return this.constructor.VERSION},t.prototype.getEventHash=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return t.name=this.name,t.plugin=this.constructor,t.instance=this,t},t.prototype.trigger=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return K(this.eventBusEl_,t,this.getEventHash(e))},t.prototype.handleStateChanged=function(t){},t.prototype.dispose=function(){var t=this.name,e=this.player;this.trigger("dispose"),this.off(),e.off("dispose",this.dispose),e[Go][t]=!1,this.player=this.state=null,e[t]=rs(t,Jo[t])},t.isBasic=function(e){var n="string"==typeof e?Zo(e):e;return"function"==typeof n&&!t.prototype.isPrototypeOf(n.prototype)},t.registerPlugin=function(e,n){if("string"!=typeof e)throw new Error('Illegal plugin name, "'+e+'", must be a string, was '+("undefined"==typeof e?"undefined":xe(e))+".");if(Qo(e))Ee.warn('A plugin named "'+e+'" already exists. You may want to avoid re-registering plugins!');else if(Ko.prototype.hasOwnProperty(e))throw new Error('Illegal plugin name, "'+e+'", cannot share a name with an existing player method!');if("function"!=typeof n)throw new Error('Illegal plugin for "'+e+'", must be a function, was '+("undefined"==typeof n?"undefined":xe(n))+".");return Jo[e]=n,e!==Yo&&(t.isBasic(n)?Ko.prototype[e]=ns(e,n):Ko.prototype[e]=rs(e,n)),n},t.deregisterPlugin=function(t){if(t===Yo)throw new Error("Cannot de-register base plugin.");Qo(t)&&(delete Jo[t],delete Ko.prototype[t])},t.getPlugins=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:Object.keys(Jo),e=void 0;return t.forEach(function(t){var n=Zo(t);n&&(e=e||{},e[t]=n)}),e},t.getPluginVersion=function(t){var e=Zo(t);return e&&e.VERSION||""},t}();is.getPlugin=Zo,is.BASE_PLUGIN_NAME=Yo,is.registerPlugin(Yo,is),Ko.prototype.usingPlugin=function(t){return!!this[Go]&&this[Go][t]===!0},Ko.prototype.hasPlugin=function(t){return!!Qo(t)};var os=function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+("undefined"==typeof e?"undefined":xe(e)));t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(t.super_=e)},ss=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=function(){t.apply(this,arguments)},r={};"object"===("undefined"==typeof e?"undefined":xe(e))?(e.constructor!==Object.prototype.constructor&&(n=e.constructor),r=e):"function"==typeof e&&(n=e),os(n,t);for(var i in r)r.hasOwnProperty(i)&&(n.prototype[i]=r[i]);return n},as=function(t){return 0===t.indexOf("#")?t.slice(1):t};if(ue.hooks_={},ue.hooks=function(t,e){return ue.hooks_[t]=ue.hooks_[t]||[],e&&(ue.hooks_[t]=ue.hooks_[t].concat(e)),ue.hooks_[t]},ue.hook=function(t,e){ue.hooks(t,e)},ue.hookOnce=function(t,e){ue.hooks(t,[].concat(e).map(function(e){var n=function r(){return ue.removeHook(t,r),e.apply(void 0,arguments)};return n}))},ue.removeHook=function(t,e){var n=ue.hooks(t).indexOf(e);return!(n<=-1)&&(ue.hooks_[t]=ue.hooks_[t].slice(),ue.hooks_[t].splice(n,1),!0)},ve.VIDEOJS_NO_DYNAMIC_STYLE!==!0&&h()){var ls=Le(".vjs-styles-defaults");if(!ls){ls=Ke("vjs-styles-defaults");var cs=Le("head");cs&&cs.insertBefore(ls,cs.firstChild),$e(ls,"\n      .video-js {\n        width: 300px;\n        height: 150px;\n      }\n\n      .vjs-fluid {\n        padding-top: 56.25%\n      }\n    ")}}return Y(1,ue),ue.VERSION=pe,ue.options=Ko.prototype.options_,ue.getPlayers=function(){return Ko.players},ue.getPlayer=function(t){var e=Ko.players,n=void 0;if("string"==typeof t){var r=as(t),i=e[r];if(i)return i;n=Le("#"+r)}else n=t;if(p(n)){var o=n,s=o.player,a=o.playerId;if(s||e[a])return s||e[a]}},ue.getAllPlayers=function(){return Object.keys(Ko.players).map(function(t){return Ko.players[t]}).filter(Boolean)},ue.players=Ko.players,ue.getComponent=un.getComponent,ue.registerComponent=function(t,e){Qr.isTech(e)&&Ee.warn("The "+t+" tech was registered as a component. It should instead be registered using videojs.registerTech(name, tech)"),un.registerComponent.call(un,t,e)},ue.getTech=Qr.getTech,ue.registerTech=Qr.registerTech,ue.use=Ut,Object.defineProperty(ue,"middleware",{value:{},writeable:!1,enumerable:!0}),Object.defineProperty(ue.middleware,"TERMINATOR",{value:ei,writeable:!1,enumerable:!0}),ue.browser=Pn,ue.TOUCH_ENABLED=An,ue.extend=ss,ue.mergeOptions=tt,ue.bind=Ye,ue.registerPlugin=is.registerPlugin,ue.deregisterPlugin=is.deregisterPlugin,ue.plugin=function(t,e){return Ee.warn("videojs.plugin() is deprecated; use videojs.registerPlugin() instead"),is.registerPlugin(t,e)},ue.getPlugins=is.getPlugins,ue.getPlugin=is.getPlugin,ue.getPluginVersion=is.getPluginVersion,ue.addLanguage=function(t,e){var n;return t=(""+t).toLowerCase(),ue.options.languages=tt(ue.options.languages,(n={},n[t]=e,n)),ue.options.languages[t]},ue.log=Ee,ue.createTimeRange=ue.createTimeRanges=it,ue.formatTime=se,ue.setFormatTime=ie,ue.resetFormatTime=oe,ue.parseUrl=sr,ue.isCrossOrigin=cr,ue.EventTarget=Qe,ue.on=X,ue.one=$,ue.off=q,ue.trigger=K,ue.xhr=Tr,ue.TextTrack=wr,ue.AudioTrack=Er,ue.VideoTrack=Sr,["isEl","isTextNode","createEl","hasClass","addClass","removeClass","toggleClass","setAttributes","getAttributes","emptyEl","appendContent","insertContent"].forEach(function(t){ue[t]=function(){return Ee.warn("videojs."+t+"() is deprecated; use videojs.dom."+t+"() instead"),Re[t].apply(null,arguments)}}),ue.computedStyle=a,ue.dom=Re,ue.url=ur,ue});
!function(e){if("object"==typeof exports&&"undefined"!=typeof module)module.exports=e();else if("function"==typeof define&&define.amd)define([],e);else{var t;t="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:this,t.videojsContribHls=e()}}(function(){var e;return function t(e,i,n){function r(s,o){if(!i[s]){if(!e[s]){var u="function"==typeof require&&require;if(!o&&u)return u(s,!0);if(a)return a(s,!0);var d=new Error("Cannot find module '"+s+"'");throw d.code="MODULE_NOT_FOUND",d}var l=i[s]={exports:{}};e[s][0].call(l.exports,function(t){var i=e[s][1][t];return r(i?i:t)},l,l.exports,t,e,i,n)}return i[s].exports}for(var a="function"==typeof require&&require,s=0;s<n.length;s++)r(n[s]);return r}({1:[function(e,t,i){"use strict";function n(e){return e&&e.__esModule?e:{"default":e}}Object.defineProperty(i,"__esModule",{value:!0});var r=function(){function e(e,t){var i=[],n=!0,r=!1,a=void 0;try{for(var s,o=e[Symbol.iterator]();!(n=(s=o.next()).done)&&(i.push(s.value),!t||i.length!==t);n=!0);}catch(u){r=!0,a=u}finally{try{!n&&o["return"]&&o["return"]()}finally{if(r)throw a}}return i}return function(t,i){if(Array.isArray(t))return t;if(Symbol.iterator in Object(t))return e(t,i);throw new TypeError("Invalid attempt to destructure non-iterable instance")}}(),a=e("global/window"),s=n(a),o=function(e,t){for(var i=e.cues,n=0;n<i.length;n++){var r=i[n];if(t>=r.adStartTime&&t<=r.adEndTime)return r}return null},u=function(e,t){var i=arguments.length<=2||void 0===arguments[2]?0:arguments[2];if(e.segments)for(var n=i,a=void 0,u=0;u<e.segments.length;u++){var d=e.segments[u];if(a||(a=o(t,n+d.duration/2)),a){if("cueIn"in d){a.endTime=n,a.adEndTime=n,n+=d.duration,a=null;continue}if(n<a.endTime){n+=d.duration;continue}a.endTime+=d.duration}else if("cueOut"in d&&(a=new s["default"].VTTCue(n,n+d.duration,d.cueOut),a.adStartTime=n,a.adEndTime=n+parseFloat(d.cueOut),t.addCue(a)),"cueOutCont"in d){var l=void 0,f=void 0,c=d.cueOutCont.split("/").map(parseFloat),h=r(c,2);l=h[0],f=h[1],a=new s["default"].VTTCue(n,n+d.duration,""),a.adStartTime=n-l,a.adEndTime=a.adStartTime+f,t.addCue(a)}n+=d.duration}};i["default"]={updateAdCues:u,findAdCue:o},t.exports=i["default"]},{"global/window":28}],2:[function(e,t,i){"use strict";Object.defineProperty(i,"__esModule",{value:!0});var n=function(e,t){return e.start(t)+"-"+e.end(t)},r=function(e,t){var i=e.toString(16);return"00".substring(0,2-i.length)+i+(t%2?" ":"")},a=function(e){return e>=32&&e<126?String.fromCharCode(e):"."},s=function(e){var t={};return Object.keys(e).forEach(function(i){var n=e[i];ArrayBuffer.isView(n)?t[i]={bytes:n.buffer,byteOffset:n.byteOffset,byteLength:n.byteLength}:t[i]=n}),t},o={hexDump:function(e){for(var t=Array.prototype.slice.call(e),i=16,n="",s=void 0,o=void 0,u=0;u<t.length/i;u++)s=t.slice(u*i,u*i+i).map(r).join(""),o=t.slice(u*i,u*i+i).map(a).join(""),n+=s+" "+o+"\n";return n},tagDump:function(e){return o.hexDump(e.bytes)},textRanges:function(e){var t="",i=void 0;for(i=0;i<e.length;i++)t+=n(e,i)+" ";return t},createTransferableMessage:s};i["default"]=o,t.exports=i["default"]},{}],3:[function(e,t,i){"use strict";Object.defineProperty(i,"__esModule",{value:!0}),i["default"]={GOAL_BUFFER_LENGTH:30},t.exports=i["default"]},{}],4:[function(e,t,i){"use strict";function n(e){return e&&e.__esModule?e:{"default":e}}Object.defineProperty(i,"__esModule",{value:!0});var r=e("global/window"),a=n(r),s=e("aes-decrypter"),o=e("./bin-utils"),u=function(e){e.onmessage=function(e){var t=e.data,i=new Uint8Array(t.encrypted.bytes,t.encrypted.byteOffset,t.encrypted.byteLength),n=new Uint32Array(t.key.bytes,t.key.byteOffset,t.key.byteLength/4),r=new Uint32Array(t.iv.bytes,t.iv.byteOffset,t.iv.byteLength/4);new s.Decrypter(i,n,r,function(e,i){a["default"].postMessage((0,o.createTransferableMessage)({source:t.source,decrypted:i}),[i.buffer])})}};i["default"]=function(e){return new u(e)},t.exports=i["default"]},{"./bin-utils":2,"aes-decrypter":21,"global/window":28}],5:[function(e,t,i){(function(t){"use strict";function n(e){return e&&e.__esModule?e:{"default":e}}function r(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function a(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}Object.defineProperty(i,"__esModule",{value:!0});var s=function(){function e(e,t){for(var i=0;i<t.length;i++){var n=t[i];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}return function(t,i,n){return i&&e(t.prototype,i),n&&e(t,n),t}}(),o=function(e,t,i){for(var n=!0;n;){var r=e,a=t,s=i;n=!1,null===r&&(r=Function.prototype);var o=Object.getOwnPropertyDescriptor(r,a);if(void 0!==o){if("value"in o)return o.value;var u=o.get;if(void 0===u)return;return u.call(s)}var d=Object.getPrototypeOf(r);if(null===d)return;e=d,t=a,i=s,n=!0,o=d=void 0}},u=e("./playlist-loader"),d=n(u),l=e("./segment-loader"),f=n(l),c=e("./ranges"),h=n(c),p="undefined"!=typeof window?window.videojs:"undefined"!=typeof t?t.videojs:null,m=n(p),y=e("./ad-cue-tags"),g=n(y),v=e("./sync-controller"),_=n(v),b=e("videojs-contrib-media-sources/es5/codec-utils"),T=e("webworkify"),S=n(T),w=e("./decrypter-worker"),k=n(w),O=3e5,P=void 0,A=function(e,t){if(typeof e!=typeof t)return!0;if(Object.keys(e).length!==Object.keys(t).length)return!0;for(var i in e)if(e[i]!==t[i])return!0;return!1},E=function(e){var t={codecCount:0,videoCodec:null,videoObjectTypeIndicator:null,audioProfile:null},i=void 0;return t.codecCount=e.split(",").length,t.codecCount=t.codecCount||2,i=/(^|\s|,)+(avc1)([^ ,]*)/i.exec(e),i&&(t.videoCodec=i[2],t.videoObjectTypeIndicator=i[3]),t.audioProfile=/(^|\s|,)+mp4a.[0-9A-Fa-f]+\.([0-9A-Fa-f]+)/i.exec(e),t.audioProfile=t.audioProfile&&t.audioProfile[2],t},x=function(e){return e.replace(/avc1\.(\d+)\.(\d+)/i,function(e){return(0,b.translateLegacyCodecs)([e])[0]})};i.mapLegacyAvcCodecs_=x;var L=function(e,t){var i="mp2t",n={videoCodec:"avc1",videoObjectTypeIndicator:".4d400d",audioProfile:"2"},r=[],a=void 0,s=null;if(!t)return[];t.segments&&t.segments.length&&t.segments[0].map&&(i="mp4"),a=t.attributes||{},a.CODECS&&!function(){var e=E(a.CODECS);Object.keys(e).forEach(function(t){n[t]=e[t]||n[t]})}(),e.mediaGroups.AUDIO&&(r=e.mediaGroups.AUDIO[a.AUDIO]);for(var o in r){if(s&&!!r[o].uri!=!!s.uri)return["video/"+i+'; codecs="'+n.videoCodec+n.videoObjectTypeIndicator+", mp4a.40."+n.audioProfile+'"',"audio/"+i+'; codecs="mp4a.40.'+n.audioProfile+'"'];s=r[o]}return s&&s.uri?["video/"+i+'; codecs="'+n.videoCodec+n.videoObjectTypeIndicator+'"',"audio/"+i+'; codecs="mp4a.40.'+n.audioProfile+'"']:["video/"+i+'; codecs="'+n.videoCodec+n.videoObjectTypeIndicator+", mp4a.40."+n.audioProfile+'"']};i.mimeTypesForPlaylist_=L;var I=function(e){function t(e){var i=this;r(this,t),o(Object.getPrototypeOf(t.prototype),"constructor",this).call(this);var n=e.url,a=e.withCredentials,s=e.mode,u=e.tech,l=e.bandwidth,c=e.externHls,h=e.useCueTags;if(!n)throw new Error("A non-empty playlist URL is required");P=c,this.withCredentials=a,this.tech_=u,this.hls_=u.hls,this.mode_=s,this.useCueTags_=h,this.useCueTags_&&(this.cueTagsTrack_=this.tech_.addTextTrack("metadata","ad-cues"),this.cueTagsTrack_.inBandMetadataTrackDispatchType=""),this.audioTracks_=[],this.requestOptions_={withCredentials:this.withCredentials,timeout:null},this.audioGroups_={},this.mediaSource=new m["default"].MediaSource({mode:s}),this.audioinfo_=null,this.mediaSource.on("audioinfo",this.handleAudioinfoUpdate_.bind(this)),this.mediaSource.addEventListener("sourceopen",this.handleSourceOpen_.bind(this)),this.seekable_=m["default"].createTimeRanges(),this.hasPlayed_=function(){return!1},this.syncController_=new _["default"],this.decrypter_=(0,S["default"])(k["default"]);var p={hls:this.hls_,mediaSource:this.mediaSource,currentTime:this.tech_.currentTime.bind(this.tech_),seekable:function(){return i.seekable()},seeking:function(){return i.tech_.seeking()},setCurrentTime:function(e){return i.tech_.setCurrentTime(e)},hasPlayed:function(){return i.hasPlayed_()},bandwidth:l,syncController:this.syncController_,decrypter:this.decrypter_,loaderType:"main"};this.masterPlaylistLoader_=new d["default"](n,this.hls_,this.withCredentials),this.setupMasterPlaylistLoaderListeners_(),this.audioPlaylistLoader_=null,this.mainSegmentLoader_=new f["default"](p),p.loaderType="audio",this.audioSegmentLoader_=new f["default"](p),this.decrypter_.onmessage=function(e){"main"===e.data.source?i.mainSegmentLoader_.handleDecrypted_(e.data):"audio"===e.data.source&&i.audioSegmentLoader_.handleDecrypted_(e.data)},this.setupSegmentLoaderListeners_(),this.masterPlaylistLoader_.start()}return a(t,e),s(t,[{key:"setupMasterPlaylistLoaderListeners_",value:function(){var e=this;this.masterPlaylistLoader_.on("loadedmetadata",function(){var t=e.masterPlaylistLoader_.media(),i=1.5*e.masterPlaylistLoader_.targetDuration*1e3;e.requestOptions_.timeout=i,t.endList&&"none"!==e.tech_.preload()&&(e.mainSegmentLoader_.playlist(t,e.requestOptions_),e.mainSegmentLoader_.load()),e.fillAudioTracks_(),e.setupAudio();try{e.setupSourceBuffers_()}catch(n){return m["default"].log.warn("Failed to create SourceBuffers",n),e.mediaSource.endOfStream("decode")}e.setupFirstPlay(),e.trigger("audioupdate"),e.trigger("selectedinitialmedia")}),this.masterPlaylistLoader_.on("loadedplaylist",function(){var t=e.masterPlaylistLoader_.media();return t?(e.useCueTags_&&e.updateAdCues_(t),e.mainSegmentLoader_.playlist(t,e.requestOptions_),e.updateDuration(),void(t.endList||!function(){var t=function(){var t=e.seekable();0!==t.length&&e.mediaSource.addSeekableRange_(t.start(0),t.end(0))};e.duration()!==1/0?!function(){var i=function n(){e.duration()===1/0?t():e.tech_.one("durationchange",n)};e.tech_.one("durationchange",i)}():t()}())):(e.initialMedia_=e.selectPlaylist(),void e.masterPlaylistLoader_.media(e.initialMedia_))}),this.masterPlaylistLoader_.on("error",function(){e.blacklistCurrentPlaylist(e.masterPlaylistLoader_.error)}),this.masterPlaylistLoader_.on("mediachanging",function(){e.mainSegmentLoader_.abort(),e.mainSegmentLoader_.pause()}),this.masterPlaylistLoader_.on("mediachange",function(){var t=e.masterPlaylistLoader_.media(),i=1.5*e.masterPlaylistLoader_.targetDuration*1e3,n=void 0,r=void 0;e.masterPlaylistLoader_.isLowestEnabledRendition_()?e.requestOptions_.timeout=0:e.requestOptions_.timeout=i,e.mainSegmentLoader_.playlist(t,e.requestOptions_),e.mainSegmentLoader_.load(),n=e.activeAudioGroup(),r=n.filter(function(e){return e.enabled})[0],r||(e.setupAudio(),e.trigger("audioupdate")),e.tech_.trigger({type:"mediachange",bubbles:!0})})}},{key:"setupSegmentLoaderListeners_",value:function(){var e=this;this.mainSegmentLoader_.on("progress",function(){e.masterPlaylistLoader_.media(e.selectPlaylist()),e.trigger("progress")}),this.mainSegmentLoader_.on("error",function(){e.blacklistCurrentPlaylist(e.mainSegmentLoader_.error())}),this.mainSegmentLoader_.on("syncinfoupdate",function(){e.onSyncInfoUpdate_()}),this.audioSegmentLoader_.on("syncinfoupdate",function(){e.onSyncInfoUpdate_()}),this.audioSegmentLoader_.on("error",function(){m["default"].log.warn("Problem encountered with the current alternate audio track. Switching back to default."),e.audioSegmentLoader_.abort(),e.audioPlaylistLoader_=null,e.setupAudio()})}},{key:"handleAudioinfoUpdate_",value:function(e){if(P.supportsAudioInfoChange_()||!this.audioInfo_||!A(this.audioInfo_,e.info))return void(this.audioInfo_=e.info);var t="had different audio properties (channels, sample rate, etc.) or changed in some other way.  This behavior is currently unsupported in Firefox 48 and below due to an issue: \n\nhttps://bugzilla.mozilla.org/show_bug.cgi?id=1247138\n\n",i=this.activeAudioGroup().map(function(e){return e.enabled}).indexOf(!0),n=this.activeAudioGroup()[i],r=this.activeAudioGroup().filter(function(e){return e.properties_&&e.properties_["default"]})[0];this.audioPlaylistLoader_?(t="The audio track '"+n.label+"' that we tried to "+("switch to "+t+" Unfortunately this means we will have to ")+("return you to the main track '"+r.label+"'. Sorry!"),r.enabled=!0,this.activeAudioGroup().splice(i,1),this.trigger("audioupdate")):(t="The rendition that we tried to switch to "+t+"Unfortunately that means we will have to blacklist the current playlist and switch to another. Sorry!",this.blacklistCurrentPlaylist()),m["default"].log.warn(t),this.setupAudio()}},{key:"mediaRequests_",value:function(){return this.audioSegmentLoader_.mediaRequests+this.mainSegmentLoader_.mediaRequests}},{key:"mediaTransferDuration_",value:function(){return this.audioSegmentLoader_.mediaTransferDuration+this.mainSegmentLoader_.mediaTransferDuration}},{key:"mediaBytesTransferred_",value:function(){return this.audioSegmentLoader_.mediaBytesTransferred+this.mainSegmentLoader_.mediaBytesTransferred}},{key:"mediaSecondsLoaded_",value:function(){return Math.max(this.audioSegmentLoader_.mediaSecondsLoaded+this.mainSegmentLoader_.mediaSecondsLoaded)}},{key:"fillAudioTracks_",value:function(){var e=this.master(),t=e.mediaGroups||{};t&&t.AUDIO&&0!==Object.keys(t.AUDIO).length&&"html5"===this.mode_||(t.AUDIO={main:{"default":{"default":!0}}});for(var i in t.AUDIO){this.audioGroups_[i]||(this.audioGroups_[i]=[]);for(var n in t.AUDIO[i]){var r=t.AUDIO[i][n],a=new m["default"].AudioTrack({id:n,kind:r["default"]?"main":"alternative",enabled:!1,language:r.language,label:n});a.properties_=r,this.audioGroups_[i].push(a)}}(this.activeAudioGroup().filter(function(e){return e.properties_["default"]})[0]||this.activeAudioGroup()[0]).enabled=!0}},{key:"load",value:function(){this.mainSegmentLoader_.load(),this.audioPlaylistLoader_&&this.audioSegmentLoader_.load()}},{key:"activeAudioGroup",value:function(){var e=this.masterPlaylistLoader_.media(),t=void 0;return e.attributes&&e.attributes.AUDIO&&(t=this.audioGroups_[e.attributes.AUDIO]),t||this.audioGroups_.main}},{key:"setupAudio",value:function(){var e=this,t=this.activeAudioGroup(),i=t.filter(function(e){return e.enabled})[0];return i||(i=t.filter(function(e){return e.properties_["default"]})[0]||t[0],i.enabled=!0),this.audioPlaylistLoader_&&(this.audioPlaylistLoader_.dispose(),this.audioPlaylistLoader_=null),this.audioSegmentLoader_.pause(),i.properties_.resolvedUri?(this.audioSegmentLoader_.resetEverything(),this.audioPlaylistLoader_=new d["default"](i.properties_.resolvedUri,this.hls_,this.withCredentials),this.audioPlaylistLoader_.start(),this.audioPlaylistLoader_.on("loadedmetadata",function(){var t=e.audioPlaylistLoader_.media();e.audioSegmentLoader_.playlist(t,e.requestOptions_),(!e.tech_.paused()||t.endList&&"none"!==e.tech_.preload())&&e.audioSegmentLoader_.load(),t.endList||e.audioPlaylistLoader_.trigger("firstplay")}),this.audioPlaylistLoader_.on("loadedplaylist",function(){var t=void 0;return e.audioPlaylistLoader_&&(t=e.audioPlaylistLoader_.media()),t?void e.audioSegmentLoader_.playlist(t,e.requestOptions_):void e.audioPlaylistLoader_.media(e.audioPlaylistLoader_.playlists.master.playlists[0])}),void this.audioPlaylistLoader_.on("error",function(){m["default"].log.warn("Problem encountered loading the alternate audio track. Switching back to default."),e.audioSegmentLoader_.abort(),e.setupAudio()})):void this.mainSegmentLoader_.resetEverything()}},{key:"fastQualityChange_",value:function(){var e=this.selectPlaylist();e!==this.masterPlaylistLoader_.media()&&(this.masterPlaylistLoader_.media(e),this.mainSegmentLoader_.resetLoader(),this.audiosegmentloader_&&this.audioSegmentLoader_.resetLoader())}},{key:"play",value:function(){if(!this.setupFirstPlay())return this.tech_.ended()&&this.tech_.setCurrentTime(0),this.hasPlayed_()&&this.load(),this.tech_.duration()===1/0&&this.tech_.currentTime()<this.tech_.seekable().start(0)?this.tech_.setCurrentTime(this.tech_.seekable().start(0)):void 0}},{key:"setupFirstPlay",value:function(){var e=void 0,t=this.masterPlaylistLoader_.media();return!(!t||this.tech_.paused()||this.hasPlayed_())&&(t.endList||(this.trigger("firstplay"),e=this.seekable(),e.length&&this.tech_.setCurrentTime(e.end(0))),this.hasPlayed_=function(){return!0},this.load(),!0)}},{key:"handleSourceOpen_",value:function(){try{this.setupSourceBuffers_()}catch(e){return m["default"].log.warn("Failed to create Source Buffers",e),this.mediaSource.endOfStream("decode")}this.tech_.autoplay()&&this.tech_.play(),this.trigger("sourceopen")}},{key:"blacklistCurrentPlaylist",value:function(){var e=arguments.length<=0||void 0===arguments[0]?{}:arguments[0],t=void 0,i=void 0;return(t=e.playlist||this.masterPlaylistLoader_.media())?(t.excludeUntil=Date.now()+O,(i=this.selectPlaylist())?(m["default"].log.warn("Problem encountered with the current HLS playlist. Switching to another playlist."),this.masterPlaylistLoader_.media(i)):(m["default"].log.warn("Problem encountered with the current HLS playlist. No suitable alternatives found."),this.error=e,this.mediaSource.endOfStream("network"))):(this.error=e,this.mediaSource.endOfStream("network"))}},{key:"pauseLoading",value:function(){this.mainSegmentLoader_.pause(),this.audioPlaylistLoader_&&this.audioSegmentLoader_.pause()}},{key:"setCurrentTime",value:function(e){var t=h["default"].findRange(this.tech_.buffered(),e);return this.masterPlaylistLoader_&&this.masterPlaylistLoader_.media()&&this.masterPlaylistLoader_.media().segments?t&&t.length?e:(this.mainSegmentLoader_.resetEverything(),this.mainSegmentLoader_.abort(),this.audioPlaylistLoader_&&(this.audioSegmentLoader_.resetEverything(),this.audioSegmentLoader_.abort()),void(this.tech_.paused()||(this.mainSegmentLoader_.load(),this.audioPlaylistLoader_&&this.audioSegmentLoader_.load()))):0}},{key:"duration",value:function(){return this.masterPlaylistLoader_?this.mediaSource?this.mediaSource.duration:P.Playlist.duration(this.masterPlaylistLoader_.media()):0}},{key:"seekable",value:function(){return this.seekable_}},{key:"onSyncInfoUpdate_",value:function(){var e=void 0,t=void 0,i=void 0;if(this.masterPlaylistLoader_&&(e=this.masterPlaylistLoader_.media(),e&&(t=P.Playlist.seekable(e),0!==t.length&&(!this.audioPlaylistLoader_||(i=P.Playlist.seekable(this.audioPlaylistLoader_.media()),0!==i.length)))))return i?void(this.seekable_=m["default"].createTimeRanges([[i.start(0)>t.start(0)?i.start(0):t.start(0),i.end(0)<t.end(0)?i.end(0):t.end(0)]])):void(this.seekable_=t)}},{key:"updateDuration",value:function(){var e=this,t=this.mediaSource.duration,i=P.Playlist.duration(this.masterPlaylistLoader_.media()),n=this.tech_.buffered(),r=function a(){e.mediaSource.duration=i,e.tech_.trigger("durationchange"),e.mediaSource.removeEventListener("sourceopen",a)};n.length>0&&(i=Math.max(i,n.end(n.length-1))),t!==i&&("open"!==this.mediaSource.readyState?this.mediaSource.addEventListener("sourceopen",r):r())}},{key:"dispose",value:function(){this.decrypter_.terminate(),this.masterPlaylistLoader_.dispose(),this.mainSegmentLoader_.dispose(),this.audioPlaylistLoader_&&this.audioPlaylistLoader_.dispose(),this.audioSegmentLoader_.dispose()}},{key:"master",value:function(){return this.masterPlaylistLoader_.master}},{key:"media",value:function(){return this.masterPlaylistLoader_.media()||this.initialMedia_}},{key:"setupSourceBuffers_",value:function(){var e=this.masterPlaylistLoader_.media(),t=void 0;if(e&&"open"===this.mediaSource.readyState){if(t=L(this.masterPlaylistLoader_.master,e),t.length<1)return this.error="No compatible SourceBuffer configuration for the variant stream:"+e.resolvedUri,this.mediaSource.endOfStream("decode");this.mainSegmentLoader_.mimeType(t[0]),t[1]&&this.audioSegmentLoader_.mimeType(t[1]),this.excludeIncompatibleVariants_(e)}}},{key:"excludeIncompatibleVariants_",value:function(e){var t=this.masterPlaylistLoader_.master,i=2,n=null,r=void 0;e.attributes&&e.attributes.CODECS&&(r=E(e.attributes.CODECS),n=r.videoCodec,i=r.codecCount),t.playlists.forEach(function(e){var t={codecCount:2,videoCodec:null};if(e.attributes&&e.attributes.CODECS){var r=e.attributes.CODECS;t=E(r),window.MediaSource&&window.MediaSource.isTypeSupported&&!window.MediaSource.isTypeSupported('video/mp4; codecs="'+x(r)+'"')&&(e.excludeUntil=1/0)}t.codecCount!==i&&(e.excludeUntil=1/0),t.videoCodec!==n&&(e.excludeUntil=1/0)})}},{key:"updateAdCues_",value:function(e){var t=0,i=this.seekable();i.length&&(t=i.start(0)),g["default"].updateAdCues(e,this.cueTagsTrack_,t)}}]),t}(m["default"].EventTarget);i.MasterPlaylistController=I}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{"./ad-cue-tags":1,"./decrypter-worker":4,"./playlist-loader":7,"./ranges":9,"./segment-loader":13,"./sync-controller":16,"videojs-contrib-media-sources/es5/codec-utils":44,webworkify:77}],6:[function(e,t,i){(function(n){"use strict";function r(e){return e&&e.__esModule?e:{"default":e}}function a(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}Object.defineProperty(i,"__esModule",{value:!0});var s=function(){function e(e,t){for(var i=0;i<t.length;i++){var n=t[i];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}return function(t,i,n){return i&&e(t.prototype,i),n&&e(t,n),t}}(),o=e("./ranges"),u=r(o),d="undefined"!=typeof window?window.videojs:"undefined"!=typeof n?n.videojs:null,l=r(d),f=["seeking","seeked","pause","playing","error"],c=function(){function e(t){var i=this;a(this,e),this.tech_=t.tech,this.seekable=t.seekable,this.consecutiveUpdates=0,this.lastRecordedTime=null,this.timer_=null,this.checkCurrentTimeTimeout_=null,t.debug&&(this.logger_=l["default"].log.bind(l["default"],"playback-watcher ->")),this.logger_("initialize");var n=function(){return i.waiting_()},r=function(){return i.cancelTimer_()};this.tech_.on("waiting",n),this.tech_.on(f,r),this.monitorCurrentTime_(),this.dispose=function(){i.logger_("dispose"),i.tech_.off("waiting",n),i.tech_.off(f,r),i.checkCurrentTimeTimeout_&&clearTimeout(i.checkCurrentTimeTimeout_),i.cancelTimer_()}}return s(e,[{key:"monitorCurrentTime_",value:function(){this.checkCurrentTime_(),this.checkCurrentTimeTimeout_&&clearTimeout(this.checkCurrentTimeTimeout_),this.checkCurrentTimeTimeout_=setTimeout(this.monitorCurrentTime_.bind(this),250)}},{key:"checkCurrentTime_",value:function(){if(!this.tech_.paused()&&!this.tech_.seeking()){var e=this.tech_.currentTime();this.consecutiveUpdates>=5&&e===this.lastRecordedTime?(this.consecutiveUpdates++,this.waiting_()):e===this.lastRecordedTime?this.consecutiveUpdates++:(this.consecutiveUpdates=0,this.lastRecordedTime=e)}}},{key:"cancelTimer_",value:function(){this.consecutiveUpdates=0,this.timer_&&(this.logger_("cancelTimer_"),clearTimeout(this.timer_)),this.timer_=null}},{key:"waiting_",value:function(){var e=this.seekable(),t=this.tech_.currentTime();if(!this.tech_.seeking()&&null===this.timer_){if(this.fellOutOfLiveWindow_(e,t)){var i=e.end(e.length-1);return this.logger_("Fell out of live window at time "+t+". Seeking to live point (seekable end) "+i),this.cancelTimer_(),this.tech_.setCurrentTime(i),void this.tech_.trigger("liveresync")}var n=this.tech_.buffered(),r=u["default"].findNextRange(n,t);if(this.videoUnderflow_(r,n,t))return this.cancelTimer_(),this.tech_.setCurrentTime(t),void this.tech_.trigger("videounderflow");if(r.length>0){var a=r.start(0)-t;this.logger_("Stopped at "+t+", setting timer for "+a+", seeking to "+r.start(0)),this.timer_=setTimeout(this.skipTheGap_.bind(this),1e3*a,t)}}}},{key:"fellOutOfLiveWindow_",value:function(e,t){return!!(e.length&&e.start(0)>0&&t<e.start(0))}},{key:"videoUnderflow_",value:function(e,t,i){if(0===e.length){var n=this.gapFromVideoUnderflow_(t,i);if(n)return this.logger_("Encountered a gap in video from "+n.start+" to "+n.end+". Seeking to current time "+i),!0}return!1}},{key:"skipTheGap_",value:function(e){var t=this.tech_.buffered(),i=this.tech_.currentTime(),n=u["default"].findNextRange(t,i);this.cancelTimer_(),0!==n.length&&i===e&&(this.logger_("skipTheGap_:","currentTime:",i,"scheduled currentTime:",e,"nextRange start:",n.start(0)),this.tech_.setCurrentTime(n.start(0)+u["default"].TIME_FUDGE_FACTOR))}},{key:"gapFromVideoUnderflow_",value:function(e,t){for(var i=u["default"].findGaps(e),n=0;n<i.length;n++){var r=i.start(n),a=i.end(n);if(t-r<4&&t-r>2)return{start:r,end:a}}return null}},{key:"logger_",value:function(){}}]),e}();i["default"]=c,t.exports=i["default"]}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{"./ranges":9}],7:[function(e,t,i){(function(n){"use strict";function r(e){return e&&e.__esModule?e:{"default":e}}Object.defineProperty(i,"__esModule",{value:!0});var a=e("./resolve-url"),s=r(a),o="undefined"!=typeof window?window.videojs:"undefined"!=typeof n?n.videojs:null,u=e("./playlist.js"),d=e("./stream"),l=r(d),f=e("m3u8-parser"),c=r(f),h=e("global/window"),p=r(h),m=function(e,t,i){var n=t.slice(),r=void 0,a=void 0;for(i=i||0,r=Math.min(e.length,t.length+i),a=i;a<r;a++)n[a-i]=(0,o.mergeOptions)(e[a],n[a-i]);return n},y=function(e,t){for(var i=!1,n=(0,o.mergeOptions)(e,{}),r=e.playlists.length,a=void 0,u=void 0,d=void 0;r--;)if(a=n.playlists[r],a.uri===t.uri){if(a.segments&&t.segments&&a.segments.length===t.segments.length&&a.mediaSequence===t.mediaSequence)continue;for(n.playlists[r]=(0,o.mergeOptions)(a,t),n.playlists[t.uri]=n.playlists[r],a.segments&&(n.playlists[r].segments=m(a.segments,t.segments,t.mediaSequence-a.mediaSequence)),d=0,n.playlists[r].segments&&(d=n.playlists[r].segments.length);d--;)u=n.playlists[r].segments[d],u.resolvedUri||(u.resolvedUri=(0,s["default"])(a.resolvedUri,u.uri)),u.key&&!u.key.resolvedUri&&(u.key.resolvedUri=(0,s["default"])(a.resolvedUri,u.key.uri)),u.map&&!u.map.resolvedUri&&(u.map.resolvedUri=(0,s["default"])(a.resolvedUri,u.map.uri));i=!0}return i?n:null},g=function v(e,t,i){var n=this,r=this,a=void 0,o=void 0,d=void 0,l=void 0,f=void 0;if(v.prototype.constructor.call(this),this.hls_=t,!e)throw new Error("A non-empty playlist URL is required");l=function(e,t,i){r.setBandwidth(d||e),d=null,i&&(r.state=i),r.error={playlist:r.master.playlists[t],status:e.status,message:"HLS playlist request error at URL: "+t,responseText:e.responseText,code:e.status>=500?4:2},r.trigger("error")},f=function(e,t){var i=void 0,n=void 0,a=void 0;r.setBandwidth(d||e),d=null,r.state="HAVE_METADATA",i=new c["default"].Parser,i.push(e.responseText),i.end(),i.manifest.uri=t,a=y(r.master,i.manifest),n=1e3*(i.manifest.targetDuration||10),r.targetDuration=i.manifest.targetDuration,a?(r.master=a,r.media_=r.master.playlists[i.manifest.uri]):n/=2,r.media().endList||(p["default"].clearTimeout(o),o=p["default"].setTimeout(function(){r.trigger("mediaupdatetimeout")},n)),r.trigger("loadedplaylist")},r.state="HAVE_NOTHING",a=this.dispose,r.dispose=function(){r.stopRequest(),p["default"].clearTimeout(o),a.call(this)},r.stopRequest=function(){if(d){var e=d;d=null,e.onreadystatechange=null,e.abort()}},r.enabledPlaylists_=function(){return r.master.playlists.filter(u.isEnabled).length},r.isLowestEnabledRendition_=function(){var e=r.media();if(!e||!e.attributes)return!1;var t=r.media().attributes.BANDWIDTH||0;return!(r.master.playlists.filter(function(e){var i=(0,u.isEnabled)(e);if(!i)return!1;var n=0;return e&&e.attributes&&(n=e.attributes.BANDWIDTH),n<=t}).length>1)},r.media=function(e){var t=r.state,n=void 0;if(!e)return r.media_;if("HAVE_NOTHING"===r.state)throw new Error("Cannot switch media playlist from "+r.state);if("string"==typeof e){if(!r.master.playlists[e])throw new Error("Unknown playlist URI: "+e);e=r.master.playlists[e]}if(n=!r.media_||e.uri!==r.media_.uri,r.master.playlists[e.uri].endList)return d&&(d.onreadystatechange=null,d.abort(),d=null),r.state="HAVE_METADATA",r.media_=e,void(n&&(r.trigger("mediachanging"),r.trigger("mediachange")));if(n){if(r.state="SWITCHING_MEDIA",d){if((0,s["default"])(r.master.uri,e.uri)===d.url)return;d.onreadystatechange=null,d.abort(),d=null}this.media_&&this.trigger("mediachanging"),d=this.hls_.xhr({uri:(0,s["default"])(r.master.uri,e.uri),withCredentials:i},function(i,n){if(d){if(i)return l(d,e.uri,t);f(n,e.uri),"HAVE_MASTER"===t?r.trigger("loadedmetadata"):r.trigger("mediachange")}})}},r.setBandwidth=function(e){r.bandwidth=e.bandwidth},r.on("mediaupdatetimeout",function(){"HAVE_METADATA"===r.state&&(r.state="HAVE_CURRENT_METADATA",d=this.hls_.xhr({uri:(0,s["default"])(r.master.uri,r.media().uri),withCredentials:i},function(e,t){if(d)return e?l(d,r.media().uri):void f(d,r.media().uri)}))}),r.on("firstplay",function(){var e=r.media();e&&(e.syncInfo={mediaSequence:e.mediaSequence,time:0})}),r.pause=function(){r.stopRequest(),p["default"].clearTimeout(o)},r.load=function(){r.started?r.media().endList?r.trigger("loadedplaylist"):r.trigger("mediaupdatetimeout"):r.start()},r.start=function(){r.started=!0,d=n.hls_.xhr({uri:e,withCredentials:i},function(t,i){var n=void 0,a=void 0,o=void 0;if(d){if(d=null,t)return r.error={status:i.status,message:"HLS playlist request error at URL: "+e,responseText:i.responseText,code:2},r.trigger("error");if(n=new c["default"].Parser,n.push(i.responseText),n.end(),r.state="HAVE_MASTER",n.manifest.uri=e,n.manifest.playlists){for(r.master=n.manifest,o=r.master.playlists.length;o--;)a=r.master.playlists[o],r.master.playlists[a.uri]=a,a.resolvedUri=(0,s["default"])(r.master.uri,a.uri);for(var u in r.master.mediaGroups.AUDIO)for(var l in r.master.mediaGroups.AUDIO[u]){var h=r.master.mediaGroups.AUDIO[u][l];h.uri&&(h.resolvedUri=(0,s["default"])(r.master.uri,h.uri))}return r.trigger("loadedplaylist"),void(d||r.media(n.manifest.playlists[0]))}return r.master={mediaGroups:{AUDIO:{},VIDEO:{},"CLOSED-CAPTIONS":{},SUBTITLES:{}},uri:p["default"].location.href,playlists:[{uri:e}]},r.master.playlists[e]=r.master.playlists[0],r.master.playlists[0].resolvedUri=e,f(i,e),r.trigger("loadedmetadata")}})}};g.prototype=new l["default"],i["default"]=g,t.exports=i["default"]}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{"./playlist.js":8,"./resolve-url":12,"./stream":15,"global/window":28,"m3u8-parser":29}],8:[function(e,t,i){(function(t){"use strict";function n(e){return e&&e.__esModule?e:{"default":e}}Object.defineProperty(i,"__esModule",{value:!0});var r="undefined"!=typeof window?window.videojs:"undefined"!=typeof t?t.videojs:null,a=e("global/window"),s=n(a),o={UNSAFE_LIVE_SEGMENTS:3},u=function(e,t){var i=0,n=t-e.mediaSequence,r=e.segments[n];if(r){if("undefined"!=typeof r.start)return{result:r.start,precise:!0};if("undefined"!=typeof r.end)return{result:r.end-r.duration,precise:!0}}for(;n--;){if(r=e.segments[n],"undefined"!=typeof r.end)return{result:i+r.end,precise:!0};if(i+=r.duration,"undefined"!=typeof r.start)return{result:i+r.start,precise:!0}}return{result:i,precise:!1}},d=function(e,t){for(var i=0,n=void 0,r=t-e.mediaSequence;r<e.segments.length;r++){if(n=e.segments[r],"undefined"!=typeof n.start)return{result:n.start-i,precise:!0};if(i+=n.duration,"undefined"!=typeof n.end)return{result:n.end-i,precise:!0}}return{result:-1,precise:!1}},l=function(e,t,i){var n=void 0,r=void 0;return"undefined"==typeof t&&(t=e.mediaSequence+e.segments.length),t<e.mediaSequence?0:(n=u(e,t),n.precise?n.result:(r=d(e,t),r.precise?r.result:n.result+i))},f=function(e,t,i){if(!e)return 0;if("number"!=typeof i&&(i=0),"undefined"==typeof t){if(e.totalDuration)return e.totalDuration;if(!e.endList)return s["default"].Infinity}return l(e,t,i)};i.duration=f;var c=function(e,t,i){var n=0;if(t>i){var r=[i,t];t=r[0],i=r[1]}if(t<0){for(var a=t;a<Math.min(0,i);a++)n+=e.targetDuration;t=0}for(var a=t;a<i;a++)n+=e.segments[a].duration;return n};i.sumDurations=c;var h=function(e){if(!e||!e.segments)return[null,null];for(var t=e.syncInfo||null,i=null,n=0,r=e.segments.length;n<r;n++){
var a=e.segments[n];if("undefined"!=typeof a.start){i={mediaSequence:e.mediaSequence+n,time:a.start};break}}return{expiredSync:t,segmentSync:i}},p=function(e,t,i){if(t&&i){var n=t.mediaSequence-e.mediaSequence,r=i.mediaSequence-e.mediaSequence,a=void 0,s=void 0;return Math.abs(n)>Math.abs(r)?(a=r,s=-i.time):(a=n,s=t.time),Math.abs(s+c(e,a,0))}if(t){var a=t.mediaSequence-e.mediaSequence;return t.time+c(e,a,0)}if(i){var a=i.mediaSequence-e.mediaSequence;return i.time-c(e,a,0)}},m=function(e){if(!e||!e.segments)return(0,r.createTimeRange)();if(e.endList)return(0,r.createTimeRange)(0,f(e));var t=h(e),i=t.expiredSync,n=t.segmentSync;if(!i&&!n)return(0,r.createTimeRange)();var a=p(e,i,n),s=a,u=Math.max(0,e.segments.length-o.UNSAFE_LIVE_SEGMENTS),d=l(e,e.mediaSequence+u,a);return(0,r.createTimeRange)(s,d)};i.seekable=m;var y=function(e){return e-Math.floor(e)===0},g=function(e,t){if(y(t))return t+.1*e;for(var i=t.toString().split(".")[1].length,n=1;n<=i;n++){var r=Math.pow(10,n),a=t*r;if(y(a)||n===i)return(a+e)/r}},v=g.bind(null,1),_=g.bind(null,-1),b=function(e,t,i,n){var r=void 0,a=void 0,s=e.segments.length,o=t-n;if(o<0){if(i>0)for(r=i-1;r>=0;r--)if(a=e.segments[r],o+=_(a.duration),o>0)return{mediaIndex:r,startTime:n-c(e,i,r)};return{mediaIndex:0,startTime:t}}if(i<0){for(r=i;r<0;r++)if(o-=e.targetDuration,o<0)return{mediaIndex:0,startTime:t};i=0}for(r=i;r<s;r++)if(a=e.segments[r],o-=v(a.duration),o<0)return{mediaIndex:r,startTime:n+c(e,i,r)};return{mediaIndex:s-1,startTime:t}};i.getMediaInfoForTime_=b;var T=function(e){return e.excludeUntil&&e.excludeUntil>Date.now()};i.isBlacklisted=T;var S=function(e){var t=T(e);return!e.disabled&&!t};i.isEnabled=S,o.duration=f,o.seekable=m,o.getMediaInfoForTime_=b,o.isEnabled=S,o.isBlacklisted=T,i["default"]=o}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{"global/window":28}],9:[function(e,t,i){(function(e){"use strict";function n(e){return e&&e.__esModule?e:{"default":e}}Object.defineProperty(i,"__esModule",{value:!0});var r=function(){function e(e,t){var i=[],n=!0,r=!1,a=void 0;try{for(var s,o=e[Symbol.iterator]();!(n=(s=o.next()).done)&&(i.push(s.value),!t||i.length!==t);n=!0);}catch(u){r=!0,a=u}finally{try{!n&&o["return"]&&o["return"]()}finally{if(r)throw a}}return i}return function(t,i){if(Array.isArray(t))return t;if(Symbol.iterator in Object(t))return e(t,i);throw new TypeError("Invalid attempt to destructure non-iterable instance")}}(),a="undefined"!=typeof window?window.videojs:"undefined"!=typeof e?e.videojs:null,s=n(a),o=1/30,u=function(e,t){var i=r(t,2),n=i[0],a=i[1];return Math.min(Math.max(n,e),a)},d=function(e,t){var i=[],n=void 0;if(e&&e.length)for(n=0;n<e.length;n++)t(e.start(n),e.end(n))&&i.push([e.start(n),e.end(n)]);return s["default"].createTimeRanges(i)},l=function(e,t){return d(e,function(e,i){return e-o<=t&&i+o>=t})},f=function(e,t){return d(e,function(e){return e-o>=t})},c=function(e){if(e.length<2)return s["default"].createTimeRanges();for(var t=[],i=1;i<e.length;i++){var n=e.end(i-1),r=e.start(i);t.push([n,r])}return s["default"].createTimeRanges(t)},h=function(e,t){var i=void 0,n=void 0,r=void 0,a=[],s=[],o=function(e){return e[0]<=r&&e[1]>=r};if(e)for(i=0;i<e.length;i++)n=e.start(i),r=e.end(i),s.push([n,r]);if(t)for(i=0;i<t.length;i++)n=t.start(i),r=t.end(i),s.some(o)||a.push(r);return 1!==a.length?null:a[0]},p=function(e,t){var i=null,n=null,r=0,a=[],o=[];if(!(e&&e.length&&t&&t.length))return s["default"].createTimeRange();for(var u=e.length;u--;)a.push({time:e.start(u),type:"start"}),a.push({time:e.end(u),type:"end"});for(u=t.length;u--;)a.push({time:t.start(u),type:"start"}),a.push({time:t.end(u),type:"end"});for(a.sort(function(e,t){return e.time-t.time}),u=0;u<a.length;u++)"start"===a[u].type?(r++,2===r&&(i=a[u].time)):"end"===a[u].type&&(r--,1===r&&(n=a[u].time)),null!==i&&null!==n&&(o.push([i,n]),i=null,n=null);return s["default"].createTimeRanges(o)},m=function(e,t,i,n){for(var r=t.end(0)-t.start(0),a=e.end(0)-e.start(0),s=r-a,o=p(e,n),u=p(t,n),d=0,l=0,f=o.length;f--;)d+=o.end(f)-o.start(f),o.start(f)===i&&(d+=s);for(f=u.length;f--;)l+=u.end(f)-u.start(f);return Math.max(d,l)/r*100},y=function(e,t,i,n){var r=e+t,a=s["default"].createTimeRanges([[e,r]]),o=s["default"].createTimeRanges([[u(e,[i,r]),r]]);if(o.start(0)===o.end(0))return 0;var d=m(o,a,i,n);return isNaN(d)||d===1/0||d===-(1/0)?0:d};i["default"]={findRange:l,findNextRange:f,findGaps:c,findSoleUncommonTimeRangesEnd:h,getSegmentBufferedPercent:y,TIME_FUDGE_FACTOR:o},t.exports=i["default"]}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],10:[function(e,t,i){(function(e){"use strict";function n(e){return e&&e.__esModule?e:{"default":e}}Object.defineProperty(i,"__esModule",{value:!0});var r="undefined"!=typeof window?window.videojs:"undefined"!=typeof e?e.videojs:null,a=n(r),s={errorInterval:30,getSource:function(e){var t=this.tech({IWillNotUseThisInPlugins:!0}),i=t.currentSource_;return e(i)}},o=function d(e,t){var i=0,n=0,r=a["default"].mergeOptions(s,t),o=function(){n&&e.currentTime(n)},u=function(t){null!==t&&void 0!==t&&(n=e.duration()!==1/0&&e.currentTime()||0,e.one("loadedmetadata",o),e.src(t),e.play())},l=function(){if(!(Date.now()-i<1e3*r.errorInterval))return r.getSource&&"function"==typeof r.getSource?(i=Date.now(),r.getSource.call(e,u)):void a["default"].log.error("ERROR: reloadSourceOnError - The option getSource must be a function!")},f=function h(){e.off("loadedmetadata",o),e.off("error",l),e.off("dispose",h)},c=function(t){f(),d(e,t)};e.on("error",l),e.on("dispose",f),e.reloadSourceOnError=c},u=function(e){o(this,e)};i["default"]=u,t.exports=i["default"]}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],11:[function(e,t,i){"use strict";function n(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}Object.defineProperty(i,"__esModule",{value:!0});var r=e("./playlist.js"),a=function(e,t,i,n){var a=e.master.playlists[t],s=(0,r.isBlacklisted)(a),o=(0,r.isEnabled)(a);return"undefined"==typeof n?o:(n?delete a.disabled:a.disabled=!0,n===o||s||i(),n)},s=function u(e,t,i){n(this,u);var r=e.masterPlaylistController_.fastQualityChange_.bind(e.masterPlaylistController_);if(t.attributes){var s=t.attributes;if(s.RESOLUTION){var o=s.RESOLUTION;this.width=o.width,this.height=o.height}this.bandwidth=s.BANDWIDTH}this.id=i,this.enabled=a.bind(this,e.playlists,t.uri,r)},o=function(e){var t=e.playlists;e.representations=function(){return t.master.playlists.filter(function(e){return!(0,r.isBlacklisted)(e)}).map(function(t,i){return new s(e,t,t.uri)})}};i["default"]=o,t.exports=i["default"]},{"./playlist.js":8}],12:[function(e,t,i){"use strict";function n(e){return e&&e.__esModule?e:{"default":e}}Object.defineProperty(i,"__esModule",{value:!0});var r=e("url-toolkit"),a=n(r),s=e("global/window"),o=n(s),u=function(e,t){return/^[a-z]+:/i.test(t)?t:(/\/\//i.test(e)||(e=a["default"].buildAbsoluteURL(o["default"].location.href,e)),a["default"].buildAbsoluteURL(e,t))};i["default"]=u,t.exports=i["default"]},{"global/window":28,"url-toolkit":41}],13:[function(e,t,i){(function(n){"use strict";function r(e){return e&&e.__esModule?e:{"default":e}}function a(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function s(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}Object.defineProperty(i,"__esModule",{value:!0});var o=function(){function e(e,t){for(var i=0;i<t.length;i++){var n=t[i];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}return function(t,i,n){return i&&e(t.prototype,i),n&&e(t,n),t}}(),u=function(e,t,i){for(var n=!0;n;){var r=e,a=t,s=i;n=!1,null===r&&(r=Function.prototype);var o=Object.getOwnPropertyDescriptor(r,a);if(void 0!==o){if("value"in o)return o.value;var u=o.get;if(void 0===u)return;return u.call(s)}var d=Object.getPrototypeOf(r);if(null===d)return;e=d,t=a,i=s,n=!0,o=d=void 0}},d=e("./playlist"),l="undefined"!=typeof window?window.videojs:"undefined"!=typeof n?n.videojs:null,f=r(l),c=e("./source-updater"),h=r(c),p=e("./config"),m=r(p),y=e("global/window"),g=r(y),v=e("./bin-utils"),_=500,b="console",T=function(){g["default"].logit&&g["default"][b].log.apply(g["default"][b],arguments)},S=function(e,t,i){if(!e)return!1;var n=e.segments,r=i===n.length;return e.endList&&"open"===t.readyState&&r},w=function(e){var t=void 0,i=void 0;return i=e.offset+e.length-1,t=e.offset,"bytes="+t+"-"+i},k=function(e){var t={};return"byterange"in e&&(t.Range=w(e.byterange)),t},O=function(e){var t=e.byterange||{length:1/0,offset:0};return[t.length,t.offset,e.resolvedUri].join(",")},P=function(e){function t(e){var i=this;a(this,t),u(Object.getPrototypeOf(t.prototype),"constructor",this).call(this);var n=void 0;if(!e)throw new TypeError("Initialization options are required");if("function"!=typeof e.currentTime)throw new TypeError("No currentTime getter specified");if(!e.mediaSource)throw new TypeError("No MediaSource specified");n=f["default"].mergeOptions(f["default"].options.hls,e),this.state="INIT",this.bandwidth=n.bandwidth,this.throughput={rate:0,count:0},this.roundTrip=NaN,this.resetStats_(),this.mediaIndex=null,this.hasPlayed_=n.hasPlayed,this.currentTime_=n.currentTime,this.seekable_=n.seekable,this.seeking_=n.seeking,this.setCurrentTime_=n.setCurrentTime,this.mediaSource_=n.mediaSource,this.hls_=n.hls,this.loaderType_=n.loaderType,this.checkBufferTimeout_=null,this.error_=void 0,this.currentTimeline_=-1,this.xhr_=null,this.pendingSegment_=null,this.mimeType_=null,this.sourceUpdater_=null,this.xhrOptions_=null,this.activeInitSegmentId_=null,this.initSegments_={},this.decrypter_=n.decrypter,this.syncController_=n.syncController,this.syncPoint_={segmentIndex:0,time:0},this.syncController_.on("syncinfoupdate",function(){return i.trigger("syncinfoupdate")}),this.fetchAtBuffer_=!1}return s(t,e),o(t,[{key:"resetStats_",value:function(){this.mediaBytesTransferred=0,this.mediaRequests=0,this.mediaTransferDuration=0,this.mediaSecondsLoaded=0}},{key:"dispose",value:function(){this.state="DISPOSED",this.abort_(),this.sourceUpdater_&&this.sourceUpdater_.dispose(),this.resetStats_()}},{key:"abort",value:function(){return"WAITING"!==this.state?void(this.pendingSegment_&&(this.pendingSegment_=null)):(this.abort_(),void(this.paused()||(this.state="READY",this.monitorBuffer_())))}},{key:"error",value:function(e){return"undefined"!=typeof e&&(this.error_=e),this.pendingSegment_=null,this.error_}},{key:"load",value:function(){if(this.monitorBuffer_(),this.playlist_)return this.syncController_.setDateTimeMapping(this.playlist_),"INIT"===this.state&&this.mimeType_?this.init_():void(!this.sourceUpdater_||"READY"!==this.state&&"INIT"!==this.state||(this.state="READY"))}},{key:"playlist",value:function(e){var t=arguments.length<=1||void 0===arguments[1]?{}:arguments[1];if(e){var i=this.playlist_,n=this.pendingSegment_;if(this.playlist_=e,this.xhrOptions_=t,this.hasPlayed_()||(e.syncInfo={mediaSequence:e.mediaSequence,time:0},this.trigger("syncinfoupdate")),this.mimeType_&&"INIT"===this.state&&!this.paused())return this.init_();if(!i||i.uri!==e.uri)return void(null!==this.mediaIndex&&this.resyncLoader());var r=e.mediaSequence-i.mediaSequence;T("mediaSequenceDiff",r),null!==this.mediaIndex&&(this.mediaIndex-=r),n&&(n.mediaIndex-=r,n.mediaIndex>=0&&(n.segment=e.segments[n.mediaIndex])),this.syncController_.saveExpiredSegmentInfo(i,e)}}},{key:"pause",value:function(){this.checkBufferTimeout_&&(g["default"].clearTimeout(this.checkBufferTimeout_),this.checkBufferTimeout_=null)}},{key:"paused",value:function(){return null===this.checkBufferTimeout_}},{key:"mimeType",value:function(e){this.mimeType_||(this.mimeType_=e,this.playlist_&&"INIT"===this.state&&!this.paused()&&this.init_())}},{key:"monitorBuffer_",value:function(){this.checkBufferTimeout_&&g["default"].clearTimeout(this.checkBufferTimeout_),this.checkBufferTimeout_=g["default"].setTimeout(this.monitorBufferTick_.bind(this),1)}},{key:"monitorBufferTick_",value:function(){"READY"===this.state&&this.fillBuffer_(),this.checkBufferTimeout_&&g["default"].clearTimeout(this.checkBufferTimeout_),this.checkBufferTimeout_=g["default"].setTimeout(this.monitorBufferTick_.bind(this),_)}},{key:"getSyncSegmentCandidate_",value:function(e){var t=this;if(this.currentTimeline_===-1)return 0;var i=e.segments.map(function(e,t){return{timeline:e.timeline,segmentIndex:t}}).filter(function(e){return e.timeline===t.currentTimeline_});return i.length?i[Math.min(i.length-1,1)].segmentIndex:Math.max(e.segments.length-1,0)}},{key:"checkBuffer_",value:function(e,t,i,n,r,a){var s=0,o=void 0;e.length&&(s=e.end(e.length-1));var u=Math.max(0,s-r);if(!t.segments.length)return null;if(T("cB_","mediaIndex:",i,"hasPlayed:",n,"currentTime:",r,"syncPoint:",a,"fetchAtBuffer:",this.fetchAtBuffer_),T("cB_ 2","bufferedTime:",u),u>=m["default"].GOAL_BUFFER_LENGTH)return null;if(!n&&u>=1)return null;if(null===a)return i=this.getSyncSegmentCandidate_(t),T("getSync",i),this.generateSegmentInfo_(t,i,null,!0);if(null!==i){T("++",i+1);var l=t.segments[i];return o=l&&l.end?l.end:s,this.generateSegmentInfo_(t,i+1,o,!1)}if(this.fetchAtBuffer_){var f=(0,d.getMediaInfoForTime_)(t,s,a.segmentIndex,a.time);i=f.mediaIndex,o=f.startTime}else{var f=(0,d.getMediaInfoForTime_)(t,r,a.segmentIndex,a.time);i=f.mediaIndex,o=f.startTime}return T("gMIFT",i,"sos",o),this.generateSegmentInfo_(t,i,o,!1)}},{key:"generateSegmentInfo_",value:function(e,t,i,n){if(t<0||t>=e.segments.length)return null;var r=e.segments[t];return{uri:r.resolvedUri,mediaIndex:t,isSyncRequest:n,startOfSegment:i,playlist:e,bytes:null,encryptedBytes:null,timestampOffset:null,timeline:r.timeline,duration:r.duration,segment:r}}},{key:"abort_",value:function(){this.xhr_&&this.xhr_.abort(),this.pendingSegment_=null}},{key:"init_",value:function(){return this.state="READY",this.sourceUpdater_=new h["default"](this.mediaSource_,this.mimeType_),this.resetEverything(),this.monitorBuffer_()}},{key:"fillBuffer_",value:function(){if(!this.sourceUpdater_.updating()){this.syncPoint_||(this.syncPoint_=this.syncController_.getSyncPoint(this.playlist_,this.mediaSource_.duration,this.currentTimeline_));var e=this.checkBuffer_(this.sourceUpdater_.buffered(),this.playlist_,this.mediaIndex,this.hasPlayed_(),this.currentTime_(),this.syncPoint_);if(e){var t=S(this.playlist_,this.mediaSource_,e.mediaIndex);return t?void this.mediaSource_.endOfStream():void((e.mediaIndex!==this.playlist_.segments.length-1||"ended"!==this.mediaSource_.readyState||this.seeking_())&&((e.timeline!==this.currentTimeline_||null!==e.startOfSegment&&e.startOfSegment<this.sourceUpdater_.timestampOffset())&&(this.syncController_.reset(),e.timestampOffset=e.startOfSegment),this.currentTimeline_=e.timeline,this.loadSegment_(e)))}}}},{key:"trimBuffer_",value:function(e){var t=this.seekable_(),i=this.currentTime_(),n=void 0;return t.length&&t.start(0)>0&&t.start(0)<i?t.start(0):n=i-60}},{key:"loadSegment_",value:function(e){var t=this,i=void 0,n=void 0,r=void 0,a=void 0,s=0;if(s=this.trimBuffer_(e),s>0&&this.sourceUpdater_.remove(0,s),i=e.segment,i.key){var o=f["default"].mergeOptions(this.xhrOptions_,{uri:i.key.resolvedUri,responseType:"arraybuffer"});n=this.hls_.xhr(o,this.handleResponse_.bind(this))}if(i.map&&!this.initSegments_[O(i.map)]){var u=f["default"].mergeOptions(this.xhrOptions_,{uri:i.map.resolvedUri,responseType:"arraybuffer",headers:k(i.map)});r=this.hls_.xhr(u,this.handleResponse_.bind(this))}this.pendingSegment_=e;var d=f["default"].mergeOptions(this.xhrOptions_,{uri:e.uri,responseType:"arraybuffer",headers:k(i)});a=this.hls_.xhr(d,this.handleResponse_.bind(this)),a.addEventListener("progress",function(e){t.trigger(e)}),this.xhr_={keyXhr:n,initSegmentXhr:r,segmentXhr:a,abort:function(){this.segmentXhr&&(this.segmentXhr.onreadystatechange=null,this.segmentXhr.abort(),this.segmentXhr=null),this.initSegmentXhr&&(this.initSegmentXhr.onreadystatechange=null,this.initSegmentXhr.abort(),this.initSegmentXhr=null),this.keyXhr&&(this.keyXhr.onreadystatechange=null,this.keyXhr.abort(),this.keyXhr=null)}},this.state="WAITING"}},{key:"handleResponse_",value:function(e,t){var i=void 0,n=void 0,r=void 0;if(this.xhr_&&(t===this.xhr_.segmentXhr||t===this.xhr_.keyXhr||t===this.xhr_.initSegmentXhr)){if(i=this.pendingSegment_,n=i.segment,t.timedout)return this.abort_(),this.bandwidth=1,this.roundTrip=NaN,this.state="READY",this.trigger("progress");if(!t.aborted&&e){var a=this.xhr_.keyXhr;return this.abort_(),this.error({status:t.status,message:t===a?"HLS key request error at URL: "+n.key.uri:"HLS segment request error at URL: "+i.uri,code:2,xhr:t}),this.state="READY",this.pause(),this.trigger("error")}if(!t.response)return void this.abort_();if(t===this.xhr_.segmentXhr&&(this.xhr_.segmentXhr=null,i.startOfAppend=Date.now(),this.roundTrip=t.roundTripTime,this.bandwidth=t.bandwidth,this.mediaBytesTransferred+=t.bytesReceived||0,this.mediaRequests+=1,this.mediaTransferDuration+=t.roundTripTime||0,n.key?i.encryptedBytes=new Uint8Array(t.response):i.bytes=new Uint8Array(t.response)),t===this.xhr_.keyXhr){if(this.xhr_.keyXhr=null,16!==t.response.byteLength)return this.abort_(),this.error({status:t.status,message:"Invalid HLS key at URL: "+n.key.uri,code:2,xhr:t}),this.state="READY",this.pause(),this.trigger("error");r=new DataView(t.response),n.key.bytes=new Uint32Array([r.getUint32(0),r.getUint32(4),r.getUint32(8),r.getUint32(12)]),n.key.iv=n.key.iv||new Uint32Array([0,0,0,i.mediaIndex+i.playlist.mediaSequence])}t===this.xhr_.initSegmentXhr&&(this.xhr_.initSegmentXhr=null,n.map.bytes=new Uint8Array(t.response),this.initSegments_[O(n.map)]=n.map),this.xhr_.segmentXhr||this.xhr_.keyXhr||this.xhr_.initSegmentXhr||(this.xhr_=null,this.processResponse_())}}},{key:"resetEverything",value:function(){this.resetLoader(),this.remove(0,1/0)}},{key:"resetLoader",value:function(){this.fetchAtBuffer_=!1,this.resyncLoader()}},{key:"resyncLoader",value:function(){this.mediaIndex=null,this.syncPoint_=null}},{key:"remove",value:function(e,t){this.sourceUpdater_&&this.sourceUpdater_.remove(e,t)}},{key:"processResponse_",value:function(){if(!this.pendingSegment_)return void(this.state="READY");this.state="DECRYPTING";var e=this.pendingSegment_,t=e.segment;t.key?this.decrypter_.postMessage((0,v.createTransferableMessage)({source:this.loaderType_,encrypted:e.encryptedBytes,key:t.key.bytes,iv:t.key.iv}),[e.encryptedBytes.buffer,t.key.bytes.buffer]):this.handleSegment_()}},{key:"handleDecrypted_",value:function(e){var t=this.pendingSegment_,i=e.decrypted;t&&(t.bytes=new Uint8Array(i.bytes,i.byteOffset,i.byteLength)),this.handleSegment_()}},{key:"handleSegment_",value:function(){var e=this;if(!this.pendingSegment_)return void(this.state="READY");this.state="APPENDING";var t=this.pendingSegment_,i=t.segment;return this.syncController_.probeSegmentInfo(t),t.isSyncRequest?(this.pendingSegment_=null,void(this.state="READY")):(null!==t.timestampOffset&&t.timestampOffset!==this.sourceUpdater_.timestampOffset()&&this.sourceUpdater_.timestampOffset(t.timestampOffset),i.map&&!function(){var t=O(i.map);if(!e.activeInitSegmentId_||e.activeInitSegmentId_!==t){var n=e.initSegments_[t];e.sourceUpdater_.appendBuffer(n.bytes,function(){e.activeInitSegmentId_=t})}}(),t.byteLength=t.bytes.byteLength,"number"==typeof i.start&&"number"==typeof i.end?this.mediaSecondsLoaded+=i.end-i.start:this.mediaSecondsLoaded+=i.duration,void this.sourceUpdater_.appendBuffer(t.bytes,this.handleUpdateEnd_.bind(this)))}},{key:"handleUpdateEnd_",value:function(){if(!this.pendingSegment_)return this.state="READY",void(this.paused()||this.monitorBuffer_());var e=this.pendingSegment_;this.pendingSegment_=null,this.recordThroughput_(e),this.mediaIndex=e.mediaIndex,this.fetchAtBuffer_=!0,T("handleUpdateEnd_",this.mediaIndex);var t=S(e.playlist,this.mediaSource_,this.mediaIndex+1);t&&this.mediaSource_.endOfStream(),this.state="READY",this.trigger("progress"),this.paused()||this.monitorBuffer_()}},{key:"recordThroughput_",value:function(e){var t=this.throughput.rate,i=Date.now()-e.startOfAppend+1,n=Math.floor(e.byteLength/i*8*1e3);this.throughput.rate+=(n-t)/++this.throughput.count}}]),t}(f["default"].EventTarget);i["default"]=P,t.exports=i["default"]}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{"./bin-utils":2,"./config":3,"./playlist":8,"./source-updater":14,"global/window":28}],14:[function(e,t,i){(function(e){"use strict";function n(e){return e&&e.__esModule?e:{"default":e}}function r(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}Object.defineProperty(i,"__esModule",{value:!0});var a=function(){function e(e,t){for(var i=0;i<t.length;i++){var n=t[i];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}return function(t,i,n){return i&&e(t.prototype,i),n&&e(t,n),t}}(),s="undefined"!=typeof window?window.videojs:"undefined"!=typeof e?e.videojs:null,o=n(s),u=function(){function e(t,i){var n=this;r(this,e);var a=function(){n.sourceBuffer_=t.addSourceBuffer(i),n.onUpdateendCallback_=function(){var e=n.pendingCallback_;n.pendingCallback_=null,e&&e(),n.runCallback_()},n.sourceBuffer_.addEventListener("updateend",n.onUpdateendCallback_),n.runCallback_()};this.callbacks_=[],this.pendingCallback_=null,this.timestampOffset_=0,this.mediaSource=t,"closed"===t.readyState?t.addEventListener("sourceopen",a):a()}return a(e,[{key:"abort",value:function(e){var t=this;this.queueCallback_(function(){t.sourceBuffer_.abort()},e)}},{key:"appendBuffer",value:function(e,t){var i=this;this.queueCallback_(function(){i.sourceBuffer_.appendBuffer(e)},t)}},{key:"buffered",value:function(){return this.sourceBuffer_?this.sourceBuffer_.buffered:o["default"].createTimeRanges()}},{key:"duration",value:function(e){var t=this;this.queueCallback_(function(){t.sourceBuffer_.duration=e})}},{key:"remove",value:function(e,t){var i=this;this.queueCallback_(function(){i.sourceBuffer_.remove(e,t)})}},{key:"updating",value:function(){return!this.sourceBuffer_||this.sourceBuffer_.updating}},{key:"timestampOffset",value:function(e){var t=this;return"undefined"!=typeof e&&(this.queueCallback_(function(){t.sourceBuffer_.timestampOffset=e}),this.timestampOffset_=e),this.timestampOffset_}},{key:"queueCallback_",value:function(e,t){this.callbacks_.push([e.bind(this),t]),this.runCallback_()}},{key:"runCallback_",value:function(){var e=void 0;this.sourceBuffer_&&!this.sourceBuffer_.updating&&this.callbacks_.length&&(e=this.callbacks_.shift(),this.pendingCallback_=e[1],e[0]())}},{key:"dispose",value:function(){this.sourceBuffer_.removeEventListener("updateend",this.onUpdateendCallback_),this.sourceBuffer_&&"open"===this.mediaSource.readyState&&this.sourceBuffer_.abort()}}]),e}();i["default"]=u,t.exports=i["default"]}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],15:[function(e,t,i){"use strict";function n(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}Object.defineProperty(i,"__esModule",{value:!0});var r=function(){function e(e,t){for(var i=0;i<t.length;i++){var n=t[i];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}return function(t,i,n){return i&&e(t.prototype,i),n&&e(t,n),t}}(),a=function(){function e(){n(this,e),this.listeners={}}return r(e,[{key:"on",value:function(e,t){this.listeners[e]||(this.listeners[e]=[]),this.listeners[e].push(t)}},{key:"off",value:function(e,t){var i=void 0;return!!this.listeners[e]&&(i=this.listeners[e].indexOf(t),this.listeners[e].splice(i,1),i>-1)}},{key:"trigger",value:function(e){var t=void 0,i=void 0,n=void 0,r=void 0;if(t=this.listeners[e])if(2===arguments.length)for(n=t.length,i=0;i<n;++i)t[i].call(this,arguments[1]);else for(r=Array.prototype.slice.call(arguments,1),n=t.length,i=0;i<n;++i)t[i].apply(this,r)}},{key:"dispose",value:function(){this.listeners={}}},{key:"pipe",value:function(e){this.on("data",function(t){e.push(t)})}}]),e}();i["default"]=a,t.exports=i["default"]},{}],16:[function(e,t,i){(function(t){"use strict";function n(e){return e&&e.__esModule?e:{"default":e}}function r(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function a(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}Object.defineProperty(i,"__esModule",{value:!0});var s=function(){function e(e,t){for(var i=0;i<t.length;i++){var n=t[i];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}return function(t,i,n){return i&&e(t.prototype,i),n&&e(t,n),t}}(),o=function(e,t,i){for(var n=!0;n;){var r=e,a=t,s=i;n=!1,null===r&&(r=Function.prototype);var o=Object.getOwnPropertyDescriptor(r,a);if(void 0!==o){if("value"in o)return o.value;var u=o.get;if(void 0===u)return;return u.call(s)}var d=Object.getPrototypeOf(r);if(null===d)return;e=d,t=a,i=s,n=!0,o=d=void 0}},u=e("mux.js/lib/mp4/probe"),d=n(u),l=e("mux.js/lib/tools/ts-inspector.js"),f=e("./playlist"),c="undefined"!=typeof window?window.videojs:"undefined"!=typeof t?t.videojs:null,h=n(c),p="console",m=function(){window.logit&&window[p].log.apply(window[p],arguments)},y=[{name:"VOD",run:function(e,t,i,n){if(i!==1/0){var r={time:0,segmentIndex:0};return r}return null}},{name:"ProgramDateTime",run:function(e,t,i,n){if(e.datetimeToDisplayTime&&t.dateTimeObject){var r=t.dateTimeObject.getTime()/1e3,a=r+e.datetimeToDisplayTime,s={time:a,segmentIndex:0};return s}return null}},{name:"Segment",run:function(e,t,i,n){for(var r=t.segments,a=r.length-1;a>=0;a--){var s=r[a];if(s.timeline===n&&"undefined"!=typeof s.start){var o={time:s.start,segmentIndex:a};return o}}return null}},{name:"Discontinuity",run:function(e,t,i,n){if(t.discontinuityStarts.length)for(var r=0;r<t.discontinuityStarts.length;r++){var a=t.discontinuityStarts[r],s=t.discontinuitySequence+r+1;if(e.discontinuities[s]){var o={time:e.discontinuities[s].time,segmentIndex:a};return o}}return null}},{name:"Playlist",run:function(e,t,i,n){if(t.syncInfo){var r={time:t.syncInfo.time,segmentIndex:t.syncInfo.mediaSequence-t.mediaSequence};return r}return null}}];i.syncPointStrategies=y;var g=function(e){function t(){r(this,t),o(Object.getPrototypeOf(t.prototype),"constructor",this).call(this),this.inspectCache_=void 0,this.timelines=[],this.discontinuities=[],this.datetimeToDisplayTime=null}return a(t,e),s(t,[{key:"getSyncPoint",value:function(e,t,i){for(var n=0;n<y.length;n++){var r=y[n],a=r.run(this,e,t,i);if(a)return m("syncPoint found via <"+r.name+">:",a),a}return null}},{key:"saveExpiredSegmentInfo",value:function(e,t){for(var i=t.mediaSequence-e.mediaSequence,n=i-1;n>=0;n--){var r=e.segments[n];if("undefined"!=typeof r.start){t.syncInfo={mediaSequence:e.mediaSequence+n,time:r.start},m("playlist sync:",t.syncInfo),this.trigger("syncinfoupdate");break}}}},{key:"setDateTimeMapping",value:function(e){if(!this.datetimeToDisplayTime&&e.dateTimeObject){var t=e.dateTimeObject.getTime()/1e3;this.datetimeToDisplayTime=-t}}},{key:"reset",value:function(){this.inspectCache_=void 0}},{key:"probeSegmentInfo",value:function(e){var t=e.segment,i=void 0;i=t.map?this.probeMp4Segment_(e):this.probeTsSegment_(e),i&&this.calculateSegmentTimeMapping_(e,i)&&this.saveDiscontinuitySyncInfo_(e)}},{key:"probeMp4Segment_",value:function(e){var t=e.segment,i=d["default"].timescale(t.map.bytes),n=d["default"].startTime(i,e.bytes);return null!==e.timestampOffset&&(e.timestampOffset-=n),{start:n,end:n+t.duration}}},{key:"probeTsSegment_",value:function(e){var t=(0,l.inspect)(e.bytes,this.inspectCache_),i=void 0,n=void 0;return t?(t.video&&2===t.video.length?(this.inspectCache_=t.video[1].dts,i=t.video[0].dtsTime,n=t.video[1].dtsTime):t.audio&&2===t.audio.length&&(this.inspectCache_=t.audio[1].dts,i=t.audio[0].dtsTime,n=t.audio[1].dtsTime),{start:i,end:n}):null}},{key:"calculateSegmentTimeMapping_",value:function(e,t){var i=e.segment,n=this.timelines[e.timeline];if(null!==e.timestampOffset)m("tsO:",e.timestampOffset),n={time:e.timestampOffset,mapping:e.timestampOffset-t.start},this.timelines[e.timeline]=n,i.start=e.timestampOffset,i.end=t.end+n.mapping;else{if(!n)return!1;i.start=t.start+n.mapping,i.end=t.end+n.mapping}return this.trigger("syncinfoupdate"),!0}},{key:"saveDiscontinuitySyncInfo_",value:function(e){var t=e.playlist,i=e.segment;if(i.discontinuity)this.discontinuities[i.timeline]={time:i.start,accuracy:0};else if(t.discontinuityStarts.length)for(var n=0;n<t.discontinuityStarts.length;n++){var r=t.discontinuityStarts[n],a=t.discontinuitySequence+n+1,s=r-e.mediaIndex;s>0&&(!this.discontinuities[a]||this.discontinuities[a].accuracy>s)&&(this.discontinuities[a]={time:i.end+(0,f.sumDurations)(t,e.mediaIndex+1,r),accuracy:s})}}}]),t}(h["default"].EventTarget);i["default"]=g}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{"./playlist":8,"mux.js/lib/mp4/probe":38,"mux.js/lib/tools/ts-inspector.js":39}],17:[function(e,t,i){(function(e){"use strict";Object.defineProperty(i,"__esModule",{value:!0});var n="undefined"!=typeof window?window.videojs:"undefined"!=typeof e?e.videojs:null,r=function(){var e=function t(e,i){if(e=(0,n.mergeOptions)({timeout:45e3},e),t.beforeRequest&&"function"==typeof t.beforeRequest){var r=t.beforeRequest(e);r&&(e=r)}var a=(0,n.xhr)(e,function(e,t){!e&&a.response&&(a.responseTime=Date.now(),a.roundTripTime=a.responseTime-a.requestTime,a.bytesReceived=a.response.byteLength||a.response.length,a.bandwidth||(a.bandwidth=Math.floor(a.bytesReceived/a.roundTripTime*8*1e3))),e||a.timedout?a.timedout=a.timedout||"ETIMEDOUT"===e.code:a.timedout=!1,e||200===t.statusCode||206===t.statusCode||0===t.statusCode||(e=new Error("XHR Failed with a response of: "+(a&&(a.response||a.responseText)))),i(e,a)});return a.requestTime=Date.now(),a};return e};i["default"]=r,t.exports=i["default"]}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],18:[function(e,t,i){"use strict";function n(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}Object.defineProperty(i,"__esModule",{value:!0});var r=function(){function e(e,t){for(var i=0;i<t.length;i++){var n=t[i];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}return function(t,i,n){return i&&e(t.prototype,i),n&&e(t,n),t}}(),a=function(){var e=[[[],[],[],[],[]],[[],[],[],[],[]]],t=e[0],i=e[1],n=t[4],r=i[4],a=void 0,s=void 0,o=void 0,u=[],d=[],l=void 0,f=void 0,c=void 0,h=void 0,p=void 0,m=void 0;for(a=0;a<256;a++)d[(u[a]=a<<1^283*(a>>7))^a]=a;for(s=o=0;!n[s];s^=l||1,o=d[o]||1)for(h=o^o<<1^o<<2^o<<3^o<<4,h=h>>8^255&h^99,n[s]=h,r[h]=s,c=u[f=u[l=u[s]]],m=16843009*c^65537*f^257*l^16843008*s,p=257*u[h]^16843008*h,a=0;a<4;a++)t[a][s]=p=p<<24^p>>>8,i[a][h]=m=m<<24^m>>>8;for(a=0;a<5;a++)t[a]=t[a].slice(0),i[a]=i[a].slice(0);return e},s=null,o=function(){function e(t){n(this,e),s||(s=a()),this._tables=[[s[0][0].slice(),s[0][1].slice(),s[0][2].slice(),s[0][3].slice(),s[0][4].slice()],[s[1][0].slice(),s[1][1].slice(),s[1][2].slice(),s[1][3].slice(),s[1][4].slice()]];var i=void 0,r=void 0,o=void 0,u=void 0,d=void 0,l=this._tables[0][4],f=this._tables[1],c=t.length,h=1;
if(4!==c&&6!==c&&8!==c)throw new Error("Invalid aes key size");for(u=t.slice(0),d=[],this._key=[u,d],i=c;i<4*c+28;i++)o=u[i-1],(i%c===0||8===c&&i%c===4)&&(o=l[o>>>24]<<24^l[o>>16&255]<<16^l[o>>8&255]<<8^l[255&o],i%c===0&&(o=o<<8^o>>>24^h<<24,h=h<<1^283*(h>>7))),u[i]=u[i-c]^o;for(r=0;i;r++,i--)o=u[3&r?i:i-4],i<=4||r<4?d[r]=o:d[r]=f[0][l[o>>>24]]^f[1][l[o>>16&255]]^f[2][l[o>>8&255]]^f[3][l[255&o]]}return r(e,[{key:"decrypt",value:function(e,t,i,n,r,a){var s=this._key[1],o=e^s[0],u=n^s[1],d=i^s[2],l=t^s[3],f=void 0,c=void 0,h=void 0,p=s.length/4-2,m=void 0,y=4,g=this._tables[1],v=g[0],_=g[1],b=g[2],T=g[3],S=g[4];for(m=0;m<p;m++)f=v[o>>>24]^_[u>>16&255]^b[d>>8&255]^T[255&l]^s[y],c=v[u>>>24]^_[d>>16&255]^b[l>>8&255]^T[255&o]^s[y+1],h=v[d>>>24]^_[l>>16&255]^b[o>>8&255]^T[255&u]^s[y+2],l=v[l>>>24]^_[o>>16&255]^b[u>>8&255]^T[255&d]^s[y+3],y+=4,o=f,u=c,d=h;for(m=0;m<4;m++)r[(3&-m)+a]=S[o>>>24]<<24^S[u>>16&255]<<16^S[d>>8&255]<<8^S[255&l]^s[y++],f=o,o=u,u=d,d=l,l=f}}]),e}();i["default"]=o,t.exports=i["default"]},{}],19:[function(e,t,i){"use strict";function n(e){return e&&e.__esModule?e:{"default":e}}function r(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function a(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}Object.defineProperty(i,"__esModule",{value:!0});var s=function(){function e(e,t){for(var i=0;i<t.length;i++){var n=t[i];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}return function(t,i,n){return i&&e(t.prototype,i),n&&e(t,n),t}}(),o=function(e,t,i){for(var n=!0;n;){var r=e,a=t,s=i;n=!1,null===r&&(r=Function.prototype);var o=Object.getOwnPropertyDescriptor(r,a);if(void 0!==o){if("value"in o)return o.value;var u=o.get;if(void 0===u)return;return u.call(s)}var d=Object.getPrototypeOf(r);if(null===d)return;e=d,t=a,i=s,n=!0,o=d=void 0}},u=e("./stream"),d=n(u),l=function(e){function t(){r(this,t),o(Object.getPrototypeOf(t.prototype),"constructor",this).call(this,d["default"]),this.jobs=[],this.delay=1,this.timeout_=null}return a(t,e),s(t,[{key:"processJob_",value:function(){this.jobs.shift()(),this.jobs.length?this.timeout_=setTimeout(this.processJob_.bind(this),this.delay):this.timeout_=null}},{key:"push",value:function(e){this.jobs.push(e),this.timeout_||(this.timeout_=setTimeout(this.processJob_.bind(this),this.delay))}}]),t}(d["default"]);i["default"]=l,t.exports=i["default"]},{"./stream":22}],20:[function(e,t,i){"use strict";function n(e){return e&&e.__esModule?e:{"default":e}}function r(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}Object.defineProperty(i,"__esModule",{value:!0});var a=function(){function e(e,t){for(var i=0;i<t.length;i++){var n=t[i];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}return function(t,i,n){return i&&e(t.prototype,i),n&&e(t,n),t}}(),s=e("./aes"),o=n(s),u=e("./async-stream"),d=n(u),l=e("pkcs7"),f=function(e){return e<<24|(65280&e)<<8|(16711680&e)>>8|e>>>24},c=function(e,t,i){var n=new Int32Array(e.buffer,e.byteOffset,e.byteLength>>2),r=new o["default"](Array.prototype.slice.call(t)),a=new Uint8Array(e.byteLength),s=new Int32Array(a.buffer),u=void 0,d=void 0,l=void 0,c=void 0,h=void 0,p=void 0,m=void 0,y=void 0,g=void 0;for(u=i[0],d=i[1],l=i[2],c=i[3],g=0;g<n.length;g+=4)h=f(n[g]),p=f(n[g+1]),m=f(n[g+2]),y=f(n[g+3]),r.decrypt(h,p,m,y,s,g),s[g]=f(s[g]^u),s[g+1]=f(s[g+1]^d),s[g+2]=f(s[g+2]^l),s[g+3]=f(s[g+3]^c),u=h,d=p,l=m,c=y;return a};i.decrypt=c;var h=function(){function e(t,i,n,a){r(this,e);var s=e.STEP,o=new Int32Array(t.buffer),u=new Uint8Array(t.byteLength),c=0;for(this.asyncStream_=new d["default"],this.asyncStream_.push(this.decryptChunk_(o.subarray(c,c+s),i,n,u)),c=s;c<o.length;c+=s)n=new Uint32Array([f(o[c-4]),f(o[c-3]),f(o[c-2]),f(o[c-1])]),this.asyncStream_.push(this.decryptChunk_(o.subarray(c,c+s),i,n,u));this.asyncStream_.push(function(){a(null,(0,l.unpad)(u))})}return a(e,[{key:"decryptChunk_",value:function(e,t,i,n){return function(){var r=c(e,t,i);n.set(r,e.byteOffset)}}}],[{key:"STEP",get:function(){return 32e3}}]),e}();i.Decrypter=h,i["default"]={Decrypter:h,decrypt:c}},{"./aes":18,"./async-stream":19,pkcs7:24}],21:[function(e,t,i){"use strict";function n(e){return e&&e.__esModule?e:{"default":e}}Object.defineProperty(i,"__esModule",{value:!0});var r=e("./decrypter"),a=e("./async-stream"),s=n(a);i["default"]={decrypt:r.decrypt,Decrypter:r.Decrypter,AsyncStream:s["default"]},t.exports=i["default"]},{"./async-stream":19,"./decrypter":20}],22:[function(e,t,i){arguments[4][15][0].apply(i,arguments)},{dup:15}],23:[function(e,t,i){"use strict";var n;t.exports=function(e){var t=n[e.byteLength%16||0],i=new Uint8Array(e.byteLength+t.length);return i.set(e),i.set(t,e.byteLength),i},n=[[16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16],[15,15,15,15,15,15,15,15,15,15,15,15,15,15,15],[14,14,14,14,14,14,14,14,14,14,14,14,14,14],[13,13,13,13,13,13,13,13,13,13,13,13,13],[12,12,12,12,12,12,12,12,12,12,12,12],[11,11,11,11,11,11,11,11,11,11,11],[10,10,10,10,10,10,10,10,10,10],[9,9,9,9,9,9,9,9,9],[8,8,8,8,8,8,8,8],[7,7,7,7,7,7,7],[6,6,6,6,6,6],[5,5,5,5,5],[4,4,4,4],[3,3,3],[2,2],[1]]},{}],24:[function(e,t,i){"use strict";i.pad=e("./pad.js"),i.unpad=e("./unpad.js")},{"./pad.js":23,"./unpad.js":25}],25:[function(e,t,i){"use strict";t.exports=function(e){return e.subarray(0,e.byteLength-e[e.byteLength-1])}},{}],26:[function(e,t,i){},{}],27:[function(e,t,i){(function(i){var n="undefined"!=typeof i?i:"undefined"!=typeof window?window:{},r=e("min-document");if("undefined"!=typeof document)t.exports=document;else{var a=n["__GLOBAL_DOCUMENT_CACHE@4"];a||(a=n["__GLOBAL_DOCUMENT_CACHE@4"]=r),t.exports=a}}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{"min-document":26}],28:[function(e,t,i){(function(e){"undefined"!=typeof window?t.exports=window:"undefined"!=typeof e?t.exports=e:"undefined"!=typeof self?t.exports=self:t.exports={}}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],29:[function(e,t,i){"use strict";function n(e){return e&&e.__esModule?e:{"default":e}}var r=e("./line-stream"),a=n(r),s=e("./parse-stream"),o=n(s),u=e("./parser"),d=n(u);t.exports={LineStream:a["default"],ParseStream:o["default"],Parser:d["default"]}},{"./line-stream":30,"./parse-stream":31,"./parser":32}],30:[function(e,t,i){"use strict";function n(e){return e&&e.__esModule?e:{"default":e}}function r(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function a(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}function s(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}Object.defineProperty(i,"__esModule",{value:!0});var o=function(){function e(e,t){for(var i=0;i<t.length;i++){var n=t[i];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}return function(t,i,n){return i&&e(t.prototype,i),n&&e(t,n),t}}(),u=e("./stream"),d=n(u),l=function(e){function t(){r(this,t);var e=a(this,(t.__proto__||Object.getPrototypeOf(t)).call(this));return e.buffer="",e}return s(t,e),o(t,[{key:"push",value:function(e){var t=void 0;for(this.buffer+=e,t=this.buffer.indexOf("\n");t>-1;t=this.buffer.indexOf("\n"))this.trigger("data",this.buffer.substring(0,t)),this.buffer=this.buffer.substring(t+1)}}]),t}(d["default"]);i["default"]=l},{"./stream":33}],31:[function(e,t,i){"use strict";function n(e){return e&&e.__esModule?e:{"default":e}}function r(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function a(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}function s(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}Object.defineProperty(i,"__esModule",{value:!0});var o=function(){function e(e,t){var i=[],n=!0,r=!1,a=void 0;try{for(var s,o=e[Symbol.iterator]();!(n=(s=o.next()).done)&&(i.push(s.value),!t||i.length!==t);n=!0);}catch(u){r=!0,a=u}finally{try{!n&&o["return"]&&o["return"]()}finally{if(r)throw a}}return i}return function(t,i){if(Array.isArray(t))return t;if(Symbol.iterator in Object(t))return e(t,i);throw new TypeError("Invalid attempt to destructure non-iterable instance")}}(),u=function(){function e(e,t){for(var i=0;i<t.length;i++){var n=t[i];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}return function(t,i,n){return i&&e(t.prototype,i),n&&e(t,n),t}}(),d=e("./stream"),l=n(d),f=function(){var e="[^=]*",t='"[^"]*"|[^,]*',i="(?:"+e+")=(?:"+t+")";return new RegExp("(?:^|,)("+i+")")},c=function(e){for(var t=e.split(f()),i={},n=t.length,r=void 0;n--;)""!==t[n]&&(r=/([^=]*)=(.*)/.exec(t[n]).slice(1),r[0]=r[0].replace(/^\s+|\s+$/g,""),r[1]=r[1].replace(/^\s+|\s+$/g,""),r[1]=r[1].replace(/^['"](.*)['"]$/g,"$1"),i[r[0]]=r[1]);return i},h=function(e){function t(){return r(this,t),a(this,(t.__proto__||Object.getPrototypeOf(t)).call(this))}return s(t,e),u(t,[{key:"push",value:function(e){var t=void 0,i=void 0;if(e=e.replace(/^[\u0000\s]+|[\u0000\s]+$/g,""),0!==e.length){if("#"!==e[0])return void this.trigger("data",{type:"uri",uri:e});if(0!==e.indexOf("#EXT"))return void this.trigger("data",{type:"comment",text:e.slice(1)});if(e=e.replace("\r",""),t=/^#EXTM3U/.exec(e))return void this.trigger("data",{type:"tag",tagType:"m3u"});if(t=/^#EXTINF:?([0-9\.]*)?,?(.*)?$/.exec(e))return i={type:"tag",tagType:"inf"},t[1]&&(i.duration=parseFloat(t[1])),t[2]&&(i.title=t[2]),void this.trigger("data",i);if(t=/^#EXT-X-TARGETDURATION:?([0-9.]*)?/.exec(e))return i={type:"tag",tagType:"targetduration"},t[1]&&(i.duration=parseInt(t[1],10)),void this.trigger("data",i);if(t=/^#ZEN-TOTAL-DURATION:?([0-9.]*)?/.exec(e))return i={type:"tag",tagType:"totalduration"},t[1]&&(i.duration=parseInt(t[1],10)),void this.trigger("data",i);if(t=/^#EXT-X-VERSION:?([0-9.]*)?/.exec(e))return i={type:"tag",tagType:"version"},t[1]&&(i.version=parseInt(t[1],10)),void this.trigger("data",i);if(t=/^#EXT-X-MEDIA-SEQUENCE:?(\-?[0-9.]*)?/.exec(e))return i={type:"tag",tagType:"media-sequence"},t[1]&&(i.number=parseInt(t[1],10)),void this.trigger("data",i);if(t=/^#EXT-X-DISCONTINUITY-SEQUENCE:?(\-?[0-9.]*)?/.exec(e))return i={type:"tag",tagType:"discontinuity-sequence"},t[1]&&(i.number=parseInt(t[1],10)),void this.trigger("data",i);if(t=/^#EXT-X-PLAYLIST-TYPE:?(.*)?$/.exec(e))return i={type:"tag",tagType:"playlist-type"},t[1]&&(i.playlistType=t[1]),void this.trigger("data",i);if(t=/^#EXT-X-BYTERANGE:?([0-9.]*)?@?([0-9.]*)?/.exec(e))return i={type:"tag",tagType:"byterange"},t[1]&&(i.length=parseInt(t[1],10)),t[2]&&(i.offset=parseInt(t[2],10)),void this.trigger("data",i);if(t=/^#EXT-X-ALLOW-CACHE:?(YES|NO)?/.exec(e))return i={type:"tag",tagType:"allow-cache"},t[1]&&(i.allowed=!/NO/.test(t[1])),void this.trigger("data",i);if(t=/^#EXT-X-MAP:?(.*)$/.exec(e)){if(i={type:"tag",tagType:"map"},t[1]){var n=c(t[1]);if(n.URI&&(i.uri=n.URI),n.BYTERANGE){var r=n.BYTERANGE.split("@"),a=o(r,2),s=a[0],u=a[1];i.byterange={},s&&(i.byterange.length=parseInt(s,10)),u&&(i.byterange.offset=parseInt(u,10))}}return void this.trigger("data",i)}if(t=/^#EXT-X-STREAM-INF:?(.*)$/.exec(e)){if(i={type:"tag",tagType:"stream-inf"},t[1]){if(i.attributes=c(t[1]),i.attributes.RESOLUTION){var d=i.attributes.RESOLUTION.split("x"),l={};d[0]&&(l.width=parseInt(d[0],10)),d[1]&&(l.height=parseInt(d[1],10)),i.attributes.RESOLUTION=l}i.attributes.BANDWIDTH&&(i.attributes.BANDWIDTH=parseInt(i.attributes.BANDWIDTH,10)),i.attributes["PROGRAM-ID"]&&(i.attributes["PROGRAM-ID"]=parseInt(i.attributes["PROGRAM-ID"],10))}return void this.trigger("data",i)}return(t=/^#EXT-X-MEDIA:?(.*)$/.exec(e))?(i={type:"tag",tagType:"media"},t[1]&&(i.attributes=c(t[1])),void this.trigger("data",i)):(t=/^#EXT-X-ENDLIST/.exec(e))?void this.trigger("data",{type:"tag",tagType:"endlist"}):(t=/^#EXT-X-DISCONTINUITY/.exec(e))?void this.trigger("data",{type:"tag",tagType:"discontinuity"}):(t=/^#EXT-X-PROGRAM-DATE-TIME:?(.*)$/.exec(e))?(i={type:"tag",tagType:"program-date-time"},t[1]&&(i.dateTimeString=t[1],i.dateTimeObject=new Date(t[1])),void this.trigger("data",i)):(t=/^#EXT-X-KEY:?(.*)$/.exec(e))?(i={type:"tag",tagType:"key"},t[1]&&(i.attributes=c(t[1]),i.attributes.IV&&("0x"===i.attributes.IV.substring(0,2).toLowerCase()&&(i.attributes.IV=i.attributes.IV.substring(2)),i.attributes.IV=i.attributes.IV.match(/.{8}/g),i.attributes.IV[0]=parseInt(i.attributes.IV[0],16),i.attributes.IV[1]=parseInt(i.attributes.IV[1],16),i.attributes.IV[2]=parseInt(i.attributes.IV[2],16),i.attributes.IV[3]=parseInt(i.attributes.IV[3],16),i.attributes.IV=new Uint32Array(i.attributes.IV))),void this.trigger("data",i)):(t=/^#EXT-X-CUE-OUT-CONT:?(.*)?$/.exec(e))?(i={type:"tag",tagType:"cue-out-cont"},t[1]?i.data=t[1]:i.data="",void this.trigger("data",i)):(t=/^#EXT-X-CUE-OUT:?(.*)?$/.exec(e))?(i={type:"tag",tagType:"cue-out"},t[1]?i.data=t[1]:i.data="",void this.trigger("data",i)):(t=/^#EXT-X-CUE-IN:?(.*)?$/.exec(e))?(i={type:"tag",tagType:"cue-in"},t[1]?i.data=t[1]:i.data="",void this.trigger("data",i)):void this.trigger("data",{type:"tag",data:e.slice(4)})}}}]),t}(l["default"]);i["default"]=h},{"./stream":33}],32:[function(e,t,i){"use strict";function n(e){return e&&e.__esModule?e:{"default":e}}function r(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function a(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}function s(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}Object.defineProperty(i,"__esModule",{value:!0});var o=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var i=arguments[t];for(var n in i)Object.prototype.hasOwnProperty.call(i,n)&&(e[n]=i[n])}return e},u=function(){function e(e,t){for(var i=0;i<t.length;i++){var n=t[i];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}return function(t,i,n){return i&&e(t.prototype,i),n&&e(t,n),t}}(),d=e("./stream"),l=n(d),f=e("./line-stream"),c=n(f),h=e("./parse-stream"),p=n(h),m=function(e){function t(){r(this,t);var e=a(this,(t.__proto__||Object.getPrototypeOf(t)).call(this));e.lineStream=new c["default"],e.parseStream=new p["default"],e.lineStream.pipe(e.parseStream);var i=e,n=[],s={},u=void 0,d=void 0,l=function(){},f={AUDIO:{},VIDEO:{},"CLOSED-CAPTIONS":{},SUBTITLES:{}},h=0;return e.manifest={allowCache:!0,discontinuityStarts:[],segments:[]},e.parseStream.on("data",function(e){var t=void 0,r=void 0;({tag:function(){(({"allow-cache":function(){this.manifest.allowCache=e.allowed,"allowed"in e||(this.trigger("info",{message:"defaulting allowCache to YES"}),this.manifest.allowCache=!0)},byterange:function a(){var a={};"length"in e&&(s.byterange=a,a.length=e.length,"offset"in e||(this.trigger("info",{message:"defaulting offset to zero"}),e.offset=0)),"offset"in e&&(s.byterange=a,a.offset=e.offset)},endlist:function(){this.manifest.endList=!0},inf:function(){"mediaSequence"in this.manifest||(this.manifest.mediaSequence=0,this.trigger("info",{message:"defaulting media sequence to zero"})),"discontinuitySequence"in this.manifest||(this.manifest.discontinuitySequence=0,this.trigger("info",{message:"defaulting discontinuity sequence to zero"})),e.duration>0&&(s.duration=e.duration),0===e.duration&&(s.duration=.01,this.trigger("info",{message:"updating zero segment duration to a small value"})),this.manifest.segments=n},key:function(){return e.attributes?"NONE"===e.attributes.METHOD?void(d=null):e.attributes.URI?(e.attributes.METHOD||this.trigger("warn",{message:"defaulting key method to AES-128"}),d={method:e.attributes.METHOD||"AES-128",uri:e.attributes.URI},void("undefined"!=typeof e.attributes.IV&&(d.iv=e.attributes.IV))):void this.trigger("warn",{message:"ignoring key declaration without URI"}):void this.trigger("warn",{message:"ignoring key declaration without attribute list"})},"media-sequence":function(){return isFinite(e.number)?void(this.manifest.mediaSequence=e.number):void this.trigger("warn",{message:"ignoring invalid media sequence: "+e.number})},"discontinuity-sequence":function(){return isFinite(e.number)?(this.manifest.discontinuitySequence=e.number,void(h=e.number)):void this.trigger("warn",{message:"ignoring invalid discontinuity sequence: "+e.number})},"playlist-type":function(){return/VOD|EVENT/.test(e.playlistType)?void(this.manifest.playlistType=e.playlistType):void this.trigger("warn",{message:"ignoring unknown playlist type: "+e.playlist})},map:function(){u={},e.uri&&(u.uri=e.uri),e.byterange&&(u.byterange=e.byterange)},"stream-inf":function(){return this.manifest.playlists=n,this.manifest.mediaGroups=this.manifest.mediaGroups||f,e.attributes?(s.attributes||(s.attributes={}),void o(s.attributes,e.attributes)):void this.trigger("warn",{message:"ignoring empty stream-inf attributes"})},media:function(){if(this.manifest.mediaGroups=this.manifest.mediaGroups||f,!(e.attributes&&e.attributes.TYPE&&e.attributes["GROUP-ID"]&&e.attributes.NAME))return void this.trigger("warn",{message:"ignoring incomplete or missing media group"});var i=this.manifest.mediaGroups[e.attributes.TYPE];i[e.attributes["GROUP-ID"]]=i[e.attributes["GROUP-ID"]]||{},t=i[e.attributes["GROUP-ID"]],r={"default":/yes/i.test(e.attributes.DEFAULT)},r["default"]?r.autoselect=!0:r.autoselect=/yes/i.test(e.attributes.AUTOSELECT),e.attributes.LANGUAGE&&(r.language=e.attributes.LANGUAGE),e.attributes.URI&&(r.uri=e.attributes.URI),e.attributes["INSTREAM-ID"]&&(r.instreamId=e.attributes["INSTREAM-ID"]),t[e.attributes.NAME]=r},discontinuity:function(){h+=1,s.discontinuity=!0,this.manifest.discontinuityStarts.push(n.length)},"program-date-time":function(){this.manifest.dateTimeString=e.dateTimeString,this.manifest.dateTimeObject=e.dateTimeObject},targetduration:function(){return!isFinite(e.duration)||e.duration<0?void this.trigger("warn",{message:"ignoring invalid target duration: "+e.duration}):void(this.manifest.targetDuration=e.duration)},totalduration:function(){return!isFinite(e.duration)||e.duration<0?void this.trigger("warn",{message:"ignoring invalid total duration: "+e.duration}):void(this.manifest.totalDuration=e.duration)},"cue-out":function(){s.cueOut=e.data},"cue-out-cont":function(){s.cueOutCont=e.data},"cue-in":function(){s.cueIn=e.data}})[e.tagType]||l).call(i)},uri:function(){s.uri=e.uri,n.push(s),!this.manifest.targetDuration||"duration"in s||(this.trigger("warn",{message:"defaulting segment duration to the target duration"}),s.duration=this.manifest.targetDuration),d&&(s.key=d),s.timeline=h,u&&(s.map=u),s={}},comment:function(){}})[e.type].call(i)}),e}return s(t,e),u(t,[{key:"push",value:function(e){this.lineStream.push(e)}},{key:"end",value:function(){this.lineStream.push("\n")}}]),t}(l["default"]);i["default"]=m},{"./line-stream":30,"./parse-stream":31,"./stream":33}],33:[function(e,t,i){"use strict";function n(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}Object.defineProperty(i,"__esModule",{value:!0});var r=function(){function e(e,t){for(var i=0;i<t.length;i++){var n=t[i];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}return function(t,i,n){return i&&e(t.prototype,i),n&&e(t,n),t}}(),a=function(){function e(){n(this,e),this.listeners={}}return r(e,[{key:"on",value:function(e,t){this.listeners[e]||(this.listeners[e]=[]),this.listeners[e].push(t)}},{key:"off",value:function(e,t){if(!this.listeners[e])return!1;var i=this.listeners[e].indexOf(t);return this.listeners[e].splice(i,1),i>-1}},{key:"trigger",value:function(e){var t=this.listeners[e],i=void 0,n=void 0,r=void 0;if(t)if(2===arguments.length)for(n=t.length,i=0;i<n;++i)t[i].call(this,arguments[1]);else for(r=Array.prototype.slice.call(arguments,1),n=t.length,i=0;i<n;++i)t[i].apply(this,r)}},{key:"dispose",value:function(){this.listeners={}}},{key:"pipe",value:function(e){this.on("data",function(t){e.push(t)})}}]),e}();i["default"]=a},{}],34:[function(e,t,i){"use strict";var n=[96e3,88200,64e3,48e3,44100,32e3,24e3,22050,16e3,12e3,11025,8e3,7350],r=function(e){return e[0]<<21|e[1]<<14|e[2]<<7|e[3]},a=function(e,t,i){var n,r="";for(n=t;n<i;n++)r+="%"+("00"+e[n].toString(16)).slice(-2);return r},s=function(e,t,i){return unescape(a(e,t,i))},o=function(e,t){var i=e[t+6]<<21|e[t+7]<<14|e[t+8]<<7|e[t+9],n=e[t+5],r=(16&n)>>4;return r?i+20:i+10},u=function(e,t){var i=(224&e[t+5])>>5,n=e[t+4]<<3,r=6144&e[t+3];return r|n|i},d=function(e,t){return e[t]==="I".charCodeAt(0)&&e[t+1]==="D".charCodeAt(0)&&e[t+2]==="3".charCodeAt(0)?"timed-metadata":e[t]&!0&&240===(240&e[t+1])?"audio":null},l=function(e){for(var t=0;t+5<e.length;){if(255===e[t]&&240===(246&e[t+1]))return n[(60&e[t+2])>>>2];t++}return null},f=function(e){var t,i,n,a;t=10,64&e[5]&&(t+=4,t+=r(e.subarray(10,14)));do{if(i=r(e.subarray(t+4,t+8)),i<1)return null;if(a=String.fromCharCode(e[t],e[t+1],e[t+2],e[t+3]),"PRIV"===a){n=e.subarray(t+10,t+i+10);for(var o=0;o<n.byteLength;o++)if(0===n[o]){var u=s(n,0,o);if("com.apple.streaming.transportStreamTimestamp"===u){var d=n.subarray(o+1),l=(1&d[3])<<30|d[4]<<22|d[5]<<14|d[6]<<6|d[7]>>>2;return l*=4,l+=3&d[7]}break}}t+=10,t+=i}while(t<e.byteLength);return null};t.exports={parseId3TagSize:o,parseAdtsSize:u,parseType:d,parseSampleRate:l,parseAacTimestamp:f}},{}],35:[function(e,t,i){"use strict";var n=e("./stream-types.js"),r=function(e){var t=31&e[1];return t<<=8,t|=e[2]},a=function(e){return!!(64&e[1])},s=function(e){var t=0;return(48&e[3])>>>4>1&&(t+=e[4]+1),t},o=function(e,t){var i=r(e);return 0===i?"pat":i===t?"pmt":t?"pes":null},u=function(e){var t=a(e),i=4+s(e);return t&&(i+=e[i]+1),(31&e[i+10])<<8|e[i+11]},d=function(e){var t={},i=a(e),n=4+s(e);if(i&&(n+=e[n]+1),1&e[n+5]){var r,o,u;r=(15&e[n+1])<<8|e[n+2],o=3+r-4,u=(15&e[n+10])<<8|e[n+11];for(var d=12+u;d<o;){var l=n+d;t[(31&e[l+1])<<8|e[l+2]]=e[l],d+=((15&e[l+3])<<8|e[l+4])+5}return t}},l=function(e,t){var i=r(e),a=t[i];switch(a){case n.H264_STREAM_TYPE:return"video";case n.ADTS_STREAM_TYPE:return"audio";case n.METADATA_STREAM_TYPE:return"timed-metadata";default:return null}},f=function(e){var t=a(e);if(!t)return null;var i,n=4+s(e),r={};return i=e[n+7],192&i&&(r.pts=(14&e[n+9])<<27|(255&e[n+10])<<20|(254&e[n+11])<<12|(255&e[n+12])<<5|(254&e[n+13])>>>3,r.pts*=4,r.pts+=(6&e[n+13])>>>1,r.dts=r.pts,64&i&&(r.dts=(14&e[n+14])<<27|(255&e[n+15])<<20|(254&e[n+16])<<12|(255&e[n+17])<<5|(254&e[n+18])>>>3,r.dts*=4,r.dts+=(6&e[n+18])>>>1)),r},c=function(e){switch(e){case 5:return"slice_layer_without_partitioning_rbsp_idr";case 6:return"sei_rbsp";case 7:return"seq_parameter_set_rbsp";case 8:return"pic_parameter_set_rbsp";case 9:return"access_unit_delimiter_rbsp";default:return null}},h=function(e){for(var t,i=4+s(e),n=e.subarray(i),r=0,a=0,o=!1;a<n.byteLength-3;a++)if(1===n[a+2]){r=a+5;break}for(;r<n.byteLength;)switch(n[r]){case 0:if(0!==n[r-1]){r+=2;break}if(0!==n[r-2]){r++;break}a+3!==r-2&&(t=c(31&n[a+3]),"slice_layer_without_partitioning_rbsp_idr"===t&&(o=!0));do r++;while(1!==n[r]&&r<n.length);a=r-2,r+=3;break;case 1:if(0!==n[r-1]||0!==n[r-2]){r+=3;break}t=c(31&n[a+3]),"slice_layer_without_partitioning_rbsp_idr"===t&&(o=!0),a=r-2,r+=3;break;default:r+=3}return n=n.subarray(a),r-=a,a=0,n&&n.byteLength>3&&(t=c(31&n[a+3]),"slice_layer_without_partitioning_rbsp_idr"===t&&(o=!0)),o};t.exports={parseType:o,parsePat:u,parsePmt:d,parsePayloadUnitStartIndicator:a,parsePesType:l,parsePesTime:f,videoPacketContainsKeyFrame:h}},{"./stream-types.js":36}],36:[function(e,t,i){"use strict";t.exports={H264_STREAM_TYPE:27,ADTS_STREAM_TYPE:15,METADATA_STREAM_TYPE:21}},{}],37:[function(e,t,i){"use strict";var n=e("../utils/stream"),r=8589934592,a=4294967296,s=function(e,t){var i=1;for(e>t&&(i=-1);Math.abs(t-e)>a;)e+=i*r;return e},o=function(e){var t,i;o.prototype.init.call(this),this.type_=e,this.push=function(e){e.type===this.type_&&(void 0===i&&(i=e.dts),e.dts=s(e.dts,i),e.pts=s(e.pts,i),t=e.dts,this.trigger("data",e))},this.flush=function(){i=t,this.trigger("done")}};o.prototype=new n,t.exports={TimestampRolloverStream:o,handleRollover:s}},{"../utils/stream":40}],38:[function(e,t,i){"use strict";var n,r,a,s;n=function(e,t){var i,a,s,o,u,d=[];if(!t.length)return null;for(i=0;i<e.byteLength;)a=e[i]<<24,a|=e[i+1]<<16,a|=e[i+2]<<8,a|=e[i+3],s=r(e.subarray(i+4,i+8)),o=a>1?i+a:e.byteLength,s===t[0]&&(1===t.length?d.push(e.subarray(i+8,o)):(u=n(e.subarray(i+8,o),t.slice(1)),u.length&&(d=d.concat(u)))),i=o;return d},r=function(e){var t="";return t+=String.fromCharCode(e[0]),t+=String.fromCharCode(e[1]),t+=String.fromCharCode(e[2]),t+=String.fromCharCode(e[3])},a=function(e){var t={},i=n(e,["moov","trak"]);return i.reduce(function(e,t){var i,r,a,s,o;return(i=n(t,["tkhd"])[0])?(r=i[0],a=0===r?12:20,s=i[a]<<24|i[a+1]<<16|i[a+2]<<8|i[a+3],(o=n(t,["mdia","mdhd"])[0])?(r=o[0],a=0===r?12:20,e[s]=o[a]<<24|o[a+1]<<16|o[a+2]<<8|o[a+3],e):null):null},t)},s=function(e,t){var i,r,a;return i=n(t,["moof","traf"]),r=[].concat.apply([],i.map(function(t){return n(t,["tfhd"]).map(function(i){var r,a,s;return r=i[4]<<24|i[5]<<16|i[6]<<8|i[7],a=e[r]||9e4,s=n(t,["tfdt"]).map(function(e){var t,i;return t=e[0],i=e[4]<<24|e[5]<<16|e[6]<<8|e[7],1===t&&(i*=Math.pow(2,32),i+=e[8]<<24|e[9]<<16|e[10]<<8|e[11]),i})[0],s=s||1/0,s/a})})),a=Math.min.apply(null,r),isFinite(a)?a:0},t.exports={parseType:r,timescale:a,startTime:s}},{}],39:[function(e,t,i){"use strict";var n=e("../m2ts/stream-types.js"),r=e("../m2ts/timestamp-rollover-stream.js").handleRollover,a={};a.ts=e("../m2ts/probe.js"),a.aac=e("../aac/probe.js");var s=9e4,o=188,u=71,d=function(e){return e[0]==="I".charCodeAt(0)&&e[1]==="D".charCodeAt(0)&&e[2]==="3".charCodeAt(0)},l=function(e,t){for(var i,n,r=0,s=o;s<e.byteLength;)if(e[r]!==u||e[s]!==u)r++,s++;else{switch(i=e.subarray(r,s),n=a.ts.parseType(i,t.pid)){case"pat":t.pid||(t.pid=a.ts.parsePat(i));break;case"pmt":t.table||(t.table=a.ts.parsePmt(i))}if(t.pid&&t.table)return;r+=o,s+=o}},f=function(e,t,i){for(var n,r,s,d,l,f=0,c=o,h=!1;c<e.byteLength;)if(e[f]!==u||e[c]!==u)f++,c++;else{switch(n=e.subarray(f,c),r=a.ts.parseType(n,t.pid)){case"pes":s=a.ts.parsePesType(n,t.table),d=a.ts.parsePayloadUnitStartIndicator(n),"audio"===s&&d&&(l=a.ts.parsePesTime(n),l.type="audio",i.audio.push(l),h=!0)}if(h)break;f+=o,c+=o}for(c=e.byteLength,f=c-o,h=!1;f>=0;)if(e[f]!==u||e[c]!==u)f--,c--;else{switch(n=e.subarray(f,c),r=a.ts.parseType(n,t.pid)){case"pes":s=a.ts.parsePesType(n,t.table),d=a.ts.parsePayloadUnitStartIndicator(n),"audio"===s&&d&&(l=a.ts.parsePesTime(n),l.type="audio",i.audio.push(l),h=!0)}if(h)break;f-=o,c-=o}},c=function(e,t,i){for(var n,r,s,d,l,f,c,h,p=0,m=o,y=!1,g={data:[],size:0};m<e.byteLength;)if(e[p]!==u||e[m]!==u)p++,m++;else{switch(n=e.subarray(p,m),r=a.ts.parseType(n,t.pid)){case"pes":if(s=a.ts.parsePesType(n,t.table),d=a.ts.parsePayloadUnitStartIndicator(n),"video"===s&&(d&&!y&&(l=a.ts.parsePesTime(n),l.type="video",i.video.push(l),y=!0),!i.firstKeyFrame)){if(d&&0!==g.size){for(f=new Uint8Array(g.size),c=0;g.data.length;)h=g.data.shift(),f.set(h,c),c+=h.byteLength;a.ts.videoPacketContainsKeyFrame(f)&&(i.firstKeyFrame=a.ts.parsePesTime(f),i.firstKeyFrame.type="video"),g.size=0}g.data.push(n),g.size+=n.byteLength}}if(y&&i.firstKeyFrame)break;p+=o,m+=o}for(m=e.byteLength,p=m-o,y=!1;p>=0;)if(e[p]!==u||e[m]!==u)p--,m--;else{switch(n=e.subarray(p,m),r=a.ts.parseType(n,t.pid)){case"pes":s=a.ts.parsePesType(n,t.table),d=a.ts.parsePayloadUnitStartIndicator(n),"video"===s&&d&&(l=a.ts.parsePesTime(n),l.type="video",i.video.push(l),y=!0)}if(y)break;p-=o,m-=o}},h=function(e,t){if(e.audio&&e.audio.length){var i=t;"undefined"==typeof i&&(i=e.audio[0].dts),e.audio.forEach(function(e){e.dts=r(e.dts,i),e.pts=r(e.pts,i),e.dtsTime=e.dts/s,e.ptsTime=e.pts/s})}if(e.video&&e.video.length){var n=t;if("undefined"==typeof n&&(n=e.video[0].dts),e.video.forEach(function(e){e.dts=r(e.dts,n),e.pts=r(e.pts,n),e.dtsTime=e.dts/s,e.ptsTime=e.pts/s}),e.firstKeyFrame){var a=e.firstKeyFrame;a.dts=r(a.dts,n),a.pts=r(a.pts,n),a.dtsTime=a.dts/s,a.ptsTime=a.dts/s}}},p=function(e){for(var t,i=!1,n=0,r=null,o=null,u=0,d=0;e.length-d>=3;){var l=a.aac.parseType(e,d);switch(l){case"timed-metadata":if(e.length-d<10){i=!0;break}if(u=a.aac.parseId3TagSize(e,d),u>e.length){i=!0;break}null===o&&(t=e.subarray(d,d+u),o=a.aac.parseAacTimestamp(t)),d+=u;break;case"audio":if(e.length-d<7){i=!0;break}if(u=a.aac.parseAdtsSize(e,d),u>e.length){i=!0;break}null===r&&(t=e.subarray(d,d+u),r=a.aac.parseSampleRate(t)),n++,d+=u;break;default:d++}if(i)return null}if(null===r||null===o)return null;var f=s/r,c={audio:[{type:"audio",dts:o,pts:o},{type:"audio",dts:o+1024*n*f,pts:o+1024*n*f}]};return c},m=function(e){var t={pid:null,table:null},i={};l(e,t);for(var r in t.table)if(t.table.hasOwnProperty(r)){var a=t.table[r];switch(a){case n.H264_STREAM_TYPE:i.video=[],c(e,t,i),0===i.video.length&&delete i.video;break;case n.ADTS_STREAM_TYPE:i.audio=[],f(e,t,i),0===i.audio.length&&delete i.audio}}return i},y=function(e,t){var i,n=d(e);return i=n?p(e):m(e),i&&(i.audio||i.video)?(h(i,t),i):null};t.exports={inspect:y}},{"../aac/probe.js":34,"../m2ts/probe.js":35,"../m2ts/stream-types.js":36,"../m2ts/timestamp-rollover-stream.js":37}],40:[function(e,t,i){"use strict";var n=function(){this.init=function(){var e={};this.on=function(t,i){e[t]||(e[t]=[]),e[t]=e[t].concat(i)},this.off=function(t,i){var n;return!!e[t]&&(n=e[t].indexOf(i),e[t]=e[t].slice(),e[t].splice(n,1),n>-1)},this.trigger=function(t){var i,n,r,a;if(i=e[t])if(2===arguments.length)for(r=i.length,n=0;n<r;++n)i[n].call(this,arguments[1]);else{for(a=[],n=arguments.length,n=1;n<arguments.length;++n)a.push(arguments[n]);for(r=i.length,n=0;n<r;++n)i[n].apply(this,a)}},this.dispose=function(){e={}}}};n.prototype.pipe=function(e){return this.on("data",function(t){e.push(t)}),this.on("done",function(t){e.flush(t)}),e},n.prototype.push=function(e){this.trigger("data",e)},n.prototype.flush=function(e){this.trigger("done",e)},t.exports=n},{}],41:[function(t,i,n){!function(t){var r=/^([^#]*)(.*)$/,a=/^([^\?]*)(.*)$/,s=/^(([a-z]+:\/\/)?[^:\/]+(?::[0-9]+)?)?(\/?.*)$/i,o={buildAbsoluteURL:function(e,t){if(t=t.trim(),/^[a-z]+:/i.test(t))return t;var i=null,n=null,u=r.exec(t);u&&(n=u[2],t=u[1]);var d=a.exec(t);d&&(i=d[2],t=d[1]);var l=r.exec(e);l&&(e=l[1]);var f=a.exec(e);f&&(e=f[1]);var c=s.exec(e);if(!c)throw new Error("Error trying to parse base URL.");var h=c[2]||"",p=c[1]||"",m=c[3];0!==m.indexOf("/")&&""!==p&&(m="/"+m);var y=null;return y=/^\/\//.test(t)?h+o.buildAbsolutePath("",t.substring(2)):/^\//.test(t)?p+"/"+o.buildAbsolutePath("",t.substring(1)):o.buildAbsolutePath(p+m,t),i&&(y+=i),n&&(y+=n),y},buildAbsolutePath:function(e,t){for(var i,n,r=t,a="",s=e.replace(/[^\/]*$/,r.replace(/(\/|^)(?:\.?\/+)+/g,"$1")),o=0;n=s.indexOf("/../",o),
n>-1;o=n+i)i=/^\/(?:\.\.\/)*/.exec(s.slice(n))[0].length,a=(a+s.substring(o,n)).replace(new RegExp("(?:\\/+[^\\/]*){0,"+(i-1)/3+"}$"),"/");return a+s.substr(o)}};"object"==typeof n&&"object"==typeof i?i.exports=o:"function"==typeof e&&e.amd?e([],function(){return o}):"object"==typeof n?n.URLToolkit=o:t.URLToolkit=o}(this)},{}],42:[function(e,t,i){(function(n){"use strict";function r(e){return e&&e.__esModule?e:{"default":e}}Object.defineProperty(i,"__esModule",{value:!0});var a=e("global/window"),s=r(a),o="undefined"!=typeof window?window.videojs:"undefined"!=typeof n?n.videojs:null,u=r(o),d=function(e){Object.defineProperties(e.frame,{id:{get:function(){return u["default"].log.warn("cue.frame.id is deprecated. Use cue.value.key instead."),e.value.key}},value:{get:function(){return u["default"].log.warn("cue.frame.value is deprecated. Use cue.value.data instead."),e.value.data}},privateData:{get:function(){return u["default"].log.warn("cue.frame.privateData is deprecated. Use cue.value.data instead."),e.value.data}}})},l=function(e){var t=void 0;return t=isNaN(e)||Math.abs(e)===1/0?Number.MAX_VALUE:e},f=function(e,t,i){var n=s["default"].WebKitDataCue||s["default"].VTTCue;if(t&&t.forEach(function(e){this.inbandTextTrack_.addCue(new n(e.startTime+this.timestampOffset,e.endTime+this.timestampOffset,e.text))},e),i){var r=l(e.mediaSource_.duration);if(i.forEach(function(e){var t=e.cueTime+this.timestampOffset;e.frames.forEach(function(e){var i=new n(t,t,e.value||e.url||e.data||"");i.frame=e,i.value=e,d(i),this.metadataTrack_.addCue(i)},this)},e),e.metadataTrack_&&e.metadataTrack_.cues&&e.metadataTrack_.cues.length){for(var a=e.metadataTrack_.cues,o=[],u=0;u<a.length;u++)o.push(a[u]);o.sort(function(e,t){return e.startTime-t.startTime});for(var f=0;f<o.length-1;f++)o[f].endTime!==o[f+1].startTime&&(o[f].endTime=o[f+1].startTime);o[o.length-1].endTime=r}}};i["default"]={addTextTrackData:f,durationOfVideo:l},t.exports=i["default"]}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{"global/window":28}],43:[function(e,t,i){"use strict";Object.defineProperty(i,"__esModule",{value:!0});var n=function(e,t,i){for(var n=e.remoteTextTracks()||[],r=0;r<n.length;r++){var a=n[r];a.kind===t&&a.label===i&&e.removeRemoteTextTrack(a)}};i.removeExistingTrack=n;var r=function(e){n(e,"captions","cc1"),n(e,"metadata","Timed Metadata")};i.cleanupTextTracks=r},{}],44:[function(e,t,i){"use strict";Object.defineProperty(i,"__esModule",{value:!0});var n=function(e){return/mp4a\.\d+.\d+/i.test(e)},r=function(e){return/avc1\.[\da-f]+/i.test(e)},a=function(e){var t={type:"",parameters:{}},i=e.trim().split(";");return t.type=i.shift().trim(),i.forEach(function(e){var i=e.trim().split("=");if(i.length>1){var n=i[0].replace(/"/g,"").trim(),r=i[1].replace(/"/g,"").trim();t.parameters[n]=r}}),t},s=function(e){return e.map(function(e){return e.replace(/avc1\.(\d+)\.(\d+)/i,function(e,t,i){var n=("00"+Number(t).toString(16)).slice(-2),r=("00"+Number(i).toString(16)).slice(-2);return"avc1."+n+"00"+r})})};i["default"]={isAudioCodec:n,parseContentType:a,isVideoCodec:r,translateLegacyCodecs:s},t.exports=i["default"]},{}],45:[function(e,t,i){"use strict";Object.defineProperty(i,"__esModule",{value:!0});var n=e("./cleanup-text-tracks"),r=function(e,t,i){var r=t.player_;i.captions&&i.captions.length&&!e.inbandTextTrack_&&((0,n.removeExistingTrack)(r,"captions","cc1"),e.inbandTextTrack_=r.addRemoteTextTrack({kind:"captions",label:"cc1"},!1).track),i.metadata&&i.metadata.length&&!e.metadataTrack_&&((0,n.removeExistingTrack)(r,"metadata","Timed Metadata",!0),e.metadataTrack_=r.addRemoteTextTrack({kind:"metadata",label:"Timed Metadata"},!1).track,e.metadataTrack_.inBandMetadataTrackDispatchType=i.metadata.dispatchType)};i["default"]=r,t.exports=i["default"]},{"./cleanup-text-tracks":43}],46:[function(e,t,i){"use strict";Object.defineProperty(i,"__esModule",{value:!0});var n={TIME_BETWEEN_CHUNKS:1,BYTES_PER_CHUNK:32768};i["default"]=n,t.exports=i["default"]},{}],47:[function(e,t,i){(function(n){"use strict";function r(e){return e&&e.__esModule?e:{"default":e}}function a(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function s(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}Object.defineProperty(i,"__esModule",{value:!0});var o=function(){function e(e,t){for(var i=0;i<t.length;i++){var n=t[i];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}return function(t,i,n){return i&&e(t.prototype,i),n&&e(t,n),t}}(),u=function(e,t,i){for(var n=!0;n;){var r=e,a=t,s=i;n=!1,null===r&&(r=Function.prototype);var o=Object.getOwnPropertyDescriptor(r,a);if(void 0!==o){if("value"in o)return o.value;var u=o.get;if(void 0===u)return;return u.call(s)}var d=Object.getPrototypeOf(r);if(null===d)return;e=d,t=a,i=s,n=!0,o=d=void 0}},d=e("global/document"),l=r(d),f="undefined"!=typeof window?window.videojs:"undefined"!=typeof n?n.videojs:null,c=r(f),h=e("./flash-source-buffer"),p=r(h),m=e("./flash-constants"),y=r(m),g=e("./codec-utils"),v=e("./cleanup-text-tracks"),_=function(e){function t(){var e=this;a(this,t),u(Object.getPrototypeOf(t.prototype),"constructor",this).call(this),this.sourceBuffers=[],this.readyState="closed",this.on(["sourceopen","webkitsourceopen"],function(t){e.swfObj=l["default"].getElementById(t.swfId),e.player_=(0,c["default"])(e.swfObj.parentNode),e.tech_=e.swfObj.tech,e.readyState="open",e.tech_.on("seeking",function(){for(var t=e.sourceBuffers.length;t--;)e.sourceBuffers[t].abort()}),e.tech_.hls&&e.tech_.hls.on("dispose",function(){(0,v.cleanupTextTracks)(e.player_)}),e.swfObj&&e.swfObj.vjs_load()})}return s(t,e),o(t,[{key:"addSeekableRange_",value:function(){}},{key:"addSourceBuffer",value:function(e){var t=(0,g.parseContentType)(e),i=void 0;if("video/mp2t"!==t.type)throw new Error("NotSupportedError (Video.js)");return i=new p["default"](this),this.sourceBuffers.push(i),i}},{key:"endOfStream",value:function(e){"network"===e?this.tech_.error(2):"decode"===e&&this.tech_.error(3),"ended"!==this.readyState&&(this.readyState="ended",this.swfObj.vjs_endOfStream())}}]),t}(c["default"].EventTarget);i["default"]=_;try{Object.defineProperty(_.prototype,"duration",{get:function(){return this.swfObj?this.swfObj.vjs_getProperty("duration"):NaN},set:function(e){var t=void 0,i=this.swfObj.vjs_getProperty("duration");if(this.swfObj.vjs_setProperty("duration",e),e<i)for(t=0;t<this.sourceBuffers.length;t++)this.sourceBuffers[t].remove(e,i);return e}})}catch(b){_.prototype.duration=NaN}for(var T in y["default"])_[T]=y["default"][T];t.exports=i["default"]}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{"./cleanup-text-tracks":43,"./codec-utils":44,"./flash-constants":46,"./flash-source-buffer":48,"global/document":27}],48:[function(e,t,i){(function(n){"use strict";function r(e){return e&&e.__esModule?e:{"default":e}}function a(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function s(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}Object.defineProperty(i,"__esModule",{value:!0});var o=function(){function e(e,t){for(var i=0;i<t.length;i++){var n=t[i];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}return function(t,i,n){return i&&e(t.prototype,i),n&&e(t,n),t}}(),u=function(e,t,i){for(var n=!0;n;){var r=e,a=t,s=i;n=!1,null===r&&(r=Function.prototype);var o=Object.getOwnPropertyDescriptor(r,a);if(void 0!==o){if("value"in o)return o.value;var u=o.get;if(void 0===u)return;return u.call(s)}var d=Object.getPrototypeOf(r);if(null===d)return;e=d,t=a,i=s,n=!0,o=d=void 0}},d=e("global/window"),l=r(d),f="undefined"!=typeof window?window.videojs:"undefined"!=typeof n?n.videojs:null,c=r(f),h=e("mux.js/lib/flv"),p=r(h),m=e("./remove-cues-from-track"),y=r(m),g=e("./create-text-tracks-if-necessary"),v=r(g),_=e("./add-text-track-data"),b=e("./flash-transmuxer-worker"),T=r(b),S=e("webworkify"),w=r(S),k=e("./flash-constants"),O=r(k),P=function(e){l["default"].setTimeout(e,O["default"].TIME_BETWEEN_CHUNKS)},A=function(){return Math.random().toString(36).slice(2,8)},E=function(e,t){("number"!=typeof t||t<0)&&(t=0);var i=Math.pow(10,t);return Math.round(e*i)/i},x=function(e){function t(e){var i=this;a(this,t),u(Object.getPrototypeOf(t.prototype),"constructor",this).call(this);var n=void 0;this.chunkSize_=O["default"].BYTES_PER_CHUNK,this.buffer_=[],this.bufferSize_=0,this.basePtsOffset_=NaN,this.mediaSource_=e,this.updating=!1,this.timestampOffset_=0,n=l["default"].btoa(String.fromCharCode.apply(null,Array.prototype.slice.call(p["default"].getFlvHeader())));var r=this.mediaSource_.player_.id().replace(/[^a-zA-Z0-9]/g,"_");this.flashEncodedHeaderName_="vjs_flashEncodedHeader_"+r+A(),this.flashEncodedDataName_="vjs_flashEncodedData_"+r+A(),l["default"][this.flashEncodedHeaderName_]=function(){return delete l["default"][i.flashEncodedHeaderName_],n},this.mediaSource_.swfObj.vjs_appendChunkReady(this.flashEncodedHeaderName_),this.transmuxer_=(0,w["default"])(T["default"]),this.transmuxer_.postMessage({action:"init",options:{}}),this.transmuxer_.onmessage=function(e){"data"===e.data.action&&i.receiveBuffer_(e.data.segment)},this.one("updateend",function(){i.mediaSource_.tech_.trigger("loadedmetadata")}),Object.defineProperty(this,"timestampOffset",{get:function(){return this.timestampOffset_},set:function(e){"number"==typeof e&&e>=0&&(this.timestampOffset_=e,this.mediaSource_.swfObj.vjs_discontinuity(),this.basePtsOffset_=NaN,this.transmuxer_.postMessage({action:"reset"}))}}),Object.defineProperty(this,"buffered",{get:function(){if(!(this.mediaSource_&&this.mediaSource_.swfObj&&"vjs_getProperty"in this.mediaSource_.swfObj))return c["default"].createTimeRange();var e=this.mediaSource_.swfObj.vjs_getProperty("buffered");return e&&e.length&&(e[0][0]=E(e[0][0],3),e[0][1]=E(e[0][1],3)),c["default"].createTimeRanges(e)}}),this.mediaSource_.player_.on("seeked",function(){(0,y["default"])(0,1/0,i.metadataTrack_),(0,y["default"])(0,1/0,i.inbandTextTrack_)}),this.mediaSource_.player_.tech_.hls.on("dispose",function(){i.transmuxer_.terminate()})}return s(t,e),o(t,[{key:"appendBuffer",value:function(e){var t=void 0;if(this.updating)throw t=new Error("SourceBuffer.append() cannot be called while an update is in progress"),t.name="InvalidStateError",t.code=11,t;this.updating=!0,this.mediaSource_.readyState="open",this.trigger({type:"update"}),this.transmuxer_.postMessage({action:"push",data:e.buffer,byteOffset:e.byteOffset,byteLength:e.byteLength},[e.buffer]),this.transmuxer_.postMessage({action:"flush"})}},{key:"abort",value:function(){this.buffer_=[],this.bufferSize_=0,this.mediaSource_.swfObj.vjs_abort(),this.updating&&(this.updating=!1,this.trigger({type:"updateend"}))}},{key:"remove",value:function(e,t){(0,y["default"])(e,t,this.metadataTrack_),(0,y["default"])(e,t,this.inbandTextTrack_),this.trigger({type:"update"}),this.trigger({type:"updateend"})}},{key:"receiveBuffer_",value:function(e){var t=this;(0,v["default"])(this,this.mediaSource_,e),(0,_.addTextTrackData)(this,e.captions,e.metadata),P(function(){var i=t.convertTagsToData_(e);0===t.buffer_.length&&P(t.processBuffer_.bind(t)),i&&(t.buffer_.push(i),t.bufferSize_+=i.byteLength)})}},{key:"processBuffer_",value:function(){var e=this,t=O["default"].BYTES_PER_CHUNK;if(!this.buffer_.length)return void(this.updating!==!1&&(this.updating=!1,this.trigger({type:"updateend"})));var i=this.buffer_[0].subarray(0,t);i.byteLength<t||this.buffer_[0].byteLength===t?this.buffer_.shift():this.buffer_[0]=this.buffer_[0].subarray(t),this.bufferSize_-=i.byteLength;for(var n=[],r=i.byteLength,a=0;a<r;a++)n.push(String.fromCharCode(i[a]));var s=l["default"].btoa(n.join(""));l["default"][this.flashEncodedDataName_]=function(){return P(e.processBuffer_.bind(e)),delete l["default"][e.flashEncodedDataName_],s},this.mediaSource_.swfObj.vjs_appendChunkReady(this.flashEncodedDataName_)}},{key:"convertTagsToData_",value:function(e){var t=0,i=this.mediaSource_.tech_,n=0,r=void 0,a=[],s=[],o=e.tags.videoTags,u=e.tags.audioTags;if(isNaN(this.basePtsOffset_)&&(o.length||u.length)){var d=o[0]||{pts:1/0},l=u[0]||{pts:1/0};this.basePtsOffset_=Math.min(l.pts,d.pts)}i.buffered().length&&(n=i.buffered().end(0)-this.timestampOffset),i.seeking()&&(n=Math.max(n,i.currentTime()-this.timestampOffset)),n*=1e3,n+=this.basePtsOffset_;for(var f=0;f<u.length;f++)u[f].pts>=n&&a.push(u[f]);for(var c=0;c<o.length;){var h=o[c];if(h.pts>=n)s.push(h);else if(h.keyFrame){for(var p=c+1,m=!1;p<o.length;){var y=o[p];if(y.pts>=n)break;if(y.keyFrame){m=!0;break}p++}if(m)c=p;else for(;c<p;)s.push(o[c]),c++;continue}c++}var g=this.getOrderedTags_(s,a);if(0!==g.length){for(var f=0;f<g.length;f++)t+=g[f].bytes.byteLength;r=new Uint8Array(t);for(var f=0,v=0;f<g.length;f++)r.set(g[f].bytes,v),v+=g[f].bytes.byteLength;return r}}},{key:"getOrderedTags_",value:function(e,t){for(var i=void 0,n=[];e.length||t.length;)i=e.length?t.length&&t[0].dts<e[0].dts?t.shift():e.shift():t.shift(),n.push(i);return n}}]),t}(c["default"].EventTarget);i["default"]=x,t.exports=i["default"]}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{"./add-text-track-data":42,"./create-text-tracks-if-necessary":45,"./flash-constants":46,"./flash-transmuxer-worker":49,"./remove-cues-from-track":51,"global/window":28,"mux.js/lib/flv":62,webworkify:76}],49:[function(e,t,i){"use strict";function n(e){return e&&e.__esModule?e:{"default":e}}function r(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}Object.defineProperty(i,"__esModule",{value:!0});var a=function(){function e(e,t){for(var i=0;i<t.length;i++){var n=t[i];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}return function(t,i,n){return i&&e(t.prototype,i),n&&e(t,n),t}}(),s=e("global/window"),o=n(s),u=e("mux.js/lib/flv"),d=n(u),l=function(e){e.on("data",function(e){o["default"].postMessage({action:"data",segment:e})}),e.on("done",function(e){o["default"].postMessage({action:"done"})})},f=function(){function e(t){r(this,e),this.options=t||{},this.init()}return a(e,[{key:"init",value:function(){this.transmuxer&&this.transmuxer.dispose(),this.transmuxer=new d["default"].Transmuxer(this.options),l(this.transmuxer)}},{key:"push",value:function(e){var t=new Uint8Array(e.data,e.byteOffset,e.byteLength);this.transmuxer.push(t)}},{key:"reset",value:function(){this.init()}},{key:"flush",value:function(){this.transmuxer.flush()}}]),e}(),c=function(e){e.onmessage=function(e){return"init"===e.data.action&&e.data.options?void(this.messageHandlers=new f(e.data.options)):(this.messageHandlers||(this.messageHandlers=new f),void(e.data&&e.data.action&&"init"!==e.data.action&&this.messageHandlers[e.data.action]&&this.messageHandlers[e.data.action](e.data)))}};i["default"]=function(e){return new c(e)},t.exports=i["default"]},{"global/window":28,"mux.js/lib/flv":62}],50:[function(e,t,i){(function(n){"use strict";function r(e){return e&&e.__esModule?e:{"default":e}}function a(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function s(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}Object.defineProperty(i,"__esModule",{value:!0});var o=function(){function e(e,t){for(var i=0;i<t.length;i++){var n=t[i];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}return function(t,i,n){return i&&e(t.prototype,i),n&&e(t,n),t}}(),u=function(e,t,i){for(var n=!0;n;){var r=e,a=t,s=i;n=!1,null===r&&(r=Function.prototype);var o=Object.getOwnPropertyDescriptor(r,a);if(void 0!==o){if("value"in o)return o.value;var u=o.get;if(void 0===u)return;return u.call(s)}var d=Object.getPrototypeOf(r);if(null===d)return;e=d,t=a,i=s,n=!0,o=d=void 0}},d=e("global/window"),l=r(d),f=e("global/document"),c=r(f),h="undefined"!=typeof window?window.videojs:"undefined"!=typeof n?n.videojs:null,p=r(h),m=e("./virtual-source-buffer"),y=r(m),g=e("./add-text-track-data"),v=e("./codec-utils"),_=e("./cleanup-text-tracks"),b=function(e){function t(){var e=this;a(this,t),u(Object.getPrototypeOf(t.prototype),"constructor",this).call(this);var i=void 0;this.nativeMediaSource_=new l["default"].MediaSource;for(i in this.nativeMediaSource_)i in t.prototype||"function"!=typeof this.nativeMediaSource_[i]||(this[i]=this.nativeMediaSource_[i].bind(this.nativeMediaSource_));this.duration_=NaN,Object.defineProperty(this,"duration",{get:function(){return this.duration_===1/0?this.duration_:this.nativeMediaSource_.duration},set:function(e){if(this.duration_=e,e!==1/0)return void(this.nativeMediaSource_.duration=e)}}),Object.defineProperty(this,"seekable",{get:function(){return this.duration_===1/0?p["default"].createTimeRanges([[0,this.nativeMediaSource_.duration]]):this.nativeMediaSource_.seekable}}),Object.defineProperty(this,"readyState",{get:function(){return this.nativeMediaSource_.readyState}}),Object.defineProperty(this,"activeSourceBuffers",{get:function(){return this.activeSourceBuffers_}}),this.sourceBuffers=[],this.activeSourceBuffers_=[],this.updateActiveSourceBuffers_=function(){e.activeSourceBuffers_.length=0;for(var t=!1,i=!0,n=0;n<e.player_.audioTracks().length;n++){var r=e.player_.audioTracks()[n];if(r.enabled&&"main"!==r.kind){t=!0,i=!1;break}}e.sourceBuffers.forEach(function(n){if(n.appendAudioInitSegment_=!0,n.videoCodec_&&n.audioCodec_)n.audioDisabled_=t;else if(n.videoCodec_&&!n.audioCodec_)n.audioDisabled_=!0,i=!1;else if(!n.videoCodec_&&n.audioCodec_&&(n.audioDisabled_=i,i))return;e.activeSourceBuffers_.push(n)})},this.onPlayerMediachange_=function(){e.sourceBuffers.forEach(function(e){e.appendAudioInitSegment_=!0})},["sourceopen","sourceclose","sourceended"].forEach(function(e){this.nativeMediaSource_.addEventListener(e,this.trigger.bind(this))},this),this.on("sourceopen",function(t){var i=c["default"].querySelector('[src="'+e.url_+'"]');i&&(e.player_=(0,p["default"])(i.parentNode),e.player_.audioTracks&&e.player_.audioTracks()&&(e.player_.audioTracks().on("change",e.updateActiveSourceBuffers_),e.player_.audioTracks().on("addtrack",e.updateActiveSourceBuffers_),e.player_.audioTracks().on("removetrack",e.updateActiveSourceBuffers_)),e.player_.on("mediachange",e.onPlayerMediachange_))}),this.on("sourceended",function(t){for(var i=(0,g.durationOfVideo)(e.duration),n=0;n<e.sourceBuffers.length;n++){var r=e.sourceBuffers[n],a=r.metadataTrack_&&r.metadataTrack_.cues;a&&a.length&&(a[a.length-1].endTime=i)}}),this.on("sourceclose",function(e){this.sourceBuffers.forEach(function(e){e.transmuxer_&&e.transmuxer_.terminate()}),this.sourceBuffers.length=0,this.player_&&((0,_.cleanupTextTracks)(this.player_),this.player_.audioTracks&&this.player_.audioTracks()&&(this.player_.audioTracks().off("change",this.updateActiveSourceBuffers_),this.player_.audioTracks().off("addtrack",this.updateActiveSourceBuffers_),this.player_.audioTracks().off("removetrack",this.updateActiveSourceBuffers_)),this.player_.el_&&this.player_.off("mediachange",this.onPlayerMediachange_))})}return s(t,e),o(t,[{key:"addSeekableRange_",value:function(e,t){var i=void 0;if(this.duration!==1/0)throw i=new Error("MediaSource.addSeekableRange() can only be invoked when the duration is Infinity"),i.name="InvalidStateError",i.code=11,i;(t>this.nativeMediaSource_.duration||isNaN(this.nativeMediaSource_.duration))&&(this.nativeMediaSource_.duration=t)}},{key:"addSourceBuffer",value:function(e){var t=void 0,i=(0,v.parseContentType)(e);if(/^(video|audio)\/mp2t$/i.test(i.type)){var n=[];i.parameters&&i.parameters.codecs&&(n=i.parameters.codecs.split(","),n=(0,v.translateLegacyCodecs)(n),n=n.filter(function(e){return(0,v.isAudioCodec)(e)||(0,v.isVideoCodec)(e)})),0===n.length&&(n=["avc1.4d400d","mp4a.40.2"]),t=new y["default"](this,n),0!==this.sourceBuffers.length&&(this.sourceBuffers[0].createRealSourceBuffers_(),t.createRealSourceBuffers_(),this.sourceBuffers[0].audioDisabled_=!0)}else t=this.nativeMediaSource_.addSourceBuffer(e);return this.sourceBuffers.push(t),t}}]),t}(p["default"].EventTarget);i["default"]=b,t.exports=i["default"]}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{"./add-text-track-data":42,"./cleanup-text-tracks":43,"./codec-utils":44,"./virtual-source-buffer":54,"global/document":27,"global/window":28}],51:[function(e,t,i){"use strict";Object.defineProperty(i,"__esModule",{value:!0});var n=function(e,t,i){var n=void 0,r=void 0;if(i&&i.cues)for(n=i.cues.length;n--;)r=i.cues[n],r.startTime<=t&&r.endTime>=e&&i.removeCue(r)};i["default"]=n,t.exports=i["default"]},{}],52:[function(e,t,i){"use strict";function n(e){return e&&e.__esModule?e:{"default":e}}function r(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}Object.defineProperty(i,"__esModule",{value:!0});var a=function(){function e(e,t){for(var i=0;i<t.length;i++){var n=t[i];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}return function(t,i,n){return i&&e(t.prototype,i),n&&e(t,n),t}}(),s=e("global/window"),o=n(s),u=e("mux.js/lib/mp4"),d=n(u),l=function(e){e.on("data",function(e){var t=e.initSegment;e.initSegment={data:t.buffer,byteOffset:t.byteOffset,byteLength:t.byteLength};var i=e.data;e.data=i.buffer,o["default"].postMessage({action:"data",segment:e,byteOffset:i.byteOffset,byteLength:i.byteLength},[e.data])}),e.captionStream&&e.captionStream.on("data",function(e){o["default"].postMessage({action:"caption",data:e})}),e.on("done",function(e){o["default"].postMessage({action:"done"})})},f=function(){function e(t){r(this,e),this.options=t||{},this.init()}return a(e,[{key:"init",value:function(){this.transmuxer&&this.transmuxer.dispose(),this.transmuxer=new d["default"].Transmuxer(this.options),l(this.transmuxer)}},{key:"push",value:function(e){var t=new Uint8Array(e.data,e.byteOffset,e.byteLength);this.transmuxer.push(t)}},{key:"reset",value:function(){this.init()}},{key:"setTimestampOffset",value:function(e){var t=e.timestampOffset||0;this.transmuxer.setBaseMediaDecodeTime(Math.round(9e4*t))}},{key:"setAudioAppendStart",value:function(e){this.transmuxer.setAudioAppendStart(Math.ceil(9e4*e.appendStart))}},{key:"flush",value:function(e){this.transmuxer.flush()}}]),e}(),c=function(e){e.onmessage=function(e){return"init"===e.data.action&&e.data.options?void(this.messageHandlers=new f(e.data.options)):(this.messageHandlers||(this.messageHandlers=new f),void(e.data&&e.data.action&&"init"!==e.data.action&&this.messageHandlers[e.data.action]&&this.messageHandlers[e.data.action](e.data)))}};i["default"]=function(e){return new c(e)},t.exports=i["default"]},{"global/window":28,"mux.js/lib/mp4":70}],53:[function(e,t,i){(function(t){"use strict";function n(e){return e&&e.__esModule?e:{"default":e}}Object.defineProperty(i,"__esModule",{value:!0});var r=e("global/window"),a=n(r),s=e("./flash-media-source"),o=n(s),u=e("./html-media-source"),d=n(u),l="undefined"!=typeof window?window.videojs:"undefined"!=typeof t?t.videojs:null,f=n(l),c=0,h={mode:"auto"};f["default"].mediaSources={};var p=function(e,t){var i=f["default"].mediaSources[e];if(!i)throw new Error("Media Source not found (Video.js)");i.trigger({type:"sourceopen",swfId:t})},m=function(){return!!a["default"].MediaSource&&!!a["default"].MediaSource.isTypeSupported&&a["default"].MediaSource.isTypeSupported('video/mp4;codecs="avc1.4d400d,mp4a.40.2"')},y=function(e){var t=f["default"].mergeOptions(h,e);return this.MediaSource={open:p,supportsNativeMediaSources:m},"html5"===t.mode||"auto"===t.mode&&m()?new d["default"]:new o["default"]};i.MediaSource=y,y.open=p,y.supportsNativeMediaSources=m;var g={createObjectURL:function(e){var t="blob:vjs-media-source/",i=void 0;return e instanceof d["default"]?(i=a["default"].URL.createObjectURL(e.nativeMediaSource_),e.url_=i,i):e instanceof o["default"]?(i=t+c,c++,f["default"].mediaSources[i]=e,i):(i=a["default"].URL.createObjectURL(e),e.url_=i,i)}};i.URL=g,f["default"].MediaSource=y,f["default"].URL=g}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{"./flash-media-source":47,"./html-media-source":50,"global/window":28}],54:[function(e,t,i){(function(n){"use strict";function r(e){return e&&e.__esModule?e:{"default":e}}function a(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function s(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}Object.defineProperty(i,"__esModule",{value:!0});var o=function(){function e(e,t){for(var i=0;i<t.length;i++){var n=t[i];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}return function(t,i,n){return i&&e(t.prototype,i),n&&e(t,n),t}}(),u=function(e,t,i){for(var n=!0;n;){var r=e,a=t,s=i;n=!1,null===r&&(r=Function.prototype);var o=Object.getOwnPropertyDescriptor(r,a);if(void 0!==o){if("value"in o)return o.value;var u=o.get;if(void 0===u)return;return u.call(s)}var d=Object.getPrototypeOf(r);if(null===d)return;e=d,t=a,i=s,n=!0,o=d=void 0}},d="undefined"!=typeof window?window.videojs:"undefined"!=typeof n?n.videojs:null,l=r(d),f=e("./create-text-tracks-if-necessary"),c=r(f),h=e("./remove-cues-from-track"),p=r(h),m=e("./add-text-track-data"),y=e("webworkify"),g=r(y),v=e("./transmuxer-worker"),_=r(v),b=e("./codec-utils"),T=function(e){function t(e,i){var n=this;a(this,t),u(Object.getPrototypeOf(t.prototype),"constructor",this).call(this,l["default"].EventTarget),this.timestampOffset_=0,this.pendingBuffers_=[],this.bufferUpdating_=!1,this.mediaSource_=e,this.codecs_=i,this.audioCodec_=null,this.videoCodec_=null,this.audioDisabled_=!1,this.appendAudioInitSegment_=!0;var r={remux:!1};this.codecs_.forEach(function(e){(0,b.isAudioCodec)(e)?n.audioCodec_=e:(0,b.isVideoCodec)(e)&&(n.videoCodec_=e)}),this.transmuxer_=(0,g["default"])(_["default"]),this.transmuxer_.postMessage({action:"init",options:r}),this.transmuxer_.onmessage=function(e){return"data"===e.data.action?n.data_(e):"done"===e.data.action?n.done_(e):void 0},Object.defineProperty(this,"timestampOffset",{get:function(){return this.timestampOffset_},set:function(e){"number"==typeof e&&e>=0&&(this.timestampOffset_=e,this.appendAudioInitSegment_=!0,this.transmuxer_.postMessage({action:"setTimestampOffset",timestampOffset:e}))}}),Object.defineProperty(this,"appendWindowStart",{get:function(){return(this.videoBuffer_||this.audioBuffer_).appendWindowStart},set:function(e){this.videoBuffer_&&(this.videoBuffer_.appendWindowStart=e),this.audioBuffer_&&(this.audioBuffer_.appendWindowStart=e)}}),Object.defineProperty(this,"updating",{get:function(){return!!(this.bufferUpdating_||!this.audioDisabled_&&this.audioBuffer_&&this.audioBuffer_.updating||this.videoBuffer_&&this.videoBuffer_.updating)}}),Object.defineProperty(this,"buffered",{get:function(){var e=null,t=null,i=0,n=[],r=[];if(!this.videoBuffer_&&!this.audioBuffer_)return l["default"].createTimeRange();if(!this.videoBuffer_)return this.audioBuffer_.buffered;if(!this.audioBuffer_)return this.videoBuffer_.buffered;if(this.audioDisabled_)return this.videoBuffer_.buffered;if(0===this.videoBuffer_.buffered.length&&0===this.audioBuffer_.buffered.length)return l["default"].createTimeRange();for(var a=this.videoBuffer_.buffered,s=this.audioBuffer_.buffered,o=a.length;o--;)n.push({time:a.start(o),type:"start"}),n.push({time:a.end(o),type:"end"});for(o=s.length;o--;)n.push({time:s.start(o),type:"start"}),n.push({time:s.end(o),type:"end"});for(n.sort(function(e,t){return e.time-t.time}),o=0;o<n.length;o++)"start"===n[o].type?(i++,2===i&&(e=n[o].time)):"end"===n[o].type&&(i--,1===i&&(t=n[o].time)),null!==e&&null!==t&&(r.push([e,t]),e=null,t=null);return l["default"].createTimeRanges(r)}})}return s(t,e),o(t,[{key:"data_",value:function(e){var t=e.data.segment;t.data=new Uint8Array(t.data,e.data.byteOffset,e.data.byteLength),t.initSegment=new Uint8Array(t.initSegment.data,t.initSegment.byteOffset,t.initSegment.byteLength),(0,c["default"])(this,this.mediaSource_,t),this.pendingBuffers_.push(t)}},{key:"done_",value:function(e){this.processPendingSegments_()}},{key:"createRealSourceBuffers_",value:function(){var e=this,t=["audio","video"];t.forEach(function(i){if(e[i+"Codec_"]&&!e[i+"Buffer_"]){var n=null;e.mediaSource_[i+"Buffer_"]?n=e.mediaSource_[i+"Buffer_"]:(n=e.mediaSource_.nativeMediaSource_.addSourceBuffer(i+'/mp4;codecs="'+e[i+"Codec_"]+'"'),e.mediaSource_[i+"Buffer_"]=n),e[i+"Buffer_"]=n,["update","updatestart","updateend"].forEach(function(r){n.addEventListener(r,function(){if("audio"!==i||!e.audioDisabled_){var n=t.every(function(t){return!("audio"!==t||!e.audioDisabled_)||(i===t||!e[t+"Buffer_"]||!e[t+"Buffer_"].updating)});return n?e.trigger(r):void 0}})})}})}},{key:"appendBuffer",value:function(e){if(this.bufferUpdating_=!0,this.audioBuffer_&&this.audioBuffer_.buffered.length){var t=this.audioBuffer_.buffered;this.transmuxer_.postMessage({action:"setAudioAppendStart",appendStart:t.end(t.length-1)})}this.transmuxer_.postMessage({action:"push",data:e.buffer,byteOffset:e.byteOffset,byteLength:e.byteLength},[e.buffer]),this.transmuxer_.postMessage({action:"flush"})}},{key:"remove",value:function(e,t){this.videoBuffer_&&this.videoBuffer_.remove(e,t),this.audioBuffer_&&this.audioBuffer_.remove(e,t),(0,p["default"])(e,t,this.metadataTrack_),(0,p["default"])(e,t,this.inbandTextTrack_)}},{key:"processPendingSegments_",value:function(){var e={video:{segments:[],bytes:0},audio:{segments:[],bytes:0},captions:[],metadata:[]};e=this.pendingBuffers_.reduce(function(e,t){var i=t.type,n=t.data,r=t.initSegment;return e[i].segments.push(n),e[i].bytes+=n.byteLength,e[i].initSegment=r,t.captions&&(e.captions=e.captions.concat(t.captions)),t.info&&(e[i].info=t.info),t.metadata&&(e.metadata=e.metadata.concat(t.metadata)),e},e),this.videoBuffer_||this.audioBuffer_||(0===e.video.bytes&&(this.videoCodec_=null),0===e.audio.bytes&&(this.audioCodec_=null),this.createRealSourceBuffers_()),e.audio.info&&this.mediaSource_.trigger({type:"audioinfo",info:e.audio.info}),e.video.info&&this.mediaSource_.trigger({type:"videoinfo",info:e.video.info}),this.appendAudioInitSegment_&&(!this.audioDisabled_&&this.audioBuffer_&&(e.audio.segments.unshift(e.audio.initSegment),e.audio.bytes+=e.audio.initSegment.byteLength),this.appendAudioInitSegment_=!1),this.videoBuffer_&&(e.video.segments.unshift(e.video.initSegment),e.video.bytes+=e.video.initSegment.byteLength,this.concatAndAppendSegments_(e.video,this.videoBuffer_),(0,m.addTextTrackData)(this,e.captions,e.metadata)),!this.audioDisabled_&&this.audioBuffer_&&this.concatAndAppendSegments_(e.audio,this.audioBuffer_),this.pendingBuffers_.length=0,this.bufferUpdating_=!1}},{key:"concatAndAppendSegments_",value:function(e,t){
var i=0,n=void 0;if(e.bytes){n=new Uint8Array(e.bytes),e.segments.forEach(function(e){n.set(e,i),i+=e.byteLength});try{t.appendBuffer(n)}catch(r){this.mediaSource_.player_&&this.mediaSource_.player_.error({code:-3,type:"APPEND_BUFFER_ERR",message:r.message,originalError:r})}}}},{key:"abort",value:function(){this.videoBuffer_&&this.videoBuffer_.abort(),this.audioBuffer_&&this.audioBuffer_.abort(),this.transmuxer_&&this.transmuxer_.postMessage({action:"reset"}),this.pendingBuffers_.length=0,this.bufferUpdating_=!1}}]),t}(l["default"].EventTarget);i["default"]=T,t.exports=i["default"]}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{"./add-text-track-data":42,"./codec-utils":44,"./create-text-tracks-if-necessary":45,"./remove-cues-from-track":51,"./transmuxer-worker":52,webworkify:76}],55:[function(e,t,i){"use strict";var n,r=e("../utils/stream.js");n=function(){var e=new Uint8Array,t=0;n.prototype.init.call(this),this.setTimestamp=function(e){t=e},this.parseId3TagSize=function(e,t){var i=e[t+6]<<21|e[t+7]<<14|e[t+8]<<7|e[t+9],n=e[t+5],r=(16&n)>>4;return r?i+20:i+10},this.parseAdtsSize=function(e,t){var i=(224&e[t+5])>>5,n=e[t+4]<<3,r=6144&e[t+3];return r|n|i},this.push=function(i){var n,r,a,s,o=0,u=0;for(e.length?(s=e.length,e=new Uint8Array(i.byteLength+s),e.set(e.subarray(0,s)),e.set(i,s)):e=i;e.length-u>=3;)if(e[u]!=="I".charCodeAt(0)||e[u+1]!=="D".charCodeAt(0)||e[u+2]!=="3".charCodeAt(0))if(e[u]&!0&&240===(240&e[u+1])){if(e.length-u<7)break;if(o=this.parseAdtsSize(e,u),o>e.length)break;a={type:"audio",data:e.subarray(u,u+o),pts:t,dts:t},this.trigger("data",a),u+=o}else u++;else{if(e.length-u<10)break;if(o=this.parseId3TagSize(e,u),o>e.length)break;r={type:"timed-metadata",data:e.subarray(u,u+o)},this.trigger("data",r),u+=o}n=e.length-u,e=n>0?e.subarray(u):new Uint8Array}},n.prototype=new r,t.exports=n},{"../utils/stream.js":75}],56:[function(e,t,i){"use strict";var n,r=e("../utils/stream.js"),a=[96e3,88200,64e3,48e3,44100,32e3,24e3,22050,16e3,12e3,11025,8e3,7350];n=function(){var e;n.prototype.init.call(this),this.push=function(t){var i,n,r,s,o,u,d=0,l=0;if("audio"===t.type)for(e?(s=e,e=new Uint8Array(s.byteLength+t.data.byteLength),e.set(s),e.set(t.data,s.byteLength)):e=t.data;d+5<e.length;)if(255===e[d]&&240===(246&e[d+1])){if(n=2*(1&~e[d+1]),i=(3&e[d+3])<<11|e[d+4]<<3|(224&e[d+5])>>5,o=1024*((3&e[d+6])+1),u=9e4*o/a[(60&e[d+2])>>>2],r=d+i,e.byteLength<r)return;if(this.trigger("data",{pts:t.pts+l*u,dts:t.dts+l*u,sampleCount:o,audioobjecttype:(e[d+2]>>>6&3)+1,channelcount:(1&e[d+2])<<2|(192&e[d+3])>>>6,samplerate:a[(60&e[d+2])>>>2],samplingfrequencyindex:(60&e[d+2])>>>2,samplesize:16,data:e.subarray(d+7+n,r)}),e.byteLength===r)return void(e=void 0);l++,e=e.subarray(r)}else d++},this.flush=function(){this.trigger("done")}},n.prototype=new r,t.exports=n},{"../utils/stream.js":75}],57:[function(e,t,i){"use strict";var n,r,a,s=e("../utils/stream.js"),o=e("../utils/exp-golomb.js");r=function(){var e,t,i=0;r.prototype.init.call(this),this.push=function(n){var r;for(t?(r=new Uint8Array(t.byteLength+n.data.byteLength),r.set(t),r.set(n.data,t.byteLength),t=r):t=n.data;i<t.byteLength-3;i++)if(1===t[i+2]){e=i+5;break}for(;e<t.byteLength;)switch(t[e]){case 0:if(0!==t[e-1]){e+=2;break}if(0!==t[e-2]){e++;break}i+3!==e-2&&this.trigger("data",t.subarray(i+3,e-2));do e++;while(1!==t[e]&&e<t.length);i=e-2,e+=3;break;case 1:if(0!==t[e-1]||0!==t[e-2]){e+=3;break}this.trigger("data",t.subarray(i+3,e-2)),i=e-2,e+=3;break;default:e+=3}t=t.subarray(i),e-=i,i=0},this.flush=function(){t&&t.byteLength>3&&this.trigger("data",t.subarray(i+3)),t=null,i=0,this.trigger("done")}},r.prototype=new s,a={100:!0,110:!0,122:!0,244:!0,44:!0,83:!0,86:!0,118:!0,128:!0,138:!0,139:!0,134:!0},n=function(){var e,t,i,s,u,d,l,f=new r;n.prototype.init.call(this),e=this,this.push=function(e){"video"===e.type&&(t=e.trackId,i=e.pts,s=e.dts,f.push(e))},f.on("data",function(n){var r={trackId:t,pts:i,dts:s,data:n};switch(31&n[0]){case 5:r.nalUnitType="slice_layer_without_partitioning_rbsp_idr";break;case 6:r.nalUnitType="sei_rbsp",r.escapedRBSP=u(n.subarray(1));break;case 7:r.nalUnitType="seq_parameter_set_rbsp",r.escapedRBSP=u(n.subarray(1)),r.config=d(r.escapedRBSP);break;case 8:r.nalUnitType="pic_parameter_set_rbsp";break;case 9:r.nalUnitType="access_unit_delimiter_rbsp"}e.trigger("data",r)}),f.on("done",function(){e.trigger("done")}),this.flush=function(){f.flush()},l=function(e,t){var i,n,r=8,a=8;for(i=0;i<e;i++)0!==a&&(n=t.readExpGolomb(),a=(r+n+256)%256),r=0===a?r:a},u=function(e){for(var t,i,n=e.byteLength,r=[],a=1;a<n-2;)0===e[a]&&0===e[a+1]&&3===e[a+2]?(r.push(a+2),a+=2):a++;if(0===r.length)return e;t=n-r.length,i=new Uint8Array(t);var s=0;for(a=0;a<t;s++,a++)s===r[0]&&(s++,r.shift()),i[a]=e[s];return i},d=function(e){var t,i,n,r,s,u,d,f,c,h,p,m,y,g,v=0,_=0,b=0,T=0,S=1;if(t=new o(e),i=t.readUnsignedByte(),r=t.readUnsignedByte(),n=t.readUnsignedByte(),t.skipUnsignedExpGolomb(),a[i]&&(s=t.readUnsignedExpGolomb(),3===s&&t.skipBits(1),t.skipUnsignedExpGolomb(),t.skipUnsignedExpGolomb(),t.skipBits(1),t.readBoolean()))for(p=3!==s?8:12,g=0;g<p;g++)t.readBoolean()&&(g<6?l(16,t):l(64,t));if(t.skipUnsignedExpGolomb(),u=t.readUnsignedExpGolomb(),0===u)t.readUnsignedExpGolomb();else if(1===u)for(t.skipBits(1),t.skipExpGolomb(),t.skipExpGolomb(),d=t.readUnsignedExpGolomb(),g=0;g<d;g++)t.skipExpGolomb();if(t.skipUnsignedExpGolomb(),t.skipBits(1),f=t.readUnsignedExpGolomb(),c=t.readUnsignedExpGolomb(),h=t.readBits(1),0===h&&t.skipBits(1),t.skipBits(1),t.readBoolean()&&(v=t.readUnsignedExpGolomb(),_=t.readUnsignedExpGolomb(),b=t.readUnsignedExpGolomb(),T=t.readUnsignedExpGolomb()),t.readBoolean()&&t.readBoolean()){switch(y=t.readUnsignedByte()){case 1:m=[1,1];break;case 2:m=[12,11];break;case 3:m=[10,11];break;case 4:m=[16,11];break;case 5:m=[40,33];break;case 6:m=[24,11];break;case 7:m=[20,11];break;case 8:m=[32,11];break;case 9:m=[80,33];break;case 10:m=[18,11];break;case 11:m=[15,11];break;case 12:m=[64,33];break;case 13:m=[160,99];break;case 14:m=[4,3];break;case 15:m=[3,2];break;case 16:m=[2,1];break;case 255:m=[t.readUnsignedByte()<<8|t.readUnsignedByte(),t.readUnsignedByte()<<8|t.readUnsignedByte()]}m&&(S=m[0]/m[1])}return{profileIdc:i,levelIdc:n,profileCompatibility:r,width:Math.ceil((16*(f+1)-2*v-2*_)*S),height:(2-h)*(c+1)*16-2*b-2*T}}},n.prototype=new s,t.exports={H264Stream:n,NalByteStream:r}},{"../utils/exp-golomb.js":74,"../utils/stream.js":75}],58:[function(e,t,i){var n=[33,16,5,32,164,27],r=[33,65,108,84,1,2,4,8,168,2,4,8,17,191,252],a=function(e){for(var t=[];e--;)t.push(0);return t},s=function(e){return Object.keys(e).reduce(function(t,i){return t[i]=new Uint8Array(e[i].reduce(function(e,t){return e.concat(t)},[])),t},{})},o={96e3:[n,[227,64],a(154),[56]],88200:[n,[231],a(170),[56]],64e3:[n,[248,192],a(240),[56]],48e3:[n,[255,192],a(268),[55,148,128],a(54),[112]],44100:[n,[255,192],a(268),[55,163,128],a(84),[112]],32e3:[n,[255,192],a(268),[55,234],a(226),[112]],24e3:[n,[255,192],a(268),[55,255,128],a(268),[111,112],a(126),[224]],16e3:[n,[255,192],a(268),[55,255,128],a(268),[111,255],a(269),[223,108],a(195),[1,192]],12e3:[r,a(268),[3,127,248],a(268),[6,255,240],a(268),[13,255,224],a(268),[27,253,128],a(259),[56]],11025:[r,a(268),[3,127,248],a(268),[6,255,240],a(268),[13,255,224],a(268),[27,255,192],a(268),[55,175,128],a(108),[112]],8e3:[r,a(268),[3,121,16],a(47),[7]]};t.exports=s(o)},{}],59:[function(e,t,i){"use strict";var n=e("../utils/stream.js"),r=function(e){this.numberOfTracks=0,this.metadataStream=e.metadataStream,this.videoTags=[],this.audioTags=[],this.videoTrack=null,this.audioTrack=null,this.pendingCaptions=[],this.pendingMetadata=[],this.pendingTracks=0,this.processedTracks=0,r.prototype.init.call(this),this.push=function(e){return e.text?this.pendingCaptions.push(e):e.frames?this.pendingMetadata.push(e):("video"===e.track.type&&(this.videoTrack=e.track,this.videoTags=e.tags,this.pendingTracks++),void("audio"===e.track.type&&(this.audioTrack=e.track,this.audioTags=e.tags,this.pendingTracks++)))}};r.prototype=new n,r.prototype.flush=function(e){var t,i,n,r,a={tags:{},captions:[],metadata:[]};if(this.pendingTracks<this.numberOfTracks){if("VideoSegmentStream"!==e&&"AudioSegmentStream"!==e)return;if(0===this.pendingTracks&&(this.processedTracks++,this.processedTracks<this.numberOfTracks))return}if(this.processedTracks+=this.pendingTracks,this.pendingTracks=0,!(this.processedTracks<this.numberOfTracks)){for(this.videoTrack?r=this.videoTrack.timelineStartInfo.pts:this.audioTrack&&(r=this.audioTrack.timelineStartInfo.pts),a.tags.videoTags=this.videoTags,a.tags.audioTags=this.audioTags,n=0;n<this.pendingCaptions.length;n++)i=this.pendingCaptions[n],i.startTime=i.startPts-r,i.startTime/=9e4,i.endTime=i.endPts-r,i.endTime/=9e4,a.captions.push(i);for(n=0;n<this.pendingMetadata.length;n++)t=this.pendingMetadata[n],t.cueTime=t.pts-r,t.cueTime/=9e4,a.metadata.push(t);a.metadata.dispatchType=this.metadataStream.dispatchType,this.videoTrack=null,this.audioTrack=null,this.videoTags=[],this.audioTags=[],this.pendingCaptions.length=0,this.pendingMetadata.length=0,this.pendingTracks=0,this.processedTracks=0,this.trigger("data",a),this.trigger("done")}},t.exports=r},{"../utils/stream.js":75}],60:[function(e,t,i){"use strict";var n=e("./flv-tag.js"),r=function(e,t,i){var r,a,s,o=new Uint8Array(9),u=new DataView(o.buffer);return e=e||0,t=void 0===t||t,i=void 0===i||i,u.setUint8(0,70),u.setUint8(1,76),u.setUint8(2,86),u.setUint8(3,1),u.setUint8(4,(t?4:0)|(i?1:0)),u.setUint32(5,o.byteLength),e<=0?(a=new Uint8Array(o.byteLength+4),a.set(o),a.set([0,0,0,0],o.byteLength),a):(r=new n(n.METADATA_TAG),r.pts=r.dts=0,r.writeMetaDataDouble("duration",e),s=r.finalize().length,a=new Uint8Array(o.byteLength+s),a.set(o),a.set(u.byteLength,s),a)};t.exports=r},{"./flv-tag.js":61}],61:[function(e,t,i){"use strict";var n;n=function(e,t){var i,r=0,a=16384,s=function(e,t){var i,n=e.position+t;n<e.bytes.byteLength||(i=new Uint8Array(2*n),i.set(e.bytes.subarray(0,e.position),0),e.bytes=i,e.view=new DataView(e.bytes.buffer))},o=n.widthBytes||new Uint8Array("width".length),u=n.heightBytes||new Uint8Array("height".length),d=n.videocodecidBytes||new Uint8Array("videocodecid".length);if(!n.widthBytes){for(i=0;i<"width".length;i++)o[i]="width".charCodeAt(i);for(i=0;i<"height".length;i++)u[i]="height".charCodeAt(i);for(i=0;i<"videocodecid".length;i++)d[i]="videocodecid".charCodeAt(i);n.widthBytes=o,n.heightBytes=u,n.videocodecidBytes=d}switch(this.keyFrame=!1,e){case n.VIDEO_TAG:this.length=16,a*=6;break;case n.AUDIO_TAG:this.length=13,this.keyFrame=!0;break;case n.METADATA_TAG:this.length=29,this.keyFrame=!0;break;default:throw new Error("Unknown FLV tag type")}this.bytes=new Uint8Array(a),this.view=new DataView(this.bytes.buffer),this.bytes[0]=e,this.position=this.length,this.keyFrame=t,this.pts=0,this.dts=0,this.writeBytes=function(e,t,i){var n,r=t||0;i=i||e.byteLength,n=r+i,s(this,i),this.bytes.set(e.subarray(r,n),this.position),this.position+=i,this.length=Math.max(this.length,this.position)},this.writeByte=function(e){s(this,1),this.bytes[this.position]=e,this.position++,this.length=Math.max(this.length,this.position)},this.writeShort=function(e){s(this,2),this.view.setUint16(this.position,e),this.position+=2,this.length=Math.max(this.length,this.position)},this.negIndex=function(e){return this.bytes[this.length-e]},this.nalUnitSize=function(){return 0===r?0:this.length-(r+4)},this.startNalUnit=function(){if(r>0)throw new Error("Attempted to create new NAL wihout closing the old one");r=this.length,this.length+=4,this.position=this.length},this.endNalUnit=function(e){var t,i;this.length===r+4?this.length-=4:r>0&&(t=r+4,i=this.length-t,this.position=r,this.view.setUint32(this.position,i),this.position=this.length,e&&e.push(this.bytes.subarray(t,t+i))),r=0},this.writeMetaDataDouble=function(e,t){var i;if(s(this,2+e.length+9),this.view.setUint16(this.position,e.length),this.position+=2,"width"===e)this.bytes.set(o,this.position),this.position+=5;else if("height"===e)this.bytes.set(u,this.position),this.position+=6;else if("videocodecid"===e)this.bytes.set(d,this.position),this.position+=12;else for(i=0;i<e.length;i++)this.bytes[this.position]=e.charCodeAt(i),this.position++;this.position++,this.view.setFloat64(this.position,t),this.position+=8,this.length=Math.max(this.length,this.position),++r},this.writeMetaDataBoolean=function(e,t){var i;for(s(this,2),this.view.setUint16(this.position,e.length),this.position+=2,i=0;i<e.length;i++)s(this,1),this.bytes[this.position]=e.charCodeAt(i),this.position++;s(this,2),this.view.setUint8(this.position,1),this.position++,this.view.setUint8(this.position,t?1:0),this.position++,this.length=Math.max(this.length,this.position),++r},this.finalize=function(){var e,i;switch(this.bytes[0]){case n.VIDEO_TAG:this.bytes[11]=7|(this.keyFrame||t?16:32),this.bytes[12]=t?0:1,e=this.pts-this.dts,this.bytes[13]=(16711680&e)>>>16,this.bytes[14]=(65280&e)>>>8,this.bytes[15]=(255&e)>>>0;break;case n.AUDIO_TAG:this.bytes[11]=175,this.bytes[12]=t?0:1;break;case n.METADATA_TAG:this.position=11,this.view.setUint8(this.position,2),this.position++,this.view.setUint16(this.position,10),this.position+=2,this.bytes.set([111,110,77,101,116,97,68,97,116,97],this.position),this.position+=10,this.bytes[this.position]=8,this.position++,this.view.setUint32(this.position,r),this.position=this.length,this.bytes.set([0,0,9],this.position),this.position+=3,this.length=this.position}return i=this.length-11,this.bytes[1]=(16711680&i)>>>16,this.bytes[2]=(65280&i)>>>8,this.bytes[3]=(255&i)>>>0,this.bytes[4]=(16711680&this.dts)>>>16,this.bytes[5]=(65280&this.dts)>>>8,this.bytes[6]=(255&this.dts)>>>0,this.bytes[7]=(4278190080&this.dts)>>>24,this.bytes[8]=0,this.bytes[9]=0,this.bytes[10]=0,s(this,4),this.view.setUint32(this.length,this.length),this.length+=4,this.position+=4,this.bytes=this.bytes.subarray(0,this.length),this.frameTime=n.frameTime(this.bytes),this}},n.AUDIO_TAG=8,n.VIDEO_TAG=9,n.METADATA_TAG=18,n.isAudioFrame=function(e){return n.AUDIO_TAG===e[0]},n.isVideoFrame=function(e){return n.VIDEO_TAG===e[0]},n.isMetaData=function(e){return n.METADATA_TAG===e[0]},n.isKeyFrame=function(e){return n.isVideoFrame(e)?23===e[11]:!!n.isAudioFrame(e)||!!n.isMetaData(e)},n.frameTime=function(e){var t=e[4]<<16;return t|=e[5]<<8,t|=e[6]<<0,t|=e[7]<<24},t.exports=n},{}],62:[function(e,t,i){t.exports={tag:e("./flv-tag"),Transmuxer:e("./transmuxer"),getFlvHeader:e("./flv-header")}},{"./flv-header":60,"./flv-tag":61,"./transmuxer":64}],63:[function(e,t,i){"use strict";var n=function(){var e=this;this.list=[],this.push=function(e){this.list.push({bytes:e.bytes,dts:e.dts,pts:e.pts})},Object.defineProperty(this,"length",{get:function(){return e.list.length}})};t.exports=n},{}],64:[function(e,t,i){"use strict";var n,r,a,s,o,u,d=e("../utils/stream.js"),l=e("./flv-tag.js"),f=e("../m2ts/m2ts.js"),c=e("../codecs/adts.js"),h=e("../codecs/h264").H264Stream,p=e("./coalesce-stream.js"),m=e("./tag-list.js");s=function(e,t){"number"==typeof t.pts&&(void 0===e.timelineStartInfo.pts?e.timelineStartInfo.pts=t.pts:e.timelineStartInfo.pts=Math.min(e.timelineStartInfo.pts,t.pts)),"number"==typeof t.dts&&(void 0===e.timelineStartInfo.dts?e.timelineStartInfo.dts=t.dts:e.timelineStartInfo.dts=Math.min(e.timelineStartInfo.dts,t.dts))},o=function(e,t){var i=new l(l.METADATA_TAG);return i.dts=t,i.pts=t,i.writeMetaDataDouble("videocodecid",7),i.writeMetaDataDouble("width",e.width),i.writeMetaDataDouble("height",e.height),i},u=function(e,t){var i,n=new l(l.VIDEO_TAG,(!0));for(n.dts=t,n.pts=t,n.writeByte(1),n.writeByte(e.profileIdc),n.writeByte(e.profileCompatibility),n.writeByte(e.levelIdc),n.writeByte(255),n.writeByte(225),n.writeShort(e.sps[0].length),n.writeBytes(e.sps[0]),n.writeByte(e.pps.length),i=0;i<e.pps.length;++i)n.writeShort(e.pps[i].length),n.writeBytes(e.pps[i]);return n},a=function(e){var t,i=[];a.prototype.init.call(this),this.push=function(t){s(e,t),e&&void 0===e.channelcount&&(e.audioobjecttype=t.audioobjecttype,e.channelcount=t.channelcount,e.samplerate=t.samplerate,e.samplingfrequencyindex=t.samplingfrequencyindex,e.samplesize=t.samplesize,e.extraData=e.audioobjecttype<<11|e.samplingfrequencyindex<<7|e.channelcount<<3),t.pts=Math.round(t.pts/90),t.dts=Math.round(t.dts/90),i.push(t)},this.flush=function(){var n,r,a,s=new m;if(0===i.length)return void this.trigger("done","AudioSegmentStream");for(a=-(1/0);i.length;)n=i.shift(),(e.extraData!==t||n.pts-a>=1e3)&&(r=new l(l.METADATA_TAG),r.pts=n.pts,r.dts=n.dts,r.writeMetaDataDouble("audiocodecid",10),r.writeMetaDataBoolean("stereo",2===e.channelcount),r.writeMetaDataDouble("audiosamplerate",e.samplerate),r.writeMetaDataDouble("audiosamplesize",16),s.push(r.finalize()),t=e.extraData,r=new l(l.AUDIO_TAG,(!0)),r.pts=n.pts,r.dts=n.dts,r.view.setUint16(r.position,e.extraData),r.position+=2,r.length=Math.max(r.length,r.position),s.push(r.finalize()),a=n.pts),r=new l(l.AUDIO_TAG),r.pts=n.pts,r.dts=n.dts,r.writeBytes(n.data),s.push(r.finalize());t=null,this.trigger("data",{track:e,tags:s.list}),this.trigger("done","AudioSegmentStream")}},a.prototype=new d,r=function(e){var t,i,n=[];r.prototype.init.call(this),this.finishFrame=function(n,r){r&&(t&&e&&e.newMetadata&&(r.keyFrame||0===n.length)&&(n.push(o(t,r.dts).finalize()),n.push(u(e,r.dts).finalize()),e.newMetadata=!1),r.endNalUnit(),n.push(r.finalize()),i=null)},this.push=function(t){s(e,t),t.pts=Math.round(t.pts/90),t.dts=Math.round(t.dts/90),n.push(t)},this.flush=function(){for(var r,a=new m;n.length&&"access_unit_delimiter_rbsp"!==n[0].nalUnitType;)n.shift();if(0===n.length)return void this.trigger("done","VideoSegmentStream");for(;n.length;)r=n.shift(),"seq_parameter_set_rbsp"===r.nalUnitType?(e.newMetadata=!0,t=r.config,e.width=t.width,e.height=t.height,e.sps=[r.data],e.profileIdc=t.profileIdc,e.levelIdc=t.levelIdc,e.profileCompatibility=t.profileCompatibility,i.endNalUnit()):"pic_parameter_set_rbsp"===r.nalUnitType?(e.newMetadata=!0,e.pps=[r.data],i.endNalUnit()):"access_unit_delimiter_rbsp"===r.nalUnitType?(i&&this.finishFrame(a,i),i=new l(l.VIDEO_TAG),i.pts=r.pts,i.dts=r.dts):("slice_layer_without_partitioning_rbsp_idr"===r.nalUnitType&&(i.keyFrame=!0),i.endNalUnit()),i.startNalUnit(),i.writeBytes(r.data);i&&this.finishFrame(a,i),this.trigger("data",{track:e,tags:a.list}),this.trigger("done","VideoSegmentStream")}},r.prototype=new d,n=function(e){var t,i,s,o,u,d,l,m,y,g,v,_,b=this;n.prototype.init.call(this),e=e||{},this.metadataStream=new f.MetadataStream,e.metadataStream=this.metadataStream,t=new f.TransportPacketStream,i=new f.TransportParseStream,s=new f.ElementaryStream,o=new f.TimestampRolloverStream("video"),u=new f.TimestampRolloverStream("audio"),d=new f.TimestampRolloverStream("timed-metadata"),l=new c,m=new h,_=new p(e),t.pipe(i).pipe(s),s.pipe(o).pipe(m),s.pipe(u).pipe(l),s.pipe(d).pipe(this.metadataStream).pipe(_),v=new f.CaptionStream,m.pipe(v).pipe(_),s.on("data",function(e){var t,i,n;if("metadata"===e.type){for(t=e.tracks.length;t--;)"video"===e.tracks[t].type?i=e.tracks[t]:"audio"===e.tracks[t].type&&(n=e.tracks[t]);i&&!y&&(_.numberOfTracks++,y=new r(i),m.pipe(y).pipe(_)),n&&!g&&(_.numberOfTracks++,g=new a(n),l.pipe(g).pipe(_))}}),this.push=function(e){t.push(e)},this.flush=function(){t.flush()},_.on("data",function(e){b.trigger("data",e)}),_.on("done",function(){b.trigger("done")})},n.prototype=new d,t.exports=n},{"../codecs/adts.js":56,"../codecs/h264":57,"../m2ts/m2ts.js":66,"../utils/stream.js":75,"./coalesce-stream.js":59,"./flv-tag.js":61,"./tag-list.js":63}],65:[function(e,t,i){"use strict";var n=4,r=128,a=e("../utils/stream"),s=function(e){for(var t=0,i={payloadType:-1,payloadSize:0},a=0,s=0;t<e.byteLength&&e[t]!==r;){for(;255===e[t];)a+=255,t++;for(a+=e[t++];255===e[t];)s+=255,t++;if(s+=e[t++],!i.payload&&a===n){i.payloadType=a,i.payloadSize=s,i.payload=e.subarray(t,t+s);break}t+=s,a=0,s=0}return i},o=function(e){return 181!==e.payload[0]?null:49!==(e.payload[1]<<8|e.payload[2])?null:"GA94"!==String.fromCharCode(e.payload[3],e.payload[4],e.payload[5],e.payload[6])?null:3!==e.payload[7]?null:e.payload.subarray(8,e.payload.length-1)},u=function(e,t){var i,n,r,a,s=[];if(!(64&t[0]))return s;for(n=31&t[0],i=0;i<n;i++)r=3*i,a={type:3&t[r+2],pts:e},4&t[r+2]&&(a.ccData=t[r+3]<<8|t[r+4],s.push(a));return s},d=function(){d.prototype.init.call(this),this.captionPackets_=[],this.field1_=new k,this.field1_.on("data",this.trigger.bind(this,"data")),this.field1_.on("done",this.trigger.bind(this,"done"))};d.prototype=new a,d.prototype.push=function(e){var t,i;"sei_rbsp"===e.nalUnitType&&(t=s(e.escapedRBSP),t.payloadType===n&&(i=o(t),i&&(this.captionPackets_=this.captionPackets_.concat(u(e.pts,i)))))},d.prototype.flush=function(){return this.captionPackets_.length?(this.captionPackets_.forEach(function(e,t){e.presortIndex=t}),this.captionPackets_.sort(function(e,t){return e.pts===t.pts?e.presortIndex-t.presortIndex:e.pts-t.pts}),this.captionPackets_.forEach(this.field1_.push,this.field1_),this.captionPackets_.length=0,void this.field1_.flush()):void this.field1_.flush()};var l={42:225,92:233,94:237,95:243,96:250,123:231,124:247,125:209,126:241,127:9608},f=function(e){return null===e?"":(e=l[e]||e,String.fromCharCode(e))},c=0,h=5152,p=5167,m=5157,y=5158,g=5159,v=5165,_=5153,b=5164,T=5166,S=14,w=function(){for(var e=[],t=S+1;t--;)e.push("");return e},k=function(){k.prototype.init.call(this),this.mode_="popOn",this.topRow_=0,this.startPts_=0,this.displayed_=w(),this.nonDisplayed_=w(),this.lastControlCode_=null,this.push=function(e){if(0===e.type){var t,i,n,r;if(t=32639&e.ccData,t===this.lastControlCode_)return void(this.lastControlCode_=null);switch(4096===(61440&t)?this.lastControlCode_=t:this.lastControlCode_=null,t){case c:break;case h:this.mode_="popOn";break;case p:this.flushDisplayed(e.pts),i=this.displayed_,this.displayed_=this.nonDisplayed_,this.nonDisplayed_=i,this.startPts_=e.pts;break;case m:this.topRow_=S-1,this.mode_="rollUp";break;case y:this.topRow_=S-2,this.mode_="rollUp";break;case g:this.topRow_=S-3,this.mode_="rollUp";break;case v:this.flushDisplayed(e.pts),this.shiftRowsUp_(),this.startPts_=e.pts;break;case _:"popOn"===this.mode_?this.nonDisplayed_[S]=this.nonDisplayed_[S].slice(0,-1):this.displayed_[S]=this.displayed_[S].slice(0,-1);break;case b:this.flushDisplayed(e.pts),this.displayed_=w();break;case T:this.nonDisplayed_=w();break;default:if(n=t>>>8,r=255&t,n>=16&&n<=23&&r>=64&&r<=127&&(16!==n||r<96)&&(n=32,r=null),(17===n||25===n)&&r>=48&&r<=63&&(n=9834,r=""),16===(240&n))return;0===n&&(n=null),0===r&&(r=null),this[this.mode_](e.pts,n,r)}}}};k.prototype=new a,k.prototype.flushDisplayed=function(e){var t=this.displayed_.map(function(e){return e.trim()}).filter(function(e){return e.length}).join("\n");t.length&&this.trigger("data",{startPts:this.startPts_,endPts:e,text:t})},k.prototype.popOn=function(e,t,i){var n=this.nonDisplayed_[S];n+=f(t),n+=f(i),this.nonDisplayed_[S]=n},k.prototype.rollUp=function(e,t,i){var n=this.displayed_[S];""===n&&(this.flushDisplayed(e),this.startPts_=e),n+=f(t),n+=f(i),this.displayed_[S]=n},k.prototype.shiftRowsUp_=function(){var e;for(e=0;e<this.topRow_;e++)this.displayed_[e]="";for(e=this.topRow_;e<S;e++)this.displayed_[e]=this.displayed_[e+1];this.displayed_[S]=""},t.exports={CaptionStream:d,Cea608Stream:k}},{"../utils/stream":75}],66:[function(e,t,i){"use strict";var n,r,a,s=e("../utils/stream.js"),o=e("./caption-stream"),u=e("./stream-types"),d=e("./timestamp-rollover-stream").TimestampRolloverStream,l=e("./stream-types.js"),f=188,c=71;n=function(){var e=new Uint8Array(f),t=0;n.prototype.init.call(this),this.push=function(i){var n,r=0,a=f;for(t?(n=new Uint8Array(i.byteLength+t),n.set(e.subarray(0,t)),n.set(i,t),t=0):n=i;a<n.byteLength;)n[r]!==c||n[a]!==c?(r++,a++):(this.trigger("data",n.subarray(r,a)),r+=f,a+=f);r<n.byteLength&&(e.set(n.subarray(r),0),t=n.byteLength-r)},this.flush=function(){t===f&&e[0]===c&&(this.trigger("data",e),t=0),this.trigger("done")}},n.prototype=new s,r=function(){var e,t,i,n;r.prototype.init.call(this),n=this,this.packetsWaitingForPmt=[],this.programMapTable=void 0,e=function(e,n){var r=0;n.payloadUnitStartIndicator&&(r+=e[r]+1),"pat"===n.type?t(e.subarray(r),n):i(e.subarray(r),n)},t=function(e,t){t.section_number=e[7],t.last_section_number=e[8],n.pmtPid=(31&e[10])<<8|e[11],t.pmtPid=n.pmtPid},i=function(e,t){var i,r,a,s;if(1&e[5]){for(n.programMapTable={},i=(15&e[1])<<8|e[2],r=3+i-4,a=(15&e[10])<<8|e[11],s=12+a;s<r;)n.programMapTable[(31&e[s+1])<<8|e[s+2]]=e[s],s+=((15&e[s+3])<<8|e[s+4])+5;for(t.programMapTable=n.programMapTable;n.packetsWaitingForPmt.length;)n.processPes_.apply(n,n.packetsWaitingForPmt.shift())}},this.push=function(t){var i={},n=4;i.payloadUnitStartIndicator=!!(64&t[1]),i.pid=31&t[1],i.pid<<=8,i.pid|=t[2],(48&t[3])>>>4>1&&(n+=t[n]+1),0===i.pid?(i.type="pat",e(t.subarray(n),i),this.trigger("data",i)):i.pid===this.pmtPid?(i.type="pmt",e(t.subarray(n),i),this.trigger("data",i)):void 0===this.programMapTable?this.packetsWaitingForPmt.push([t,n,i]):this.processPes_(t,n,i)},this.processPes_=function(e,t,i){i.streamType=this.programMapTable[i.pid],i.type="pes",i.data=e.subarray(t),this.trigger("data",i)}},r.prototype=new s,r.STREAM_TYPES={h264:27,adts:15},a=function(){var e=this,t={data:[],size:0},i={data:[],size:0},n={data:[],size:0},r=function(e,t){var i;t.dataAlignmentIndicator=0!==(4&e[6]),i=e[7],192&i&&(t.pts=(14&e[9])<<27|(255&e[10])<<20|(254&e[11])<<12|(255&e[12])<<5|(254&e[13])>>>3,t.pts*=4,t.pts+=(6&e[13])>>>1,t.dts=t.pts,64&i&&(t.dts=(14&e[14])<<27|(255&e[15])<<20|(254&e[16])<<12|(255&e[17])<<5|(254&e[18])>>>3,t.dts*=4,t.dts+=(6&e[18])>>>1)),t.data=e.subarray(9+e[8])},s=function(t,i){var n,a=new Uint8Array(t.size),s={type:i},o=0;if(t.data.length){for(s.trackId=t.data[0].pid;t.data.length;)n=t.data.shift(),a.set(n.data,o),o+=n.data.byteLength;r(a,s),t.size=0,e.trigger("data",s)}};a.prototype.init.call(this),this.push=function(r){({pat:function(){},pes:function(){var e,a;switch(r.streamType){case u.H264_STREAM_TYPE:case l.H264_STREAM_TYPE:e=t,a="video";break;case u.ADTS_STREAM_TYPE:e=i,a="audio";break;case u.METADATA_STREAM_TYPE:e=n,a="timed-metadata";break;default:return}r.payloadUnitStartIndicator&&s(e,a),e.data.push(r),e.size+=r.data.byteLength},pmt:function(){var t,i,n={type:"metadata",tracks:[]},a=r.programMapTable;for(t in a)a.hasOwnProperty(t)&&(i={timelineStartInfo:{baseMediaDecodeTime:0}},i.id=+t,a[t]===l.H264_STREAM_TYPE?(i.codec="avc",i.type="video"):a[t]===l.ADTS_STREAM_TYPE&&(i.codec="adts",i.type="audio"),n.tracks.push(i));e.trigger("data",n)}})[r.type]()},this.flush=function(){s(t,"video"),s(i,"audio"),s(n,"timed-metadata"),this.trigger("done")}},a.prototype=new s;var h={PAT_PID:0,MP2T_PACKET_LENGTH:f,TransportPacketStream:n,TransportParseStream:r,ElementaryStream:a,TimestampRolloverStream:d,CaptionStream:o.CaptionStream,Cea608Stream:o.Cea608Stream,MetadataStream:e("./metadata-stream")};for(var p in u)u.hasOwnProperty(p)&&(h[p]=u[p]);t.exports=h},{"../utils/stream.js":75,"./caption-stream":65,"./metadata-stream":67,"./stream-types":68,"./stream-types.js":68,"./timestamp-rollover-stream":69}],67:[function(e,t,i){"use strict";var n,r=e("../utils/stream"),a=e("./stream-types"),s=function(e,t,i){var n,r="";for(n=t;n<i;n++)r+="%"+("00"+e[n].toString(16)).slice(-2);return r},o=function(e,t,i){return decodeURIComponent(s(e,t,i))},u=function(e,t,i){return unescape(s(e,t,i))},d=function(e){return e[0]<<21|e[1]<<14|e[2]<<7|e[3]},l={TXXX:function(e){var t;if(3===e.data[0]){for(t=1;t<e.data.length;t++)if(0===e.data[t]){e.description=o(e.data,1,t),e.value=o(e.data,t+1,e.data.length-1);break}e.data=e.value}},WXXX:function(e){var t;if(3===e.data[0])for(t=1;t<e.data.length;t++)if(0===e.data[t]){e.description=o(e.data,1,t),e.url=o(e.data,t+1,e.data.length);break}},PRIV:function(e){var t;for(t=0;t<e.data.length;t++)if(0===e.data[t]){e.owner=u(e.data,0,t);break}e.privateData=e.data.subarray(t+1),e.data=e.privateData}};n=function(e){var t,i={debug:!(!e||!e.debug),descriptor:e&&e.descriptor},r=0,s=[],o=0;if(n.prototype.init.call(this),this.dispatchType=a.METADATA_STREAM_TYPE.toString(16),i.descriptor)for(t=0;t<i.descriptor.length;t++)this.dispatchType+=("00"+i.descriptor[t].toString(16)).slice(-2);this.push=function(e){var t,n,a,u,f,c;if("timed-metadata"===e.type){if(e.dataAlignmentIndicator&&(o=0,s.length=0),0===s.length&&(e.data.length<10||e.data[0]!=="I".charCodeAt(0)||e.data[1]!=="D".charCodeAt(0)||e.data[2]!=="3".charCodeAt(0)))return void(i.debug&&console.log("Skipping unrecognized metadata packet"));if(s.push(e),o+=e.data.byteLength,1===s.length&&(r=d(e.data.subarray(6,10)),r+=10),!(o<r)){for(t={data:new Uint8Array(r),frames:[],pts:s[0].pts,dts:s[0].dts},f=0;f<r;)t.data.set(s[0].data.subarray(0,r-f),f),f+=s[0].data.byteLength,o-=s[0].data.byteLength,s.shift();n=10,64&t.data[5]&&(n+=4,n+=d(t.data.subarray(10,14)),r-=d(t.data.subarray(16,20)));do{if(a=d(t.data.subarray(n+4,n+8)),a<1)return console.log("Malformed ID3 frame encountered. Skipping metadata parsing.");if(c=String.fromCharCode(t.data[n],t.data[n+1],t.data[n+2],t.data[n+3]),u={id:c,data:t.data.subarray(n+10,n+a+10)},u.key=u.id,l[u.id]&&(l[u.id](u),"com.apple.streaming.transportStreamTimestamp"===u.owner)){var h=u.data,p=(1&h[3])<<30|h[4]<<22|h[5]<<14|h[6]<<6|h[7]>>>2;p*=4,p+=3&h[7],u.timeStamp=p,void 0===t.pts&&void 0===t.dts&&(t.pts=u.timeStamp,t.dts=u.timeStamp),this.trigger("timestamp",u)}t.frames.push(u),n+=10,n+=a}while(n<r);this.trigger("data",t)}}}},n.prototype=new r,t.exports=n},{"../utils/stream":75,"./stream-types":68}],68:[function(e,t,i){arguments[4][36][0].apply(i,arguments)},{dup:36}],69:[function(e,t,i){arguments[4][37][0].apply(i,arguments)},{"../utils/stream":75,dup:37}],70:[function(e,t,i){t.exports={generator:e("./mp4-generator"),Transmuxer:e("./transmuxer").Transmuxer,AudioSegmentStream:e("./transmuxer").AudioSegmentStream,VideoSegmentStream:e("./transmuxer").VideoSegmentStream}},{"./mp4-generator":71,"./transmuxer":72}],71:[function(e,t,i){"use strict";var n,r,a,s,o,u,d,l,f,c,h,p,m,y,g,v,_,b,T,S,w,k,O,P,A,E,x,L,I,C,U,D,M,j,B,R,N=Math.pow(2,32)-1;!function(){var e;if(O={avc1:[],avcC:[],btrt:[],dinf:[],dref:[],esds:[],ftyp:[],hdlr:[],mdat:[],mdhd:[],mdia:[],mfhd:[],minf:[],moof:[],moov:[],mp4a:[],mvex:[],mvhd:[],sdtp:[],smhd:[],stbl:[],stco:[],stsc:[],stsd:[],stsz:[],stts:[],styp:[],tfdt:[],tfhd:[],traf:[],trak:[],trun:[],trex:[],tkhd:[],vmhd:[]},"undefined"!=typeof Uint8Array){for(e in O)O.hasOwnProperty(e)&&(O[e]=[e.charCodeAt(0),e.charCodeAt(1),e.charCodeAt(2),e.charCodeAt(3)]);P=new Uint8Array(["i".charCodeAt(0),"s".charCodeAt(0),"o".charCodeAt(0),"m".charCodeAt(0)]),E=new Uint8Array(["a".charCodeAt(0),"v".charCodeAt(0),"c".charCodeAt(0),"1".charCodeAt(0)]),A=new Uint8Array([0,0,0,1]),x=new Uint8Array([0,0,0,0,0,0,0,0,118,105,100,101,0,0,0,0,0,0,0,0,0,0,0,0,86,105,100,101,111,72,97,110,100,108,101,114,0]),L=new Uint8Array([0,0,0,0,0,0,0,0,115,111,117,110,0,0,0,0,0,0,0,0,0,0,0,0,83,111,117,110,100,72,97,110,100,108,101,114,0]),I={video:x,audio:L},D=new Uint8Array([0,0,0,0,0,0,0,1,0,0,0,12,117,114,108,32,0,0,0,1]),U=new Uint8Array([0,0,0,0,0,0,0,0]),M=new Uint8Array([0,0,0,0,0,0,0,0]),j=M,B=new Uint8Array([0,0,0,0,0,0,0,0,0,0,0,0]),R=M,C=new Uint8Array([0,0,0,1,0,0,0,0,0,0,0,0])}}(),n=function(e){var t,i,n,r=[],a=0;for(t=1;t<arguments.length;t++)r.push(arguments[t]);for(t=r.length;t--;)a+=r[t].byteLength;for(i=new Uint8Array(a+8),n=new DataView(i.buffer,i.byteOffset,i.byteLength),n.setUint32(0,i.byteLength),i.set(e,4),t=0,a=8;t<r.length;t++)i.set(r[t],a),a+=r[t].byteLength;return i},r=function(){return n(O.dinf,n(O.dref,D))},a=function(e){return n(O.esds,new Uint8Array([0,0,0,0,3,25,0,0,0,4,17,64,21,0,6,0,0,0,218,192,0,0,218,192,5,2,e.audioobjecttype<<3|e.samplingfrequencyindex>>>1,e.samplingfrequencyindex<<7|e.channelcount<<3,6,1,2]))},s=function(){return n(O.ftyp,P,A,P,E)},v=function(e){return n(O.hdlr,I[e])},o=function(e){return n(O.mdat,e);
},g=function(e){var t=new Uint8Array([0,0,0,0,0,0,0,2,0,0,0,3,0,1,95,144,e.duration>>>24&255,e.duration>>>16&255,e.duration>>>8&255,255&e.duration,85,196,0,0]);return e.samplerate&&(t[12]=e.samplerate>>>24&255,t[13]=e.samplerate>>>16&255,t[14]=e.samplerate>>>8&255,t[15]=255&e.samplerate),n(O.mdhd,t)},y=function(e){return n(O.mdia,g(e),v(e.type),d(e))},u=function(e){return n(O.mfhd,new Uint8Array([0,0,0,0,(4278190080&e)>>24,(16711680&e)>>16,(65280&e)>>8,255&e]))},d=function(e){return n(O.minf,"video"===e.type?n(O.vmhd,C):n(O.smhd,U),r(),b(e))},l=function(e,t){for(var i=[],r=t.length;r--;)i[r]=S(t[r]);return n.apply(null,[O.moof,u(e)].concat(i))},f=function(e){for(var t=e.length,i=[];t--;)i[t]=p(e[t]);return n.apply(null,[O.moov,h(4294967295)].concat(i).concat(c(e)))},c=function(e){for(var t=e.length,i=[];t--;)i[t]=w(e[t]);return n.apply(null,[O.mvex].concat(i))},h=function(e){var t=new Uint8Array([0,0,0,0,0,0,0,1,0,0,0,2,0,1,95,144,(4278190080&e)>>24,(16711680&e)>>16,(65280&e)>>8,255&e,0,1,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,64,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,255,255,255,255]);return n(O.mvhd,t)},_=function(e){var t,i,r=e.samples||[],a=new Uint8Array(4+r.length);for(i=0;i<r.length;i++)t=r[i].flags,a[i+4]=t.dependsOn<<4|t.isDependedOn<<2|t.hasRedundancy;return n(O.sdtp,a)},b=function(e){return n(O.stbl,T(e),n(O.stts,R),n(O.stsc,j),n(O.stsz,B),n(O.stco,M))},function(){var e,t;T=function(i){return n(O.stsd,new Uint8Array([0,0,0,0,0,0,0,1]),"video"===i.type?e(i):t(i))},e=function(e){var t,i=e.sps||[],r=e.pps||[],a=[],s=[];for(t=0;t<i.length;t++)a.push((65280&i[t].byteLength)>>>8),a.push(255&i[t].byteLength),a=a.concat(Array.prototype.slice.call(i[t]));for(t=0;t<r.length;t++)s.push((65280&r[t].byteLength)>>>8),s.push(255&r[t].byteLength),s=s.concat(Array.prototype.slice.call(r[t]));return n(O.avc1,new Uint8Array([0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,(65280&e.width)>>8,255&e.width,(65280&e.height)>>8,255&e.height,0,72,0,0,0,72,0,0,0,0,0,0,0,1,19,118,105,100,101,111,106,115,45,99,111,110,116,114,105,98,45,104,108,115,0,0,0,0,0,0,0,0,0,0,0,0,0,24,17,17]),n(O.avcC,new Uint8Array([1,e.profileIdc,e.profileCompatibility,e.levelIdc,255].concat([i.length]).concat(a).concat([r.length]).concat(s))),n(O.btrt,new Uint8Array([0,28,156,128,0,45,198,192,0,45,198,192])))},t=function(e){return n(O.mp4a,new Uint8Array([0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,(65280&e.channelcount)>>8,255&e.channelcount,(65280&e.samplesize)>>8,255&e.samplesize,0,0,0,0,(65280&e.samplerate)>>8,255&e.samplerate,0,0]),a(e))}}(),m=function(e){var t=new Uint8Array([0,0,0,7,0,0,0,0,0,0,0,0,(4278190080&e.id)>>24,(16711680&e.id)>>16,(65280&e.id)>>8,255&e.id,0,0,0,0,(4278190080&e.duration)>>24,(16711680&e.duration)>>16,(65280&e.duration)>>8,255&e.duration,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,64,0,0,0,(65280&e.width)>>8,255&e.width,0,0,(65280&e.height)>>8,255&e.height,0,0]);return n(O.tkhd,t)},S=function(e){var t,i,r,a,s,o,u;return t=n(O.tfhd,new Uint8Array([0,0,0,58,(4278190080&e.id)>>24,(16711680&e.id)>>16,(65280&e.id)>>8,255&e.id,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0])),o=Math.floor(e.baseMediaDecodeTime/(N+1)),u=Math.floor(e.baseMediaDecodeTime%(N+1)),i=n(O.tfdt,new Uint8Array([1,0,0,0,o>>>24&255,o>>>16&255,o>>>8&255,255&o,u>>>24&255,u>>>16&255,u>>>8&255,255&u])),s=92,"audio"===e.type?(r=k(e,s),n(O.traf,t,i,r)):(a=_(e),r=k(e,a.length+s),n(O.traf,t,i,r,a))},p=function(e){return e.duration=e.duration||4294967295,n(O.trak,m(e),y(e))},w=function(e){var t=new Uint8Array([0,0,0,0,(4278190080&e.id)>>24,(16711680&e.id)>>16,(65280&e.id)>>8,255&e.id,0,0,0,1,0,0,0,0,0,0,0,0,0,1,0,1]);return"video"!==e.type&&(t[t.length-1]=0),n(O.trex,t)},function(){var e,t,i;i=function(e,t){var i=0,n=0,r=0,a=0;return e.length&&(void 0!==e[0].duration&&(i=1),void 0!==e[0].size&&(n=2),void 0!==e[0].flags&&(r=4),void 0!==e[0].compositionTimeOffset&&(a=8)),[0,0,i|n|r|a,1,(4278190080&e.length)>>>24,(16711680&e.length)>>>16,(65280&e.length)>>>8,255&e.length,(4278190080&t)>>>24,(16711680&t)>>>16,(65280&t)>>>8,255&t]},t=function(e,t){var r,a,s,o;for(a=e.samples||[],t+=20+16*a.length,r=i(a,t),o=0;o<a.length;o++)s=a[o],r=r.concat([(4278190080&s.duration)>>>24,(16711680&s.duration)>>>16,(65280&s.duration)>>>8,255&s.duration,(4278190080&s.size)>>>24,(16711680&s.size)>>>16,(65280&s.size)>>>8,255&s.size,s.flags.isLeading<<2|s.flags.dependsOn,s.flags.isDependedOn<<6|s.flags.hasRedundancy<<4|s.flags.paddingValue<<1|s.flags.isNonSyncSample,61440&s.flags.degradationPriority,15&s.flags.degradationPriority,(4278190080&s.compositionTimeOffset)>>>24,(16711680&s.compositionTimeOffset)>>>16,(65280&s.compositionTimeOffset)>>>8,255&s.compositionTimeOffset]);return n(O.trun,new Uint8Array(r))},e=function(e,t){var r,a,s,o;for(a=e.samples||[],t+=20+8*a.length,r=i(a,t),o=0;o<a.length;o++)s=a[o],r=r.concat([(4278190080&s.duration)>>>24,(16711680&s.duration)>>>16,(65280&s.duration)>>>8,255&s.duration,(4278190080&s.size)>>>24,(16711680&s.size)>>>16,(65280&s.size)>>>8,255&s.size]);return n(O.trun,new Uint8Array(r))},k=function(i,n){return"audio"===i.type?e(i,n):t(i,n)}}(),t.exports={ftyp:s,mdat:o,moof:l,moov:f,initSegment:function(e){var t,i=s(),n=f(e);return t=new Uint8Array(i.byteLength+n.byteLength),t.set(i),t.set(n,i.byteLength),t}}},{}],72:[function(e,t,i){"use strict";var n,r,a,s,o,u,d,l,f,c,h,p=e("../utils/stream.js"),m=e("./mp4-generator.js"),y=e("../m2ts/m2ts.js"),g=e("../codecs/adts.js"),v=e("../codecs/h264").H264Stream,_=e("../aac"),b=e("../data/silence"),T=e("../utils/clock"),S=["audioobjecttype","channelcount","samplerate","samplingfrequencyindex","samplesize"],w=["width","height","profileIdc","levelIdc","profileCompatibility"],k=9e4;o=function(){return{size:0,flags:{isLeading:0,dependsOn:1,isDependedOn:0,hasRedundancy:0,degradationPriority:0}}},u=function(e){return e[0]==="I".charCodeAt(0)&&e[1]==="D".charCodeAt(0)&&e[2]==="3".charCodeAt(0)},c=function(e,t){var i;if(e.length!==t.length)return!1;for(i=0;i<e.length;i++)if(e[i]!==t[i])return!1;return!0},h=function(e){var t,i,n=0;for(t=0;t<e.length;t++)i=e[t],n+=i.data.byteLength;return n},r=function(e){var t=[],i=0,n=0,a=0,s=1/0;r.prototype.init.call(this),this.push=function(i){d(e,i),e&&S.forEach(function(t){e[t]=i[t]}),t.push(i)},this.setEarliestDts=function(t){n=t-e.timelineStartInfo.baseMediaDecodeTime},this.setVideoBaseMediaDecodeTime=function(e){s=e},this.setAudioAppendStart=function(e){a=e},this.flush=function(){var n,r,a,s;return 0===t.length?void this.trigger("done","AudioSegmentStream"):(n=this.trimAdtsFramesByEarliestDts_(t),e.baseMediaDecodeTime=f(e),this.prefixWithSilence_(e,n),e.samples=this.generateSampleTable_(n),a=m.mdat(this.concatenateFrameData_(n)),t=[],r=m.moof(i,[e]),s=new Uint8Array(r.byteLength+a.byteLength),i++,s.set(r),s.set(a,r.byteLength),l(e),this.trigger("data",{track:e,boxes:s}),void this.trigger("done","AudioSegmentStream"))},this.prefixWithSilence_=function(e,t){var i,n,r,o=0,u=0,d=0,l=0;if(t.length&&(i=T.audioTsToVideoTs(e.baseMediaDecodeTime,e.samplerate),a&&s&&a<i&&(u=i-s,o=Math.ceil(k/(e.samplerate/1024)),d=Math.floor(u/o),l=d*o),!(d<1||l>k/2))){for(n=b[e.samplerate],n||(n=t[0].data),r=0;r<d;r++)t.splice(r,0,{data:n});e.baseMediaDecodeTime-=Math.floor(T.videoTsToAudioTs(l,e.samplerate))}},this.trimAdtsFramesByEarliestDts_=function(t){return e.minSegmentDts>=n?t:(e.minSegmentDts=1/0,t.filter(function(t){return t.dts>=n&&(e.minSegmentDts=Math.min(e.minSegmentDts,t.dts),e.minSegmentPts=e.minSegmentDts,!0)}))},this.generateSampleTable_=function(e){var t,i,n=[];for(t=0;t<e.length;t++)i=e[t],n.push({size:i.data.byteLength,duration:1024});return n},this.concatenateFrameData_=function(e){var t,i,n=0,r=new Uint8Array(h(e));for(t=0;t<e.length;t++)i=e[t],r.set(i.data,n),n+=i.data.byteLength;return r}},r.prototype=new p,n=function(e){var t,i,r=0,a=[];n.prototype.init.call(this),delete e.minPTS,this.gopCache_=[],this.push=function(n){d(e,n),"seq_parameter_set_rbsp"!==n.nalUnitType||t||(t=n.config,e.sps=[n.data],w.forEach(function(i){e[i]=t[i]},this)),"pic_parameter_set_rbsp"!==n.nalUnitType||i||(i=n.data,e.pps=[n.data]),a.push(n)},this.flush=function(){for(var t,i,n,s,o,u;a.length&&"access_unit_delimiter_rbsp"!==a[0].nalUnitType;)a.shift();return 0===a.length?(this.resetStream_(),void this.trigger("done","VideoSegmentStream")):(t=this.groupNalsIntoFrames_(a),n=this.groupFramesIntoGops_(t),n[0][0].keyFrame||(i=this.getGopForFusion_(a[0],e),i?(n.unshift(i),n.byteLength+=i.byteLength,n.nalCount+=i.nalCount,n.pts=i.pts,n.dts=i.dts,n.duration+=i.duration):n=this.extendFirstKeyFrame_(n)),d(e,n),e.samples=this.generateSampleTable_(n),o=m.mdat(this.concatenateNalData_(n)),this.gopCache_.unshift({gop:n.pop(),pps:e.pps,sps:e.sps}),this.gopCache_.length=Math.min(6,this.gopCache_.length),a=[],e.baseMediaDecodeTime=f(e),this.trigger("baseMediaDecodeTime",e.baseMediaDecodeTime),this.trigger("timelineStartInfo",e.timelineStartInfo),s=m.moof(r,[e]),u=new Uint8Array(s.byteLength+o.byteLength),r++,u.set(s),u.set(o,s.byteLength),this.trigger("data",{track:e,boxes:u}),this.resetStream_(),void this.trigger("done","VideoSegmentStream"))},this.resetStream_=function(){l(e),t=void 0,i=void 0},this.getGopForFusion_=function(t){var i,n,r,a,s,o=45e3,u=1e4,d=1/0;for(s=0;s<this.gopCache_.length;s++)a=this.gopCache_[s],r=a.gop,e.pps&&c(e.pps[0],a.pps[0])&&e.sps&&c(e.sps[0],a.sps[0])&&(r.dts<e.timelineStartInfo.dts||(i=t.dts-r.dts-r.duration,i>=-u&&i<=o&&(!n||d>i)&&(n=a,d=i)));return n?n.gop:null},this.extendFirstKeyFrame_=function(e){var t;return e[0][0].keyFrame||(t=e.shift(),e.byteLength-=t.byteLength,e.nalCount-=t.nalCount,e[0][0].dts=t.dts,e[0][0].pts=t.pts,e[0][0].duration+=t.duration),e},this.groupNalsIntoFrames_=function(e){var t,i,n=[],r=[];for(n.byteLength=0,t=0;t<e.length;t++)i=e[t],"access_unit_delimiter_rbsp"===i.nalUnitType?(n.length&&(n.duration=i.dts-n.dts,r.push(n)),n=[i],n.byteLength=i.data.byteLength,n.pts=i.pts,n.dts=i.dts):("slice_layer_without_partitioning_rbsp_idr"===i.nalUnitType&&(n.keyFrame=!0),n.duration=i.dts-n.dts,n.byteLength+=i.data.byteLength,n.push(i));return r.length&&(!n.duration||n.duration<=0)&&(n.duration=r[r.length-1].duration),r.push(n),r},this.groupFramesIntoGops_=function(e){var t,i,n=[],r=[];for(n.byteLength=0,n.nalCount=0,n.duration=0,n.pts=e[0].pts,n.dts=e[0].dts,r.byteLength=0,r.nalCount=0,r.duration=0,r.pts=e[0].pts,r.dts=e[0].dts,t=0;t<e.length;t++)i=e[t],i.keyFrame?(n.length&&(r.push(n),r.byteLength+=n.byteLength,r.nalCount+=n.nalCount,r.duration+=n.duration),n=[i],n.nalCount=i.length,n.byteLength=i.byteLength,n.pts=i.pts,n.dts=i.dts,n.duration=i.duration):(n.duration+=i.duration,n.nalCount+=i.length,n.byteLength+=i.byteLength,n.push(i));return r.length&&n.duration<=0&&(n.duration=r[r.length-1].duration),r.byteLength+=n.byteLength,r.nalCount+=n.nalCount,r.duration+=n.duration,r.push(n),r},this.generateSampleTable_=function(e,t){var i,n,r,a,s,u=t||0,d=[];for(i=0;i<e.length;i++)for(a=e[i],n=0;n<a.length;n++)s=a[n],r=o(),r.dataOffset=u,r.compositionTimeOffset=s.pts-s.dts,r.duration=s.duration,r.size=4*s.length,r.size+=s.byteLength,s.keyFrame&&(r.flags.dependsOn=2),u+=r.size,d.push(r);return d},this.concatenateNalData_=function(e){var t,i,n,r,a,s,o=0,u=e.byteLength,d=e.nalCount,l=u+4*d,f=new Uint8Array(l),c=new DataView(f.buffer);for(t=0;t<e.length;t++)for(r=e[t],i=0;i<r.length;i++)for(a=r[i],n=0;n<a.length;n++)s=a[n],c.setUint32(o,s.data.byteLength),o+=4,f.set(s.data,o),o+=s.data.byteLength;return f}},n.prototype=new p,d=function(e,t){"number"==typeof t.pts&&(void 0===e.timelineStartInfo.pts&&(e.timelineStartInfo.pts=t.pts),void 0===e.minSegmentPts?e.minSegmentPts=t.pts:e.minSegmentPts=Math.min(e.minSegmentPts,t.pts),void 0===e.maxSegmentPts?e.maxSegmentPts=t.pts:e.maxSegmentPts=Math.max(e.maxSegmentPts,t.pts)),"number"==typeof t.dts&&(void 0===e.timelineStartInfo.dts&&(e.timelineStartInfo.dts=t.dts),void 0===e.minSegmentDts?e.minSegmentDts=t.dts:e.minSegmentDts=Math.min(e.minSegmentDts,t.dts),void 0===e.maxSegmentDts?e.maxSegmentDts=t.dts:e.maxSegmentDts=Math.max(e.maxSegmentDts,t.dts))},l=function(e){delete e.minSegmentDts,delete e.maxSegmentDts,delete e.minSegmentPts,delete e.maxSegmentPts},f=function(e){var t,i,n=e.minSegmentDts-e.timelineStartInfo.dts;return t=e.timelineStartInfo.baseMediaDecodeTime,t+=n,t=Math.max(0,t),"audio"===e.type&&(i=e.samplerate/k,t*=i,t=Math.floor(t)),t},s=function(e,t){this.numberOfTracks=0,this.metadataStream=t,"undefined"!=typeof e.remux?this.remuxTracks=!!e.remux:this.remuxTracks=!0,this.pendingTracks=[],this.videoTrack=null,this.pendingBoxes=[],this.pendingCaptions=[],this.pendingMetadata=[],this.pendingBytes=0,this.emittedTracks=0,s.prototype.init.call(this),this.push=function(e){return e.text?this.pendingCaptions.push(e):e.frames?this.pendingMetadata.push(e):(this.pendingTracks.push(e.track),this.pendingBoxes.push(e.boxes),this.pendingBytes+=e.boxes.byteLength,"video"===e.track.type&&(this.videoTrack=e.track),void("audio"===e.track.type&&(this.audioTrack=e.track)))}},s.prototype=new p,s.prototype.flush=function(e){var t,i,n,r,a=0,s={captions:[],metadata:[],info:{}},o=0;if(this.pendingTracks.length<this.numberOfTracks){if("VideoSegmentStream"!==e&&"AudioSegmentStream"!==e)return;if(this.remuxTracks)return;if(0===this.pendingTracks.length)return this.emittedTracks++,void(this.emittedTracks>=this.numberOfTracks&&(this.trigger("done"),this.emittedTracks=0))}for(this.videoTrack?(o=this.videoTrack.timelineStartInfo.pts,w.forEach(function(e){s.info[e]=this.videoTrack[e]},this)):this.audioTrack&&(o=this.audioTrack.timelineStartInfo.pts,S.forEach(function(e){s.info[e]=this.audioTrack[e]},this)),1===this.pendingTracks.length?s.type=this.pendingTracks[0].type:s.type="combined",this.emittedTracks+=this.pendingTracks.length,n=m.initSegment(this.pendingTracks),s.initSegment=new Uint8Array(n.byteLength),s.initSegment.set(n),s.data=new Uint8Array(this.pendingBytes),r=0;r<this.pendingBoxes.length;r++)s.data.set(this.pendingBoxes[r],a),a+=this.pendingBoxes[r].byteLength;for(r=0;r<this.pendingCaptions.length;r++)t=this.pendingCaptions[r],t.startTime=t.startPts-o,t.startTime/=9e4,t.endTime=t.endPts-o,t.endTime/=9e4,s.captions.push(t);for(r=0;r<this.pendingMetadata.length;r++)i=this.pendingMetadata[r],i.cueTime=i.pts-o,i.cueTime/=9e4,s.metadata.push(i);s.metadata.dispatchType=this.metadataStream.dispatchType,this.pendingTracks.length=0,this.videoTrack=null,this.pendingBoxes.length=0,this.pendingCaptions.length=0,this.pendingBytes=0,this.pendingMetadata.length=0,this.trigger("data",s),this.emittedTracks>=this.numberOfTracks&&(this.trigger("done"),this.emittedTracks=0)},a=function(e){var t,i,o=this,d=!0;a.prototype.init.call(this),e=e||{},this.baseMediaDecodeTime=e.baseMediaDecodeTime||0,this.transmuxPipeline_={},this.setupAacPipeline=function(){var t={};this.transmuxPipeline_=t,t.type="aac",t.metadataStream=new y.MetadataStream,t.aacStream=new _,t.audioTimestampRolloverStream=new y.TimestampRolloverStream("audio"),t.timedMetadataTimestampRolloverStream=new y.TimestampRolloverStream("timed-metadata"),t.adtsStream=new g,t.coalesceStream=new s(e,t.metadataStream),t.headOfPipeline=t.aacStream,t.aacStream.pipe(t.audioTimestampRolloverStream).pipe(t.adtsStream),t.aacStream.pipe(t.timedMetadataTimestampRolloverStream).pipe(t.metadataStream).pipe(t.coalesceStream),t.metadataStream.on("timestamp",function(e){t.aacStream.setTimestamp(e.timeStamp)}),t.aacStream.on("data",function(e){"timed-metadata"!==e.type||t.audioSegmentStream||(i=i||{timelineStartInfo:{baseMediaDecodeTime:o.baseMediaDecodeTime},codec:"adts",type:"audio"},t.coalesceStream.numberOfTracks++,t.audioSegmentStream=new r(i),t.adtsStream.pipe(t.audioSegmentStream).pipe(t.coalesceStream))}),t.coalesceStream.on("data",this.trigger.bind(this,"data")),t.coalesceStream.on("done",this.trigger.bind(this,"done"))},this.setupTsPipeline=function(){var a={};this.transmuxPipeline_=a,a.type="ts",a.metadataStream=new y.MetadataStream,a.packetStream=new y.TransportPacketStream,a.parseStream=new y.TransportParseStream,a.elementaryStream=new y.ElementaryStream,a.videoTimestampRolloverStream=new y.TimestampRolloverStream("video"),a.audioTimestampRolloverStream=new y.TimestampRolloverStream("audio"),a.timedMetadataTimestampRolloverStream=new y.TimestampRolloverStream("timed-metadata"),a.adtsStream=new g,a.h264Stream=new v,a.captionStream=new y.CaptionStream,a.coalesceStream=new s(e,a.metadataStream),a.headOfPipeline=a.packetStream,a.packetStream.pipe(a.parseStream).pipe(a.elementaryStream),a.elementaryStream.pipe(a.videoTimestampRolloverStream).pipe(a.h264Stream),a.elementaryStream.pipe(a.audioTimestampRolloverStream).pipe(a.adtsStream),a.elementaryStream.pipe(a.timedMetadataTimestampRolloverStream).pipe(a.metadataStream).pipe(a.coalesceStream),a.h264Stream.pipe(a.captionStream).pipe(a.coalesceStream),a.elementaryStream.on("data",function(e){var s;if("metadata"===e.type){for(s=e.tracks.length;s--;)t||"video"!==e.tracks[s].type?i||"audio"!==e.tracks[s].type||(i=e.tracks[s],i.timelineStartInfo.baseMediaDecodeTime=o.baseMediaDecodeTime):(t=e.tracks[s],t.timelineStartInfo.baseMediaDecodeTime=o.baseMediaDecodeTime);t&&!a.videoSegmentStream&&(a.coalesceStream.numberOfTracks++,a.videoSegmentStream=new n(t),a.videoSegmentStream.on("timelineStartInfo",function(e){i&&(i.timelineStartInfo=e,a.audioSegmentStream.setEarliestDts(e.dts))}),a.videoSegmentStream.on("baseMediaDecodeTime",function(e){i&&a.audioSegmentStream.setVideoBaseMediaDecodeTime(e)}),a.h264Stream.pipe(a.videoSegmentStream).pipe(a.coalesceStream)),i&&!a.audioSegmentStream&&(a.coalesceStream.numberOfTracks++,a.audioSegmentStream=new r(i),a.adtsStream.pipe(a.audioSegmentStream).pipe(a.coalesceStream))}}),a.coalesceStream.on("data",this.trigger.bind(this,"data")),a.coalesceStream.on("done",this.trigger.bind(this,"done"))},this.setBaseMediaDecodeTime=function(e){var n=this.transmuxPipeline_;this.baseMediaDecodeTime=e,i&&(i.timelineStartInfo.dts=void 0,i.timelineStartInfo.pts=void 0,l(i),i.timelineStartInfo.baseMediaDecodeTime=e),t&&(n.videoSegmentStream&&(n.videoSegmentStream.gopCache_=[]),t.timelineStartInfo.dts=void 0,t.timelineStartInfo.pts=void 0,l(t),t.timelineStartInfo.baseMediaDecodeTime=e)},this.setAudioAppendStart=function(e){i&&this.transmuxPipeline_.audioSegmentStream.setAudioAppendStart(e)},this.push=function(e){if(d){var t=u(e);t&&"aac"!==this.transmuxPipeline_.type?this.setupAacPipeline():t||"ts"===this.transmuxPipeline_.type||this.setupTsPipeline(),d=!1}this.transmuxPipeline_.headOfPipeline.push(e)},this.flush=function(){d=!0,this.transmuxPipeline_.headOfPipeline.flush()}},a.prototype=new p,t.exports={Transmuxer:a,VideoSegmentStream:n,AudioSegmentStream:r,AUDIO_PROPERTIES:S,VIDEO_PROPERTIES:w}},{"../aac":55,"../codecs/adts.js":56,"../codecs/h264":57,"../data/silence":58,"../m2ts/m2ts.js":66,"../utils/clock":73,"../utils/stream.js":75,"./mp4-generator.js":71}],73:[function(e,t,i){var n,r,a,s,o,u,d=9e4;n=function(e){return e*d},r=function(e,t){return e*t},a=function(e){return e/d},s=function(e,t){return e/t},o=function(e,t){return n(s(e,t))},u=function(e,t){return r(a(e),t)},t.exports={secondsToVideoTs:n,secondsToAudioTs:r,videoTsToSeconds:a,audioTsToSeconds:s,audioTsToVideoTs:o,videoTsToAudioTs:u}},{}],74:[function(e,t,i){"use strict";var n;n=function(e){var t=e.byteLength,i=0,n=0;this.length=function(){return 8*t},this.bitsAvailable=function(){return 8*t+n},this.loadWord=function(){var r=e.byteLength-t,a=new Uint8Array(4),s=Math.min(4,t);if(0===s)throw new Error("no bytes available");a.set(e.subarray(r,r+s)),i=new DataView(a.buffer).getUint32(0),n=8*s,t-=s},this.skipBits=function(e){var r;n>e?(i<<=e,n-=e):(e-=n,r=Math.floor(e/8),e-=8*r,t-=r,this.loadWord(),i<<=e,n-=e)},this.readBits=function(e){var r=Math.min(n,e),a=i>>>32-r;return n-=r,n>0?i<<=r:t>0&&this.loadWord(),r=e-r,r>0?a<<r|this.readBits(r):a},this.skipLeadingZeros=function(){var e;for(e=0;e<n;++e)if(0!==(i&2147483648>>>e))return i<<=e,n-=e,e;return this.loadWord(),e+this.skipLeadingZeros()},this.skipUnsignedExpGolomb=function(){this.skipBits(1+this.skipLeadingZeros())},this.skipExpGolomb=function(){this.skipBits(1+this.skipLeadingZeros())},this.readUnsignedExpGolomb=function(){var e=this.skipLeadingZeros();return this.readBits(e+1)-1},this.readExpGolomb=function(){var e=this.readUnsignedExpGolomb();return 1&e?1+e>>>1:-1*(e>>>1)},this.readBoolean=function(){return 1===this.readBits(1)},this.readUnsignedByte=function(){return this.readBits(8)},this.loadWord()},t.exports=n},{}],75:[function(e,t,i){arguments[4][40][0].apply(i,arguments)},{dup:40}],76:[function(e,t,i){var n=arguments[3],r=arguments[4],a=arguments[5],s=JSON.stringify;t.exports=function(e){for(var t,i=Object.keys(a),o=0,u=i.length;o<u;o++){var d=i[o];if(a[d].exports===e){t=d;break}}if(!t){t=Math.floor(Math.pow(16,8)*Math.random()).toString(16);for(var l={},o=0,u=i.length;o<u;o++){var d=i[o];l[d]=d}r[t]=[Function(["require","module","exports"],"("+e+")(self)"),l]}var f=Math.floor(Math.pow(16,8)*Math.random()).toString(16),c={};c[t]=t,r[f]=[Function(["require"],"require("+s(t)+")(self)"),c];var h="("+n+")({"+Object.keys(r).map(function(e){return s(e)+":["+r[e][0]+","+s(r[e][1])+"]"}).join(",")+"},{},["+s(f)+"])",p=window.URL||window.webkitURL||window.mozURL||window.msURL;return new Worker(p.createObjectURL(new Blob([h],{type:"text/javascript"})))}},{}],77:[function(e,t,i){arguments[4][76][0].apply(i,arguments)},{dup:76}],78:[function(e,t,i){(function(i){"use strict";function n(e){return e&&e.__esModule?e:{"default":e}}function r(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function a(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}var s=function(){function e(e,t){for(var i=0;i<t.length;i++){var n=t[i];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}return function(t,i,n){return i&&e(t.prototype,i),n&&e(t,n),t}}(),o=function(e,t,i){for(var n=!0;n;){var r=e,a=t,s=i;n=!1,null===r&&(r=Function.prototype);var o=Object.getOwnPropertyDescriptor(r,a);if(void 0!==o){if("value"in o)return o.value;var u=o.get;if(void 0===u)return;return u.call(s)}var d=Object.getPrototypeOf(r);if(null===d)return;e=d,t=a,i=s,n=!0,o=d=void 0}},u=e("global/document"),d=n(u),l=e("./playlist-loader"),f=n(l),c=e("./playlist"),h=n(c),p=e("./xhr"),m=n(p),y=e("aes-decrypter"),g=e("./bin-utils"),v=n(g),_=e("videojs-contrib-media-sources"),b=e("m3u8-parser"),T=n(b),S="undefined"!=typeof window?window.videojs:"undefined"!=typeof i?i.videojs:null,w=n(S),k=e("./master-playlist-controller"),O=e("./config"),P=n(O),A=e("./rendition-mixin"),E=n(A),x=e("global/window"),L=n(x),I=e("./playback-watcher"),C=n(I),U=e("./reload-source-on-error"),D=n(U),M={PlaylistLoader:f["default"],Playlist:h["default"],Decrypter:y.Decrypter,AsyncStream:y.AsyncStream,decrypt:y.decrypt,utils:v["default"],xhr:(0,m["default"])()};Object.defineProperty(M,"GOAL_BUFFER_LENGTH",{get:function(){return w["default"].log.warn("using Hls.GOAL_BUFFER_LENGTH is UNSAFE be sure you know what you are doing"),P["default"].GOAL_BUFFER_LENGTH},set:function(e){return w["default"].log.warn("using Hls.GOAL_BUFFER_LENGTH is UNSAFE be sure you know what you are doing"),"number"!=typeof e||e<=0?void w["default"].log.warn("value passed to Hls.GOAL_BUFFER_LENGTH must be a number and greater than 0"):void(P["default"].GOAL_BUFFER_LENGTH=e)}});var j=1.2,B=function(e,t){var i=void 0;return e?(i=L["default"].getComputedStyle(e),i?i[t]:""):""},R=function(e,t){for(var i=t.media(),n=-1,r=0;r<e.length;r++)if(e[r].id===i.uri){n=r;break}e.selectedIndex_=n,e.trigger({selectedIndex:n,type:"change"})},N=function(e,t){t.representations().forEach(function(t){e.addQualityLevel(t)}),R(e,t.playlists)},F=function(e,t){var i=e.slice();e.sort(function(e,n){var r=t(e,n);return 0===r?i.indexOf(e)-i.indexOf(n):r})};M.STANDARD_PLAYLIST_SELECTOR=function(){var e=this.playlists.master.playlists.slice(),t=[],i=void 0,n=void 0,r=void 0,a=void 0,s=void 0,o=void 0,u=void 0,d=[],l=[],f=[];return F(e,M.comparePlaylistBandwidth),e=e.filter(h["default"].isEnabled),o=this.systemBandwidth,t=e.filter(function(e){return e.attributes&&e.attributes.BANDWIDTH&&e.attributes.BANDWIDTH*j<o}),i=t.filter(function(e){return e.attributes.BANDWIDTH===t[t.length-1].attributes.BANDWIDTH})[0],F(t,M.comparePlaylistResolution),a=parseInt(B(this.tech_.el(),"width"),10),s=parseInt(B(this.tech_.el(),"height"),10),u=t.filter(function(e){return e.attributes&&e.attributes.RESOLUTION&&e.attributes.RESOLUTION.width&&e.attributes.RESOLUTION.height}),f=u.filter(function(e){return e.attributes.RESOLUTION.width===a&&e.attributes.RESOLUTION.height===s}),r=f.filter(function(e){return e.attributes.BANDWIDTH===f[f.length-1].attributes.BANDWIDTH})[0],r||(d=u.filter(function(e){return e.attributes.RESOLUTION.width>a||e.attributes.RESOLUTION.height>s}),l=d.filter(function(e){return e.attributes.RESOLUTION.width===d[0].attributes.RESOLUTION.width&&e.attributes.RESOLUTION.height===d[0].attributes.RESOLUTION.height}),n=l.filter(function(e){return e.attributes.BANDWIDTH===l[l.length-1].attributes.BANDWIDTH})[0]),n||r||i||e[0]},M.canPlaySource=function(){return w["default"].log.warn("HLS is no longer a tech. Please remove it from your player's techOrder.")},M.supportsNativeHls=function(){var e=d["default"].createElement("video");if(!w["default"].getTech("Html5").isSupported())return!1;var t=["application/vnd.apple.mpegurl","audio/mpegurl","audio/x-mpegurl","application/x-mpegurl","video/x-mpegurl","video/mpegurl","application/mpegurl"];return t.some(function(t){return/maybe|probably/i.test(e.canPlayType(t))})}(),M.isSupported=function(){return w["default"].log.warn("HLS is no longer a tech. Please remove it from your player's techOrder.")};var G=L["default"].navigator&&L["default"].navigator.userAgent||"";M.supportsAudioInfoChange_=function(){if(w["default"].browser.IS_FIREFOX){var e=/Firefox\/([\d.]+)/i.exec(G),t=parseInt(e[1],10);return t>=49}return!0};var q=w["default"].getComponent("Component"),H=function(e){function t(e,i,n){var a=this;if(r(this,t),o(Object.getPrototypeOf(t.prototype),"constructor",this).call(this,i),i.options_&&i.options_.playerId){var s=(0,w["default"])(i.options_.playerId);s.hasOwnProperty("hls")||Object.defineProperty(s,"hls",{get:function(){return w["default"].log.warn("player.hls is deprecated. Use player.tech_.hls instead."),a}})}if(w["default"].options.hls.overrideNative&&(i.featuresNativeVideoTracks||i.featuresNativeAudioTracks))throw new Error("Overriding native HLS requires emulated tracks. See https://git.io/vMpjB");this.tech_=i,this.source_=e,this.stats={},this.ignoreNextSeekingEvent_=!1,this.options_=w["default"].mergeOptions(w["default"].options.hls||{},n.hls),this.setOptions_(),this.on(d["default"],["fullscreenchange","webkitfullscreenchange","mozfullscreenchange","MSFullscreenChange"],function(e){var t=d["default"].fullscreenElement||d["default"].webkitFullscreenElement||d["default"].mozFullScreenElement||d["default"].msFullscreenElement;t&&t.contains(a.tech_.el())&&a.masterPlaylistController_.fastQualityChange_()}),this.on(this.tech_,"seeking",function(){return this.ignoreNextSeekingEvent_?void(this.ignoreNextSeekingEvent_=!1):void this.setCurrentTime(this.tech_.currentTime())}),this.on(this.tech_,"error",function(){this.masterPlaylistController_&&this.masterPlaylistController_.pauseLoading()}),this.audioTrackChange_=function(){a.masterPlaylistController_.setupAudio()},this.on(this.tech_,"play",this.play)}return a(t,e),s(t,[{key:"setOptions_",value:function(){var e=this;this.options_.withCredentials=this.options_.withCredentials||!1,"number"!=typeof this.options_.bandwidth&&(this.options_.bandwidth=4194304),["withCredentials","bandwidth"].forEach(function(t){"undefined"!=typeof e.source_[t]&&(e.options_[t]=e.source_[t])}),this.bandwidth=this.options_.bandwidth}},{key:"src",value:function(e){var t=this;e&&(this.setOptions_(),this.options_.url=this.source_.src,this.options_.tech=this.tech_,this.options_.externHls=M,this.masterPlaylistController_=new k.MasterPlaylistController(this.options_),this.playbackWatcher_=new C["default"](w["default"].mergeOptions(this.options_,{seekable:function(){return t.seekable()}})),this.masterPlaylistController_.selectPlaylist=this.selectPlaylist?this.selectPlaylist.bind(this):M.STANDARD_PLAYLIST_SELECTOR.bind(this),this.playlists=this.masterPlaylistController_.masterPlaylistLoader_,this.mediaSource=this.masterPlaylistController_.mediaSource,Object.defineProperties(this,{selectPlaylist:{get:function(){return this.masterPlaylistController_.selectPlaylist},set:function(e){this.masterPlaylistController_.selectPlaylist=e.bind(this)}},throughput:{get:function(){return this.masterPlaylistController_.mainSegmentLoader_.throughput.rate},set:function(e){this.masterPlaylistController_.mainSegmentLoader_.throughput.rate=e,this.masterPlaylistController_.mainSegmentLoader_.throughput.count=1}},bandwidth:{get:function(){return this.masterPlaylistController_.mainSegmentLoader_.bandwidth},set:function(e){this.masterPlaylistController_.mainSegmentLoader_.bandwidth=e,this.masterPlaylistController_.mainSegmentLoader_.throughput={rate:0,count:0}}},systemBandwidth:{get:function(){var e=1/(this.bandwidth||1),t=void 0;t=this.throughput>0?1/this.throughput:0;var i=Math.floor(1/(e+t));return i},set:function(){w["default"].log.error('The "systemBandwidth" property is read-only')}}}),Object.defineProperties(this.stats,{bandwidth:{get:function(){return t.bandwidth||0},enumerable:!0},mediaRequests:{get:function(){return t.masterPlaylistController_.mediaRequests_()||0},enumerable:!0},mediaTransferDuration:{get:function(){return t.masterPlaylistController_.mediaTransferDuration_()||0},enumerable:!0},mediaBytesTransferred:{get:function(){return t.masterPlaylistController_.mediaBytesTransferred_()||0},enumerable:!0},mediaSecondsLoaded:{get:function(){return t.masterPlaylistController_.mediaSecondsLoaded_()||0},enumerable:!0}}),this.tech_.one("canplay",this.masterPlaylistController_.setupFirstPlay.bind(this.masterPlaylistController_)),this.masterPlaylistController_.on("sourceopen",function(){t.tech_.audioTracks().addEventListener("change",t.audioTrackChange_)}),this.masterPlaylistController_.on("selectedinitialmedia",function(){(0,E["default"])(t)}),this.masterPlaylistController_.on("audioupdate",function(){t.tech_.clearTracks("audio"),t.masterPlaylistController_.activeAudioGroup().forEach(function(e){t.tech_.audioTracks().addTrack(e)})}),this.on(this.masterPlaylistController_,"progress",function(){this.tech_.trigger("progress")}),this.on(this.masterPlaylistController_,"firstplay",function(){this.ignoreNextSeekingEvent_=!0}),this.tech_.ready(function(){return t.setupQualityLevels_()}),this.tech_.el()&&this.tech_.src(w["default"].URL.createObjectURL(this.masterPlaylistController_.mediaSource)))}},{key:"setupQualityLevels_",value:function(){var e=this,t=w["default"].players[this.tech_.options_.playerId];t&&t.qualityLevels&&(this.qualityLevels_=t.qualityLevels(),this.masterPlaylistController_.on("selectedinitialmedia",function(){N(e.qualityLevels_,e)}),this.playlists.on("mediachange",function(){R(e.qualityLevels_,e.playlists)}))}},{key:"activeAudioGroup_",value:function(){return this.masterPlaylistController_.activeAudioGroup()}},{key:"play",value:function(){this.masterPlaylistController_.play()}},{key:"setCurrentTime",value:function(e){this.masterPlaylistController_.setCurrentTime(e)}},{key:"duration",value:function(){return this.masterPlaylistController_.duration()}},{key:"seekable",value:function(){return this.masterPlaylistController_.seekable()}},{key:"dispose",value:function(){this.playbackWatcher_&&this.playbackWatcher_.dispose(),
this.masterPlaylistController_&&this.masterPlaylistController_.dispose(),this.qualityLevels_&&this.qualityLevels_.dispose(),this.tech_.audioTracks().removeEventListener("change",this.audioTrackChange_),o(Object.getPrototypeOf(t.prototype),"dispose",this).call(this)}}]),t}(q),V=function z(e){return{canHandleSource:function(t){return(!w["default"].options.hls||!w["default"].options.hls.mode||w["default"].options.hls.mode===e)&&z.canPlayType(t.type)},handleSource:function(t,i,n){"flash"===e&&i.setTimeout(function(){i.trigger("loadstart")},1);var r=w["default"].mergeOptions(n,{hls:{mode:e}});return i.hls=new H(t,i,r),i.hls.xhr=(0,m["default"])(),w["default"].Hls.xhr.beforeRequest&&(i.hls.xhr.beforeRequest=w["default"].Hls.xhr.beforeRequest),i.hls.src(t.src),i.hls},canPlayType:function(e){return z.canPlayType(e)?"maybe":""}}};M.comparePlaylistBandwidth=function(e,t){var i=void 0,n=void 0;return e.attributes&&e.attributes.BANDWIDTH&&(i=e.attributes.BANDWIDTH),i=i||L["default"].Number.MAX_VALUE,t.attributes&&t.attributes.BANDWIDTH&&(n=t.attributes.BANDWIDTH),n=n||L["default"].Number.MAX_VALUE,i-n},M.comparePlaylistResolution=function(e,t){var i=void 0,n=void 0;return e.attributes&&e.attributes.RESOLUTION&&e.attributes.RESOLUTION.width&&(i=e.attributes.RESOLUTION.width),i=i||L["default"].Number.MAX_VALUE,t.attributes&&t.attributes.RESOLUTION&&t.attributes.RESOLUTION.width&&(n=t.attributes.RESOLUTION.width),n=n||L["default"].Number.MAX_VALUE,i===n&&e.attributes.BANDWIDTH&&t.attributes.BANDWIDTH?e.attributes.BANDWIDTH-t.attributes.BANDWIDTH:i-n},V.canPlayType=function(e){if(w["default"].browser.IE_VERSION&&w["default"].browser.IE_VERSION<=10)return!1;var t=/^(audio|video|application)\/(x-|vnd\.apple\.)?mpegurl/i;return!(!w["default"].options.hls.overrideNative&&M.supportsNativeHls)&&t.test(e)},"undefined"!=typeof w["default"].MediaSource&&"undefined"!=typeof w["default"].URL||(w["default"].MediaSource=_.MediaSource,w["default"].URL=_.URL);var X=w["default"].getTech("Flash");_.MediaSource.supportsNativeMediaSources()&&w["default"].getTech("Html5").registerSourceHandler(V("html5"),0),L["default"].Uint8Array&&X&&X.registerSourceHandler(V("flash")),w["default"].HlsHandler=H,w["default"].HlsSourceHandler=V,w["default"].Hls=M,w["default"].use||w["default"].registerComponent("Hls",M),w["default"].m3u8=T["default"],w["default"].options.hls=w["default"].options.hls||{},w["default"].registerPlugin?w["default"].registerPlugin("reloadSourceOnError",D["default"]):w["default"].plugin("reloadSourceOnError",D["default"]),t.exports={Hls:M,HlsHandler:H,HlsSourceHandler:V}}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{"./bin-utils":2,"./config":3,"./master-playlist-controller":5,"./playback-watcher":6,"./playlist":8,"./playlist-loader":7,"./reload-source-on-error":10,"./rendition-mixin":11,"./xhr":17,"aes-decrypter":21,"global/document":27,"global/window":28,"m3u8-parser":29,"videojs-contrib-media-sources":53}]},{},[78])(78)});
!function(){"use strict";if(navigator.userAgent.match(/IEMobile\/10\.0/)){var e=document.createElement("style");e.appendChild(document.createTextNode("@-ms-viewport{width:auto!important}")),document.querySelector("head").appendChild(e)}}();
!function(){"use strict";var e=null;e="undefined"==typeof window.videojs&&"function"==typeof require?require("video.js"):window.videojs,function(e,t){var r,n={ui:!0},l=t.getComponent("MenuItem"),s=t.extend(l,{constructor:function(e,r){r.selectable=!0,l.call(this,e,r),this.src=r.src,e.on("resolutionchange",t.bind(this,this.update))}});s.prototype.handleClick=function(e){l.prototype.handleClick.call(this,e),this.player_.currentResolution(this.options_.label)},s.prototype.update=function(){var e=this.player_.currentResolution();this.selected(this.options_.label===e.label)},l.registerComponent("ResolutionMenuItem",s);var o=t.getComponent("MenuButton"),i=t.extend(o,{constructor:function(e,r){if(this.label=document.createElement("span"),r.label="Quality",o.call(this,e,r),this.el().setAttribute("aria-label","Quality"),this.controlText("Quality"),r.dynamicLabel)t.addClass(this.label,"vjs-resolution-button-label"),this.el().appendChild(this.label);else{var n=document.createElement("span");t.addClass(n,"vjs-menu-icon"),this.el().appendChild(n)}e.on("updateSources",t.bind(this,this.update))}});i.prototype.createItems=function(){var e=[],t=this.sources&&this.sources.label||{};for(var r in t)t.hasOwnProperty(r)&&e.push(new s(this.player_,{label:r,src:t[r],selected:r===(!!this.currentSelection&&this.currentSelection.label)}));return e},i.prototype.update=function(){return this.sources=this.player_.getGroupedSrc(),this.currentSelection=this.player_.currentResolution(),this.label.innerHTML=this.currentSelection?this.currentSelection.label:"",o.prototype.update.call(this)},i.prototype.buildCSSClass=function(){return o.prototype.buildCSSClass.call(this)+" vjs-resolution-button"},o.registerComponent("ResolutionMenuButton",i),r=function(e){function r(e,t){return e.res&&t.res?+t.res-+e.res:0}function l(e){var t={label:{},res:{},type:{}};return e.map(function(e){s(t,"label",e),s(t,"res",e),s(t,"type",e),o(t,"label",e),o(t,"res",e),o(t,"type",e)}),t}function s(e,t,r){null==e[t][r[t]]&&(e[t][r[t]]=[])}function o(e,t,r){e[t][r[t]].push(r)}function u(e,t){var r=c["default"],n="";return"high"===r?(r=t[0].res,n=t[0].label):"low"!==r&&null!=r&&e.res[r]?e.res[r]&&(n=e.res[r][0].label):(r=t[t.length-1].res,n=t[t.length-1].label),{res:r,label:n,sources:e.res[r]}}function a(e){var t={highres:{res:1080,label:"1080",yt:"highres"},hd1080:{res:1080,label:"1080",yt:"hd1080"},hd720:{res:720,label:"720",yt:"hd720"},large:{res:480,label:"480",yt:"large"},medium:{res:360,label:"360",yt:"medium"},small:{res:240,label:"240",yt:"small"},tiny:{res:144,label:"144",yt:"tiny"},auto:{res:0,label:"auto",yt:"auto"}},r=function(t,r,n){return e.tech_.ytPlayer.setPlaybackQuality(r[0]._yt),e.trigger("updateSources"),e};c.customSourcePicker=r,e.tech_.ytPlayer.setPlaybackQuality("auto"),e.tech_.ytPlayer.addEventListener("onPlaybackQualityChange",function(n){for(var l in t)if(l.yt===n.data)return void e.currentResolution(l.label,r)}),e.one("play",function(){var n=e.tech_.ytPlayer.getAvailableQualityLevels(),s=[];n.map(function(r){s.push({src:e.src().src,type:e.src().type,label:t[r].label,res:t[r].res,_yt:t[r].yt})}),e.groupedSrc=l(s);var o={label:"auto",res:0,sources:e.groupedSrc.label.auto};this.currentResolutionState={label:o.label,sources:o.sources},e.trigger("updateSources"),e.setSourcesSanitized(o.sources,o.label,r)})}var c=t.mergeOptions(n,e),h=this;h.updateSrc=function(e){if(!e)return h.src();e=e.filter(function(e){try{return""!==h.canPlayType(e.type)}catch(t){return!0}}),this.currentSources=e.sort(r),this.groupedSrc=l(this.currentSources);var t=u(this.groupedSrc,this.currentSources);return this.currentResolutionState={label:t.label,sources:t.sources},h.trigger("updateSources"),h.setSourcesSanitized(t.sources,t.label),h.trigger("resolutionchange"),h},h.currentResolution=function(e,t){if(null==e)return this.currentResolutionState;if(this.groupedSrc&&this.groupedSrc.label&&this.groupedSrc.label[e]){var r=this.groupedSrc.label[e],n=h.currentTime(),l=h.paused();!l&&this.player_.options_.bigPlayButton&&this.player_.bigPlayButton.hide();var s="loadeddata";return"Youtube"!==this.player_.techName_&&"none"===this.player_.preload()&&"Flash"!==this.player_.techName_&&(s="timeupdate"),h.setSourcesSanitized(r,e,t||c.customSourcePicker).one(s,function(){h.currentTime(n),h.handleTechSeeked_(),l||h.play().handleTechSeeked_(),h.trigger("resolutionchange")}),h}},h.getGroupedSrc=function(){return this.groupedSrc},h.setSourcesSanitized=function(e,t,r){return this.currentResolutionState={label:t,sources:e},"function"==typeof r?r(h,e,t):(h.src(e.map(function(e){return{src:e.src,type:e.type,res:e.res}})),h)},h.ready(function(){if(c.ui){var e=new i(h,c);h.controlBar.resolutionSwitcher=h.controlBar.el_.insertBefore(e.el_,h.controlBar.getChild("fullscreenToggle").el_),h.controlBar.resolutionSwitcher.dispose=function(){this.parentNode.removeChild(this)}}h.options_.sources.length>1&&h.updateSrc(h.options_.sources),"Youtube"===h.techName_&&a(h)})},t.plugin("videoJsResolutionSwitcher",r)}(window,e)}();