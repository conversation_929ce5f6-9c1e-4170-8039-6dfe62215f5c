/******/ (function() { // webpackBootstrap
/******/ 	// The require scope
/******/ 	var __webpack_require__ = {};
/******/ 	
/************************************************************************/
/******/ 	/* webpack/runtime/global */
/******/ 	!function() {
/******/ 		__webpack_require__.g = (function() {
/******/ 			if (typeof globalThis === 'object') return globalThis;
/******/ 			try {
/******/ 				return this || new Function('return this')();
/******/ 			} catch (e) {
/******/ 				if (typeof window === 'object') return window;
/******/ 			}
/******/ 		})();
/******/ 	}();
/******/ 	
/************************************************************************/
/*!*********************************************!*\
  !*** ./resources/assets/js/course/basic.js ***!
  \*********************************************/
(function ($) {
  var app;
  function Singleton() {
    if (app) {
      return app;
    }
    app = this;
    this.init = function () {
      app.setVars();
      $(document).on("click", ".head", function () {
        var classList = $(this).attr("class").split(/\s+/);
        if (!classList.includes("disabled")) {
          $(this).next().toggle();
          $(this).toggleClass("is-open");
          $(this).closest(".stage").find(".lesson-group-item").each(function () {
            $(this).addClass("is-open");
          });
        }
      });
      $(document).on("click", ".btn-teacher-info", function () {
        $("#modal-teacher-info").modal("show");
        $(".teacher-info").hide();
        $($(this).data("id")).show();
      });
      $(document).on("click", ".btn-more-feedback", function () {
        $(".feedback-more-item").show();
        $(this).hide();
      });
      app.openAllButton.on("click", function () {
        if (!$(this).hasClass("active")) {
          app.head.next().show();
          app.head.addClass("is-open");
          $(this).addClass("active");
          $(this).text("Đóng tất cả các phần");
          app.overlayFoot.hide();
          app.btnShowAllStage.hide();
          app.stageItem.show();
        } else {
          app.head.next().hide();
          app.head.removeClass("is-open");
          $(this).removeClass("active");
          $(this).text("Mở rộng tất cả các phần");
        }
      });
      app.btnShowAllStage.on("click", function () {
        app.overlayFoot.hide();
        app.btnShowAllStage.hide();
        app.stageItem.show();
      });
      $(document).on("click", ".lesson-group-item__head", function () {
        $(this).closest(".lesson-group-item").toggleClass("is-open");
      });
      $(document).on("click", ".course-step", function () {
        var currentStep = parseInt($(this).data("step"));
        var isDesktop = window.innerWidth > 1024;
        // Scroll
        // Desktop
        if (isDesktop) {
          if (currentStep == 2) {
            var target = $("#step-content-3"); // Get target element
            if (target.length) {
              $("html, body").animate({
                scrollTop: target.offset().top - 270
              }, 100);
            }
          } else {
            window.scrollTo({
              top: 0,
              behavior: "smooth"
            });
          }
        } else {
          // Mobile
          if (currentStep == 1) {
            var target = $("#step-content-2"); // Get target element
            if (target.length) {
              $("html, body").animate({
                scrollTop: target.offset().top - 450
              }, 100);
            }
          } else if (currentStep == 2) {
            var target = $("#step-content-3"); // Get target element
            if (target.length) {
              $("html, body").animate({
                scrollTop: target.offset().top - 330
              }, 100);
            }
          } else {
            window.scrollTo({
              top: 0,
              behavior: "smooth"
            });
          }
        }
        if (currentStep == 3 && !isDesktop) {
          $(".tutorial").remove();
          enableScroll();
          return;
        }
        if ($("[data-step=" + (currentStep + 1) + "]").length > 0) {
          $(".course-step").removeClass("active");
          $("[data-step=" + (currentStep + 1) + "]").addClass("active");
        } else {
          $(".course-step").removeClass("active");
          $(".tutorial").remove();
          enableScroll();
        }
      });
      $(document).on("click", ".lesson-step", function () {
        var currentStep = parseInt($(this).data("step"));
        var isDesktop = window.innerWidth > 1024;
        // Scroll

        if (!isDesktop && currentStep == 3) {
          $(".lesson-tutorial").remove();
          enableScroll();
          $("#lesson-basic-container").removeClass("training");
          return;
        }
        if (currentStep == 1 && isDesktop && !$("#tabList").length) {
          // Skip step 2
          currentStep += 1;
        }
        if (currentStep == 2) {
          $("#menuTabComment").trigger("click");
          $("#lesson-basic-container").addClass("training");
        }
        if (currentStep == 3) {
          $("#menuTabLessonList").trigger("click");
          $("#lesson-basic-container").addClass("training");
        }
        if (currentStep == 4) {
          $(".lesson-tutorial").remove();
          enableScroll();
          $("#lesson-basic-container").removeClass("training");
          return;
        }
        if ($("[data-step=" + (currentStep + 1) + "]").length > 0) {
          $(".lesson-step").removeClass("active");
          $("[data-step=" + (currentStep + 1) + "]").addClass("active");
        } else {
          $(".lesson-step").removeClass("active");
          $(".lesson-tutorial").remove();
          $("#lesson-basic-container").removeClass("training");
          enableScroll();
        }
      });
      fixRequireButton();
      $(document).on("click", ".btn-profile", function () {
        $(this).find(".menu").toggleClass("hidden");
      });
    };
    app.setVars = function () {
      app.openAllButton = $(".open-all");
      app.head = $(".head");
      app.overlayFoot = $(".overlay-foot");
      app.btnShowAllStage = $(".btn-show-all-stage");
      app.stageItem = $(".stage");
    };
    return app;
  }
  $.fn.singleton = function () {
    var singletonInstance = new Singleton();
    singletonInstance.init();
    return this;
  };
})(jQuery);

// Usage
$(document).ready(function () {
  $(document).singleton();
});
__webpack_require__.g.fixRequireButton = function () {
  // Event require button
  $(".require-icon").each(function (i, el) {
    if ($(el).closest(".truncate")) {
      var scrollWidth = $(el).closest(".truncate")[0].scrollWidth;
      var innerWidth = $(el).parent().innerWidth();
      if (scrollWidth > 0 && scrollWidth === innerWidth) {
        $(el).addClass("text-over");
      }
    }
  });
};

// Prevent scroll
var keys = {
  37: 1,
  38: 1,
  39: 1,
  40: 1
};
function preventDefault(e) {
  e.preventDefault();
}
function preventDefaultForScrollKeys(e) {
  if (keys[e.keyCode]) {
    preventDefault(e);
    return false;
  }
}

// modern Chrome requires { passive: false } when adding event
var supportsPassive = false;
try {
  window.addEventListener("test", null, Object.defineProperty({}, "passive", {
    get: function get() {
      supportsPassive = true;
    }
  }));
} catch (e) {}
var wheelOpt = supportsPassive ? {
  passive: false
} : false;
var wheelEvent = "onwheel" in document.createElement("div") ? "wheel" : "mousewheel";

// call this to Disable
__webpack_require__.g.disableScroll = function () {
  window.addEventListener("DOMMouseScroll", preventDefault, false); // older FF
  window.addEventListener(wheelEvent, preventDefault, wheelOpt); // modern desktop
  window.addEventListener("touchmove", preventDefault, wheelOpt); // mobile
  window.addEventListener("keydown", preventDefaultForScrollKeys, false);
};

// call this to Enable
__webpack_require__.g.enableScroll = function () {
  window.removeEventListener("DOMMouseScroll", preventDefault, false);
  window.removeEventListener(wheelEvent, preventDefault, wheelOpt);
  window.removeEventListener("touchmove", preventDefault, wheelOpt);
  window.removeEventListener("keydown", preventDefaultForScrollKeys, false);
};
/******/ })()
;