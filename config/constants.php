<?php
if (! defined('DEFAULT_TIMEZONE')) {
    define('DEFAULT_TIMEZONE', 'Asia/Ho_Chi_Minh');
}

// API meta code
if (! defined('HTTP_STATUS_SUCCESS')) {
    define('HTTP_STATUS_SUCCESS', 200);
}

if (! defined('HTTP_STATUS_SUCCESS_NO_CONTENT')) {
    define('HTTP_STATUS_SUCCESS_NO_CONTENT', 204);
}

if (! defined('HTTP_STATUS_BAD_REQUEST')) {
    define('HTTP_STATUS_BAD_REQUEST', 400);
}

if (! defined('HTTP_STATUS_UNAUTHORIZED')) {
    define('HTTP_STATUS_UNAUTHORIZED', 401);
}

if (! defined('HTTP_STATUS_FORBIDDEN')) {
    define('HTTP_STATUS_FORBIDDEN', 403);
}

if (! defined('HTTP_STATUS_NOT_FOUND')) {
    define('HTTP_STATUS_NOT_FOUND', 404);
}

if (! defined('HTTP_STATUS_METHOD_NOT_ALLOW')) {
    define('HTTP_STATUS_METHOD_NOT_ALLOW', 405);
}

if (! defined('HTTP_STATUS_NOT_ACCEPT')) {
    define('HTTP_STATUS_NOT_ACCEPT', 406);
}

if (! defined('HTTP_STATUS_WRONG_PARAM')) {
    define('HTTP_STATUS_WRONG_PARAM', 412);
}

// Status
if (! defined('INACTIVE')) {
    define('INACTIVE', 0);
}

if (! defined('ACTIVE')) {
    define('ACTIVE', 1);
}
if (! defined('DEFAULT_PER_PAGE')) {
    define('DEFAULT_PER_PAGE', 10);
}
if (! defined('DEFAULT_PAGINATE')) {
    define('DEFAULT_PAGINATE', 10);
}

if (! defined('DEFAULT_PER_PAGE')) {
    define('DEFAULT_PER_PAGE', 10);
}

// Các dạng bài test
if (! defined('TEST_TYPE')) {
    define('TEST_TYPE', [
        1 => 'community', // Community Tests
    ]);
}
if (! defined("SKILL")) {
    define("SKILL", [
        "VOCABULARY" => 1,
        "KANJI"      => 2,
        "GRAMMAR"    => 3,
        "READING"    => 4,
        "LISTENING"  => 5,
    ]);
}
if (! defined("SKILL_LABEL")) {
    define("SKILL_LABEL", [
        1 => "VOCABULARY",
        2 => "KANJI",
        3 => "GRAMMAR",
        4 => "READING",
        5 => "LISTENING",
    ]);
}
if (! defined("LEVEL")) {
    define("LEVEL", [
        "N1" => "N1",
        "N2" => "N2",
        "N3" => "N3",
        "N4" => "N4",
        "N5" => "N5",
    ]);
}
if (! defined("OFFLINE_LEVEL")) {
    define("OFFLINE_LEVEL", [
        "N1" => "5",
        "N2" => "4",
        "N3" => "3",
        "N4" => "2",
        "N5" => "1",
    ]);
}
// Các dạng bài test
if (! defined('ONLINE_COURSES')) {
    define('ONLINE_COURSES', [
        17 => ['N1'],
        16 => ['N2'],
        3  => ['N3'],
        4  => ['N4'],
        5  => ['N5'],
        39 => ['N5'],
        40 => ['N4'],
        25 => ['kaiwacb', 'kaiwasc'],
        34 => ['kaiwatc', 'kaiwatc2'],
        35 => ['kaiwanc'],
        33 => ['ldn1'],
        32 => ['ldn2'],
        31 => ['ldn3'],
        30 => ['ldn4'],
        41 => ['ldn5'],
        44 => ['N3'],
        45 => ['N2'],
        46 => ['N1'],
    ]);
}
// Các dạng bài test
if (! defined('JLPT_STRUCTURES')) {
    define('JLPT_STRUCTURES', [
        'N1' => [[2, 1, 3, 4], 5],
        'N2' => [[2, 1, 3, 4], 5],
        'N3' => [[2, 1], [3, 4], 5],
        'N4' => [[2, 1], [3, 4], 5],
        'N5' => [[2, 1], [3, 4], 5],
    ]);
}
// Các dạng bài test
if (! defined('JLPT_RESULT_STRUCTURES')) {
    define('JLPT_RESULT_STRUCTURES', [
        'N1' => [[2, 1], [3, 4], 5],
        'N2' => [[2, 1], [3, 4], 5],
        'N3' => [[2, 1], [3, 4], 5],
        'N4' => [[2, 1, 3, 4], 5],
        'N5' => [[2, 1, 3, 4], 5],
    ]);
}

// Level test mới
if (! defined('BASIC_LEVELS')) {
    define('BASIC_LEVELS', [
        30 => 'LD_N4',
        39 => 'N5',
        40 => 'N4',
        41 => 'LD_N5',
        44 => 'N3',
        45 => 'N2',
        46 => 'N1',
        47 => 'KANJI_N5'
    ]);
}
// Các dạng bài test
if (! defined('SCHOOL_ROLES')) {
    define('SCHOOL_ROLES', [
        'SUPER_ADMIN' => 1,
        'TEACHER'     => 2,
        'STUDENT'     => 3,
        'STAFF'       => 4,
    ]);
}
if (! defined("INVOICE_STATUS")) {
    define("INVOICE_STATUS", [
        "NEW"       => "new",
        "COMPLETED" => "completed",
        "CANCELED"  => "canceled",
    ]);
}

if (! defined("COURSE_BASIC_NEW_FEEDBACK")) {
    define("COURSE_BASIC_NEW_FEEDBACK", [
        [
            "name"    => "Lê Quốc Trung",
            "content" => "SS ơi em đậu N3 rồi ạ! Nhớ món quà tinh thần của các Sensei và những video tổng hợp em xem lúc bắt đầu học tiếng Nhật và theo dõi Dungmori!",
            "time"    => "01/01/2024",
        ],
        [
            "name"    => "Kim Quynh Anh",
            "content" => "SS ơi em đậu N3 rồi ạ! Nhớ món quà tinh thần của các Sensei và những video tổng hợp em xem lúc bắt đầu học tiếng Nhật và theo dõi Dungmori!",
            "time"    => "01/01/2024",
        ],

        [
            "name"    => "Ha Phan",
            "content" => "SS ơi em đậu N3 rồi ạ! Nhớ món quà tinh thần của các Sensei và những video tổng hợp em xem lúc bắt đầu học tiếng Nhật và theo dõi Dungmori!",
            "time"    => "01/01/2024",
        ],
        [
            "name"    => "Nguyen Phương",
            "content" => "SS ơi em đậu N3 rồi ạ! Nhớ món quà tinh thần của các Sensei và những video tổng hợp em xem lúc bắt đầu học tiếng Nhật và theo dõi Dungmori!",
            "time"    => "01/01/2024",
        ],

    ]);
}

if (! defined("NEW_BASIC_COURSE")) {
    define("NEW_BASIC_COURSE", [
        'so-cap-n5',
        'so-cap-n4',
        'luyen-de-n4',
        'luyen-de-n5',
        'chu-han-n5',
    ]);
}

if (! defined("NEW_HIGHLEVEL_COURSE")) {
    define("NEW_HIGHLEVEL_COURSE", [
        'n3-moi',
        'n2-moi',
        'n1-moi',
    ]);
}

if (! defined("EXCHANGE_RATE_JP_VND")) {
    define("EXCHANGE_RATE_JP_VND", 177);
}

return [
    'provinces' => [
        '0'  => 'Nhật Bản',
        '-1' => 'Khác',
        '1'  => 'Hồ Chí Minh',
        '2'  => 'Hà Nội',
        '3'  => 'Đà Nẵng',
        '4'  => 'Bình Dương',
        '5'  => 'Đồng Nai',
        '6'  => 'Khánh Hòa',
        '7'  => 'Hải Phòng',
        '8'  => 'Long An',
        '9'  => 'Quảng Nam',
        '10' => 'Bà Rịa Vũng Tàu',
        '11' => 'Đắk Lắk',
        '12' => 'Cần Thơ',
        '13' => 'Bình Thuận',
        '14' => 'Lâm Đồng',
        '15' => 'Thừa Thiên Huế',
        '16' => 'Kiên Giang',
        '17' => 'Bắc Ninh',
        '18' => 'Quảng Ninh',
        '19' => 'Thanh Hóa',
        '20' => 'Nghệ An',
        '21' => 'Hải Dương',
        '22' => 'Gia Lai',
        '23' => 'Bình Phước',
        '24' => 'Hưng Yên',
        '25' => 'Bình Định',
        '26' => 'Tiền Giang',
        '27' => 'Thái Bình',
        '28' => 'Bắc Giang',
        '29' => 'Hòa Bình',
        '30' => 'An Giang',
        '31' => 'Vĩnh Phúc',
        '32' => 'Tây Ninh',
        '33' => 'Thái Nguyên',
        '34' => 'Lào Cai',
        '35' => 'Nam Định',
        '36' => 'Quảng Ngãi',
        '37' => 'Bến Tre',
        '38' => 'Đắk Nông',
        '39' => 'Cà Mau',
        '40' => 'Vĩnh Long',
        '41' => 'Ninh Bình',
        '42' => 'Phú Thọ',
        '43' => 'Ninh Thuận',
        '44' => 'Phú Yên',
        '45' => 'Hà Nam',
        '46' => 'Hà Tĩnh',
        '47' => 'Đồng Tháp',
        '48' => 'Sóc Trăng',
        '49' => 'Kon Tum',
        '50' => 'Quảng Bình',
        '51' => 'Quảng Trị',
        '52' => 'Trà Vinh',
        '53' => 'Hậu Giang',
        '54' => 'Sơn La',
        '55' => 'Bạc Liêu',
        '56' => 'Yên Bái',
        '57' => 'Tuyên Quang',
        '58' => 'Điện Biên',
        '59' => 'Lai Châu',
        '60' => 'Lạng Sơn',
        '61' => 'Hà Giang',
        '62' => 'Bắc Kạn',
        '63' => 'Cao Bằng',
    ],
];
